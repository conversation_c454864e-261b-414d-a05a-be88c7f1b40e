import { useState } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { isAddress } from "viem";
import { z } from "zod";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import { Dialog, DialogClose, DialogContent } from "@/components/ui/dialog";
import { TextInput } from "@/components/ui/text-input";
import { useConnectExternalWalletMutation } from "@/queries";

interface ConnectExternalWalletModalProps {
  open: boolean;
  setOpen: (value: boolean) => void;
}

export const ConnectExternalWalletModal = ({
  open,
  setOpen,
}: ConnectExternalWalletModalProps) => {
  const [connect, setConnect] = useState(false);
  const [success, setSuccess] = useState(false);

  const { mutateAsync: connectExternalWallet, isPending } =
    useConnectExternalWalletMutation();

  const ConnectedWalletInput = z.object({
    connectedWalletAddress: z.custom<string>(isAddress, "Invalid Address"),
  });
  type ConnectedWalletInputType = z.infer<typeof ConnectedWalletInput>;

  const form = useForm<ConnectedWalletInputType>({
    resolver: zodResolver(ConnectedWalletInput),
    reValidateMode: "onChange",
  });

  const onSubmit = async () => {
    const isValid = await form.trigger();
    if (!isValid) return;
    await connectExternalWallet(form.getValues());
    // TODO: add toast
    setSuccess(true);
  };

  const handleConnect = () => {
    setConnect(true);
  };
  const handleDone = () => {
    setSuccess(false);
    setConnect(false);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="flex h-full w-full flex-col overflow-auto bg-dark-bk p-0 px-6 backdrop-blur-sm sm:h-auto"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <div className="flex items-center gap-2 pt-[calc(1rem+env(safe-area-inset-top))]">
          <div className="flex-1">
            <DialogClose className="flex flex-shrink-0">
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </DialogClose>
          </div>
          <h2 className="text-base font-semibold leading-[22px] text-off-white">
            Connect External Wallet
          </h2>
          <div className="flex-1" />
        </div>
        <div className="mx-auto my-2 flex items-center justify-center">
          <div className="w-[340px] rounded-[10px]">
            <img
              src="/images/the-looty.png"
              className="object-cover"
              alt="Looty"
            />
          </div>
        </div>
        {!connect && !success && (
          <>
            <div className="mx-4 flex flex-col gap-3">
              <p className="whitespace-pre-wrap text-base font-semibold leading-[24px] text-off-white">
                Provide a wallet on the Avalanche C&#8209;Chain to be eligible
                for Looty keys
              </p>
              <p className="text-sm  text-[#B5B5B5]">
                Please provide a secure external wallet address to be shared
                with the Looty team.
              </p>
              <p className="text-sm text-[#B5B5B5]">
                Submitted wallets will be eligible to mint Looty Keys on
                Looty.fi, based on their staked $ARENA balance. These keys can
                then be used to open loot boxes.
              </p>
            </div>
            <div className="mt-auto pb-[calc(1.5rem+env(safe-area-inset-bottom))]">
              <Button onClick={handleConnect} className="w-full">
                Continue
              </Button>
            </div>
          </>
        )}
        {connect && !success && (
          <>
            <div className="mx-4 flex flex-col gap-2">
              <p className="whitespace-pre-wrap text-base font-semibold leading-[24px] text-off-white">
                Please make sure the wallet you have provided is correct before
                proceeding
              </p>
              <p className="text-sm text-[#B5B5B5]">
                Please ensure the provided wallet is correct before proceeding.
                This wallet will be used to mint your Looty Keys and open loot
                boxes on Looty.fi.
              </p>
            </div>
            <form
              className="flex flex-col"
              onSubmit={(e) => e.preventDefault()}
            >
              <TextInput
                label="WALLET ADDRESS"
                placeholder="Enter wallet address"
                {...form.register("connectedWalletAddress")}
                errorMessage={
                  form.formState.errors.connectedWalletAddress?.message
                }
              />
            </form>
            <div className="mt-auto flex gap-2 pb-[calc(1.5rem+env(safe-area-inset-bottom))]">
              <Button
                className="flex w-1/2"
                variant="outline"
                onClick={() => {
                  setConnect(false);
                }}
              >
                Back
              </Button>
              <Button
                className="flex w-1/2"
                type="submit"
                onClick={onSubmit}
                disabled={isPending}
              >
                Confirm
              </Button>
            </div>
          </>
        )}
        {success && (
          <>
            <div className="mx-4 flex flex-col gap-3">
              <p className="whitespace-pre-wrap text-xl leading-[20px] text-off-white">
                Congratulations!
              </p>
              <p className="mb-1 text-sm text-[#B5B5B5]">
                You have connected your wallet to your Arena account to be
                eligible in the upcoming Looty mint.
              </p>
              <p className="mb-8 text-sm text-[#B5B5B5]">
                You can browse through the latest claimed rewards on Looty.fi
              </p>
            </div>

            <div className="mt-auto flex gap-2 pb-[calc(1.5rem+env(safe-area-inset-bottom))]">
              <Button onClick={handleDone} className="w-full">
                Back to profile
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};
