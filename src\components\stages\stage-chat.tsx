"use client";

import { memo, useEffect, useLayoutEffect, useRef, useState } from "react";

import {
  ReceivedChatMessage,
  useTrackMutedIndicator,
} from "@livekit/components-react";
import Document from "@tiptap/extension-document";
import HardBreak from "@tiptap/extension-hard-break";
import History from "@tiptap/extension-history";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, useEditor } from "@tiptap/react";
import { Emoji } from "emoji-picker-react";
import DOMPurify from "isomorphic-dompurify";
import { Track } from "livekit-client";
import { ErrorBoundary } from "react-error-boundary";
import { Virtuoso, VirtuosoHandle } from "react-virtuoso";

import { useTippableCurrenciesQuery } from "@/queries/currency-queries";
import { useStageStore } from "@/stores/stage";
import { cn, numberFormatter } from "@/utils";
import { IS_ANDROID, IS_IOS } from "@/utils/window-environment";

import { SendOutlineIcon } from "../icons";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Role, ROLE_NAMES, ROLES } from "./constants";
import { StageUserProfileModal } from "./stage-user-profile-modal";
import { useDataChannelsContext } from "./stores/data-channels-context";

export const StageChatContainer = memo(() => {
  const isOpen = useStageStore((state) => state.chat.isOpen);

  if (!isOpen) return null;

  return (
    <ErrorBoundary fallback={null}>
      <StageChat />
    </ErrorBoundary>
  );
});

StageChatContainer.displayName = "StageChatContainer";

export const StageChat = () => {
  const { send, chatMessages, isSending } = useDataChannelsContext().chat;
  const parentRef = useRef<HTMLDivElement>(null);
  const virtuoso = useRef<VirtuosoHandle | null>(null);

  const isFullScreen = useStageStore((state) => state.chat.isFullScreen);

  const extensions = [
    Document.extend({
      addKeyboardShortcuts() {
        return {
          Enter: () => {
            if (IS_IOS || IS_ANDROID) {
              // If on mobile, insert a hard break instead of sending the message
              this.editor.commands.insertContent("<br/>");
              return true;
            }
            if (this.editor.getText().trim() === "") {
              return true;
            }
            const text = this.editor.getHTML();
            let _message = text.replace(/<p>/g, "").replace(/<\/p>/g, "");
            this.editor.chain().focus().clearContent().run();
            _message = DOMPurify.sanitize(_message);

            const message = {
              type: "text",
              message: _message,
            };
            virtuoso.current?.scrollToIndex({
              index: "LAST",
              behavior: "smooth",
            });
            send(JSON.stringify(message));
            return true;
          },
        };
      },
    }),
    Text,
    Paragraph,
    HardBreak,
    History,
    Placeholder.configure({
      placeholder: ({ editor }) => {
        return (
          editor.options.element.getAttribute("data-placeholder") ??
          "Type something..."
        );
      },
    }),
  ];

  const editor = useEditor({
    immediatelyRender: false,
    extensions,
    editorProps: {
      attributes: {
        class:
          "focus:outline-none h-auto max-h-20 w-full overflow-y-auto py-4 pl-5 pr-11 text-sm",
      },
    },
  });

  const handleSend = async () => {
    if (!editor || editor?.getText().trim() === "") return true;

    let _message = "";

    const text = editor.getHTML();
    _message = text.replace(/<p>/g, "").replace(/<\/p>/g, "");
    editor.chain().focus().clearContent().run();

    _message = DOMPurify.sanitize(_message);

    const message = {
      type: "text",
      message: _message,
    };

    virtuoso.current?.scrollToIndex({
      index: "LAST",
      behavior: "smooth",
    });
    await send(JSON.stringify(message));
  };

  useEffect(() => {
    if ((!IS_IOS && !IS_ANDROID) || typeof window === "undefined") {
      return undefined;
    }

    const { visualViewport } = window;
    if (!visualViewport) {
      return undefined;
    }

    const handleResize = () => {
      const isFixNeeded =
        visualViewport.height < document.documentElement.clientHeight;

      parentRef.current?.parentElement?.parentElement?.parentElement?.parentElement?.classList.toggle(
        "tbottom-stage-pwa",
        isFixNeeded,
      );
    };

    visualViewport.addEventListener("resize", handleResize);

    return () => {
      visualViewport.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <div
      className={cn(
        "stage-chat-mask absolute inset-x-0 bottom-0 z-10 flex flex-col bg-chat-bg-gradient",
        // isFullScreen ? "h-full" : "h-1/2",
        "h-1/2",
      )}
    >
      <Virtuoso
        ref={virtuoso}
        className="h-full overscroll-contain py-6"
        data={chatMessages}
        itemContent={(index, message) => (
          <ErrorBoundary fallback={null} key={message.id ?? index}>
            <Message message={message} />
          </ErrorBoundary>
        )}
        initialTopMostItemIndex={chatMessages.length - 1}
        alignToBottom
        followOutput="smooth"
        components={{
          Header,
          Footer,
        }}
      />
      <div className="flex flex-shrink-0 flex-col gap-4 bg-[#141414] bg-opacity-[0.88] py-1 pr-2 backdrop-blur-sm">
        <div className="flex items-end gap-1">
          <div className="relative flex min-w-0 flex-grow items-end bg-transparent">
            <EditorContent editor={editor} className="w-full" />
          </div>
          <button
            className="mb-1 flex size-[45px] flex-shrink-0 items-center justify-center"
            disabled={isSending}
            onClick={() => {
              handleSend();
            }}
          >
            <SendOutlineIcon className="size-6 text-off-white" />
          </button>
        </div>
      </div>
      <div className="pointer-events-none absolute inset-x-0 top-0 z-20 h-[100px] bg-chat-overlay-gradient" />
    </div>
  );
};

const Header = () => {
  return <div className="h-16" />;
};

const Footer = () => {
  return <div className="h-6" />;
};

const Message = ({ message }: { message: ReceivedChatMessage }) => {
  const [isOpen, setIsOpen] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);
  const [isMultiline, setIsMultiline] = useState(false);
  const { isMuted } = useTrackMutedIndicator({
    participant: message.from!,
    source: Track.Source.Microphone,
  });

  const currentUser = {
    id: message.from?.attributes?.id ?? "",
    name: message.from?.attributes?.name ?? "",
    avatar: message.from?.attributes?.avatar ?? "",
    username: message.from?.attributes?.username ?? "",
    role: message.from?.attributes?.role ?? "",
  };

  const messageData = JSON.parse(message.message) as ReceivedChatMessageType;

  const handleProfileClick = () => {
    setIsOpen(true);
  };

  useLayoutEffect(() => {
    const checkLineCount = () => {
      if (textRef.current) {
        const lineHeight = parseInt(
          window.getComputedStyle(textRef.current).lineHeight,
        );
        const height = textRef.current.offsetHeight;
        setIsMultiline(height > lineHeight * 1.5); // Use 1.5 to account for potential fractional values
      }
    };

    checkLineCount();
    window.addEventListener("resize", checkLineCount);

    return () => {
      window.removeEventListener("resize", checkLineCount);
    };
  }, [message.message]);

  if (!messageData) return null;

  return (
    <li className="flex flex-col gap-1 px-3 py-0.5">
      <div
        className={cn(
          "flex w-full gap-2 px-2 py-1.5",
          isMultiline || messageData.type === "tip-party"
            ? "items-start"
            : "items-center",
          (currentUser.role === ROLES.HOST ||
            currentUser.role === ROLES.COHOST) &&
            "items-start rounded-[10px] bg-chat-bubble",
        )}
      >
        <StageUserProfileModal
          user={currentUser}
          isMuted={isMuted}
          open={isOpen}
          setOpen={setIsOpen}
        >
          <span role="button" onClick={handleProfileClick}>
            <Avatar className="size-[30px] flex-shrink-0">
              <AvatarImage src={currentUser.avatar} />
              <AvatarFallback />
            </Avatar>
          </span>
        </StageUserProfileModal>
        <div className="flex-grow text-sm" ref={textRef}>
          <span
            role="button"
            onClick={handleProfileClick}
            className="mr-2"
            style={{
              color: stringToColor(currentUser.id ?? "default"),
            }}
          >
            {currentUser.name}
          </span>
          {currentUser.role === ROLES.HOST ||
          currentUser.role === ROLES.COHOST ? (
            <span
              role="button"
              onClick={handleProfileClick}
              className="-mt-0.5 mr-2 inline-block rounded bg-brand-orange px-1 py-0.5 text-xs leading-none"
            >
              {ROLE_NAMES[currentUser.role as Role]}
            </span>
          ) : null}
          <div
            className={cn(
              currentUser.role === ROLES.HOST ||
                currentUser.role === ROLES.COHOST
                ? "mt-1"
                : "inline",
            )}
          >
            <ErrorBoundary fallback={null}>
              <MessageSwitcher messageData={messageData} />
            </ErrorBoundary>
          </div>
        </div>
      </div>
    </li>
  );
};

type ReceivedChatMessageType =
  | {
      type: "text";
      message: string;
    }
  | {
      type: "emote";
      emote: string;
    }
  | {
      type: "tip";
      data: {
        amount: number;
        currency: string;
        to: {
          id: string;
          name: string;
          avatar: string;
          username: string;
        };
      };
    }
  | {
      type: "tip-party";
      data: {
        amount: number;
        currency: string;
        type: "EVERY_SPEAKER" | "EVERY_PARTICIPANT";
      };
    };

const MessageSwitcher = ({
  messageData,
}: {
  messageData: ReceivedChatMessageType;
}) => {
  if (!messageData) return null;

  switch (messageData.type) {
    case "text":
      return <TextMessage message={messageData.message} />;
    case "emote":
      return <EmoteMessage emote={messageData.emote} />;
    case "tip":
      return (
        <TipMessage
          amount={messageData.data.amount}
          currency={messageData.data.currency}
          to={messageData.data.to}
        />
      );
    case "tip-party":
      return (
        <TipPartyMessage
          amount={messageData.data.amount}
          currency={messageData.data.currency}
          type={messageData.data.type}
        />
      );
    default:
      return null;
  }
};

const TextMessage = ({ message }: { message: string }) => {
  return <span dangerouslySetInnerHTML={{ __html: message }} />;
};

const EmoteMessage = ({ emote }: { emote: string }) => {
  return (
    <div className="relative top-1 inline-block [&_img]:scale-125">
      <Emoji unified={emote} size={20} />
    </div>
  );
};

const TipMessage = ({
  amount,
  currency,
  to,
}: {
  amount: number;
  currency: string;
  to: { id: string; name: string; avatar: string; username: string };
}) => {
  const { data: currenciesData } = useTippableCurrenciesQuery();
  const token = currenciesData?.currencies.find((c) => c.symbol === currency);

  return (
    <>
      <img
        src={token?.photoURL}
        className="inline-block size-5 rounded-full"
        alt={token?.symbol + " logo"}
      />
      <span className="ml-1 text-sm text-off-white">
        tipped <span className="font-semibold">{to.name}</span>{" "}
        {numberFormatter.format(amount)} {token?.symbol}
      </span>
    </>
  );
};

const TipPartyMessage = ({
  amount,
  currency,
  type,
}: {
  amount: number;
  currency: string;
  type: "EVERY_SPEAKER" | "EVERY_PARTICIPANT";
}) => {
  const { data: currenciesData } = useTippableCurrenciesQuery();
  const token = currenciesData?.currencies.find((c) => c.symbol === currency);

  return (
    <div className="relative isolate mt-2 flex w-full flex-col gap-2 overflow-hidden rounded-lg border border-off-white/50 bg-purple-gradient p-4">
      <h5 className="text-sm font-semibold text-off-white">Tipping Party!</h5>
      <div className="flex flex-col text-off-white">
        <div className="text-xl font-semibold">
          {numberFormatter.format(amount)} {token?.symbol}
        </div>
        <div className="text-sm">
          {type === "EVERY_PARTICIPANT"
            ? "Sent to every current participant."
            : "Sent to every speaker."}
        </div>
      </div>
      <img
        src={token?.photoURL}
        className="absolute -bottom-1 -right-4 -z-10 size-20 -rotate-12 rounded-full"
        alt={token?.photoURL + " logo"}
      />
      <img src="/assets/confetti-bg.png" className="absolute inset-0 -z-20" />
    </div>
  );
};

const DEFAULT_COLOR_LIST = [
  "#D53535",
  "#EB540A",
  "#B6B840",
  "#40B877",
  "#40B8B8",
  "#0059E0",
  "#8340B8",
  "#B84081",
];

function stringToColor(str: string, colorList: string[] = DEFAULT_COLOR_LIST) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }

  const index = Math.abs(hash) % colorList.length;
  return colorList[index];
}
