import axios from "axios";
import { OptimalRate, SwapSide } from "paraswap-core";

import { COMMISION_WALLET } from "@/app/(main)/community/_components/consts";
import { MinTokenData } from "@/environments/tokens";

type Address = string;
type NumberAsString = string;

interface TransactionParams {
  to: Address;
  from: Address;
  value: NumberAsString;
  data: string;
  gasPrice: NumberAsString;
  gas?: NumberAsString;
  chainId: number;
}

interface Swapper {
  getRate(params: {
    srcToken: Pick<MinTokenData, "address" | "decimals">;
    destToken: Pick<MinTokenData, "address" | "decimals">;
    srcAmount: NumberAsString;
    side?: SwapSide;
    signal?: AbortSignal;
  }): Promise<OptimalRate>;

  getBuyRate(params: {
    srcToken: Pick<MinTokenData, "address" | "decimals">;
    destToken: Pick<MinTokenData, "address" | "decimals">;
    destAmount: NumberAsString;
    signal?: AbortSignal;
  }): Promise<OptimalRate>;

  buildSwap(params: {
    srcToken: Pick<MinTokenData, "address" | "decimals">;
    destToken: Pick<MinTokenData, "address" | "decimals">;
    srcAmount: NumberAsString;
    priceRoute: OptimalRate;
    userAddress: Address;
    receiver?: Address;
    slippage?: number;
    partnerAddress?: string;
    partnerFeeBps?: string;
    isDirectFeeTransfer?: boolean;
    signal?: AbortSignal;
  }): Promise<TransactionParams>;
}

type PriceQueryParams = {
  srcToken: string;
  destToken: string;
  srcDecimals: string;
  destDecimals: string;
  amount: string;
  side: SwapSide;
  network: string;
  version: string;
  maxImpact: string;
};

const VERSION = "6.2";
const NETWORK_ID = 43114;

const getRate: Swapper["getRate"] = async ({
  srcToken,
  destToken,
  srcAmount,
  side = SwapSide.SELL,
  signal,
}) => {
  const queryParams: PriceQueryParams = {
    srcToken: srcToken.address,
    destToken: destToken.address,
    srcDecimals: srcToken.decimals.toString(),
    destDecimals: destToken.decimals.toString(),
    amount: srcAmount,
    side,
    network: NETWORK_ID.toString(),
    version: VERSION.toString(),
    maxImpact: "100",
  };

  const apiURL = "https://api.paraswap.io";
  const searchString = new URLSearchParams(queryParams);
  const pricesURL = `${apiURL}/prices/?${searchString}`;

  const {
    data: { priceRoute },
  } = await axios.get<{ priceRoute: OptimalRate }>(pricesURL, { signal });

  return priceRoute;
};

const buildSwap: Swapper["buildSwap"] = async ({
  srcToken,
  destToken,
  srcAmount,
  priceRoute,
  userAddress,
  slippage = 0,
  partnerAddress,
  partnerFeeBps,
  isDirectFeeTransfer,
  signal,
}) => {
  const txURL = `https://api.paraswap.io/transactions/${NETWORK_ID}`;

  const precision = 10_000n;
  const slippageBps = BigInt(Math.round(slippage * 100));

  let txSrcAmount: string;
  let txDestAmount: string;

  if (priceRoute.side === SwapSide.BUY) {
    txSrcAmount = (
      (BigInt(priceRoute.srcAmount) * (precision + slippageBps)) /
      precision
    ).toString();
    txDestAmount = priceRoute.destAmount;
  } else {
    txSrcAmount = srcAmount;
    txDestAmount = (
      (BigInt(priceRoute.destAmount) * (precision - slippageBps)) /
      precision
    ).toString();
  }

  const txConfig = {
    priceRoute,
    srcToken: srcToken.address,
    srcDecimals: srcToken.decimals,
    destToken: destToken.address,
    destDecimals: destToken.decimals,
    srcAmount: txSrcAmount,
    destAmount: txDestAmount,
    userAddress,
    partner: "ARENA",
    partnerAddress,
    partnerFeeBps,
    isDirectFeeTransfer,
  };

  const { data } = await axios.post<TransactionParams>(txURL, txConfig, {
    signal,
  });

  return data;
};

const getBuyRate: Swapper["getBuyRate"] = async ({
  srcToken,
  destToken,
  destAmount,
  signal,
}) => {
  return getRate({
    srcToken,
    destToken,
    srcAmount: destAmount,
    signal,
    side: SwapSide.BUY,
  });
};

const buildSwapWithCommission: Swapper["buildSwap"] = async ({
  srcToken,
  destToken,
  srcAmount,
  priceRoute,
  userAddress,
  slippage = 0,
  signal,
}) => {
  return buildSwap({
    srcToken,
    destToken,
    srcAmount,
    priceRoute,
    userAddress,
    slippage,
    partnerAddress: COMMISION_WALLET,
    partnerFeeBps: "50",
    isDirectFeeTransfer: true,
    signal,
  });
};

export const useSwap = () => {
  return {
    buildSwapWithCommission,
    buildSwap,
    getRate,
    getBuyRate,
    COMMISION_WALLET,
  };
};
