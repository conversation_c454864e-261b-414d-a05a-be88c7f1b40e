"use client";

import { useState } from "react";

import * as Dialog from "@radix-ui/react-dialog";

import { CloseOutlineIcon } from "./icons";
import { ImagePreview } from "./image-preview";
import { DialogOverlay } from "./ui/dialog";

interface ImagePreviewModalProps {
  url: string;
  children: React.ReactNode;
}

export const ImagePreviewModal = ({
  url,
  children,
}: ImagePreviewModalProps) => {
  const [open, setOpen] = useState(false);

  return (
    <Dialog.Root open={open} onOpenChange={setOpen}>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Dialog.Portal>
        <DialogOverlay />
        <Dialog.Content
          className="fixed inset-0 z-[999] flex flex-col bg-transparent"
          onClick={(e) => {
            if ((e.target as HTMLElement).tagName !== "IMG") {
              setOpen(false);
            }
          }}
        >
          <ImagePreview url={url} />
          <Dialog.Close className="absolute left-2 top-[calc(0.5rem+env(safe-area-inset-top))] flex h-10 w-10 items-center justify-center rounded-full  bg-dark-bk/65 text-white backdrop-blur-md focus-visible:outline-none">
            <CloseOutlineIcon className="h-6 w-6" />
          </Dialog.Close>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
