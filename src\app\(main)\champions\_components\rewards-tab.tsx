import { ChampionSocialBenefits } from "@/app/(main)/champions/_components/champion-social-benefits";
import { RewardSection } from "@/app/(main)/champions/_components/reward-section";
import { RewardsResponse } from "@/queries/types/token-portal";

interface RewardsTabParams {
  rewards: RewardsResponse | undefined;
  isLoading: boolean;
  onLearnMoreClick: () => void;
}

export const RewardsTab = ({
  rewards,
  isLoading,
  onLearnMoreClick,
}: RewardsTabParams) => (
  <div className="mx-[24px] flex h-full flex-grow flex-col justify-between pb-[calc(48px+env(safe-area-inset-bottom))] pt-[16px]">
    <div className="uprising-gradient absolute inset-0 z-[-1] h-full w-full"></div>
    <div className="flex flex-col gap-3">
      <div className="mt-1 px-1">Upcoming Rewards</div>
      <div className="flex flex-col gap-6 rounded-[10px] border border-[#2a2a2a] bg-[#0f0f0f] px-4 py-5 shadow-[0px_0px_10px_0px_rgba(111,21,200,0.5)]">
        <RewardSection
          rewards={rewards?.upcomingRewards}
          isLoading={isLoading}
        />
      </div>
      <div className="mt-1 px-1">Previous Rewards</div>
      <div className="flex flex-col gap-8 rounded-[10px] border border-[#2a2a2a] bg-[#0f0f0f] px-4 py-5">
        <RewardSection
          title="AIRDROPS"
          rewards={rewards?.previousRewards.airdrops}
          isLoading={isLoading}
        />
        <RewardSection
          title="STAKING REWARDS"
          rewards={rewards?.previousRewards.stakingRewards}
          isLoading={isLoading}
        />
      </div>
    </div>
    <ChampionSocialBenefits onClick={onLearnMoreClick} />
  </div>
);
