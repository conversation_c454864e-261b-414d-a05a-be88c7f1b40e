"use client";

import { useState } from "react";

import {
  useLocalParticipant,
  useParticipantAttribute,
  useRemoteParticipant,
  useTrackMutedIndicator,
} from "@livekit/components-react";
import { RemoteParticipant, Track } from "livekit-client";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useInviteStageMutation, useUpdateRoleMutation } from "@/queries";
import { StageUser } from "@/queries/types";
import { useStageStore } from "@/stores/stage";
import { cn } from "@/utils";

import {
  ArrowDownFilledIcon,
  BanOutlineIcon,
  MutedOutlineIcon,
} from "../icons";
import { UsersOutlineIcon } from "../icons/users-outline";
import { Button } from "../ui/button";
import { Drawer, DrawerContent, DrawerTrigger } from "../ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { BlockStageUserModal } from "./block-stage-user-modal";
import { ROLES } from "./constants";
import { useDataChannelsContext } from "./stores/data-channels-context";
import { UserListItem } from "./user-list-item";

export const SpeakerUserListItem = ({ user }: { user: StageUser }) => {
  const participant = useRemoteParticipant({
    identity: user.userId,
  });

  if (!participant) return null;

  return (
    <SpeakerUserListItemWithParticipant user={user} participant={participant} />
  );
};

const SpeakerUserListItemWithParticipant = ({
  user,
  participant,
}: {
  user: StageUser;
  participant: RemoteParticipant;
}) => {
  const local = useLocalParticipant();
  const role = useParticipantAttribute("role", {
    participant: local.localParticipant,
  });
  const { isMuted } = useTrackMutedIndicator({
    participant,
    source: Track.Source.Microphone,
  });
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const currentUser = {
    id: user?.user.id ?? "",
    name: user?.user.twitterName ?? "",
    avatar: user?.user.twitterPicture ?? "",
    username: user?.user.twitterHandle ?? "",
    role: user.role ?? "",
  };

  const [open, setOpen] = useState(false);
  const [isBlockOpen, setIsBlockOpen] = useState(false);
  const id = useStageStore((state) => state.id!);
  const { sendInvalidateStageInfo, sendMuteMic } = useDataChannelsContext();
  const { mutateAsync: updateRole, isPending } = useUpdateRoleMutation({
    onSuccess: () => {
      sendInvalidateStageInfo();
    },
  });
  const { mutateAsync: invite, isPending: isInvitePending } =
    useInviteStageMutation({
      onSuccess: () => {
        sendInvalidateStageInfo();
      },
    });

  const isButtonDisabled = isInvitePending || isPending;
  const hasLeft = user.isPresent === false;

  const handleInvite = async () => {
    setOpen(false);
    await invite({
      stageId: id,
      invitedUserId: user.userId,
      roleType: "COHOST",
    });
  };

  const handleRemovePrivilege = async () => {
    setOpen(false);
    await updateRole({
      stageId: id,
      role: "LISTENER",
      userId: user.userId,
    });
  };

  const handleBlockUser = async () => {
    setOpen(false);
    setIsBlockOpen(true);
  };

  const handleMute = async () => {
    setOpen(false);
    sendMuteMic({
      identity: participant.identity,
    });
  };

  return (
    <>
      <UserListItem
        user={user.user}
        badge={hasLeft ? "left" : undefined}
        isMuted={isMuted}
      >
        {hasLeft ? (
          <Button
            variant="outline"
            className="h-[34px] w-28"
            onClick={handleRemovePrivilege}
          >
            Downgrade
          </Button>
        ) : (
          <>
            {isTablet && (
              <DropdownMenu open={open} onOpenChange={setOpen}>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="h-[34px] w-28">
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[240px]">
                  {(role === ROLES.HOST || role === ROLES.COHOST) && (
                    <DropdownMenuItem
                      className={cn("w-full gap-4", isMuted && "opacity-50")}
                      asChild
                    >
                      <button
                        onClick={handleMute}
                        disabled={isPending || isMuted}
                      >
                        <MutedOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                        <span>Mute</span>
                      </button>
                    </DropdownMenuItem>
                  )}
                  {role === ROLES.HOST && (
                    <DropdownMenuItem className="w-full gap-4" asChild>
                      <button
                        onClick={handleInvite}
                        disabled={isButtonDisabled}
                      >
                        <UsersOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                        <span>Invite to Co-Host</span>
                      </button>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem className="w-full gap-4" asChild>
                    <button
                      onClick={handleRemovePrivilege}
                      disabled={isButtonDisabled}
                    >
                      <ArrowDownFilledIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Downgrade to Listener</span>
                    </button>
                  </DropdownMenuItem>
                  {role === ROLES.HOST || role === ROLES.COHOST ? (
                    <DropdownMenuItem className="w-full gap-4" asChild>
                      <button
                        onClick={handleBlockUser}
                        disabled={isButtonDisabled}
                      >
                        <BanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                        <span>Remove</span>
                      </button>
                    </DropdownMenuItem>
                  ) : null}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            {!isTablet && (
              <Drawer open={open} onOpenChange={setOpen}>
                <DrawerTrigger asChild>
                  <Button variant="outline" className="h-[34px] w-28">
                    Actions
                  </Button>
                </DrawerTrigger>
                <DrawerContent className="px-4 pt-4">
                  {role === ROLES.HOST || role === ROLES.COHOST ? (
                    <button
                      className={cn(
                        "flex items-center gap-2 p-2 text-base leading-5",
                        isMuted && "opacity-50",
                      )}
                      onClick={handleMute}
                      disabled={isPending || isMuted}
                    >
                      <MutedOutlineIcon className="size-6 text-gray-text" />{" "}
                      Mute
                    </button>
                  ) : null}
                  {role === ROLES.HOST && (
                    <button
                      className="flex items-center gap-2 p-2 text-base leading-5"
                      onClick={handleInvite}
                      disabled={isButtonDisabled}
                    >
                      <UsersOutlineIcon className="size-6 text-gray-text" />{" "}
                      Invite to Co-Host
                    </button>
                  )}
                  <button
                    className="flex items-center gap-2 p-2 text-base leading-5"
                    onClick={handleRemovePrivilege}
                    disabled={isButtonDisabled}
                  >
                    <ArrowDownFilledIcon className="size-6 text-gray-text" />{" "}
                    Downgrade to Listener
                  </button>
                  {role === ROLES.HOST || role === ROLES.COHOST ? (
                    <button
                      className="flex items-center gap-2 p-2 text-base leading-5"
                      onClick={handleBlockUser}
                      disabled={isButtonDisabled}
                    >
                      <BanOutlineIcon className="size-6 text-gray-text" />{" "}
                      Remove
                    </button>
                  ) : null}
                </DrawerContent>
              </Drawer>
            )}
          </>
        )}
      </UserListItem>
      <BlockStageUserModal
        open={isBlockOpen}
        setOpen={setIsBlockOpen}
        user={currentUser}
        stageId={id}
      />
    </>
  );
};
