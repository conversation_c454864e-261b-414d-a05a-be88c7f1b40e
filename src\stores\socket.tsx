"use client";

import { createContext, useContext, useEffect, useMemo, useState } from "react";

import { useQueryClient } from "@tanstack/react-query";
import io, { Socket } from "socket.io-client";

import { env } from "@/env";
import { SOCKET_MESSAGE } from "@/environments/socket-messages";
import { UnseenNotificationsResponse } from "@/queries/types/notifications";

import { useUser } from "./user";

const SocketContext = createContext<{
  socket: Socket | null;
  setSeen: (groupId: string, date: number) => void;
}>({
  socket: null,
  setSeen: () => {},
});

export function useSocket() {
  return useContext(SocketContext);
}

interface SocketProviderProps {
  children: React.ReactNode;
}

export function SocketProvider({ children }: SocketProviderProps) {
  const queryClient = useQueryClient();
  const { token, user } = useUser();
  const [socket, setSocket] = useState<Socket | null>(null);

  function setSeen(groupId: string, date: number): void {
    socket?.emit("set-conv-seen", { groupId, date });
  }

  useEffect(() => {
    const socketIo = io(env.NEXT_PUBLIC_SOCKET_URL, {
      auth: {
        token,
      },
      forceNew: true,
      transports: ["websocket", "polling"],
      reconnection: true,
      reconnectionAttempts: 10,
      upgrade: true,
    });

    socketIo.on("error", (error) => {
      console.error("Socket error: ", error);
    });

    socketIo.on("connect", () => {
      console.log("Socket connected");
      setSocket(socketIo);
    });

    socketIo.on("disconnect", () => {
      console.log("Socket disconnected");
      setSocket(null);
    });

    socketIo.connect();

    if (user && token) {
      socketIo?.emit("subscribe", `user-${user.id}`);
    }

    return function () {
      socketIo.disconnect();
    };
  }, [token, user]);

  const value = useMemo(
    () => ({
      socket,
      setSeen,
    }),
    [socket, setSeen],
  );

  useEffect(() => {
    if (socket) {
      socket.on(SOCKET_MESSAGE.PUSH_NOTIFICATION, () => {
        queryClient.setQueryData(
          ["notifications", "unseen"],
          (oldData: UnseenNotificationsResponse) => {
            if (!oldData) return;

            return {
              ...oldData,
              count: oldData.count + 1,
            };
          },
        );
        queryClient.invalidateQueries({
          queryKey: ["notifications"],
        });
      });
    }
  }, [queryClient, socket]);

  return (
    <SocketContext.Provider value={value}>{children}</SocketContext.Provider>
  );
}
