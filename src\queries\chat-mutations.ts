import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import {
  postPinConversation,
  postPinMessage,
  postSendMessage,
  reactToMessage,
  ReactToMessageData,
  unreactToMessage,
  UnreactToMessageData,
} from "@/api/client/chat";

import {
  PinConversationRequest,
  PinMessageRequest,
  SendMessageRequest,
} from "./types/chats";

type SendMessageMutationType = MutationOptions<
  unknown,
  DefaultError,
  SendMessageRequest,
  any
>;

export const useSendMessageMutation = (options?: SendMessageMutationType) => {
  return useMutation({
    mutationFn: postSendMessage,
    ...options,
  });
};

type PinConversationMutationType = MutationOptions<
  unknown,
  DefaultError,
  PinConversationRequest,
  any
>;

export const usePinConversation = (options?: PinConversationMutationType) => {
  return useMutation({
    mutationFn: postPinConversation,
    ...options,
  });
};

type ReactToMessageMutationType = MutationOptions<
  unknown,
  DefaultError,
  ReactToMessageData,
  any
>;

export const useReactToMessage = (options?: ReactToMessageMutationType) => {
  return useMutation({
    mutationFn: reactToMessage,
    ...options,
  });
};

type UnreactToMessageMutationType = MutationOptions<
  unknown,
  DefaultError,
  UnreactToMessageData,
  any
>;

export const useUnreactToMessage = (options?: UnreactToMessageMutationType) => {
  return useMutation({
    mutationFn: unreactToMessage,
    ...options,
  });
};

type PinMessageMutationType = MutationOptions<
  unknown,
  DefaultError,
  PinMessageRequest,
  any
>;

export const usePinMessage = (options?: PinMessageMutationType) => {
  return useMutation({
    mutationFn: postPinMessage,
    ...options,
  });
};
