"use client";

import { ChangeEvent, FC, useEffect, useRef, useState } from "react";

import { motion, useMotionValue } from "framer-motion";
import { Virtuoso } from "react-virtuoso";

import { TrendingCommunityProfiles } from "@/app/_components/trending-group-profiles";
import {
  ArenaLogo,
  CloseOutlineIcon,
  SearchFilledIcon,
} from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import useThrottle from "@/hooks/use-throttle";
import { useGroupsSearchQuery } from "@/queries/groups-queries";
import { TradesGroupsTrendingResponse } from "@/queries/types";
import { cn } from "@/utils";

import { CommunityActivityTab } from "./community-activity-tab";
import { CommunityItem } from "./community-item";
import { NewCommunitiesTab } from "./new-communities";
import { OfficialCommunitiesTab } from "./official-communities";
import { TopCommunitiesTab } from "./top-communities";
import { UserItemSkeleton } from "./user-item-skeleton";

type GroupsTabsProps = {
  trendingGroupsData?: TradesGroupsTrendingResponse;
  isTrendingGroupsLoading: boolean;
};

export const GroupsTabs: FC<GroupsTabsProps> = ({
  trendingGroupsData,
  isTrendingGroupsLoading,
}) => {
  const isLargeScreen = useMediaQuery(BREAKPOINTS.lg);
  const [mounted, setMounted] = useState(false);

  const tabRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLInputElement>(null);

  const [currentTab, setCurrentTab] = useState<string>("top");
  const [searchValue, setSearchValue] = useState<string>("");
  const throttledSearchValue = useThrottle(searchValue);

  const backgroundColor = useMotionValue("rgb(20 20 20 / 0)");
  const [isSticked, setIsSticked] = useState(false);
  const [noDuration, setNoDuration] = useState(false);

  const { data: searchData, isLoading: isSearchDataLoading } =
    useGroupsSearchQuery(throttledSearchValue);

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleTabChange = (value: string) => {
    setCurrentTab(value);
    setNoDuration(true);
    setTimeout(() => {
      setNoDuration(false);
    }, 300);
  };

  useEffect(() => {
    const handleScroll = () => {
      if (isLargeScreen) return;
      const topSafeArea = +getComputedStyle(document.documentElement)
        .getPropertyValue("--sat")
        .replace("px", "");

      if (
        tabRef.current &&
        tabRef.current.getBoundingClientRect().top <= 68 + topSafeArea
      ) {
        backgroundColor.set("rgb(20 20 20 / 0.88)");
        setIsSticked(true);
      } else {
        backgroundColor.set("rgb(20 20 20 / 0)");
        setIsSticked(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [backgroundColor]);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  if (isLargeScreen) {
    return (
      <Tabs
        value={currentTab}
        onValueChange={handleTabChange}
        ref={tabRef}
        className="flex-grow"
      >
        <div className="sticky top-[calc(env(safe-area-inset-top,0px))] z-10 w-full bg-[rgba(20,20,20,0.88)] pt-6 backdrop-blur-[9px]">
          <div className={cn("px-6", throttledSearchValue && "pb-5")}>
            <div className="relative">
              <Input
                value={searchValue}
                onChange={handleSearch}
                placeholder="Search the Arena"
                className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-10 ring-0  placeholder:text-gray-text"
              />
              <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
              {searchValue && (
                <button className="absolute right-[10px] top-1/2 -translate-y-1/2 rounded-full border border-dark-gray bg-[rgba(20,20,20,0.88)] p-1">
                  <CloseOutlineIcon
                    className="pointer-events-auto size-[14px] select-none text-off-white"
                    onClick={() => setSearchValue("")}
                  />
                </button>
              )}
            </div>
          </div>
          {!throttledSearchValue && (
            <>
              <div className="z-20 mt-6 w-full px-6">
                <div className="relative isolate flex flex-row items-center justify-between overflow-hidden rounded-[10px] bg-[linear-gradient(285deg,#6F15C8_6.36%,#30005F_119.12%)] p-4 shadow-[0px_0px_10px_2px_rgba(170,88,251,0.25)]">
                  <div className="text-large pl-3 font-semibold text-off-white">
                    <div>Create your token</div>
                    <div>on Arena Launch</div>
                  </div>
                  <div className="h-full pr-3">
                    <button className="rounded-full border border-off-white px-4 py-2.5">
                      <ProgressBarLink href="/create-community">
                        Create Token
                      </ProgressBarLink>
                    </button>
                    <ArenaLogo className="absolute -right-10 -top-20 -z-10 w-[270px] opacity-15" />
                    <div className="absolute inset-0 -z-10 rounded-[10px] border border-off-white opacity-10" />
                  </div>
                </div>
              </div>
              <TabsList className="mt-3 flex w-full justify-between">
                <TabsTrigger value="top">Top</TabsTrigger>
                <TabsTrigger value="new">New</TabsTrigger>
                <TabsTrigger value="official">Official</TabsTrigger>
                <TabsTrigger value="activity">Activity</TabsTrigger>
              </TabsList>
            </>
          )}
        </div>
        {!throttledSearchValue && (
          <>
            <TabsContent value="top" className="h-[calc(100%-139px)] py-2">
              <TopCommunitiesTab />
            </TabsContent>
            <TabsContent value="new" className="h-[calc(100%-139px)] py-2">
              <NewCommunitiesTab />
            </TabsContent>
            <TabsContent value="official" className="h-[calc(100%-139px)] py-2">
              <OfficialCommunitiesTab />
            </TabsContent>
            <TabsContent value="activity" className="h-[calc(100%-139px)] py-2">
              <CommunityActivityTab />
            </TabsContent>
          </>
        )}
        {throttledSearchValue && isSearchDataLoading && (
          <div className="mt-2">
            {Array(9)
              .fill(null)
              .map((_, index) => (
                <UserItemSkeleton key={index} />
              ))}
          </div>
        )}
        {throttledSearchValue &&
          !isSearchDataLoading &&
          searchData &&
          searchData.communities?.length > 0 && (
            <div className="mt-2">
              <Virtuoso
                useWindowScroll
                data={searchData.communities || []}
                overscan={200}
                itemContent={(index, community) => {
                  return <CommunityItem community={community} />;
                }}
              />
            </div>
          )}
        {throttledSearchValue &&
          !isSearchDataLoading &&
          searchData &&
          searchData.communities.length === 0 && (
            <div className="mt-10 flex w-full items-center justify-center">
              <div className="max-w-64 text-center">
                <h4 className="text-sm font-semibold text-[#EDEDED]">
                  No tokens found!
                </h4>
              </div>
            </div>
          )}
      </Tabs>
    );
  }

  return (
    <>
      <div className="px-6 pt-[23px]">
        <div className="relative">
          <Input
            ref={searchRef}
            value={searchValue}
            onChange={handleSearch}
            placeholder="Search the Arena"
            className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-10 placeholder:text-gray-text"
          />
          <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
          {searchValue && (
            <button className="absolute right-[10px]  top-1/2 -translate-y-1/2 rounded-full border border-dark-gray bg-dark-bk p-1">
              <CloseOutlineIcon
                className="pointer-events-auto size-[14px] select-none text-off-white"
                onClick={() => setSearchValue("")}
              />
            </button>
          )}
        </div>
      </div>
      {throttledSearchValue && isSearchDataLoading && (
        <div className="mt-2">
          {Array(9)
            .fill(null)
            .map((_, index) => (
              <UserItemSkeleton key={index} />
            ))}
        </div>
      )}
      {throttledSearchValue &&
        !isSearchDataLoading &&
        searchData &&
        searchData.communities.length > 0 && (
          <div className="mt-2">
            <Virtuoso
              useWindowScroll
              data={searchData.communities || []}
              overscan={200}
              itemContent={(_, community) => {
                return <CommunityItem community={community} />;
              }}
            />
          </div>
        )}
      {throttledSearchValue &&
        !isSearchDataLoading &&
        searchData &&
        searchData.communities.length === 0 && (
          <div className="mt-10 flex w-full items-center justify-center">
            <div className="max-w-64 text-center">
              <h4 className="text-sm font-semibold text-[#EDEDED]">
                No tokens found!
              </h4>
            </div>
          </div>
        )}
      {!throttledSearchValue && (
        <>
          <div className="relative z-20 mb-2 mt-6 h-auto w-full px-6">
            <div className="relative isolate flex flex-row items-center justify-between overflow-hidden rounded-[10px] bg-[linear-gradient(285deg,#6F15C8_6.36%,#30005F_119.12%)] p-4 shadow-[0px_0px_10px_2px_rgba(170,88,251,0.25)]">
              <div className="pl-3 font-semibold text-off-white">
                <div>Create your token</div>
                <div>on Arena Launch</div>
              </div>
              <div className="h-full pr-3">
                <button className="rounded-full border border-off-white px-4 py-2.5">
                  <ProgressBarLink href="/create-community">
                    Create Token
                  </ProgressBarLink>
                </button>
                <ArenaLogo className="absolute -right-20 -top-20 -z-10 w-[300px] opacity-20" />
              </div>
            </div>
          </div>
          <div className="relative">
            <motion.div
              className={cn("relative z-20", isSticked && "select-none")}
              animate={{
                y: isSticked ? -81 : 0,
                visibility: isSticked ? "hidden" : "visible",
              }}
              transition={{
                ease: "linear",
                duration: noDuration ? 0 : 0.2,
                visibility: {
                  delay: noDuration ? 0 : isSticked ? 0.2 : 0,
                },
              }}
            >
              <TrendingCommunityProfiles
                communities={trendingGroupsData}
                isLoading={isTrendingGroupsLoading}
              />
            </motion.div>
          </div>
          <Tabs
            value={currentTab}
            onValueChange={handleTabChange}
            ref={tabRef}
            className="flex-grow"
          >
            <motion.div
              className="sticky top-0 z-10 mt-[calc(-68px-env(safe-area-inset-top))] w-full pt-[calc(81px+env(safe-area-inset-top))] shadow-[0px_-5px_14px_20px_rgba(0,0,0,0.25)] backdrop-blur-[9px]"
              style={{ backgroundColor }}
              transition={{
                ease: "linear",
                duration: noDuration ? 0 : 0.1,
              }}
            >
              <motion.div
                className="absolute inset-x-0 top-[calc(23px+env(safe-area-inset-top))] overflow-hidden px-6 opacity-0"
                animate={{
                  opacity: isSticked ? 1 : 0,
                }}
                transition={{
                  ease: "easeInOut",
                  duration: noDuration ? 0 : 0.1,
                }}
              >
                <div className="relative">
                  <Input
                    onClick={() => {
                      setNoDuration(true);
                      scrollTo({
                        top: 0,
                      });
                      searchRef.current?.focus();
                      setTimeout(() => {
                        setNoDuration(false);
                      }, 300);
                    }}
                    value={searchValue}
                    onChange={handleSearch}
                    placeholder="Search the Arena"
                    className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-5 placeholder:text-gray-text focus-visible:ring-0"
                  />
                  <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
                </div>
              </motion.div>
              <TabsList className="flex w-full justify-between">
                <TabsTrigger value="top">Top</TabsTrigger>
                <TabsTrigger value="new">New</TabsTrigger>
                <TabsTrigger value="official">Official</TabsTrigger>
                <TabsTrigger value="activity">Activity</TabsTrigger>
              </TabsList>
            </motion.div>
            <TabsContent value="top" className="h-full py-2">
              <TopCommunitiesTab />
            </TabsContent>
            <TabsContent value="new" className="h-full py-2">
              <NewCommunitiesTab />
            </TabsContent>
            <TabsContent value="official" className="h-full py-2">
              <OfficialCommunitiesTab />
            </TabsContent>
            <TabsContent value="activity" className="h-full py-2">
              <CommunityActivityTab />
            </TabsContent>
          </Tabs>
        </>
      )}
    </>
  );
};
