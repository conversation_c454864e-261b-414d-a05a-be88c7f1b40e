import { ArrowBackOutlineIcon } from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { ReportTicket } from "@/queries/types/report-tickets";

export const TicketHeader = ({ ticket }: { ticket: ReportTicket }) => (
  <header className="sticky top-0 z-10 flex items-center gap-4 bg-[#141414] p-4">
    <ProgressBarLink href="/moderation/reports" className="size-5">
      <ArrowBackOutlineIcon className="size-5 text-off-white" />
    </ProgressBarLink>
    <h1 className="flex-1 text-center font-semibold">{ticket.type}</h1>
    <div className="size-5" />
  </header>
);
