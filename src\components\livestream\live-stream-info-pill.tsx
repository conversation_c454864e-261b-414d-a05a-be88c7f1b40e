"use client";

import { forwardRef, useMemo } from "react";
import { useRouter } from "next/navigation";

import { useQuery } from "@tanstack/react-query";
import { cva, VariantProps } from "class-variance-authority";
import { useInView } from "react-intersection-observer";

import { livestreamQueries, useSharesStatsQuery } from "@/queries";
import { LiveStageMinimal } from "@/queries/types";
import { LivestreamUserSimple } from "@/queries/types/livestream";
import { useUser } from "@/stores";
import { useLivestreamStore } from "@/stores/livestream";
import { useStageStore } from "@/stores/stage";
import { ThreadPrivacyTypeEnum } from "@/types";

import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

interface LiveStreamInfoPillProps extends VariantProps<typeof containerStyles> {
  livestream: LiveStageMinimal;
}

export const LiveStreamInfoPill = ({
  livestream,
  variant,
}: LiveStreamInfoPillProps) => {
  const livestreamId = useLivestreamStore((store) => store.id);
  const actions = useLivestreamStore((store) => store.actions);
  const stageActions = useStageStore((store) => store.actions);
  const { inView, ref } = useInView({
    threshold: 0.9,
  });
  const { user } = useUser();
  const router = useRouter();
  const { data, isLoading } = useQuery({
    ...livestreamQueries.livestreamSimpleInfo(livestream.id),
    refetchInterval: (query) => {
      if (inView && query.state.data?.livestream.isActive) {
        return 5 * 1000;
      }

      return false;
    },
  });
  const activeLivestream = data?.livestream;
  const { data: statsData, isLoading: isStatsDataLoading } =
    useSharesStatsQuery({
      userId: user?.id,
    });

  const isHoldingUserTicket = useMemo(() => {
    if (!activeLivestream) return false;
    if (isStatsDataLoading) return false;

    if (!activeLivestream.hostId) return false;

    if (user?.id === activeLivestream.hostId) return true;

    if (!statsData?.holdingsByUser) return false;

    return parseFloat((statsData?.holdingsByUser).toString()) > 0;
  }, [activeLivestream, isStatsDataLoading, statsData]);

  if (isLoading) {
    return (
      <Pill
        livestream={{
          name: "",
        }}
        host={{
          id: "",
          role: "HOST",
          user: {
            twitterPicture: "",
            twitterName: "",
            twitterHandle: "",
          },
        }}
        variant={variant}
        ref={ref}
        listenersCount={0}
      />
    );
  }

  if (
    !activeLivestream ||
    (!activeLivestream.isActive && activeLivestream.endedOn)
  ) {
    return null;
  }

  if (
    activeLivestream.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS &&
    !isHoldingUserTicket
  ) {
    return (
      <Pill
        livestream={activeLivestream}
        host={data?.host}
        onClick={() => {
          router.push(
            `/${data?.host.user.twitterHandle}/status/${activeLivestream.threadId}`,
          );
          stageActions.reset();
        }}
        variant={variant}
        ref={ref}
        listenersCount={data?.listenersCount}
      />
    );
  }

  return (
    <Pill
      livestream={activeLivestream}
      host={data?.host}
      onClick={() => {
        router.push(`/live/${data?.host.user.twitterHandle}`);
        stageActions.reset();
        if (livestreamId && livestreamId !== activeLivestream.id) {
          actions.setIsNewStream(true);
        }
      }}
      variant={variant}
      ref={ref}
      listenersCount={data?.listenersCount}
    />
  );
};

interface PillProps extends VariantProps<typeof containerStyles> {
  livestream: {
    name: string;
  };
  host: LivestreamUserSimple;
  listenersCount: number;
  onClick?: () => void;
}

const Pill = forwardRef<HTMLDivElement, PillProps>(
  ({ livestream, host, listenersCount, onClick, variant }, ref) => {
    return (
      <div
        role="button"
        className={containerStyles({ variant })}
        onClick={onClick}
        ref={ref}
      >
        <div className="flex items-center gap-1">
          <div className="flex -space-x-5">
            <Avatar key={host.id} className={avatarStyles({ variant })}>
              <AvatarImage src={host.user.twitterPicture} />
              <AvatarFallback />
            </Avatar>
          </div>
        </div>
        <div className="truncate text-sm font-semibold text-off-white">
          {livestream.name}
        </div>
        <div className="ml-auto mr-1 flex items-center justify-center gap-2">
          {listenersCount > 0 ? (
            <span className="text-sm font-semibold text-white">
              +{abbreviateNumber(listenersCount)}
            </span>
          ) : null}
          <div className="rounded-md bg-danger px-1.5 py-1 text-[10px] font-semibold leading-none text-off-white">
            LIVE
          </div>
        </div>
      </div>
    );
  },
);

Pill.displayName = "LiveStageInfoPill";

const containerStyles = cva(
  "flex items-center gap-[10px] rounded-[10px] bg-light-background border border-brand-orange",
  {
    variants: {
      variant: {
        mobile: "h-[58px] w-[85vw] px-3 py-2 snap-center snap-always",
        desktop: "h-[52px] w-full px-3 py-2 ",
      },
    },
    defaultVariants: {
      variant: "mobile",
    },
  },
);

const avatarStyles = cva("border border-light-gray-text", {
  variants: {
    variant: {
      mobile: "size-[42px]",
      desktop: "size-[36px]",
    },
  },
  defaultVariants: {
    variant: "mobile",
  },
});

function abbreviateNumber(number: number) {
  const formatter = new Intl.NumberFormat("en-US", {
    notation: "compact",
    compactDisplay: "short",
  });
  return formatter.format(number);
}
