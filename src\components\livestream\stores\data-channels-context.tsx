"use client";

import { createContext, useCallback, useContext, useMemo } from "react";

import {
  ChatMessage,
  ReceivedChatMessage,
  useChat,
  useDataChannel,
} from "@livekit/components-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import { livestreamQueries } from "@/queries";
import { useLivestreamStore } from "@/stores/livestream";

import { PoppedOutRightPanel } from "../popped-out-right-panel";

interface DataChannelsContextType {
  chat: {
    send: (message: string) => Promise<ReceivedChatMessage>;
    update: (
      message: string,
      originalMessageOrId: string | ChatMessage,
    ) => Promise<{
      readonly message: string;
      readonly editTimestamp: number;
      readonly id: string;
      readonly timestamp: number;
    }>;
    chatMessages: ReceivedChatMessage[];
    isSending: boolean;
  };
  sendInvalidateTippingStats: () => void;
}

const DataChannelsContext = createContext<DataChannelsContextType | null>(null);

export const DataChannelsProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const encoder = new TextEncoder();
  // const local = useLocalParticipant();
  const queryClient = useQueryClient();
  const decoder = new TextDecoder();
  const id = useLivestreamStore((state) => state.id!);

  const { data } = useQuery(livestreamQueries.livestreamSimpleInfo(id));

  // useDataChannel("invitation", () => {
  //   queryClient.invalidateQueries({
  //     queryKey: stageQueries.isUserInvitedKey({
  //       stageId: id,
  //       userId: local.localParticipant.identity,
  //     }),
  //   });
  // });

  const { send: sendInvalidateTippingStatsRaw } = useDataChannel(
    "invalidate-tipping-stats",
    (msg) => {
      const message = JSON.parse(decoder.decode(msg.payload));
      if (message === true) {
        queryClient.invalidateQueries({
          queryKey: livestreamQueries.tippingStatsKey(id),
        });
        queryClient.invalidateQueries({
          queryKey: livestreamQueries.topCreatorTippersKey(data?.host.id ?? ""),
        });
        queryClient.invalidateQueries({
          queryKey: livestreamQueries.topLivestreamTippersKey(id),
        });
      }
    },
  );

  useDataChannel("data-event", (msg) => {
    const message = decoder.decode(msg.payload);
    switch (message) {
      case "livestream_ended":
        toast.green("Live Stream ended");
        break;
      case "livestream_suspended":
        toast.danger("Live Stream suspended");
        break;
      case "user_blocked":
        toast.danger("You were blocked from joining this Live Stream.");
        break;
      default:
        break;
    }
  });

  const chat = useChat();

  const sendInvalidateTippingStats = useCallback(() => {
    const dataToSend = encoder.encode(JSON.stringify(true));
    sendInvalidateTippingStatsRaw(dataToSend, {
      reliable: true,
    });
    queryClient.invalidateQueries({
      queryKey: livestreamQueries.tippingStatsKey(id),
    });
    queryClient.invalidateQueries({
      queryKey: livestreamQueries.topCreatorTippersKey(data?.host.id ?? ""),
    });
    queryClient.invalidateQueries({
      queryKey: livestreamQueries.topLivestreamTippersKey(id),
    });
  }, [sendInvalidateTippingStatsRaw, queryClient, id, data]);

  const value = useMemo(
    () => ({
      chat,
      sendInvalidateTippingStats,
    }),
    [chat, sendInvalidateTippingStats],
  );

  return (
    <DataChannelsContext.Provider value={value}>
      {children}
      <PoppedOutRightPanel />
    </DataChannelsContext.Provider>
  );
};

export function useDataChannelsContext() {
  const context = useContext(DataChannelsContext);
  if (!context) {
    throw new Error("Missing DataChannelsContext");
  }
  return context;
}
