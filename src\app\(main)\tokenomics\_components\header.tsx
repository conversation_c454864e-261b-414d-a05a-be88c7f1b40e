"use client";

import { useRouter } from "next/navigation";

import { motion, useTransform } from "framer-motion";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { useBoundedScroll } from "@/hooks";

export const Header = () => {
  const router = useRouter();
  const { scrollYBoundedProgress } = useBoundedScroll(300);
  const scrollYBoundedProgressDelayed = useTransform(
    scrollYBoundedProgress,
    [0, 0.3, 1],
    [0, 1, 1],
  );

  const handleBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push("/home");
    }
  };

  return (
    <motion.div
      className="sticky top-0 z-10 flex items-center border-b border-dark-gray bg-dark-bk/65 px-6 pb-4 pt-[calc(1rem+env(safe-area-inset-top))] backdrop-blur-md"
      style={{
        y: useTransform(scrollYBoundedProgressDelayed, [0, 1], ["0%", "-100%"]),
      }}
    >
      <button className="flex flex-shrink-0" onClick={handleBack}>
        <ArrowBackOutlineIcon className="size-5 text-off-white" />
      </button>
    </motion.div>
  );
};
