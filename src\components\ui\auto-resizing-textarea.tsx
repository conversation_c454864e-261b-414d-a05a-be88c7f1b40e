import * as React from "react";

import { cn } from "@/utils";

export interface AutoResizingTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  maxLength: number;
  value: string;
}

const AutoResizingTextarea = React.forwardRef<
  HTMLTextAreaElement,
  AutoResizingTextareaProps
>(({ className, maxLength, value, ...props }, ref) => {
  const localRef = React.useRef<HTMLTextAreaElement | null>(null);

  React.useEffect(() => {
    const textarea = localRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, [value]);

  return (
    <div className="relative">
      <textarea
        ref={(node) => {
          localRef.current = node;
          if (typeof ref === "function") {
            ref(node);
          } else if (ref) {
            ref.current = node;
          }
        }}
        className={cn(
          "block w-full resize-none overflow-hidden rounded-lg border border-gray-text bg-transparent px-4 py-3 pr-12 text-sm text-off-white placeholder:text-gray-text focus:outline-none focus-visible:ring-1 focus-visible:ring-off-white focus-visible:ring-offset-2 focus-visible:ring-offset-[#0F0F0F] disabled:cursor-not-allowed disabled:opacity-50",
          className,
        )}
        value={value || ""}
        maxLength={maxLength}
        rows={1}
        {...props}
      />
      <span className="absolute bottom-3 right-4 text-sm text-gray-text">
        {maxLength - value.length}
      </span>
    </div>
  );
});

AutoResizingTextarea.displayName = "AutoResizingTextarea";

export { AutoResizingTextarea };
