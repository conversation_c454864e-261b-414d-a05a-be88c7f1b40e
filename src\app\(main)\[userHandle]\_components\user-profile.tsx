"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import Link from "next/link";
import { usePara<PERSON>, useRouter } from "next/navigation";

import { useQueryClient } from "@tanstack/react-query";
import { motion, useMotionValue } from "framer-motion";
import DOMPurify from "isomorphic-dompurify";
import Skeleton from "react-loading-skeleton";

import { AirdropPointsModal } from "@/components/airdrop-points-modal";
import {
  ArrowBackOutlineIcon,
  ChatbubbleOutlineIcon,
  ExternalLinkOutlineIcon,
} from "@/components/icons";
import { ChatbubbleOutlineIcon as ChatbubbleOutlineIconV2 } from "@/components/icons-v2";
import { BlockUserOutlineIcon } from "@/components/icons/block-user-outline";
import { SuspendedUserProfileOutlineIcon } from "@/components/icons/suspended-user";
import { WarningOutlineIcon } from "@/components/icons/warning-outline";
import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { UserBadges } from "@/components/user-badges";
import {
  useFeePercentQuery,
  useFollowMutation,
  useGroupByUserIdQuery,
  useIsBlockedByUserQuery,
  useIsUserBlockedQuery,
  useSharesStatsQuery,
  useUnfollowMutation,
  useUserByHandleQuery,
} from "@/queries";
import { useAirdropQuery } from "@/queries/airdrop-queries";
import { TradesUsersTrendingResponse } from "@/queries/types";
import { useUser } from "@/stores";
import { useTutorialStore } from "@/stores/tutorial";
import { User, UserFlaggedEnum } from "@/types";
import { abbreviateNumber, checkContent, cn, formatAvax } from "@/utils";

import { ProfileLoadingSkeleton } from "./profile-loading-skeleton";
import { ProfileMenu } from "./profile-menu";
import { ThreadsTab } from "./threads-tab";
import { TicketHoldersTab } from "./ticket-holders-tabs";
import { TipModal } from "./tip-modal";
import { TradeTicketsModal } from "./trade-tickets-modal";
import { UnblockUserModal } from "./unblock-user-modal";
import { UserNotFound } from "./user-not-found";
import { XLogo } from "./x-logo-svg";

export const formatPrice = (price?: string) => {
  if (!price) return "";
  return formatAvax(price);
};

export const UserProfile = () => {
  const queryClient = useQueryClient();
  const params = useParams() as { userHandle: string };
  const router = useRouter();

  const [isUnblockOpen, setIsUnblockOpen] = useState(false);

  const tabRef = useRef<HTMLDivElement>(null);
  const backgroundColor = useMotionValue("rgb(20 20 20 / 0)");
  const [isSticked, setIsSticked] = useState(false);
  const [noDuration, setNoDuration] = useState(false);
  const [showReposts, setRepostsVisibility] = useState(true);

  const isTutorialOpen = useTutorialStore((state) => state.isTutorialOpen);
  const actions = useTutorialStore((state) => state.actions);

  const { user } = useUser();

  const { data, isLoading: isUserDataLoading } = useUserByHandleQuery(
    params.userHandle,
  );

  const { data: fees } = useFeePercentQuery({
    address: data?.user?.addressBeforeDynamicMigration || data?.user?.address,
  });

  const isMe = useMemo(() => {
    if (!user || !data) return false;

    if (user?.id === data?.user?.id) {
      return true;
    }

    return false;
  }, [data, user]);

  useEffect(() => {
    if (user?.tutorial && !user?.tutorial?.profileTutorialShown && isMe) {
      actions.setIsTutorialOpen(true);
    }
  }, [user, actions, isMe]);

  const { data: airdropData, isLoading: isAirdropLoading } = useAirdropQuery({
    // disable the query if it is not the current user's profile
    enabled: isMe,
  });

  const { data: statsData, isLoading: isStatsDataLoading } =
    useSharesStatsQuery({
      userId: data?.user?.id,
    });
  const { data: isUserBlocked, isLoading: isUserBlockedLoading } =
    useIsUserBlockedQuery(data?.user?.id);
  const { data: isBlockedByUser, isLoading: isBlockedByUserLoading } =
    useIsBlockedByUserQuery(data?.user?.id);
  const { data: groupData } = useGroupByUserIdQuery({ userId: data?.user?.id });

  const isBlocked =
    (isUserBlocked && !isUserBlockedLoading) ||
    (isBlockedByUser && !isBlockedByUserLoading);
  const isSuspended = data?.user?.flag === UserFlaggedEnum.SUSPENDED;
  const isUserSuspended = user?.flag === UserFlaggedEnum.SUSPENDED;

  const { mutateAsync: follow } = useFollowMutation({
    onMutate: async () => {
      toast.green(`You're now following ${data?.user.twitterName}!`);
      await queryClient.cancelQueries({
        queryKey: ["user", "handle", params.userHandle],
      });
      await queryClient.cancelQueries({
        queryKey: ["trade", "users", "trending"],
      });

      const previousUser = queryClient.getQueryData([
        "user",
        "handle",
        params.userHandle,
      ]);
      const previousTrendingUsers = queryClient.getQueryData([
        "trade",
        "users",
        "trending",
      ]);

      queryClient.setQueryData(
        ["user", "handle", params.userHandle],
        (
          old:
            | {
                user: User;
              }
            | undefined,
        ) => {
          if (!old) {
            return old;
          }

          return {
            ...old,
            user: {
              ...old.user,
              following: true,
            },
          };
        },
      );
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        (old: TradesUsersTrendingResponse) => {
          if (!old) return old;

          return {
            ...old,
            users: old.users.map((u) => {
              if (u.id === data?.user.id) {
                return {
                  ...u,
                  following: true,
                };
              }
              return u;
            }),
          };
        },
      );

      return { previousUser, previousTrendingUsers };
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to follow ${data?.user.twitterName}.`);
      queryClient.setQueryData(
        ["user", "handle", params.userHandle],
        context.previousUser,
      );
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        context?.previousTrendingUsers,
      );
    },
  });
  const { mutateAsync: unfollow } = useUnfollowMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["user", "handle", params.userHandle],
      });
      await queryClient.cancelQueries({
        queryKey: ["trade", "users", "trending"],
      });

      const previousUser = queryClient.getQueryData([
        "user",
        "handle",
        params.userHandle,
      ]);
      const previousTrendingUsers = queryClient.getQueryData([
        "trade",
        "users",
        "trending",
      ]);

      queryClient.setQueryData(
        ["user", "handle", params.userHandle],
        (
          old:
            | {
                user: User;
              }
            | undefined,
        ) => {
          if (!old) {
            return old;
          }

          return {
            ...old,
            user: {
              ...old.user,
              following: false,
            },
          };
        },
      );
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        (old: TradesUsersTrendingResponse) => {
          if (!old) return old;

          return {
            ...old,
            users: old.users.map((u) => {
              if (u.id === data?.user.id) {
                return {
                  ...u,
                  following: false,
                };
              }
              return u;
            }),
          };
        },
      );

      return { previousUser, previousTrendingUsers };
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to unfollow ${data?.user.twitterName}.`);
      queryClient.setQueryData(
        ["user", "handle", params.userHandle],
        context.previousUser,
      );
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        context?.previousTrendingUsers,
      );
    },
  });

  const ticketPrice = useMemo(() => {
    if (isUserDataLoading || isStatsDataLoading) return "";

    const formattedEther = formatPrice(
      statsData?.stats?.keyPrice || data?.user?.keyPrice,
    );

    return formattedEther;
  }, [
    statsData?.stats?.keyPrice,
    data?.user?.keyPrice,
    isUserDataLoading,
    isStatsDataLoading,
  ]);

  const volume = useMemo(() => {
    if (isUserDataLoading || isStatsDataLoading) return "";

    const formattedEther = formatPrice(statsData?.stats?.volume ?? "0");

    return formattedEther;
  }, [isStatsDataLoading, isUserDataLoading, statsData?.stats?.volume]);

  const isHoldingUserTicket = useMemo(() => {
    if (isStatsDataLoading || isUserDataLoading) return false;

    if (!data?.user?.id) return false;

    if (!statsData?.holdingsByUser) return false;

    return parseFloat((statsData?.holdingsByUser).toString()) > 0;
  }, [data, isStatsDataLoading, statsData, isUserDataLoading]);

  const profileDescription = useMemo(() => {
    const [content] = checkContent({
      content: data?.user?.twitterDescription ?? "",
      truncate: false,
    });

    return DOMPurify.sanitize(content);
  }, [data?.user?.twitterDescription]);

  const followersCount = abbreviateNumber(data?.user?.followerCount ?? 0);
  const followingsCount = abbreviateNumber(data?.user?.followingsCount ?? 0);
  const twitterFollowers = abbreviateNumber(data?.user?.twitterFollowers ?? 0);

  const handleFollow = () => {
    if (!data) return;

    if (data?.user?.following) {
      unfollow({ userId: data.user.id });
    } else {
      follow({ userId: data.user.id });
    }
  };

  const goBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push("/home");
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      const topSafeArea = +getComputedStyle(document.documentElement)
        .getPropertyValue("--sat")
        .replace("px", "");

      if (
        tabRef.current &&
        tabRef.current.getBoundingClientRect().top <= 0 + topSafeArea
      ) {
        backgroundColor.set("rgb(20 20 20 / 0.88)");
        setIsSticked(true);
      } else {
        backgroundColor.set("rgb(20 20 20 / 0)");
        setIsSticked(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [backgroundColor]);

  if (isUserSuspended) {
    router.push("/home");
  }

  return (
    <>
      {(isUserDataLoading ||
        isUserBlockedLoading ||
        isBlockedByUserLoading) && <ProfileLoadingSkeleton />}

      {!isUserDataLoading && data?.user === null && <UserNotFound />}
      {data?.user &&
        !isUserDataLoading &&
        !isUserBlockedLoading &&
        !isBlockedByUserLoading && (
          <div className="pt-pwa relative z-30">
            <button
              className="absolute left-5 top-[calc(2rem+env(safe-area-inset-top))] flex size-8 items-center justify-center rounded-full bg-dark-bk"
              onClick={goBack}
            >
              <ArrowBackOutlineIcon className="size-6 text-white" />
            </button>
            <div className="absolute right-5 top-[calc(2rem+env(safe-area-inset-top))] flex size-8 items-center justify-center rounded-full bg-dark-bk sm:hidden">
              <ProfileMenu
                user={data?.user}
                isMe={isMe}
                showReposts={showReposts}
                setRepostsVisibility={setRepostsVisibility}
              />
            </div>
            {isSuspended ? (
              <div
                className="aspect-[3/1] w-full bg-cover bg-center bg-no-repeat"
                style={{ backgroundImage: `url()` }}
              />
            ) : (
              <div
                className="aspect-[3/1] w-full bg-cover bg-center bg-no-repeat"
                style={{ backgroundImage: `url(${data?.user?.bannerUrl})` }}
              />
            )}
            <motion.div
              className={cn("px-5", isSticked && "select-none")}
              animate={{
                y: isSticked ? -81 : 0,
                visibility: isSticked ? "hidden" : "visible",
              }}
              transition={{
                ease: "linear",
                duration: noDuration ? 0 : 0.1,
                visibility: {
                  delay: noDuration ? 0 : isSticked ? 0.1 : 0,
                },
              }}
            >
              <div className="-mt-10 flex items-end gap-5">
                {isTutorialOpen && isMe && (
                  <div className="absolute inset-x-0 bottom-0 top-10 z-10 bg-dark-bk/65" />
                )}
                {isSuspended ? (
                  <SuspendedUserProfileOutlineIcon />
                ) : (
                  <Avatar className="size-[92px] border border-[#E8E8E8]">
                    <AvatarImage src={data?.user?.twitterPicture} />
                    <AvatarFallback />
                  </Avatar>
                )}
                <div className="flex gap-6 sm:hidden">
                  {isBlocked || isSuspended ? (
                    <div className="flex flex-col gap-0.5 font-medium text-off-white">
                      <span className="text-sm text-gray-text">Followers</span>
                      <span className="text-base leading-5">{0}</span>
                    </div>
                  ) : (
                    <ProgressBarLink
                      href={`/${data?.user?.twitterHandle}/followers`}
                      className="flex flex-col gap-0.5 font-medium text-off-white"
                    >
                      <span className="text-sm text-gray-text">Followers</span>
                      <span className="text-base leading-5">
                        {followersCount}
                      </span>
                    </ProgressBarLink>
                  )}
                  {isBlocked || isSuspended ? (
                    <div className="flex flex-col gap-0.5 font-medium text-off-white">
                      <span className="text-sm text-gray-text">Following</span>
                      <span className="text-base leading-5">{0}</span>
                    </div>
                  ) : (
                    <ProgressBarLink
                      href={`/${data?.user?.twitterHandle}/following`}
                      className="flex flex-col gap-0.5 font-medium text-off-white"
                    >
                      <span className="text-sm text-gray-text">Following</span>
                      <span className="text-base leading-5">
                        {followingsCount}
                      </span>
                    </ProgressBarLink>
                  )}
                  {isMe ? (
                    <AirdropPointsModal>
                      <button className="flex flex-col gap-0.5 font-medium text-off-white">
                        <span className="text-sm text-gray-text">Rank</span>
                        <span className="text-base leading-5">
                          {airdropData?.airdrop?.rank || "N/A"}
                        </span>
                      </button>
                    </AirdropPointsModal>
                  ) : isBlocked || isSuspended ? (
                    <div className="flex flex-col gap-0.5 font-medium text-off-white">
                      <span className="py-1 text-gray-text">
                        <XLogo />
                      </span>
                      <span className="text-base leading-5">{0}</span>
                    </div>
                  ) : (
                    <a
                      href={`https://twitter.com/${data?.user?.twitterHandle}`}
                      target="_blank"
                      className="flex flex-col gap-0.5 font-medium text-off-white"
                    >
                      <span className="py-1 text-gray-text">
                        <XLogo />
                      </span>
                      <span className="text-base leading-5">
                        {twitterFollowers}
                      </span>
                    </a>
                  )}
                </div>

                <div className="hidden w-full gap-[10px] sm:flex sm:items-center">
                  {isMe ? (
                    <>
                      <TradeTicketsModal>
                        <Button
                          className={cn(
                            "flex-1 gap-1 px-[10px] py-2",
                            isTutorialOpen &&
                              isMe &&
                              "relative z-40 shadow-[0px_0px_10px_0px_rgba(255,255,255,0.60)]",
                          )}
                          disabled={isUserBlocked || false}
                        >
                          {isHoldingUserTicket ? "Trade Ticket" : "Buy Ticket"}
                          <img
                            src="/assets/coins/avax.png"
                            className="ml-0.5 size-3 rounded-full brightness-110 grayscale"
                            alt={`AVAX logo`}
                          />
                          <span className="text-xs font-medium leading-5 text-off-white">
                            {ticketPrice}
                          </span>
                        </Button>
                      </TradeTicketsModal>
                      <Button
                        variant="outline"
                        className="flex-1 px-[10px] py-2"
                        disabled={isSuspended}
                        asChild
                      >
                        <ProgressBarLink href={`/${user?.twitterHandle}/edit`}>
                          Edit Profile
                        </ProgressBarLink>
                      </Button>
                      <ProfileMenu
                        user={data?.user}
                        isMe={isMe}
                        showReposts={showReposts}
                        setRepostsVisibility={setRepostsVisibility}
                      />
                    </>
                  ) : (
                    <>
                      <TradeTicketsModal>
                        <Button
                          className={cn(
                            "flex-1 gap-1 px-[10px] py-2",
                            isTutorialOpen &&
                              isMe &&
                              "relative z-40 shadow-[0px_0px_10px_0px_rgba(255,255,255,0.60)]",
                          )}
                          disabled={isUserBlocked || false}
                        >
                          {isHoldingUserTicket ? "Trade Ticket" : "Buy Ticket"}
                          <img
                            src="/assets/coins/avax.png"
                            className="ml-0.5 size-3 rounded-full brightness-110 grayscale"
                            alt={`AVAX logo`}
                          />
                          <span className="text-xs font-medium leading-5 text-off-white">
                            {ticketPrice}
                          </span>
                        </Button>
                      </TradeTicketsModal>
                      {isUserBlocked && !isSuspended ? (
                        <Button
                          variant="destructive"
                          className="group w-[120px] px-[10px] py-2"
                          onClick={() => setIsUnblockOpen(true)}
                        >
                          <span className="group-hover:hidden">Blocked</span>
                          <span className="hidden group-hover:inline">
                            Unblock
                          </span>
                        </Button>
                      ) : (
                        <Button
                          disabled={isBlockedByUser || isSuspended || false}
                          variant="outline"
                          className={cn(
                            "w-[120px] px-[10px] py-2",
                            !data?.user?.following && "border-brand-orange",
                          )}
                          onClick={handleFollow}
                        >
                          {isSuspended
                            ? "Follow"
                            : data?.user?.following
                              ? "Unfollow"
                              : "Follow"}
                        </Button>
                      )}
                      {!isSuspended &&
                        (groupData?.group ? (
                          <Button
                            disabled={isBlocked || isSuspended || false}
                            variant="outline"
                            className="size-[38px] flex-shrink-0 p-0"
                            asChild
                          >
                            <Link href={`/messages/${groupData?.group?.id}`}>
                              <ChatbubbleOutlineIcon className="size-[18px] text-gray-text" />
                            </Link>
                          </Button>
                        ) : (
                          <TradeTicketsModal>
                            <Button
                              disabled={isBlocked || isSuspended || false}
                              variant="outline"
                              className="size-[38px] flex-shrink-0 p-0"
                            >
                              <ChatbubbleOutlineIcon className="size-[18px] text-gray-text" />
                            </Button>
                          </TradeTicketsModal>
                        ))}
                      <TipModal />
                      <ProfileMenu
                        user={data?.user}
                        isMe={isMe}
                        showReposts={showReposts}
                        setRepostsVisibility={setRepostsVisibility}
                      />
                    </>
                  )}
                </div>
              </div>
              <div className="mt-4 flex flex-col gap-0.5">
                <h1 className="flex items-center gap-1.5 text-base font-medium leading-5 text-off-white">
                  {data?.user?.twitterName}
                  {statsData?.badges && (
                    <UserBadges badges={statsData?.badges} className="h-4" />
                  )}
                </h1>
                <span className="text-xs text-gray-text">
                  @{data?.user?.twitterHandle}
                </span>
              </div>
              {profileDescription && !isSuspended && (
                <div
                  className="post-content mt-5 text-sm text-off-white"
                  dangerouslySetInnerHTML={{
                    __html: profileDescription,
                  }}
                />
              )}
              <div className="mt-5 flex items-center gap-[10px] sm:hidden">
                {isMe ? (
                  <>
                    <TradeTicketsModal>
                      <Button
                        className={cn(
                          "flex-1 gap-1 px-[10px] py-2",
                          isTutorialOpen &&
                            isMe &&
                            "relative z-40 shadow-[0px_0px_10px_0px_rgba(255,255,255,0.60)]",
                        )}
                      >
                        {isHoldingUserTicket ? "Trade Ticket" : "Buy Ticket"}
                        <img
                          src="/assets/coins/avax.png"
                          className="ml-0.5 size-3 rounded-full brightness-110 grayscale"
                          alt={`AVAX logo`}
                        />
                        <span className="text-xs font-medium leading-5 text-off-white">
                          {ticketPrice}
                        </span>
                      </Button>
                    </TradeTicketsModal>
                    <Button
                      variant="outline"
                      className="flex-1 px-[10px] py-2"
                      disabled={isSuspended}
                      asChild
                    >
                      <ProgressBarLink href={`/${user?.twitterHandle}/edit`}>
                        Edit Profile
                      </ProgressBarLink>
                    </Button>
                  </>
                ) : (
                  <>
                    <TradeTicketsModal>
                      <Button
                        className={cn(
                          "flex-1 gap-1 px-[10px] py-2",
                          isTutorialOpen &&
                            isMe &&
                            "relative z-40 shadow-[0px_0px_10px_0px_rgba(255,255,255,0.60)]",
                        )}
                        disabled={isUserBlocked || false}
                      >
                        {isHoldingUserTicket ? "Trade Ticket" : "Buy Ticket"}
                        <img
                          src="/assets/coins/avax.png"
                          className="ml-0.5 size-3 rounded-full brightness-110 grayscale"
                          alt={`AVAX logo`}
                        />
                        <span className="text-xs font-medium leading-5 text-off-white">
                          {ticketPrice}
                        </span>
                      </Button>
                    </TradeTicketsModal>
                    {isUserBlocked && !isSuspended ? (
                      <Button
                        variant="destructive"
                        className="group w-[120px] px-[10px] py-2"
                        onClick={() => setIsUnblockOpen(true)}
                      >
                        <span className="group-hover:hidden">Blocked</span>
                        <span className="hidden group-hover:inline">
                          Unblock
                        </span>
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        className={cn(
                          "w-[120px] px-[10px] py-2",
                          !data?.user?.following && "border-brand-orange",
                        )}
                        onClick={handleFollow}
                        disabled={isBlockedByUser || isSuspended || isSuspended}
                      >
                        {isSuspended
                          ? "Follow"
                          : data?.user?.following
                            ? "Unfollow"
                            : "Follow"}
                      </Button>
                    )}
                    <TipModal />
                  </>
                )}
              </div>
              <div className="mt-5 hidden items-center gap-5 sm:flex">
                {isBlocked || isSuspended ? (
                  <div className="hidden items-center gap-5 sm:flex">
                    <div className="flex gap-2 font-medium text-off-white">
                      <span className="text-base leading-5">{0}</span>
                      <span className="text-sm text-gray-text">Followers</span>
                    </div>
                    <div className="flex gap-2 font-medium text-off-white">
                      <span className="text-base leading-5">{0}</span>
                      <span className="text-sm text-gray-text">Following</span>
                    </div>
                    <div className="flex gap-2 font-medium text-off-white">
                      <span className="text-base leading-5">{0}</span>
                      <span className="py-1 text-gray-text">
                        <XLogo />
                      </span>
                    </div>
                  </div>
                ) : (
                  <div className="hidden items-center gap-5 sm:flex">
                    <ProgressBarLink
                      href={`/${data?.user?.twitterHandle}/followers`}
                      className="flex gap-2 font-medium text-off-white"
                    >
                      <span className="text-base leading-5">
                        {followersCount}
                      </span>
                      <span className="text-sm text-gray-text">Followers</span>
                    </ProgressBarLink>
                    <ProgressBarLink
                      href={`/${data?.user?.twitterHandle}/following`}
                      className="flex gap-2 font-medium text-off-white"
                    >
                      <span className="text-base leading-5">
                        {followingsCount}
                      </span>
                      <span className="text-sm text-gray-text">Following</span>
                    </ProgressBarLink>
                    <a
                      href={`https://twitter.com/${data?.user?.twitterHandle}`}
                      target="_blank"
                      className="flex gap-2 font-medium text-off-white"
                    >
                      <span className="text-base leading-5">
                        {twitterFollowers}
                      </span>
                      <span className="py-1 text-gray-text">
                        <XLogo />
                      </span>
                    </a>
                  </div>
                )}
                {isMe && (
                  <AirdropPointsModal>
                    <button className="flex gap-2 font-medium text-off-white">
                      <span className="text-base leading-5">
                        {isAirdropLoading ? (
                          <Skeleton className="h-4 w-8" />
                        ) : (
                          airdropData?.airdrop?.rank || "N/A"
                        )}
                      </span>
                      <span className="text-sm text-gray-text">Rank</span>
                    </button>
                  </AirdropPointsModal>
                )}
              </div>
            </motion.div>
          </div>
        )}
      {data?.user != null &&
        isUserBlocked &&
        !isSuspended &&
        !isUserBlockedLoading && (
          <div className="flex w-full items-center justify-center px-6 py-32 text-base text-off-white">
            @{data?.user?.twitterHandle} is blocked
          </div>
        )}
      {data?.user != null &&
        isBlockedByUser &&
        !isSuspended &&
        !isBlockedByUserLoading && (
          <div className="text-gray-400 flex w-full items-center justify-center px-6 py-[48px] text-center text-base">
            <div className="flex w-full select-none flex-col items-center justify-center rounded-[10px] bg-[#0e0e0e] p-4 text-center">
              <BlockUserOutlineIcon />
              <span className="mt-2 font-bold text-white">
                You were blocked
              </span>
              <div className="mt-4 font-medium text-[#808080]">
                You won&apos;t be able to follow or see this user&apos;s post.
              </div>
              <span className="font-medium text-[#808080]">
                You can still trade this user&apos;s tickets.
              </span>
            </div>
          </div>
        )}
      {data?.user != null && isSuspended && (
        <div className="text-gray-400 flex w-full items-center justify-center px-6 py-[48px] text-center text-base">
          <div className="flex w-full select-none flex-col items-center justify-center rounded-[10px] bg-[#0e0e0e] p-4 text-center">
            <WarningOutlineIcon color="#EB540A" />
            <span className="mt-2 font-bold text-white">Account Suspended</span>
            <div className="mt-4 font-medium text-[#808080]">
              This account has been suspended for violating the{" "}
              <ProgressBarLink
                href="/terms-of-use"
                className="font-semibold text-white"
              >
                terms of use
              </ProgressBarLink>{" "}
              of The Arena.
            </div>
          </div>
        </div>
      )}
      {!isBlocked && !isSuspended && data?.user != null && (
        <Tabs
          onValueChange={() => {
            setNoDuration(true);
            setTimeout(() => {
              setNoDuration(false);
            }, 300);
          }}
          ref={tabRef}
          defaultValue="threads"
          className="flex-grow"
        >
          <motion.div
            className="sticky top-0 z-10 mt-[calc(-40px-env(safe-area-inset-top))] w-full pt-[calc(54px+env(safe-area-inset-top))]  shadow-[0px_-5px_14px_20px_rgba(0,0,0,0.25)] backdrop-blur-[9px]"
            style={{ backgroundColor }}
            transition={{
              ease: "linear",
              duration: noDuration ? 0 : 0.1,
            }}
          >
            <motion.div
              className="absolute inset-x-0 top-[calc(14px+env(safe-area-inset-top))] overflow-hidden px-6 opacity-0"
              animate={{
                opacity: isSticked ? 1 : 0,
              }}
              transition={{
                ease: "easeInOut",
                duration: noDuration ? 0 : 0.1,
              }}
            >
              <div className="flex justify-between">
                <div className="flex items-center">
                  <button onClick={goBack}>
                    <ArrowBackOutlineIcon className="size-6 text-white" />
                  </button>
                  <Avatar className="ml-3 size-[34px] border border-[#E8E8E8]">
                    <AvatarImage src={data?.user?.twitterPicture} />
                    <AvatarFallback />
                  </Avatar>
                </div>
                <div className="flex items-center gap-3">
                  {!isMe && data?.user != null && <TipModal />}
                  <ProfileMenu
                    user={data?.user}
                    isMe={isMe}
                    showReposts={showReposts}
                    setRepostsVisibility={setRepostsVisibility}
                  />
                </div>
              </div>
            </motion.div>
            <TabsList className="mt-2 w-full">
              <TabsTrigger value="threads">Threads</TabsTrigger>
              <TabsTrigger value="ticket-holders">Ticket Holders</TabsTrigger>
              <TabsTrigger value="stats">Stats</TabsTrigger>
            </TabsList>
          </motion.div>
          <TabsContent value="threads" className="h-full">
            <ThreadsTab showReposts={showReposts} />
          </TabsContent>
          <TabsContent value="ticket-holders" className="h-full py-2">
            <TicketHoldersTab />
          </TabsContent>
          <TabsContent
            value="stats"
            className="grid h-full grid-cols-2 gap-3 px-6 pb-[calc(1.5rem+env(safe-area-inset-bottom))] pt-6"
          >
            <div className="flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
              <h4 className="text-light-gray-text">Creator Fee</h4>
              <p className="text-base text-off-white">
                {isStatsDataLoading ? (
                  <Skeleton className="h-4 w-16" />
                ) : (
                  `${fees ? fees.subjectFee : 0}%`
                )}
              </p>
            </div>
            <div className="flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
              <h4 className="text-light-gray-text">Buys/Sells</h4>
              <p className="text-base text-off-white">
                {isStatsDataLoading ? (
                  <Skeleton className="h-4 w-16" />
                ) : (
                  <>
                    <span className="text-green">
                      +{statsData?.stats?.buys || 0}
                    </span>
                    /
                    <span className="text-[#F00]">
                      -{statsData?.stats?.sells || 0}
                    </span>
                  </>
                )}
              </p>
            </div>
            <div className="flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
              <h4 className="text-light-gray-text">You hold</h4>
              <p className="text-base text-off-white">
                {isStatsDataLoading ? (
                  <Skeleton className="h-4 w-16" />
                ) : (
                  statsData?.holdingsByUser || 0
                )}
              </p>
            </div>
            <div className="flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
              <h4 className="text-light-gray-text">Supply</h4>
              <p className="text-base text-off-white">
                {isStatsDataLoading ? (
                  <Skeleton className="h-4 w-16" />
                ) : (
                  statsData?.stats?.supply || 0
                )}
              </p>
            </div>
            <div className="flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
              <h4 className="text-light-gray-text">Holdings</h4>
              <p className="text-base text-off-white">
                {isStatsDataLoading ? (
                  <Skeleton className="h-4 w-16" />
                ) : (
                  statsData?.totalHoldings || 0
                )}
              </p>
            </div>
            <div className="flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
              <h4 className="text-light-gray-text">Holders</h4>
              <p className="text-base text-off-white">
                {isStatsDataLoading ? (
                  <Skeleton className="h-4 w-16" />
                ) : (
                  statsData?.totalHolders || 0
                )}
              </p>
            </div>
            <div className="col-span-2 flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
              <h4 className="text-light-gray-text">Total Volume</h4>
              <div className="flex items-center gap-2 text-base text-off-white">
                <img
                  src="/assets/coins/avax.png"
                  className="size-4 rounded-full"
                  alt={`AVAX logo`}
                />
                <span>
                  {isStatsDataLoading ? (
                    <Skeleton className="h-4 w-16" />
                  ) : (
                    volume
                  )}
                </span>
              </div>
            </div>
            <Button className="col-span-2" asChild>
              <a
                href={`https://arena.trade/user/${data?.user?.address}`}
                target="_blank"
                rel="noreferrer"
              >
                See in ArenaBook{" "}
                <ExternalLinkOutlineIcon className="ml-1 size-5" />
              </a>
            </Button>
          </TabsContent>
        </Tabs>
      )}
      {data?.user != null && !isSuspended && (
        <UnblockUserModal
          open={isUnblockOpen}
          setOpen={setIsUnblockOpen}
          user={data?.user}
        />
      )}
      {data?.user != null && !isSuspended && !isMe && (
        <>
          {groupData?.group ? (
            <Button
              disabled={isUserBlocked || isSuspended || false}
              className="fixed bottom-[calc(70px+env(safe-area-inset-bottom))] right-5 flex size-[44px] items-center justify-center p-0 sm:hidden"
              asChild
            >
              <Link href={`/messages/${groupData.group.id}`}>
                <ChatbubbleOutlineIconV2 className="size-6 text-off-white" />
              </Link>
            </Button>
          ) : (
            <TradeTicketsModal>
              <Button
                disabled={isUserBlocked || isSuspended || false}
                className="fixed bottom-[calc(70px+env(safe-area-inset-bottom))] right-5 flex size-[44px] items-center justify-center p-0 sm:hidden"
              >
                <ChatbubbleOutlineIconV2 className="size-6 text-off-white" />
              </Button>
            </TradeTicketsModal>
          )}
        </>
      )}
      {isTutorialOpen && isMe && (
        <div className="absolute inset-0 z-20 bg-dark-bk/65" />
      )}
    </>
  );
};
