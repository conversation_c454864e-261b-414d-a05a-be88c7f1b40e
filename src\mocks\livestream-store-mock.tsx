import React, { createContext, useContext } from "react";

const LivestreamStoreContext = createContext<any>({
  id: "mock-livestream-id",
});

export const useLivestreamStore = (selector?: any) => {
  const store = useContext(LivestreamStoreContext);
  return selector ? selector(store) : store;
};

export const LivestreamStoreProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => (
  <LivestreamStoreContext.Provider value={{ id: "mock-livestream-id" }}>
    {children}
  </LivestreamStoreContext.Provider>
);
