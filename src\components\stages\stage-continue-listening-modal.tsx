"use client";

import { useMemo, useState } from "react";

import { useQuery } from "@tanstack/react-query";
import { Loader2Icon } from "lucide-react";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useJoinStageMutation } from "@/queries";
import { stageQueries } from "@/queries/stage-queries";
import { Stage, StageUser } from "@/queries/types";
import { useStageStore } from "@/stores/stage";

import { ArrowBackOutlineIcon } from "../icons";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { Dialog, DialogClose, DialogContent } from "../ui/dialog";
import { Drawer, DrawerContent } from "../ui/drawer";
import { Role, ROLE_NAMES } from "./constants";

export const StageContinueListeningModal = ({
  stageUser,
}: {
  stageUser: StageUser;
}) => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [open, setOpen] = useState(true);
  const { data, isLoading } = useQuery({
    ...stageQueries.stageInfo(stageUser.stageId),
    refetchInterval: (query) => {
      if (
        (query.state.data?.stage.isActive && open) ||
        (query.state.data?.stage.isRecorded &&
          !query.state.data?.stage.isRecordingComplete)
      ) {
        return 3000;
      }

      return false;
    },
  });
  const actions = useStageStore((state) => state.actions);
  const { mutateAsync: joinStage, isPending } = useJoinStageMutation({
    onSuccess: (data) => {
      setOpen(false);
      actions.setToken(data.token);
      actions.setId(stageUser.stageId);
    },
  });

  const activeStage = data?.stage ?? stageUser.stage;

  const live = useMemo(() => {
    if (!data) return [];
    return data.live.slice(0, 4);
  }, [data]);

  const handleListen = async () => {
    await joinStage({ stageId: stageUser.stageId });
  };

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-md gap-0">
          <div className="mb-6 flex items-center gap-2">
            <DialogClose>
              <ArrowBackOutlineIcon className="size-5" />
            </DialogClose>
            <h2 className="text-base font-semibold text-off-white">
              Live Stage
            </h2>
          </div>
          <StageInfoModalContent
            isLoading={isLoading}
            live={live}
            stage={activeStage}
            listenersCount={data?.listenersCount ?? 0}
            handleListen={handleListen}
            isPending={isPending}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="justify-start gap-0 text-left">
        <StageInfoModalContent
          isLoading={isLoading}
          live={live}
          stage={activeStage}
          listenersCount={data?.listenersCount ?? 0}
          handleListen={handleListen}
          isPending={isPending}
        />
      </DrawerContent>
    </Drawer>
  );
};

interface StageInfoModalContentProps {
  isLoading: boolean;
  live?: StageUser[];
  stage: Stage;
  listenersCount: number;
  handleListen: () => void;
  isPending: boolean;
}

export const StageInfoModalContent = ({
  isLoading,
  live,
  stage,
  listenersCount,
  handleListen,
  isPending,
}: StageInfoModalContentProps) => {
  return (
    <>
      {isLoading && (
        <div className="flex items-center justify-center py-10">
          <Loader2Icon className="size-10 animate-spin text-brand-orange" />
        </div>
      )}
      {!isLoading && live && stage && (
        <>
          {stage.isRecorded ? (
            <div className="mb-3 flex items-center gap-1 self-start rounded-md border border-light-gray-text px-1.5 py-0.5">
              <div className="size-3 rounded-full bg-[#E74141]" />
              <div className="select-none text-sm font-semibold uppercase text-off-white">
                REC
              </div>
            </div>
          ) : null}
          <div className="flex flex-col gap-0.5">
            <h3 className="text-xl font-semibold leading-[22px] text-off-white">
              {stage.name}
            </h3>
            <p className="mt-1 text-sm text-gray-text">
              {listenersCount} listeners
            </p>
          </div>
          {listenersCount === 0 && (
            <div className="flex items-center justify-center py-10">
              <div className="max-w-64 text-center">
                <p className="text-sm leading-[18px] text-[#9D9D9D]">
                  No one joined yet!
                </p>
              </div>
            </div>
          )}
          {listenersCount > 0 && (
            <div className="mt-6 grid grid-cols-4 gap-3">
              {live.map((stageUser) => {
                return (
                  <div className="flex flex-col text-center" key={stageUser.id}>
                    <Avatar className="size-[60px] self-center">
                      <AvatarImage src={stageUser.user.twitterPicture} />
                      <AvatarFallback />
                    </Avatar>
                    <div className="mt-2 truncate  text-sm font-semibold text-off-white">
                      {stageUser.user.twitterName}
                    </div>
                    <div className="flex items-center gap-1 self-center">
                      <span className="text-sm text-gray-text">
                        {ROLE_NAMES[stageUser.role as Role]}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
          <Button className="mt-6" onClick={handleListen} loading={isPending}>
            Start Listening
          </Button>
        </>
      )}
    </>
  );
};
