"use client";

import { useUpdateRoleMutation } from "@/queries";
import { StageUser } from "@/queries/types";
import { useStageStore } from "@/stores/stage";

import { Button } from "../ui/button";
import { useDataChannelsContext } from "./stores/data-channels-context";
import { UserListItem } from "./user-list-item";

export const RequestedToSpeakUserListItem = ({ user }: { user: StageUser }) => {
  const id = useStageStore((state) => state.id!);
  const { sendInvalidateStageInfo } = useDataChannelsContext();
  const { mutateAsync: updateRole, isPending } = useUpdateRoleMutation({
    onSuccess: () => {
      sendInvalidateStageInfo();
    },
  });

  const handleAllow = async () => {
    await updateRole({
      stageId: id,
      role: "SPEAKER",
      userId: user.userId,
    });
  };

  return (
    <UserListItem user={user.user}>
      <Button
        className="h-[34px] w-28"
        loading={isPending}
        onClick={handleAllow}
      >
        Allow
      </Button>
    </UserListItem>
  );
};
