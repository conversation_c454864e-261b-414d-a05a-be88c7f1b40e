"use client";

import { memo, useEffect, useMemo, useState } from "react";

import { useQuery } from "@tanstack/react-query";
import { format, parseISO } from "date-fns";
import { useInView } from "react-intersection-observer";

import { useRemindStageMutation, useStartStageMutation } from "@/queries";
import { stageQueries } from "@/queries/stage-queries";
import { Stage, StageBadgeType } from "@/queries/types";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";
import { useStageRecordingPlayerStore } from "@/stores/stage-recording-player";
import { ThreadPrivacyTypeEnum, ThreadUser } from "@/types";
import { abbreviateNumber, cn, formatTime } from "@/utils";

import {
  BarChartFilledIcon,
  CalendarOutlineIcon,
  LockOutlineIcon,
  PlayFilledIcon,
} from "../icons";
import { toast } from "../toast";
import { TradeTicketsModal } from "../trade-tickets-modal";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { StageInfoModal } from "./stage-info-modal";
import { formatTimeDistance } from "./utils";

interface PostStageInfoProps {
  stage: Stage;
  host: ThreadUser;
}

export const PostStageInfo = memo(({ stage, host }: PostStageInfoProps) => {
  const { inView, ref } = useInView({
    threshold: 0.7,
  });
  const { user } = useUser();
  const [, setRefresh] = useState(0);
  const { data, isLoading, refetch } = useQuery({
    ...stageQueries.stageInfoSimple(stage.id),
    refetchInterval: (query) => {
      if (
        inView &&
        query.state.data?.stage.scheduledStartTime &&
        !query.state.data?.stage.endedOn
      ) {
        return 5 * 1000;
      }

      if (
        inView &&
        (query.state.data?.stage.isActive ||
          (query.state.data?.stage.isRecorded &&
            !query.state.data?.stage.isRecordingComplete))
      ) {
        return 5 * 1000;
      }

      return false;
    },
  });
  const stageId = useStageStore((state) => state.id);
  const actions = useStageStore((state) => state.actions);
  const playerStageId = useStageRecordingPlayerStore((state) => state.id);
  const playerActions = useStageRecordingPlayerStore((state) => state.actions);
  const activeStage = data?.stage ?? stage;

  const { mutate: startStage, isPending } = useStartStageMutation({
    onSuccess: (data) => {
      actions.setToken(data.token);
      actions.setId(stage.id);
    },
  });

  const { mutate: remindStage, isPending: isRemindPending } =
    useRemindStageMutation({
      onSuccess: () => {
        toast.green("You will be reminded when the stage starts");
        refetch();
      },
    });

  const live = useMemo(() => {
    if (!data) return [];
    return data.live;
  }, [data]);

  const lockIcon = useMemo(
    () => <LockOutlineIcon className="size-5 text-off-white" />,
    [],
  );

  useEffect(() => {
    if (!stage.scheduledStartTime) return;

    const scheduledTime = new Date(stage.scheduledStartTime).getTime();
    const now = Date.now();

    if (scheduledTime <= now) return;

    const timeUntilScheduled = scheduledTime - now;
    const timer = setTimeout(() => {
      setRefresh((prev) => prev + 1);
    }, timeUntilScheduled);

    return () => clearTimeout(timer);
  }, [stage.scheduledStartTime, refetch]);

  if (!activeStage.isActive && activeStage.endedOn) {
    const startDate = parseISO(
      activeStage?.startedOn ?? activeStage.createdOn,
    ).getTime();
    const endDate = parseISO(activeStage.endedOn).getTime();
    const duration = formatTimeDistance(startDate, endDate);

    return (
      <div
        className="flex flex-col gap-2 rounded-[10px] border border-purple bg-dark-bk p-4"
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <div className="flex items-center gap-2 ">
          <Avatar className="size-[20px] border border-off-white">
            <AvatarImage src={host.twitterPicture} />
            <AvatarFallback />
          </Avatar>
          <span className="text-sm text-off-white">{host.twitterName}</span>
          <div className="rounded bg-purple-gradient px-1.5 py-1 text-xs leading-none">
            Host
          </div>
        </div>
        <h5 className="line-clamp-2 text-lg font-semibold text-off-white">
          {activeStage.name}
        </h5>
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-1 text-sm font-semibold text-light-gray-text">
            <span>{abbreviateNumber(data?.tunedInCount ?? 0)} tuned in</span>・
            <span>{formatTime(activeStage.createdOn, "MMM d")}</span>・
            <span>{duration}</span>
          </div>
          {activeStage.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS ? (
            <div className="flex items-center gap-1">
              {lockIcon}
              <span className="text-sm font-semibold text-off-white">
                Ticket Gated
              </span>
            </div>
          ) : activeStage.privacyType === ThreadPrivacyTypeEnum.BADGEHOLDERS ? (
            <div className="flex items-center gap-1">
              {lockIcon}
              <span className="text-sm font-semibold text-off-white">
                Badge Gated
              </span>
              {activeStage.badgeTypes && (
                <Badges badges={activeStage.badgeTypes} />
              )}
            </div>
          ) : null}
        </div>
        {activeStage.isRecorded ? (
          <>
            {playerStageId && playerStageId === activeStage.id ? (
              <Button
                className="w-full bg-purple-gradient py-3"
                onClick={() => {
                  playerActions.setIsRecordingPlayerFullScreen(true);
                }}
              >
                Playing
              </Button>
            ) : activeStage.isRecordingComplete &&
              !data?.canJoin &&
              activeStage.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS ? (
              <TradeTicketsModal
                userHandle={data?.host.user?.twitterHandle ?? ""}
                setIsTicketPurchased={(isTicketPurchased) => {
                  if (isTicketPurchased) {
                    refetch();
                  }
                }}
              >
                <Button className="w-full bg-purple-gradient py-3">
                  Buy a Ticket to Play
                </Button>
              </TradeTicketsModal>
            ) : activeStage.isRecordingComplete &&
              !data?.canJoin &&
              activeStage.privacyType === ThreadPrivacyTypeEnum.BADGEHOLDERS ? (
              <Button className="w-full bg-purple-gradient py-3" disabled>
                You need a Badge to Play
              </Button>
            ) : activeStage.isRecordingComplete && activeStage.recordingUrl ? (
              <Button
                className="w-full bg-purple-gradient py-3"
                onClick={() => {
                  if (stageId) {
                    toast.danger("You are joined to a stage");
                  } else {
                    if (activeStage.recordingUrl) {
                      playerActions.setPlayer({
                        id: activeStage.id,
                        url: activeStage.recordingUrl,
                      });
                    }
                  }
                }}
              >
                <PlayFilledIcon className="size-4" /> Play Recording
              </Button>
            ) : !activeStage.isRecordingComplete ? (
              <Button className="w-full bg-purple-gradient py-3" disabled>
                Saving recording...
              </Button>
            ) : null}
          </>
        ) : null}
      </div>
    );
  }

  if (activeStage.scheduledStartTime && !activeStage.isActive) {
    const scheduledDate = parseISO(activeStage.scheduledStartTime);
    const isScheduledTimeInFuture =
      new Date(activeStage.scheduledStartTime).getTime() > Date.now();

    return (
      <div
        className="flex flex-col gap-2 rounded-[10px] bg-purple-gradient p-4"
        onClick={(e) => {
          e.stopPropagation();
        }}
        ref={ref}
      >
        <div className="flex items-center gap-2 ">
          <Avatar className="size-[20px] border border-off-white">
            <AvatarImage src={host.twitterPicture} />
            <AvatarFallback />
          </Avatar>
          <span className="text-sm text-off-white">{host.twitterName}</span>
          <div className="rounded border border-off-white/10 bg-purple px-1.5 py-1 text-xs leading-none">
            Host
          </div>
        </div>
        <h5 className="line-clamp-2 text-lg font-semibold text-off-white">
          {activeStage.name}
        </h5>
        {isScheduledTimeInFuture ||
        activeStage.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS ||
        activeStage.privacyType === ThreadPrivacyTypeEnum.BADGEHOLDERS ? (
          <div className="mb-2 flex flex-col gap-2">
            {isScheduledTimeInFuture ? (
              <div className="flex items-center gap-1">
                <CalendarOutlineIcon className="size-5 text-off-white" />
                <span className="text-sm font-semibold text-light-gray-text">
                  {format(scheduledDate, "EEEE, MMM d 'at' h:mm a")}
                </span>
              </div>
            ) : null}
            {activeStage.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS ? (
              <div className="flex items-center gap-1">
                {lockIcon}
                <span className="text-sm font-semibold text-off-white">
                  Ticket Gated
                </span>
              </div>
            ) : activeStage.privacyType ===
              ThreadPrivacyTypeEnum.BADGEHOLDERS ? (
              <div className="flex items-center gap-1">
                {lockIcon}
                <span className="text-sm font-semibold text-off-white">
                  Badge Gated
                </span>
                {activeStage.badgeTypes && (
                  <Badges badges={activeStage.badgeTypes} />
                )}
              </div>
            ) : null}
          </div>
        ) : null}
        {isScheduledTimeInFuture ? (
          <>
            {activeStage.hostId === user?.id ? (
              <Button variant="secondary" disabled>
                Start Stage Now
              </Button>
            ) : data?.hasSetReminder ? (
              <Button variant="secondary" disabled>
                Reminder Set
              </Button>
            ) : (
              <Button
                variant="secondary"
                disabled={isRemindPending}
                onClick={() => {
                  remindStage({ stageId: activeStage.id });
                }}
              >
                Remind Me
              </Button>
            )}
          </>
        ) : (
          <>
            {activeStage.hostId === user?.id ? (
              <Button
                variant="secondary"
                disabled={isPending}
                onClick={() => {
                  startStage({ stageId: activeStage.id });
                }}
              >
                Start Stage Now
              </Button>
            ) : (
              <Button variant="secondary" disabled>
                Waiting for Host
              </Button>
            )}
          </>
        )}
      </div>
    );
  }

  return (
    <div
      className="flex flex-col gap-2 rounded-[10px] bg-purple-gradient p-4"
      onClick={(e) => {
        e.stopPropagation();
      }}
      ref={ref}
    >
      <div className="flex items-center gap-2 ">
        <Avatar className="size-[20px] border border-off-white">
          <AvatarImage src={host.twitterPicture} />
          <AvatarFallback />
        </Avatar>
        <span className="text-sm text-off-white">{host.twitterName}</span>
        <div className="rounded border border-off-white/10 bg-purple px-1.5 py-1 text-xs leading-none">
          Host
        </div>
      </div>
      <h5 className="line-clamp-2 text-lg font-semibold text-off-white">
        {activeStage.name}
      </h5>
      <div className="mb-2 flex flex-col gap-2">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <BarChartFilledIcon className="w-4 text-brand-orange" />
            <span className="text-sm font-semibold uppercase text-off-white">
              Live
            </span>
          </div>
          <span className="text-sm font-semibold text-light-gray-text">
            {abbreviateNumber(data?.listenersCount ?? 0)} Listeners
          </span>
        </div>
        {activeStage.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS ? (
          <div className="flex items-center gap-1">
            {lockIcon}
            <span className="text-sm font-semibold text-off-white">
              Ticket Gated
            </span>
          </div>
        ) : activeStage.privacyType === ThreadPrivacyTypeEnum.BADGEHOLDERS ? (
          <div className="flex items-center gap-1">
            {lockIcon}
            <span className="text-sm font-semibold text-off-white">
              Badge Gated
            </span>
            {activeStage.badgeTypes && (
              <Badges badges={activeStage.badgeTypes} />
            )}
          </div>
        ) : null}
      </div>
      {stageId && stageId === activeStage.id ? (
        <Button
          variant="secondary"
          onClick={() => {
            actions.setFullScreen(true);
          }}
        >
          Joined
        </Button>
      ) : stage.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS &&
        !data?.canJoin ? (
        <TradeTicketsModal
          userHandle={data?.host.user?.twitterHandle ?? ""}
          setIsTicketPurchased={(isTicketPurchased) => {
            if (isTicketPurchased) {
              refetch();
            }
          }}
        >
          <Button variant="secondary">Buy a Ticket to Join</Button>
        </TradeTicketsModal>
      ) : stage.privacyType === ThreadPrivacyTypeEnum.BADGEHOLDERS &&
        !data?.canJoin ? (
        <Button disabled variant="secondary">
          You need a badge to join
        </Button>
      ) : (
        <StageInfoModal
          live={live}
          stage={activeStage}
          isLoading={isLoading}
          listenersCount={data?.listenersCount ?? 0}
        >
          <Button variant="secondary">Join the Stage</Button>
        </StageInfoModal>
      )}
    </div>
  );
});

PostStageInfo.displayName = "PostStageInfo";

interface UserBadgesProps {
  badges: StageBadgeType[];
  className?: string;
}

const orders = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 17];

export const Badges = ({ badges, className }: UserBadgesProps) => {
  const sortedBadges = badges.sort((a, b) => {
    const indexA = orders.indexOf(a.badgeType);
    const indexB = orders.indexOf(b.badgeType);
    return indexA - indexB;
  });

  return (
    <div className="flex items-center gap-1">
      {sortedBadges.map((badge) => (
        <img
          src={`/assets/badges/badge-type-${badge.badgeType}.png`}
          key={badge.id}
          alt="Badge"
          className={cn("h-4 w-auto", className)}
        />
      ))}
    </div>
  );
};
