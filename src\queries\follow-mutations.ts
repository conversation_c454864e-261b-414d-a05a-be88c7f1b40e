import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import {
  followTopUsers,
  postFollowCommunity,
  postFollowUser,
  postUnfollowCommunity,
  postUnfollowUser,
} from "@/api/client/follow";

import { FollowCommunityData, FollowUserData } from "./types";

type FollowMutationType = MutationOptions<
  unknown,
  DefaultError,
  FollowUserData,
  any
>;

type FollowTopUsersutationType = MutationOptions<unknown, DefaultError, any>;

export const useFollowMutation = (options?: FollowMutationType) => {
  return useMutation({
    mutationFn: postFollowUser,
    ...options,
  });
};

export const useUnfollowMutation = (options?: FollowMutationType) => {
  return useMutation({
    mutationFn: postUnfollowUser,
    ...options,
  });
};

type FollowCommunityMutationType = MutationOptions<
  unknown,
  DefaultError,
  FollowCommunityData,
  any
>;

export const useFollowCommunityMutation = (
  options?: FollowCommunityMutationType,
) => {
  return useMutation({
    mutationFn: postFollowCommunity,
    ...options,
  });
};

export const useUnfollowCommunityMutation = (
  options?: FollowCommunityMutationType,
) => {
  return useMutation({
    mutationFn: postUnfollowCommunity,
    ...options,
  });
};

export const useFollowTopUserMutation = (
  options?: FollowTopUsersutationType,
) => {
  return useMutation({
    mutationFn: followTopUsers,
  });
};
