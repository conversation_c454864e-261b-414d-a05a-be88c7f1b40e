"use client";

import { useEffect, useLayoutEffect, useState } from "react";
import dynamic from "next/dynamic";
import { useRouter, useSearchParams } from "next/navigation";

import { EthereumWalletConnectors } from "@dynamic-labs/ethereum";
import { GlobalWalletExtension } from "@dynamic-labs/global-wallet";
import {
  DynamicContextProvider,
  getAuthToken,
  mergeNetworks,
} from "@dynamic-labs/sdk-react-core";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { AxiosError } from "axios";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import posthog from "posthog-js";
import { PostHogProvider } from "posthog-js/react";
import { CookiesProvider } from "react-cookie";
import { SkeletonTheme } from "react-loading-skeleton";
import { Toaster } from "sonner";

import { logout } from "@/actions/logout";
import { GlobalModals } from "@/components/global-modals";
import { MiniLivestreamContainer } from "@/components/livestream/mini-livestream-container";
import { ProgressBar } from "@/components/progress-bar";
import { StageContainer } from "@/components/stages/stage-container";
import { StagePlayerContainer } from "@/components/stages/stage-player-container";
import { toast } from "@/components/toast";
import { Tutorials } from "@/components/tutorial";
import { env } from "@/env";
import { axios } from "@/lib/axios";
import { PostHogPageView } from "@/lib/posthog-page-view";
import { DEFAULT_LOGIN_REDIRECT } from "@/routes";
import { FeatureFlagsStoreProvider } from "@/stores/flags";
import { LivestreamStoreProvider } from "@/stores/livestream";
import { SocketProvider } from "@/stores/socket";
import { StageStoreProvider } from "@/stores/stage";
import { StageRecordingPlayerStoreProvider } from "@/stores/stage-recording-player";
import { UserProvider, useUser } from "@/stores/user";
import { Me, TwitterUser } from "@/types";

const CloudMessaging = dynamic(() => import("./_components/cloud-messaging"), {
  ssr: false,
});

if (typeof window !== "undefined") {
  posthog.init(env.NEXT_PUBLIC_POSTHOG_KEY!, {
    api_host: env.NEXT_PUBLIC_POSTHOG_HOST,
    person_profiles: "identified_only", // or 'always' to create profiles for anonymous users as well
    capture_pageview: false,
    capture_pageleave: false,
    autocapture: false,
  });
}

interface ProvidersProps {
  children: React.ReactNode;
  token: string | null;
  user: Me | null;
  twitterUser: TwitterUser | null;
  flags: Record<string, string | boolean>;
}

export function Providers({
  children,
  token,
  user,
  twitterUser,
  flags,
}: ProvidersProps) {
  const router = useRouter();

  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            refetchOnMount: true,
            staleTime: 0,
          },
          mutations: {
            onError: (error) => {
              if (error instanceof AxiosError) {
                console.error(error.response?.data);
                toast.danger(
                  error.response?.data.message ||
                    "An unexpected error occurred. Please try again later.",
                );
              }
            },
          },
        },
      }),
  );

  useLayoutEffect(() => {
    if (token) {
      axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    } else {
      delete axios.defaults.headers.common["Authorization"];
    }
  }, [token]);

  useEffect(() => {
    const handleServiceWorkerMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === "PUSH_NOTIFICATION_REDIRECT") {
        router.push(event.data.url);
      }
    };

    if (navigator.serviceWorker) {
      navigator.serviceWorker.addEventListener(
        "message",
        handleServiceWorkerMessage,
      );
    }

    return () => {
      if (navigator.serviceWorker) {
        navigator.serviceWorker.removeEventListener(
          "message",
          handleServiceWorkerMessage,
        );
      }
    };
  }, [router]);

  const searchParams = useSearchParams();
  const ref = searchParams.get("ref");

  return (
    <DynamicContextProvider
      settings={{
        environmentId: env.NEXT_PUBLIC_DYNAMIC_ENVIRONMENT_ID,
        walletConnectors: [EthereumWalletConnectors],
        walletConnectorExtensions: [GlobalWalletExtension],
        events: {
          onLogout: async (event) => {
            await logout();
            router.push("/");
          },
        },
      }}
    >
      <QueryClientProvider client={queryClient}>
        <NuqsAdapter>
          <CookiesProvider>
            <UserProvider user={user} twitterUser={twitterUser} token={token}>
              <SocketProvider>
                <PostHogProvider client={posthog}>
                  <PostHogAuthProvider>
                    <FeatureFlagsStoreProvider flags={flags}>
                      <StageStoreProvider>
                        <StageRecordingPlayerStoreProvider>
                          <LivestreamStoreProvider>
                            <ProgressBar className="top-pwa fixed inset-x-0 top-0 z-[999] h-0.5 bg-brand-orange">
                              <SkeletonTheme
                                baseColor="#3B3B3B"
                                highlightColor="#4B4B4B"
                              >
                                {children}
                                <CloudMessaging />
                                <StageContainer />
                                <StagePlayerContainer />
                                <GlobalModals />
                                <Tutorials />
                                <MiniLivestreamContainer />
                                {/* <PostHogPageView /> */}
                              </SkeletonTheme>
                            </ProgressBar>
                          </LivestreamStoreProvider>
                        </StageRecordingPlayerStoreProvider>
                      </StageStoreProvider>
                    </FeatureFlagsStoreProvider>
                  </PostHogAuthProvider>
                </PostHogProvider>
              </SocketProvider>
            </UserProvider>
          </CookiesProvider>
        </NuqsAdapter>
        <Toaster
          position="top-center"
          richColors
          className="pointer-events-auto flex flex-col items-center"
        />
        {/* <ReactQueryDevtools initialIsOpen={false} /> */}
      </QueryClientProvider>
    </DynamicContextProvider>
  );
}

const PostHogAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const { user } = useUser();

  useEffect(() => {
    if (user) {
      posthog.identify(user.twitterHandle, {
        id: user.id,
        name: user.twitterName,
        handle: user.twitterHandle,
      });
    } else {
      posthog.reset();
    }
  }, [user]);

  return <>{children}</>;
};
