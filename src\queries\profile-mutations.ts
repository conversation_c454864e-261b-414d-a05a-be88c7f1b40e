import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import {
  postMigrateDynamic,
  postUpdateBanner,
  postUpdateBio,
} from "@/api/client/profile";

type UpdateBioMutation = MutationOptions<
  unknown,
  DefaultError,
  {
    bio: string;
  },
  any
>;

export const useUpdateBioMutation = (options?: UpdateBioMutation) => {
  return useMutation({
    mutationFn: postUpdateBio,
    ...options,
  });
};

type UpdateBannerMutation = MutationOptions<
  unknown,
  DefaultError,
  {
    bannerUrl: string;
  },
  any
>;

export const useUpdateBannerMutation = (options?: UpdateBannerMutation) => {
  return useMutation({
    mutationFn: postUpdateBanner,
    ...options,
  });
};

type MigrateDynamicMutation = MutationOptions<unknown, DefaultError, void, any>;

export const useMigrateDynamicMutation = (options?: MigrateDynamicMutation) => {
  return useMutation({
    mutationFn: postMigrateDynamic,
    ...options,
  });
};
