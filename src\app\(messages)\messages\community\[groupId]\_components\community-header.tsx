"use client";

import { useEffect, useState } from "react";

import Skeleton from "react-loading-skeleton";

import { HANDLE_PHASE } from "@/app/(main)/community/_components/consts";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { GroupIcon, OfficialGroupIcon } from "@/components/icons-v2/group-logo";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { SOCKET_MESSAGE } from "@/environments/socket-messages";
import { useUser } from "@/stores";
import { useSocket } from "@/stores/socket";
import { cn } from "@/utils";

import { useCommunity } from "../context/community-context";

interface Typing {
  groupId: string;
  user: {
    id: string;
    name: string;
  };
  date: number;
}

export const CommunityHeader = () => {
  const { user } = useUser();
  const [typing, setTyping] = useState<Typing>();
  const { socket } = useSocket();

  const { data, isLoading, groupId } = useCommunity();

  useEffect(() => {
    socket?.on(SOCKET_MESSAGE.CHAT_TYPING, (data: { typing: Typing }) => {
      if (
        data.typing &&
        data.typing.groupId === groupId &&
        data.typing.user.id !== user?.id
      ) {
        setTyping({ ...data.typing, date: Date.now() });
      }
    });

    return () => {
      socket?.off(SOCKET_MESSAGE.CHAT_TYPING);
    };
  }, [socket, groupId, user]);

  useEffect(() => {
    const interval = setInterval(() => {
      setTyping((prev) => {
        const now = Date.now();

        if (prev && now - prev.date > 500) {
          return undefined;
        }

        return prev;
      });
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  return (
    <div className="flex items-center justify-between bg-[#141414] bg-opacity-[0.88] px-6 pb-3 pt-[22px] backdrop-blur-sm">
      <div className="flex items-center justify-start gap-2">
        <ProgressBarLink href="/messages" className="size-5">
          <ArrowBackOutlineIcon className="size-5 text-[#F3F3F3]" />
        </ProgressBarLink>
        <ProgressBarLink
          href={
            data && data.community && data.community?.tokenPhase >= HANDLE_PHASE
              ? `/community/${data.community?.name}`
              : `/community/${data?.community?.contractAddress}`
          }
        >
          <div className="flex items-center gap-3">
            <Avatar className="size-7">
              <AvatarImage src={data?.community?.photoURL} />
              <AvatarFallback />
            </Avatar>
            <div className="relative">
              <h3
                className={cn(
                  "text-base font-semibold leading-5 text-white transition-transform",
                  typing ? "-translate-y-2" : "translate-y-0",
                )}
              >
                {isLoading ? (
                  <Skeleton className="h-4 w-24" />
                ) : (
                  <div className="justfiy-center flex items-center gap-1.5 truncate text-sm font-semibold leading-4 text-off-white">
                    {data?.community?.isOfficial ? (
                      <OfficialGroupIcon />
                    ) : (
                      <GroupIcon />
                    )}
                    {`$${data?.community?.ticker}`}
                  </div>
                )}
              </h3>
              <p
                className={cn(
                  "absolute -bottom-2 left-0 whitespace-nowrap text-xs leading-4 text-gray-text transition-opacity",
                  typing ? "opacity-100" : "opacity-0",
                )}
              >
                {typing && <span>{`${typing.user.name} is typing...`}</span>}
              </p>
            </div>
          </div>
        </ProgressBarLink>
      </div>
    </div>
  );
};
