"use client";

import { useEffect } from "react";

import { motion, useSpring, useTransform } from "framer-motion";

import { abbreviateNumber } from "@/utils/abbreviate-number";

export function AnimatedNumber({
  value,
  maximumFractionDigits = 2,
}: {
  value: number;
  maximumFractionDigits?: number;
}) {
  const numberFormatter = new Intl.NumberFormat("en-US", {
    maximumFractionDigits,
  });
  let spring = useSpring(value, { mass: 0.8, stiffness: 75, damping: 15 });
  let display = useTransform(spring, (current) =>
    current < 1000000
      ? numberFormatter.format(current)
      : abbreviateNumber(current, 2),
  );

  useEffect(() => {
    spring.set(value);
  }, [spring, value]);

  return <motion.span>{display}</motion.span>;
}
