import { ComponentProps } from "react";

export const TemplateOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 18 18"
    fill="none"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.25 1.5C2.1837 1.5 2.12011 1.52634 2.07322 1.57322C2.02634 1.62011 2 1.6837 2 1.75V3.75C2 3.8163 2.02634 3.87989 2.07322 3.92678C2.12011 3.97366 2.1837 4 2.25 4H16.25C16.3163 4 16.3799 3.97366 16.4268 3.92678C16.4737 3.87989 16.5 3.81631 16.5 3.75V1.75C16.5 1.68369 16.4737 1.62011 16.4268 1.57322C16.3799 1.52634 16.3163 1.5 16.25 1.5H2.25ZM1.01256 0.512563C1.34075 0.184375 1.78587 0 2.25 0H16.25C16.7141 0 17.1592 0.184375 17.4874 0.512563C17.8156 0.840753 18 1.28587 18 1.75V3.75C18 4.21413 17.8156 4.65925 17.4874 4.98744C17.1592 5.31563 16.7141 5.5 16.25 5.5H2.25C1.78587 5.5 1.34075 5.31563 1.01256 4.98744C0.684375 4.65925 0.5 4.21413 0.5 3.75V1.75C0.5 1.28587 0.684375 0.840752 1.01256 0.512563ZM2.25 9.5C2.1837 9.5 2.12011 9.52634 2.07322 9.57322C2.02634 9.62011 2 9.6837 2 9.75V15.75C2 15.8163 2.02634 15.8799 2.07322 15.9268C2.12011 15.9737 2.18369 16 2.25 16H8.25C8.31631 16 8.37989 15.9737 8.42678 15.9268C8.47366 15.8799 8.5 15.8163 8.5 15.75V9.75C8.5 9.6837 8.47366 9.62011 8.42678 9.57322C8.37989 9.52634 8.3163 9.5 8.25 9.5H2.25ZM1.01256 8.51256C1.34075 8.18438 1.78587 8 2.25 8H8.25C8.71413 8 9.15925 8.18438 9.48744 8.51256C9.81563 8.84075 10 9.28587 10 9.75V15.75C10 16.2141 9.81563 16.6592 9.48744 16.9874C9.15925 17.3156 8.71413 17.5 8.25 17.5H2.25C1.78587 17.5 1.34075 17.3156 1.01256 16.9874C0.684375 16.6592 0.5 16.2141 0.5 15.75V9.75C0.5 9.28587 0.684375 8.84075 1.01256 8.51256ZM14.25 9.5C14.1837 9.5 14.1201 9.52634 14.0732 9.57322C14.0263 9.62011 14 9.68369 14 9.75V15.75C14 15.8163 14.0263 15.8799 14.0732 15.9268C14.1201 15.9737 14.1837 16 14.25 16H16.25C16.3163 16 16.3799 15.9737 16.4268 15.9268C16.4737 15.8799 16.5 15.8163 16.5 15.75V9.75C16.5 9.68369 16.4737 9.62011 16.4268 9.57322C16.3799 9.52634 16.3163 9.5 16.25 9.5H14.25ZM13.0126 8.51256C13.3408 8.18437 13.7859 8 14.25 8H16.25C16.7141 8 17.1592 8.18437 17.4874 8.51256C17.8156 8.84075 18 9.28587 18 9.75V15.75C18 16.2141 17.8156 16.6592 17.4874 16.9874C17.1592 17.3156 16.7141 17.5 16.25 17.5H14.25C13.7859 17.5 13.3408 17.3156 13.0126 16.9874C12.6844 16.6592 12.5 16.2141 12.5 15.75V9.75C12.5 9.28587 12.6844 8.84075 13.0126 8.51256Z"
      fill="currentColor"
    />
  </svg>
);
