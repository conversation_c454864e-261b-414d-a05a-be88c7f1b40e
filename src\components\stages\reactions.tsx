"use client";

import { useEffect, useState } from "react";

import { useDataChannel } from "@livekit/components-react";
import { Emoji } from "emoji-picker-react";

import { useStageStore } from "@/stores/stage";

import { Button } from "../ui/button";

// ['❤️', '🤣', '👍', '👎',  "💯", "🤯", "🔥",]
const reactions = [
  "2764-fe0f",
  "1f923",
  "1f44d",
  "1f44e",
  "1f4af",
  "1f92f",
  "1f525",
];

export const ReactionButtons = () => {
  const encoder = new TextEncoder();
  const decoder = new TextDecoder();
  const actions = useStageStore((state) => state.actions);

  const { send } = useDataChannel("reactions", (msg) => {
    const emote = JSON.parse(decoder.decode(msg.payload));
    actions.addReaction(emote);
  });

  const addReaction = (emote: string) => {
    const id = Date.now();
    actions.addReaction({
      value: emote,
      id,
    });
    send(encoder.encode(JSON.stringify({ value: emote, id })), {
      reliable: false,
    });
  };

  return (
    <div className="flex items-center gap-2 ">
      {reactions.map((emote) => (
        <Button
          variant="outline"
          onClick={() => addReaction(emote)}
          key={emote}
          className="size-10 rounded-full border-off-white/10 p-0"
        >
          <Emoji unified={emote} size={20} />
        </Button>
      ))}
    </div>
  );
};

export const Reactions = () => {
  const reactions = useStageStore((state) => state.reactions);
  const actions = useStageStore((state) => state.actions);

  const removeReaction = (id: number) => {
    actions.removeReaction(id);
  };

  return (
    <div className="reactionMask pointer-events-none absolute inset-x-0 bottom-0 h-[200px] overflow-hidden">
      {reactions.map(({ value, id }) => (
        <Reaction
          key={id}
          onAnimationEnd={() => removeReaction(id)}
          reaction={value}
        />
      ))}
    </div>
  );
};

export const Reaction = ({
  onAnimationEnd,
  reaction,
}: {
  onAnimationEnd: () => void;
  reaction: string;
}) => {
  const [style] = useState(() => ({
    left: `${Math.random() * 100}%`,
    animationDuration: `${1 + Math.random() * 2}s`,
    "--tx": `${Math.random() * 40 - 20}px`,
    "--rotate": `${Math.random() * 60 - 30}deg`,
  }));

  useEffect(() => {
    const timer = setTimeout(onAnimationEnd, 2000);
    return () => {
      clearTimeout(timer);
    };
  }, []);

  return (
    <div className="reaction" style={style as React.CSSProperties}>
      <Emoji unified={reaction} size={32} />
    </div>
  );
};
