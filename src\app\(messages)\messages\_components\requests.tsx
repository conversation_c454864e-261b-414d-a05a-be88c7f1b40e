"use client";

import { <PERSON>E<PERSON>, Dispatch, FC, SetStateAction, useState } from "react";

import { Virtuoso } from "react-virtuoso";

import { ArrowBackOutlineIcon } from "@/components/icons";
import useThrottle from "@/hooks/use-throttle";
import { useRequestUsersSearchQuery } from "@/queries/user-queries";

import { NoUsersFound } from "./no-users-found";
import { RequestItem, RequestItemSkeletons } from "./request-item";
import { Search } from "./search";

export const SearchRequest: FC = () => {
  const [searchValue, setSearchValue] = useState("");
  const throttledSearchValue = useThrottle(searchValue);

  const { data: searchData, isLoading: isSearchDataLoading } =
    useRequestUsersSearchQuery(throttledSearchValue);

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  return (
    <>
      <Search
        placeholder="Search The Arena"
        search={searchValue}
        inputOnChange={handleSearch}
        closeOnClick={() => setSearchValue("")}
      />

      {throttledSearchValue && isSearchDataLoading && <RequestItemSkeletons />}
      {throttledSearchValue &&
        !isSearchDataLoading &&
        searchData &&
        searchData.users.length > 0 && (
          <div className="mt-2">
            <Virtuoso
              useWindowScroll
              data={searchData.users || []}
              overscan={200}
              itemContent={(_, user) => {
                return <RequestItem user={user} />;
              }}
            />
          </div>
        )}
      {throttledSearchValue &&
        !isSearchDataLoading &&
        searchData &&
        searchData.users.length === 0 && <NoUsersFound />}
    </>
  );
};

interface RequestProps {
  setCurrentPage: Dispatch<
    SetStateAction<"messages" | "request" | "receivedRequest">
  >;
}

export const Request: FC<RequestProps> = ({ setCurrentPage }) => {
  return (
    <>
      <div className="flex h-[48px] items-center px-6 pt-[18px] backdrop-blur-sm">
        <div className="flex items-center justify-start gap-2">
          <button onClick={() => setCurrentPage("messages")}>
            <ArrowBackOutlineIcon className="size-5 text-[#F3F3F3]" />
          </button>
        </div>
        <h4
          className="absolute text-base font-semibold text-white"
          style={{ transform: "translateX(-50%)", left: "50%" }}
        >
          New Direct Message
        </h4>
      </div>
      <SearchRequest />
    </>
  );
};
