"use client";

import { SetStateAction, useEffect, useRef, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import { fetchProposals } from "@/api/client/token-portal";
import { ArrowBackOutlineIcon, LogoIcon } from "@/components/icons";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useAirdropQuery } from "@/queries/airdrop-queries";
import { useTokenPortalClaimableBalanceQuery } from "@/queries/token-portal-queries";
import { useUser } from "@/stores";
import { cn } from "@/utils";

import { TabsSection } from "./_components/tab-section";

function TokenPortalPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const videoRef = useRef<HTMLVideoElement>(null);
  const { user } = useUser();
  const [play, setPlay] = useState(false);
  const [activeTab, setActiveTab] = useState(
    searchParams.get("tab") || "arena-claim",
  );
  const [proposals, setProposals] = useState([]);

  const handleTabChange = (value: SetStateAction<string>) => {
    setActiveTab(value);
    router.push(`/wallet/token-portal?tab=${value}`);
  };

  useEffect(() => {
    const tab = searchParams.get("tab");
    if (!tab) return;
    setActiveTab(tab);
  }, [searchParams]);

  useEffect(() => {
    const fetchData = async () => {
      const proposalsData: any = await fetchProposals("the-arena.eth");
      setProposals(proposalsData);
    };
    void fetchData();
  }, []);

  return (
    <div className="pb-pwa relative flex h-full flex-col">
      <div className="z-20 mt-[calc(-0px+env(safe-area-inset-top))] flex items-center justify-between px-4 pt-4">
        <button
          onClick={() => {
            router.push("/wallet");
          }}
          className="flex flex-shrink-0"
        >
          <ArrowBackOutlineIcon className="size-5" />
        </button>
        <h4 className="mr-6 text-base font-semibold leading-5 text-white">
          Arena Portal
        </h4>
        <div className="flex flex-shrink-0" />
      </div>
      <TabsSection
        activeTab={activeTab}
        setActiveTab={handleTabChange}
        user={user as any}
        proposals={proposals}
        router={router}
        videoRef={videoRef}
        play={play}
        setPlay={setPlay}
      />
      <div className="absolute inset-0 -z-20 h-full w-full overflow-hidden">
        <div className="absolute -top-1/2 left-[10%] block size-[1200px] rounded-full bg-brand-orange blur-[200px]" />
        <LogoIcon className="h-[135%] w-[195%] scale-125 transform opacity-35" />
      </div>
      <div className="absolute inset-0 -z-10 bg-[#141414]/85 backdrop-blur-[10px]" />
      <video
        ref={videoRef}
        src="/assets/arena-claim.mp4"
        className={cn(
          "absolute inset-0 z-20 h-full w-full object-cover transition-opacity duration-300",
          play ? "opacity-100" : "pointer-events-none opacity-0",
        )}
        disablePictureInPicture
        preload="auto"
        onEnded={() => setPlay(false)}
        muted
        playsInline
      />
    </div>
  );
}

export default TokenPortalPage;
