import { <PERSON><PERSON><PERSON> } from "buffer";

import CryptoJS from "crypto-js";
import { ec as EC } from "elliptic";

const ec = new EC("secp256k1");

export const em = (
  plaintext: string,
  recipientPublicKeyString: string,
  privateKeyString?: string,
): { iv: string; ephem_key: string; ciphertext: string; mac: string } => {
  if (!privateKeyString) {
    privateKeyString = ec.genKeyPair().getPrivate("hex");
  }
  const recipientPublicKey = ec.keyFromPublic(recipientPublicKeyString, "hex");
  const privateKey = ec.keyFromPrivate(privateKeyString, "hex");

  const shared = Buffer.from(
    privateKey.derive(recipientPublicKey.getPublic()).toString("hex"),
    "hex",
  );
  const hash = CryptoJS.SHA512(
    CryptoJS.enc.Hex.parse(shared.toString("hex")),
  ).toString(CryptoJS.enc.Hex);

  const iv = CryptoJS.lib.WordArray.random(16);
  const ek = CryptoJS.enc.Hex.parse(hash.slice(0, 64));
  const macKey = CryptoJS.enc.Hex.parse(hash.slice(64));

  const cipher = CryptoJS.algo.AES.createEncryptor(ek, {
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
    iv: iv,
  });
  const ciphertext = cipher.finalize(plaintext);

  const dataToMac = Buffer.concat([
    Buffer.from(iv.toString(), "hex"),
    Buffer.from(privateKey.getPublic("hex"), "hex"),
    Buffer.from(ciphertext.toString(CryptoJS.enc.Hex), "hex"),
  ]);
  const mac = CryptoJS.HmacSHA256(
    CryptoJS.enc.Hex.parse(dataToMac.toString("hex")),
    macKey,
  ).toString(CryptoJS.enc.Hex);

  return {
    iv: iv.toString(CryptoJS.enc.Hex),
    ephem_key: privateKey.getPublic("hex"),
    ciphertext: ciphertext.toString(CryptoJS.enc.Hex),
    mac: mac,
  };
};

export const dc = (
  incomingMessage: any,
  recipientPrivateKeyString: string,
): string | null => {
  const recipientPrivateKey = ec.keyFromPrivate(
    recipientPrivateKeyString,
    "hex",
  );
  const ephemPublicKey = ec.keyFromPublic(incomingMessage.ephem_key, "hex");
  const shared = Buffer.from(
    recipientPrivateKey.derive(ephemPublicKey.getPublic()).toString("hex"),
    "hex",
  );
  const hash = CryptoJS.SHA512(
    CryptoJS.enc.Hex.parse(shared.toString("hex")),
  ).toString(CryptoJS.enc.Hex);

  const encryptionKey = CryptoJS.enc.Hex.parse(hash.slice(0, 64));
  const macKey = CryptoJS.enc.Hex.parse(hash.slice(64));

  const iv = CryptoJS.enc.Hex.parse(incomingMessage.iv);
  const ciphertext = CryptoJS.enc.Hex.parse(incomingMessage.ciphertext);
  const mac = CryptoJS.enc.Hex.parse(incomingMessage.mac);

  const dataToMac = Buffer.concat([
    Buffer.from(iv.toString(), "hex"),
    Buffer.from(ephemPublicKey.getPublic("hex"), "hex"),
    Buffer.from(ciphertext.toString(CryptoJS.enc.Hex), "hex"),
  ]);
  const realMac = CryptoJS.HmacSHA256(
    CryptoJS.enc.Hex.parse(dataToMac.toString("hex")),
    macKey,
  ).toString(CryptoJS.enc.Hex);

  if (realMac !== mac.toString(CryptoJS.enc.Hex)) {
    return null;
  }

  const decipher = CryptoJS.algo.AES.createDecryptor(encryptionKey, {
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
    iv: iv,
  });
  const plaintext = decipher.finalize(ciphertext).toString(CryptoJS.enc.Utf8);

  return plaintext;
};

export const genKeyPair = () => {
  return ec.genKeyPair();
};
