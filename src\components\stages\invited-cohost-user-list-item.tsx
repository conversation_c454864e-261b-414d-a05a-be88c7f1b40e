"use client";

import {
  useLocalParticipant,
  useParticipantAttribute,
} from "@livekit/components-react";

import { useCancelInvitationMutation } from "@/queries";
import { StageUser } from "@/queries/types";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";

import { Button } from "../ui/button";
import { ROLES } from "./constants";
import { useDataChannelsContext } from "./stores/data-channels-context";
import { UserListItem } from "./user-list-item";

export function InvitedCohostUserListItem({ user }: { user: StageUser }) {
  const { user: me } = useUser();
  const local = useLocalParticipant();
  const role = useParticipantAttribute("role", {
    participant: local.localParticipant,
  });

  const id = useStageStore((state) => state.id!);
  const { sendInvalidateStageInfo } = useDataChannelsContext();
  const { mutateAsync: cancelInvitation, isPending } =
    useCancelInvitationMutation({
      onSuccess: () => {
        sendInvalidateStageInfo();
      },
    });

  return (
    <UserListItem user={user.user} badge="invited">
      {user.userId !== me?.id && role === ROLES.HOST && (
        <Button
          variant="outline"
          className="h-[34px] w-28"
          disabled={isPending}
          onClick={async () => {
            await cancelInvitation({
              stageId: id,
              invitedUserId: user.userId,
            });
          }}
        >
          Cancel
        </Button>
      )}
    </UserListItem>
  );
}
