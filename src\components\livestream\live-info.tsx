"use client";

import { useEffect, useState } from "react";

import { useParticipants } from "@livekit/components-react";
import { useQuery } from "@tanstack/react-query";
import { RoomEvent } from "livekit-client";

import { livestreamQueries } from "@/queries";
import { useLivestreamStore } from "@/stores/livestream";

import { ClockOutlineIcon, EyeOutlineIcon } from "../icons";

export function TimeCounter() {
  const id = useLivestreamStore((state) => state.id!);
  const [elapsedTime, setElapsedTime] = useState<string>("00:00:00");

  const { data } = useQuery(livestreamQueries.livestreamSimpleInfo(id));

  const startedOn = data?.livestream.startedOn;

  useEffect(() => {
    if (!startedOn) return;

    const updateTimer = () => {
      const start = new Date(startedOn).getTime();
      const now = new Date().getTime();
      const diff = now - start;

      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      const formattedTime = `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
      setElapsedTime(formattedTime);
    };

    updateTimer();

    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [startedOn]);

  return (
    <div className="flex items-center gap-1">
      <ClockOutlineIcon
        className="size-6 text-brand-orange"
        strokeWidth={1.5}
      />
      <span className="w-[70px] text-sm font-semibold text-light-gray-text">
        {elapsedTime}
      </span>
    </div>
  );
}

export function ViewCounter() {
  const id = useLivestreamStore((state) => state.id!);
  const { data } = useQuery(livestreamQueries.livestreamSimpleInfo(id));

  const participants = useParticipants({
    updateOnlyOn: [
      RoomEvent.ParticipantConnected,
      RoomEvent.ParticipantDisconnected,
    ],
  });

  const viewCount = participants.filter(
    (participant) =>
      participant.identity !== data?.livestream.hostId &&
      !participant.identity.includes("-ingress"),
  ).length;

  return (
    <div className="flex items-center gap-1">
      <EyeOutlineIcon className="size-6 text-brand-orange" strokeWidth={1.5} />
      <span className="text-sm font-semibold text-light-gray-text">
        {viewCount}
      </span>
    </div>
  );
}
