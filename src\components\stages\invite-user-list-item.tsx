"use client";

import {
  useLocalParticipant,
  useParticipantAttribute,
} from "@livekit/components-react";

import {
  useCancelInvitationMutation,
  useInviteStageMutation,
  useUpdateRoleMutation,
} from "@/queries";
import { StageParticipant } from "@/queries/types";
import { useStageStore } from "@/stores/stage";

import { Button } from "../ui/button";
import { InvitedRole, ROLES } from "./constants";
import { useDataChannelsContext } from "./stores/data-channels-context";
import { ParticipantListItem } from "./user-list-item";

export const InviteUserListItem = ({
  user,
  invited,
  role,
  accepted,
}: {
  user: StageParticipant;
  invited: boolean;
  accepted: boolean;
  role: InvitedRole;
}) => {
  const local = useLocalParticipant();
  const myRole = useParticipantAttribute("role", {
    participant: local.localParticipant,
  });
  const id = useStageStore((state) => state.id!);

  const { sendInvalidateStageInfo } = useDataChannelsContext();

  const { mutateAsync: invite, isPending: isInvitePending } =
    useInviteStageMutation({
      onSuccess: () => {
        sendInvalidateStageInfo();
      },
    });

  const { mutateAsync: cancelInvitation, isPending: isCancelInvitePending } =
    useCancelInvitationMutation({
      onSuccess: () => {
        sendInvalidateStageInfo();
      },
    });

  const { mutateAsync: updateRole, isPending: isUpdateRolePending } =
    useUpdateRoleMutation({
      onSuccess: () => {
        sendInvalidateStageInfo();
      },
    });

  const handleInvite = async () => {
    await invite({
      stageId: id,
      invitedUserId: user.id,
      roleType: role,
    });
  };

  const handleCancelInvite = async () => {
    await cancelInvitation({
      stageId: id,
      invitedUserId: user.id,
    });
  };

  const handleRemovePrivilege = async () => {
    await updateRole({
      stageId: id,
      role: "LISTENER",
      userId: user.id,
    });
  };

  if (role === "COHOST" && myRole !== ROLES.HOST) {
    return <ParticipantListItem user={user} />;
  }

  return (
    <ParticipantListItem user={user}>
      {invited ? (
        <Button
          variant="secondary"
          className="h-[34px] w-24"
          onClick={handleCancelInvite}
          loading={isCancelInvitePending}
        >
          Cancel
        </Button>
      ) : accepted ? (
        <Button
          variant="destructive"
          className="h-[34px] w-24"
          onClick={handleRemovePrivilege}
          loading={isUpdateRolePending}
        >
          Remove
        </Button>
      ) : (
        <Button
          className="h-[34px] w-24"
          onClick={handleInvite}
          loading={isInvitePending}
        >
          Add
        </Button>
      )}
    </ParticipantListItem>
  );
};
