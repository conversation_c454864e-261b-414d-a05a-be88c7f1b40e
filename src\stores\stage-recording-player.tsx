"use client";

import { createContext, useContext, useState } from "react";

import { createStore, StoreApi, useStore } from "zustand";

interface Store {
  id: string | null;
  url: string | null;
  isRecordingPlayerFullScreen: boolean;
  isPlaying: boolean;
  progress: number;
  duration: number;
  currentTime: number;
  isLoaded: boolean;
  audioRef: {
    current: HTMLAudioElement | null;
  };
}

interface Actions {
  actions: {
    reset: () => void;
    setPlayer: ({ id, url }: { id: string; url: string }) => void;
    setId: (id: string) => void;
    setUrl: (url: string | null) => void;
    setIsRecordingPlayerFullScreen: (
      isRecordingPlayerFullScreen: boolean,
    ) => void;
    setIsPlaying: (isPlaying: boolean) => void;
    setProgress: (progress: number) => void;
    setDuration: (duration: number) => void;
    setCurrentTime: (currentTime: number) => void;
    setIsLoaded: (isLoaded: boolean) => void;
  };
}

type StageState = Store & Actions;

const StageRecordingPlayerStoreContext =
  createContext<StoreApi<StageState> | null>(null);

export const StageRecordingPlayerStoreProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [store] = useState(() =>
    createStore<StageState>((set, get) => ({
      id: null,
      url: null,
      isRecordingPlayerFullScreen: true,
      isPlaying: false,
      progress: 0,
      duration: 0,
      currentTime: 0,
      isLoaded: false,
      audioRef: {
        current: null,
      },
      actions: {
        reset: () => {
          set({
            id: null,
            url: null,
            isRecordingPlayerFullScreen: true,
            isPlaying: false,
            progress: 0,
            duration: 0,
            currentTime: 0,
            isLoaded: false,
            audioRef: {
              current: null,
            },
          });
        },
        setPlayer: ({ id, url }) => {
          set({
            id,
            url,
            isRecordingPlayerFullScreen: true,
            isPlaying: false,
            progress: 0,
            duration: 0,
            currentTime: 0,
            isLoaded: false,
            audioRef: {
              current: null,
            },
          });
        },
        setId: (id) => {
          set({ id });
        },
        setUrl: (url) => {
          set({ url });
        },
        setIsRecordingPlayerFullScreen: (isRecordingPlayerFullScreen) => {
          set({ isRecordingPlayerFullScreen });
        },
        setIsPlaying: (isPlaying) => {
          set({ isPlaying });
        },
        setProgress: (progress) => {
          set({ progress });
        },
        setDuration: (duration) => {
          set({ duration });
        },
        setCurrentTime: (currentTime) => {
          set({ currentTime });
        },
        setIsLoaded: (isLoaded) => {
          set({ isLoaded });
        },
      },
    })),
  );

  return (
    <StageRecordingPlayerStoreContext.Provider value={store}>
      {children}
    </StageRecordingPlayerStoreContext.Provider>
  );
};

export function useStageRecordingPlayerStore(): StageState;
export function useStageRecordingPlayerStore<T>(
  selector: (state: StageState) => T,
): T;
export function useStageRecordingPlayerStore<T>(
  selector?: (state: StageState) => T,
) {
  const store = useContext(StageRecordingPlayerStoreContext);
  if (!store) {
    throw new Error("Missing StageStoreProvider");
  }
  return useStore(store, selector!);
}
