"use client";

import { useCallback } from "react";

import { postUpdateSpeakingDuration } from "@/api/client/stage";
import { useStageStore } from "@/stores/stage";

export function useLogSpeakingDuration() {
  const stageId = useStageStore((state) => state.id);
  const actions = useStageStore((state) => state.actions);

  const logSpeakingDuration = useCallback(() => {
    if (!stageId) return;

    const speakingDuration = actions.getSpeakingDurationAndReset();
    if (speakingDuration > 0) {
      postUpdateSpeakingDuration({
        stageId,
        speakingDuration,
      });
    }
  }, [actions, stageId]);

  return logSpeakingDuration;
}
