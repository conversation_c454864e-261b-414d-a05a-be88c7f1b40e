import { memo } from "react";

import { parseISO } from "date-fns";
import { Virtuoso } from "react-virtuoso";

import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useRecentTradesQuery } from "@/queries";
import { Trade } from "@/queries/types";
import { formatAvax, formatTimeDistance } from "@/utils";

export const ActivityTab = memo(function ActivityTab() {
  const { data } = useRecentTradesQuery();

  return (
    <>
      <p className="px-6 text-sm text-[#808080]">Recent Trading Activity</p>
      {data && data.trades.length === 0 && (
        <div className="flex h-full items-center justify-center">
          <div className="mt-20 max-w-64 text-center">
            <h4 className="text-sm font-semibold text-[#EDEDED]">
              No activity to show!
            </h4>
          </div>
        </div>
      )}
      <Virtuoso
        useWindowScroll
        data={data?.trades || []}
        overscan={200}
        itemContent={(index, trade) => {
          return <TradeItem trade={trade} />;
        }}
      />
    </>
  );
});

interface TradeItemProps {
  trade: Trade;
}

const TradeItem = ({ trade }: TradeItemProps) => {
  const createdOnDate = parseISO(trade.createdOn);

  const formattedAmount = formatAvax(trade.amount);

  return (
    <div className="flex flex-col px-6 py-4">
      <div className="flex w-full justify-between">
        <div className="flex -space-x-2">
          <Avatar className="z-[1] size-[29px]" asChild>
            <ProgressBarLink href={`/${trade.traderUser?.twitterHandle}`}>
              <AvatarImage src={trade.traderUser?.twitterPicture} />
              <AvatarFallback />
            </ProgressBarLink>
          </Avatar>
          <Avatar className="size-[29px]" asChild>
            <ProgressBarLink href={`/${trade.subjectUser?.twitterHandle}`}>
              <AvatarImage src={trade.subjectUser?.twitterPicture} />
              <AvatarFallback />
            </ProgressBarLink>
          </Avatar>
        </div>
        <div className="flex-shrink-0 text-xs leading-5 text-gray-text">
          {formatTimeDistance(createdOnDate)}
        </div>
      </div>
      <div className="mt-2 text-sm text-white">
        <ProgressBarLink
          href={`/${trade.traderUser?.twitterHandle}`}
          className="font-semibold"
        >
          {trade.traderUser?.twitterName}
        </ProgressBarLink>{" "}
        {trade.isBuy ? "bought" : "sold"} {trade?.shareAmount} tickets of{" "}
        <ProgressBarLink
          href={`/${trade.subjectUser?.twitterHandle}`}
          className="font-semibold"
        >
          {trade.subjectUser?.twitterName}
        </ProgressBarLink>{" "}
        for {formattedAmount} AVAX.
      </div>
    </div>
  );
};
