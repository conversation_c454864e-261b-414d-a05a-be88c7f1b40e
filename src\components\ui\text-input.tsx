import { forwardRef, useId } from "react";

import type { ComponentPropsWithoutRef, ElementRef, ReactElement } from "react";

import { Input } from "./input";
import { Label } from "./label";

interface TextInputProps extends ComponentPropsWithoutRef<"input"> {
  errorMessage?: string;
  label?: string;
  labelWithAmount?: ReactElement<any, any>;
  onMaxClick?: () => void;
  optional?: boolean;
}

export const TextInput = forwardRef<ElementRef<"input">, TextInputProps>(
  (
    {
      errorMessage,
      label,
      labelWithAmount,
      optional,
      onMaxClick,
      children,
      ...props
    },
    ref,
  ) => {
    const id = useId();
    const formItemId = `form-item-${id}`;

    return (
      <div
        className={
          label || labelWithAmount ? "flex flex-col gap-2" : "flex flex-col"
        }
      >
        <div className="flex items-center justify-between gap-2">
          {label && <Label htmlFor={formItemId}>{label}</Label>}
          {labelWithAmount && (
            <Label htmlFor={formItemId}>{labelWithAmount}</Label>
          )}
          {children}
        </div>
        <Input
          id={formItemId}
          ref={ref}
          onMaxClick={onMaxClick}
          {...props}
          variant={errorMessage ? "error" : "default"}
          aria-invalid={errorMessage ? "true" : "false"}
        />
        {errorMessage && (
          <span className="text-xs text-danger">{errorMessage}</span>
        )}
      </div>
    );
  },
);

TextInput.displayName = "TextInput";
