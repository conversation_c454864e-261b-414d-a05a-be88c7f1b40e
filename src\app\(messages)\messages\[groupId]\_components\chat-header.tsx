"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

import Skeleton from "react-loading-skeleton";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { SuspendedUserChatHeaderOutlineIcon } from "@/components/icons/suspended-user";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { SOCKET_MESSAGE } from "@/environments/socket-messages";
import { useUserByIdQuery } from "@/queries";
import { useUser } from "@/stores";
import { useSocket } from "@/stores/socket";
import { UserFlaggedEnum } from "@/types";
import { cn } from "@/utils";

import { useGroup } from "../context/group-context";

interface Typing {
  groupId: string;
  user: {
    id: string;
    name: string;
  };
  date: number;
}

export const ChatHeader = () => {
  const { user } = useUser();
  const [typing, setTyping] = useState<Typing>();
  const { socket } = useSocket();
  const router = useRouter();

  const { data, isLoading, groupId } = useGroup();

  useEffect(() => {
    socket?.on(SOCKET_MESSAGE.CHAT_TYPING, (data: { typing: Typing }) => {
      if (
        data.typing &&
        data.typing.groupId === groupId &&
        data.typing.user.id !== user?.id
      ) {
        setTyping({ ...data.typing, date: Date.now() });
      }
    });

    return () => {
      socket?.off(SOCKET_MESSAGE.CHAT_TYPING);
    };
  }, [socket, groupId, user]);

  useEffect(() => {
    const interval = setInterval(() => {
      setTyping((prev) => {
        const now = Date.now();

        if (prev && now - prev.date > 500) {
          return undefined;
        }

        return prev;
      });
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  const chatMate = useUserByIdQuery(data?.group?.chatMateId || "");
  const owner = useUserByIdQuery(data?.group?.ownerUserId || "");
  const isMe = user !== null && user?.id === data?.group?.ownerUserId;
  let name = isMe ? data?.group?.chatMateName : data?.group?.name;
  const getAvatarImageSrc = (groupName: string | undefined) => {
    if (groupName === "OG Badge Arena") {
      return "/assets/badges/badge-type-1.png";
    }
    if (groupName === "Dokyo Badge Arena") {
      return "/assets/badges/badge-type-3.png";
    }
    if (groupName === "DeGods Badge Arena") {
      return "/assets/badges/badge-type-2.png";
    }
    if (groupName === "SappySeals Badge Arena") {
      return "/assets/badges/badge-type-4.png";
    }
    if (groupName === "Steady Badge Arena") {
      return "/assets/badges/badge-type-8.png";
    }
    if (groupName === "Gurs Badge Arena") {
      return "/assets/coins/gurs.png";
    }
    return data?.group?.isDirect
      ? isMe
        ? chatMate?.data?.user?.twitterPicture
        : data?.group?.profilePictureUrl
      : data?.group?.profilePictureUrl;
  };

  const userHandle = data?.group?.isDirect
    ? isMe
      ? chatMate?.data?.user?.twitterHandle
      : owner?.data?.user?.twitterHandle
    : owner?.data?.user?.twitterHandle;

  let isSuspended = false;
  if (data?.group?.isDirect) {
    isSuspended = isMe
      ? chatMate?.data?.user?.flag === UserFlaggedEnum.SUSPENDED
      : owner?.data?.user?.flag === UserFlaggedEnum.SUSPENDED;

    if (isSuspended) {
      name = "Suspended Account";
    }

    if (
      data.group.chatMateId !== user?.id &&
      data.group.ownerUserId !== user?.id
    ) {
      router.push("/messages");
      return null;
    }
  } else {
    isSuspended = owner?.data?.user?.flag === UserFlaggedEnum.SUSPENDED;
  }

  const headerContent = (
    <div className="flex items-center gap-3">
      {isSuspended ? (
        <SuspendedUserChatHeaderOutlineIcon />
      ) : (
        <Avatar className="size-7">
          <AvatarImage src={getAvatarImageSrc(data?.group?.name)} />
          <AvatarFallback />
        </Avatar>
      )}
      <div className="relative">
        <h3
          className={cn(
            "text-base font-semibold leading-5 text-white transition-transform",
            typing ? "-translate-y-2" : "translate-y-0",
          )}
        >
          {isLoading ? (
            <Skeleton className="h-4 w-24" />
          ) : (
            name || data?.group?.name
          )}
        </h3>
        <p
          className={cn(
            "absolute -bottom-2 left-0 whitespace-nowrap text-xs leading-4 text-gray-text transition-opacity",
            typing ? "opacity-100" : "opacity-0",
          )}
        >
          {typing && <span>{typing.user.name} is typing...</span>}
        </p>
      </div>
    </div>
  );

  return (
    <div className="flex items-center justify-between bg-[#141414] bg-opacity-[0.88] px-6 pb-3 pt-[22px] backdrop-blur-sm">
      <div className="flex items-center justify-start gap-2">
        <ProgressBarLink href="/messages" className="size-5">
          <ArrowBackOutlineIcon className="size-5 text-[#F3F3F3]" />
        </ProgressBarLink>
        {data?.group?.isBadge ? (
          headerContent
        ) : (
          <ProgressBarLink href={`/${userHandle}`}>
            {headerContent}
          </ProgressBarLink>
        )}
      </div>
    </div>
  );
};
