import React from "react";

import { TippingPartySettingsIcon } from "../icons/tipping-party-settings";
import { ArenaDialogHeader } from "./arena-dialog-header";
import { Button } from "./button";
import { Dialog, DialogContent } from "./dialog";

export default {
  title: "UI/ModalHeader",
  component: ArenaDialogHeader,
};

const StoryWrapper = ({ children }: { children: React.ReactNode }) => (
  <Dialog open>
    <DialogContent className="flex h-full w-full flex-col bg-[#0F0F0F]/90 px-0 pt-0 backdrop-blur-sm sm:h-auto sm:max-w-[524px] sm:rounded-[20px] sm:border sm:border-[rgba(59,59,59,0.30)] sm:bg-[rgba(15,15,15,0.90)]">
      {children}
      <div className="bg-gray-800/20 flex-grow" />
    </DialogContent>
  </Dialog>
);

export const TitleOnly = () => (
  <StoryWrapper>
    <ArenaDialogHeader title="Just a Title" showBack={false} />
  </StoryWrapper>
);

export const WithDialogClose = () => (
  <StoryWrapper>
    <ArenaDialogHeader title="With DialogClose" showBack />
  </StoryWrapper>
);

export const WithCustomBack = () => (
  <StoryWrapper>
    <ArenaDialogHeader
      title="With Custom Back"
      showBack
      onBack={() => alert("Back pressed!")}
    />
  </StoryWrapper>
);

export const WithRightButton = () => (
  <StoryWrapper>
    <ArenaDialogHeader
      title="With Right Button"
      showBack={false}
      rightButton={
        <Button variant="ghost" size="icon" onClick={() => alert("Settings!")}>
          <TippingPartySettingsIcon className="size-4 text-off-white" />
        </Button>
      }
    />
  </StoryWrapper>
);

export const FullFeatured = () => (
  <StoryWrapper>
    <ArenaDialogHeader
      title="Full Featured"
      showBack
      onBack={() => alert("Back pressed!")}
      rightButton={
        <Button variant="ghost" size="icon" onClick={() => alert("Settings!")}>
          <TippingPartySettingsIcon className="size-4 text-off-white" />
        </Button>
      }
    />
  </StoryWrapper>
);
