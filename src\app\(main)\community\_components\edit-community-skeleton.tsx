import Skeleton from "react-loading-skeleton";

import { TextInput } from "@/components/ui/text-input";
import { Textarea } from "@/components/ui/textarea";

export const EditCommunitySkeleton = () => {
  return (
    <div className="mb-6 flex flex-1 flex-col">
      <div className="relative mb-6 flex flex-col gap-3">
        <div className="relative">
          <div
            className="aspect-[4/1] w-full overflow-hidden bg-[#0e0e0e]"
            style={{
              backgroundImage: `url(${"/images/default-banner.png"})`,
              backgroundSize: "cover",
            }}
          />

          <div className="absolute right-8 top-full -translate-y-1/2 leading-none">
            <Skeleton circle className="h-11 w-11" />
          </div>
        </div>
      </div>
      <div className="-mt-[15px] flex flex-col gap-4 px-6">
        <Skeleton className="h-[92px] w-[92px] !rounded-lg border border-[#E8E8E8]" />
        <div className="flex flex-col gap-1 pt-[2px] leading-none">
          <div className="flex gap-1 leading-none">
            <Skeleton className="h-5 w-6" />
            <Skeleton className="h-5 w-20" />
          </div>
          <Skeleton className="h-5 w-20" />
        </div>
      </div>
      <div className="relative mt-6 flex flex-1 flex-col gap-2 px-6 pt-[2px]">
        <div className="flex flex-col gap-2">
          <label className="text-xs font-semibold text-[#f3f3f3]">
            DESCRIPTION
          </label>
          <div className="relative">
            <Textarea
              className="min-h-[55px] py-4 pr-16 text-[#808080]"
              placeholder="What is your token about?"
              disabled
            />
          </div>
        </div>
        <div className="relative mt-2 flex flex-col gap-2">
          <label className="text-xs font-semibold  text-[#808080]">
            PROFILE PAGE HANDLE
          </label>
          <TextInput placeholder="new_handle" disabled />
        </div>
      </div>
      <div className="mt-auto px-6">
        <div className="w-full overflow-hidden rounded-full leading-none">
          <Skeleton className="h-11 w-full" />
        </div>
      </div>
    </div>
  );
};
