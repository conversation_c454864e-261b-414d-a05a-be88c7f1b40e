import { ComponentProps } from "react";

export const ChatAlt2OutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 28 28"
    fill="none"
    {...props}
  >
    <path
      d="M19 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12V18C23 18.5304 22.7893 19.0391 22.4142 19.4142C22.0391 19.7893 21.5304 20 21 20H19V24L15 20H11C10.7373 20.0003 10.4772 19.9486 10.2345 19.8481C9.99187 19.7475 9.77148 19.6 9.586 19.414M9.586 19.414L13 16H17C17.5304 16 18.0391 15.7893 18.4142 15.4142C18.7893 15.0391 19 14.5304 19 14V8C19 7.46957 18.7893 6.96086 18.4142 6.58579C18.0391 6.21071 17.5304 6 17 6H7C6.46957 6 5.96086 6.21071 5.58579 6.58579C5.21071 6.96086 5 7.46957 5 8V14C5 14.5304 5.21071 15.0391 5.58579 15.4142C5.96086 15.7893 6.46957 16 7 16H9V20L9.586 19.414Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M27 6C27 9.31371 24.3137 12 21 12C17.6863 12 15 9.31371 15 6C15 2.68629 17.6863 0 21 0C24.3137 0 27 2.68629 27 6Z"
      fill="#EB540A"
    />
    <path
      d="M22 2V10H20.7088V3.41016H20.6671L19 4.60547V3.27734L20.7715 2H22Z"
      fill="currentColor"
    />
  </svg>
);

export const ChatAltDoubleOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M17 8H19C19.5304 8 20.0391 8.21071 20.4142 8.58579C20.7893 8.96086 21 9.46957 21 10V16C21 16.5304 20.7893 17.0391 20.4142 17.4142C20.0391 17.7893 19.5304 18 19 18H17V22L13 18H9C8.73733 18.0003 8.47719 17.9486 8.23453 17.8481C7.99187 17.7475 7.77148 17.6 7.586 17.414M7.586 17.414L11 14H15C15.5304 14 16.0391 13.7893 16.4142 13.4142C16.7893 13.0391 17 12.5304 17 12V6C17 5.46957 16.7893 4.96086 16.4142 4.58579C16.0391 4.21071 15.5304 4 15 4H5C4.46957 4 3.96086 4.21071 3.58579 4.58579C3.21071 4.96086 3 5.46957 3 6V12C3 12.5304 3.21071 13.0391 3.58579 13.4142C3.96086 13.7893 4.46957 14 5 14H7V18L7.586 17.414Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
