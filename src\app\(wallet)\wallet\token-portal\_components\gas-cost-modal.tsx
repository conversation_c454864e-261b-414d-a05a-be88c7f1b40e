import * as React from "react";
import { useEffect, useMemo, useState } from "react";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import * as RadioGroup from "@radix-ui/react-radio-group";
import { formatGwei, parseGwei } from "viem";

import { CogOutlineIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { TextInput } from "@/components/ui/text-input";
import { formatEther } from "@/utils";

enum GasCostOption {
  None = "None",
  Normal = "Normal",
  Fast = "Fast",
  Custom = "Custom",
}

export const GasCostModal = ({
  gasUsed,
  onOpenChange,
  onSubmit,
  open,
  userBalance,
}: {
  gasUsed: bigint;
  open: boolean;
  onOpenChange: (isOpen: boolean) => void;
  userBalance: string;
  onSubmit?: (
    maxFeePerGas: bigint,
    defaultMaxPriorityFeePerGas: bigint,
  ) => Promise<void> | undefined;
}) => {
  const [defaultMaxFeePerGasInGwei, setDefaultMaxFeePerGasInGwei] = useState(0);
  const [defaultMaxPriorityFeePerGas, setDefaultMaxPriorityFeePerGas] =
    useState(0n);
  const [option, setOption] = useState<GasCostOption>(GasCostOption.None);
  const [estimateCost, setEstimateCost] = useState<string>("0");
  const [customGasFee, setCustomGasFee] = useState("");
  const [maxFeePerGas, setMaxFeePerGas] = useState(0n);
  const [maxPriorityFeePerGas, setMaxPriorityFeePerGas] = useState(0n);

  const { primaryWallet } = useDynamicContext();

  const handleOptionChange = async (value: GasCostOption) => {
    setOption(value);

    if (!primaryWallet) return;
    if (!isEthereumWallet(primaryWallet)) {
      throw new Error("This wallet is not a Ethereum wallet");
    }

    // const publicClient = await primaryWallet.getPublicClient();

    let maxFeePerGas1;
    let maxPriorityFeePerGas1;

    switch (value) {
      case GasCostOption.Custom:
        return;
      case GasCostOption.Fast:
        maxFeePerGas1 = parseGwei((defaultMaxFeePerGasInGwei * 2).toString());
        maxPriorityFeePerGas1 = parseGwei("2");
        break;
      case GasCostOption.None:
      case GasCostOption.Normal:
        maxFeePerGas1 = parseGwei(
          (defaultMaxFeePerGasInGwei * 1.25).toString(),
        );
        maxPriorityFeePerGas1 = parseGwei("1.25");
        break;
    }
    if (!maxFeePerGas1) return;

    // const approvalAmount = ethers.parseUnits(stakeAmount, 18);

    // const gasUsed = await publicClient.estimateContractGas({
    //   address: getContractAddress("ARENA") as Address,
    //   abi: tokenContractABI,
    //   functionName: "approve",
    //   args: [stakingContractAddress, approvalAmount],
    //   account: primaryWallet.address as Hex,
    //   maxFeePerGas: maxFeePerGas1,
    //   maxPriorityFeePerGas: maxPriorityFeePerGas1,
    // });

    setMaxFeePerGas(maxFeePerGas1);
    setMaxPriorityFeePerGas(maxPriorityFeePerGas1);

    setEstimateCost(formatEther((gasUsed * maxFeePerGas1).toString()));
  };

  const handleSubmit = async () => {
    if (onSubmit) {
      await onSubmit(maxFeePerGas, maxPriorityFeePerGas);
    } else {
      onOpenChange(false);
    }
  };

  const isConfirmButtonDisabled = useMemo(
    () => !estimateCost || option === GasCostOption.None,
    [option, estimateCost, onSubmit],
  );

  useEffect(() => {
    const getFeeData = async () => {
      if (!primaryWallet) return;
      if (!isEthereumWallet(primaryWallet)) {
        throw new Error("This wallet is not a Ethereum wallet");
      }

      const publicClient = await primaryWallet.getPublicClient();

      const baseFee = formatGwei(await publicClient.getGasPrice());

      setDefaultMaxPriorityFeePerGas(
        await publicClient.estimateMaxPriorityFeePerGas(),
      );
      setDefaultMaxFeePerGasInGwei(
        Number(baseFee) + Number(defaultMaxPriorityFeePerGas),
      );

      if (
        Number(baseFee) + Number(defaultMaxPriorityFeePerGas) <
        Number(defaultMaxPriorityFeePerGas)
      ) {
        throw "Error: Max fee per gas cannot be less than max priority fee per gas";
      }
    };

    void getFeeData();
  }, [primaryWallet]);

  useEffect(() => {}, [onSubmit]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[87%] gap-6 rounded-[20px] border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] backdrop-blur-sm sm:max-w-[524px]">
        <div className="flex items-center gap-3">
          <div className="flex-col">
            <h3 className="text-base font-semibold leading-[22px] text-off-white">
              Gas costs are higher than usual!
            </h3>
            <h4 className="text-[12px] leading-[30px] text-gray-text">
              Please manually adjust the gas cost.
            </h4>
          </div>
        </div>
        <RadioGroup.Root
          onValueChange={handleOptionChange}
          defaultValue={GasCostOption.None}
          className="flex justify-between gap-2"
        >
          <RadioGroup.Item
            value={GasCostOption.Normal}
            className="rounded-[10px] border border-[#646464] aria-checked:border-none aria-checked:bg-[linear-gradient(109deg,#F36A27_10.68%,#CE4909_79.68%)]"
            asChild
          >
            <button className="flex flex-1 flex-col items-center gap-2 px-4 py-3 text-left">
              <div className="text-center text-sm font-semibold text-off-white">
                Normal
              </div>
              <div className="text-center text-xs text-off-white">
                {(defaultMaxFeePerGasInGwei * 1.25).toFixed()} nAVAX
              </div>
            </button>
          </RadioGroup.Item>
          <RadioGroup.Item
            value={GasCostOption.Fast}
            className="rounded-[10px] border border-[#646464] aria-checked:border-none aria-checked:bg-[linear-gradient(109deg,#F36A27_10.68%,#CE4909_79.68%)]"
            asChild
          >
            <button className="flex flex-1 flex-col items-center gap-2 px-4 py-3 text-left">
              <div className="text-center text-sm font-semibold text-off-white">
                Fast
              </div>
              <div className="text-center text-xs text-off-white">
                {(defaultMaxFeePerGasInGwei * 2).toFixed()} nAVAX
              </div>
            </button>
          </RadioGroup.Item>
          <RadioGroup.Item
            value={GasCostOption.Custom}
            className="rounded-[10px] border border-[#646464] aria-checked:border-none aria-checked:bg-[linear-gradient(109deg,#F36A27_10.68%,#CE4909_79.68%)]"
            asChild
          >
            <button className="flex flex-1 flex-col items-center gap-2 px-4 py-3 text-left">
              <div className="text-center text-sm font-semibold text-off-white">
                Custom
              </div>
              <CogOutlineIcon className="size-5 text-off-white" />
            </button>
          </RadioGroup.Item>
        </RadioGroup.Root>
        <form
          onSubmit={(e) => e.preventDefault()}
          className="flex flex-col gap-4"
        >
          <div className="mb-2 flex gap-1">
            <label className="text-sm text-light-gray-text">Balance:</label>
            <label className="text-sm text-off-white">{userBalance}</label>
          </div>
          {option === GasCostOption.Custom ? (
            <div className="relative">
              <TextInput
                placeholder="Set gas fee (nAVAX)"
                value={customGasFee}
                onChange={(event) => {
                  let value = event.target.value;
                  if (value === "") {
                    setCustomGasFee(value);
                    return;
                  }
                  setCustomGasFee(parseInt(value).toString());

                  setMaxFeePerGas(parseGwei((parseInt(value) - 2).toString()));
                  setMaxPriorityFeePerGas(parseGwei("2"));
                }}
                type="string"
                className="rounded-[10px] border border-gray-text px-4 py-3"
              />
              <div className="absolute right-4 top-1/2 -translate-y-1/2 cursor-pointer">
                <span className="text-sm text-off-white">
                  {"= " + formatGwei(BigInt(customGasFee) * gasUsed) + " AVAX"}
                </span>
              </div>
            </div>
          ) : (
            <div className="flex flex-1 justify-between gap-2 rounded-[10px] border border-gray-text px-4 py-4">
              <div className="text-sm font-semibold text-gray-text">
                Estimated cost
              </div>
              <div className="flex items-center justify-items-end gap-1">
                <span className="text-sm text-off-white">
                  {estimateCost === "0" ? "N/A" : estimateCost + " AVAX"}
                </span>
              </div>
            </div>
          )}

          <Button
            className="mt-4 w-full"
            type="submit"
            disabled={isConfirmButtonDisabled}
            onClick={handleSubmit}
          >
            Confirm
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
};
