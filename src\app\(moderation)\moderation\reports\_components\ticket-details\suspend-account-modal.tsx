"use client";

import { toast } from "@/components/toast";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface SuspendAccountModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: {
    twitterHandle: string;
    twitterName: string;
  };
  onConfirm: () => void;
}

export const SuspendAccountModal = ({
  open,
  setOpen,
  user,
  onConfirm,
}: SuspendAccountModalProps) => {
  const handleSuspend = async () => {
    try {
      await onConfirm();
      setOpen(false);
      toast.green(`${user.twitterName} has been suspended!`);
    } catch (error) {
      toast.red("Failed to suspend account. Please try again.");
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-sm">
        <DialogHeader className="space-y-4">
          <DialogTitle>Suspend @{user.twitterHandle}?</DialogTitle>
          <DialogDescription className="text-gray-text">
            When you suspend @{user.twitterHandle}, they will be temporarily
            restricted from using the platform. Are you sure you want to
            proceed?
          </DialogDescription>
        </DialogHeader>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            className="flex-1"
            onClick={() => setOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            className="flex-1"
            onClick={handleSuspend}
          >
            Suspend
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
