import { queryOptions } from "@tanstack/react-query";

import {
  getCurrentlyListening,
  getEmotes,
  getIsUserInvited,
  getLiveStages,
  getMostRecentTip,
  getStageInfo,
  getStageInfoSimple,
  getStageSpeakers,
} from "@/api/client/stage";

export const stageQueries = {
  allKey: () => ["stages"],
  liveStagesKey: () => [...stageQueries.allKey(), "live"],
  liveStages: () =>
    queryOptions({
      queryKey: [...stageQueries.liveStagesKey()],
      queryFn: () => getLiveStages(),
    }),
  stageInfoKey: (id: string) => [...stageQueries.allKey(), "stage", id],
  stageInfoSimpleKey: (id: string) => [
    ...stageQueries.allKey(),
    "stage-simple",
    id,
  ],
  stageInfo: (id: string) =>
    queryOptions({
      queryKey: [...stageQueries.stageInfoKey(id)],
      queryFn: () => getStageInfo(id),
    }),
  stageInfoSimple: (id: string) =>
    queryOptions({
      queryKey: [...stageQueries.stageInfoSimpleKey(id)],
      queryFn: () => getStageInfoSimple(id),
    }),
  stageSpeakersKey: (id: string) => [
    ...stageQueries.allKey(),
    "stage-speakers",
    id,
  ],
  stageSpeakers: (id: string) =>
    queryOptions({
      queryKey: [...stageQueries.stageSpeakersKey(id)],
      queryFn: () => getStageSpeakers(id),
    }),
  mostRecentTipKey: () => [...stageQueries.allKey(), "mostRecentTip"],
  mostRecentTip: () =>
    queryOptions({
      queryKey: [...stageQueries.mostRecentTipKey()],
      queryFn: () => getMostRecentTip(),
    }),
  currentlyListeningKey: () => [...stageQueries.allKey(), "currentlyListening"],
  currentlyListening: () =>
    queryOptions({
      queryKey: [...stageQueries.currentlyListeningKey()],
      queryFn: () => getCurrentlyListening(),
    }),
  isUserInvitedKey: (params: { stageId: string; userId: string }) => [
    ...stageQueries.allKey(),
    "isUserInvited",
    params,
  ],
  isUserInvited: (params: { stageId: string; userId: string }) =>
    queryOptions({
      queryKey: [...stageQueries.isUserInvitedKey(params)],
      queryFn: () => getIsUserInvited(params),
    }),
  emotesKey: () => [...stageQueries.allKey(), "emotes"],
  emotes: () =>
    queryOptions({
      queryKey: [...stageQueries.emotesKey()],
      queryFn: () => getEmotes(),
    }),
};
