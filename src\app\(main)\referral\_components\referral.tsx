"use client";

import { useEffect, useMemo, useState } from "react";

import { useQueryClient } from "@tanstack/react-query";
import CopyToClipboard from "react-copy-to-clipboard";
import Skeleton from "react-loading-skeleton";
import QRCode from "react-qr-code";

import { CheckFilledIcon, CopyOutlineIcon } from "@/components/icons";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { AVAX } from "@/environments/tokens";
import { useReferralStatsQuery, useReferrersQuery } from "@/queries";
import { useConfirmReferrerMutation } from "@/queries/user-mutations";
import { useUser } from "@/stores";
import { formatEther } from "@/utils";

export const Referral = () => {
  const { user } = useUser();
  const queryClient = useQueryClient();
  const [isMounted, setIsMounted] = useState(false);

  const { data: referrersData, isLoading: isReferrersLoading } =
    useReferrersQuery();
  const { data: statsData, isLoading: isStatsLoading } =
    useReferralStatsQuery();

  const { mutateAsync: confirmReferrer, isPending: isConfirmReferrerPending } =
    useConfirmReferrerMutation({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ["user", "referrers"],
        });
      },
    });

  const inviteLink =
    typeof window !== "undefined"
      ? window.location.origin + "/?ref=" + user?.twitterHandle
      : "";

  const earnings = useMemo(() => {
    if (!statsData) return "0.00";

    return formatEther(statsData.stats.referralsEarned.toString());
  }, [statsData]);

  const handleConfirmReferrer = async (answer: boolean) => {
    if (
      !referrersData ||
      (referrersData && referrersData.referrers.length === 0)
    )
      return;

    const referralId = referrersData.referrers[0].id;
    const referrerId = referrersData.referrers[0].referrerId;

    await confirmReferrer({
      referralId,
      referrerId,
      answer,
    });
  };

  useEffect(() => {
    setIsMounted(true);

    return () => {
      setIsMounted(false);
    };
  }, []);

  useEffect(() => {
    console.log("referrersData:", referrersData);
  }, [referrersData]);

  return (
    <>
      <div className="mt-6 flex gap-4">
        <div className="flex flex-1 flex-col gap-2 rounded-[10px] border border-gray-text px-4 py-3">
          <div className="text-xs text-gray-text">Referrals</div>
          <div className="text-2xl font-semibold text-off-white">
            {isStatsLoading && <Skeleton className="h-6 w-10" />}
            {!isStatsLoading && statsData?.stats.referrerCount}
          </div>
        </div>
        <div className="flex flex-1 flex-col gap-2 rounded-[10px] border border-gray-text px-4 py-3">
          <div className="text-xs text-gray-text">Earnings</div>
          <div className="flex items-center gap-2">
            <img
              src={AVAX.icon}
              className="size-5 rounded-full"
              alt="AVAX logo"
            />
            <span className="text-2xl font-semibold text-off-white">
              {isStatsLoading && <Skeleton className="h-6 w-10" />}
              {!isStatsLoading && earnings}
            </span>
          </div>
        </div>
      </div>
      <div className="mx-auto mt-6 size-[219px] rounded-[20px] bg-white p-4">
        {isMounted && (
          <QRCode
            size={256}
            value={inviteLink}
            viewBox={`0 0 256 256`}
            className="h-full w-full"
          />
        )}
      </div>
      <div className="mt-6 flex flex-col gap-2">
        <Label>Your Link</Label>
        <div className="flex items-center justify-between gap-4 rounded-lg bg-[#313131] px-4 py-3 text-sm text-[#656565]">
          <span className="min-w-0 flex-grow truncate">
            {isMounted && inviteLink}
          </span>
          <CopyToClipboard
            text={isMounted ? inviteLink : ""}
            onCopy={() => {
              toast.green("Link copied!");
            }}
          >
            <button className="flex-shrink-0">
              <CopyOutlineIcon className="size-5 text-[#9E9E9E]" />
            </button>
          </CopyToClipboard>
        </div>
      </div>
      {!isReferrersLoading &&
        referrersData &&
        referrersData.referrers.length > 0 &&
        referrersData.referrers[0].isYourReferrerAnswer !== false && (
          <>
            <div className="mt-6 flex flex-col gap-2">
              <Label>
                {referrersData.referrers[0].isYourReferrerAnswer == null
                  ? "Is your referrer correct?"
                  : "Your referrer"}
              </Label>
              <div className="flex items-center justify-between rounded-lg border border-gray-text px-4 py-3 text-sm text-gray-text">
                <span className="truncate">
                  @{referrersData.referrers[0]?.twitterHandle}
                </span>
                {referrersData.referrers[0].isYourReferrerAnswer === true && (
                  <CheckFilledIcon className="size-6 text-off-white" />
                )}
              </div>
            </div>
            {referrersData.referrers[0].isYourReferrerAnswer === null && (
              <div className="mt-8 flex w-full gap-2">
                <Button
                  className="flex-1"
                  disabled={isConfirmReferrerPending}
                  onClick={() => {
                    handleConfirmReferrer(true);
                  }}
                >
                  Yes
                </Button>
                <Button
                  variant="outline"
                  className="flex-1"
                  disabled={isConfirmReferrerPending}
                  onClick={() => {
                    handleConfirmReferrer(false);
                  }}
                >
                  No
                </Button>
              </div>
            )}
          </>
        )}
      {!isReferrersLoading &&
        ((referrersData &&
          (referrersData.referrers.length === 0 ||
            (referrersData.referrers[0] &&
              referrersData.referrers[0].isYourReferrerAnswer === false))) ||
          !referrersData) && (
          <div className="mt-6 flex flex-col gap-2">
            <Label>You do not have a referrer</Label>
            <div className="flex h-[52px] items-center justify-between rounded-lg border border-gray-text bg-gray-bg" />
          </div>
        )}
    </>
  );
};
