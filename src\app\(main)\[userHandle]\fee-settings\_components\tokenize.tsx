// "use client";
//
// import { useState } from "react";
//
// import { InformationCircleOutlineAltIcon } from "@/components/icons/information-circle-outline-alt";
// import { toast } from "@/components/toast";
// import { Button } from "@/components/ui/button";
//
// export const Tokenize = () => {
//   const [canTokenize, setCanTokenize] = useState(true);
//
//   const handleTokenize = () => {
//     toast.green("Not implemented!");
//     setCanTokenize(false);
//   };
//
//   return (
//     <div className="mt-4 flex flex-col gap-4">
//       <span className="">Tokenize your tickets</span>
//       <div className="flex flex-row justify-between gap-8">
//         <span className="text-xs text-gray-text">
//           Allow your tickets to be transferable and fractionalized.
//         </span>
//         <Button
//           className="px-8 py-2 text-xs/[19px] text-off-white"
//           disabled={!canTokenize}
//           onClick={handleTokenize}
//         >
//           {canTokenize ? "Enable" : "Enabled"}
//         </Button>
//       </div>
//       <div className="flex flex-col gap-2">
//         <span className="text-[11px]/[20px]">HOW DOES IT WORK?</span>
//         <p className="text-xs/[22px] text-gray-text">
//           Tokenizing your ticket will prompt The Arena to deploy an ERC-20
//           contract on your behalf, incurring a small gas fee.{" "}
//         </p>
//         <p className="text-xs/[22px] text-gray-text">
//           This action won&apos;t impact your current ticket prices, but will
//           make your tickets transferable and allow trading of fractions as small
//           as 1% of a ticket.
//         </p>
//         {canTokenize && (
//           <div className="flex flex-row items-center gap-[10px] rounded-[10px] bg-[#1a1a1a] p-[10px]">
//             <InformationCircleOutlineAltIcon />
//             <span className="text-xs/[22px] text-light-gray-text">
//               Tokenizing your tickets is a one time action.
//             </span>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };
