"use client";

import { useCallback, useState } from "react";

import {
  CameraDisabledIcon,
  CameraIcon,
  MicDisabledIcon,
  MicIcon,
  useMaybeRoomContext,
  useMediaDeviceSelect,
  usePersistentUserChoices,
  useTrackToggle,
} from "@livekit/components-react";
import { RoomEvent, Track } from "livekit-client";

import { cn } from "@/utils";
import { supportsScreenSharing } from "@/utils/supports-screen-sharing";

import { ScreenShareOutlineIcon } from "../icons";
import { Select, SelectContent, SelectItem, SelectTrigger } from "../ui/select";

export function MicButton() {
  // Toggle mic and save preferences
  const { saveAudioInputEnabled, saveAudioInputDeviceId } =
    usePersistentUserChoices();

  const {
    buttonProps: { className, onClick, ...buttonProps },
    enabled,
  } = useTrackToggle({
    initialState: true,
    source: Track.Source.Microphone,
    onChange: (enabled: boolean, isUserInitiated: boolean) =>
      microphoneOnChange(enabled, isUserInitiated),
    onDeviceError: (error) => {
      console.log({ source: Track.Source.Microphone, error });
    },
  });

  const room = useMaybeRoomContext();

  const microphoneOnChange = useCallback(
    (enabled: boolean, isUserInitiated: boolean) =>
      isUserInitiated ? saveAudioInputEnabled(enabled) : null,
    [saveAudioInputEnabled],
  );

  const handleError = useCallback(
    (e: Error) => {
      if (room) {
        // awkwardly emit the event from outside of the room, as we don't have other means to raise a MediaDeviceError
        room.emit(RoomEvent.MediaDevicesError, e);
      }
      console.log(e);
    },
    [room],
  );
  const { devices, activeDeviceId, setActiveMediaDevice } =
    useMediaDeviceSelect({
      kind: "audioinput",
      room,
      onError: handleError,
    });

  const handleActiveDeviceChange = async (deviceId: string) => {
    try {
      await setActiveMediaDevice(deviceId);
      saveAudioInputDeviceId(deviceId ?? "");
    } catch (e) {
      if (e instanceof Error) {
        console.log(e);
      } else {
        throw e;
      }
    }
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    onClick?.(event);
  };

  return (
    <div className="flex items-center">
      <button
        className={cn(
          "flex items-center gap-1 rounded px-4 py-2 text-sm font-semibold text-off-white ",
          enabled ? "bg-[#363636]" : "bg-red-600 hover:bg-red-600/90",
          devices.length > 0 && "rounded-r-none",
        )}
        {...buttonProps}
        onClick={handleClick}
      >
        {enabled ? (
          <MicIcon className="size-5 translate-y-px text-off-white" />
        ) : (
          <MicDisabledIcon
            className={cn("size-5 translate-y-px text-off-white")}
          />
        )}
        <span className="">Microphone</span>
      </button>
      {devices.length > 0 ? (
        <Select value={activeDeviceId} onValueChange={handleActiveDeviceChange}>
          <SelectTrigger className="size-9 justify-center rounded rounded-l-none border-none bg-[#2A2A2A] p-0 pr-1 hover:bg-[#363636]  focus:ring-0 focus:ring-offset-0"></SelectTrigger>
          <SelectContent position="popper">
            {devices.map((device) => (
              <SelectItem key={device.deviceId} value={device.deviceId}>
                {device.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : null}
    </div>
  );
}

export function CameraButton() {
  // Toggle mic and save preferences
  const { saveVideoInputEnabled, saveVideoInputDeviceId } =
    usePersistentUserChoices();

  const {
    buttonProps: { className, onClick, ...buttonProps },
    enabled,
    toggle,
  } = useTrackToggle({
    source: Track.Source.Camera,
    onChange: (enabled: boolean, isUserInitiated: boolean) =>
      cameraOnChange(enabled, isUserInitiated),
    onDeviceError: (error) => {
      console.log({ source: Track.Source.Camera, error });
    },
  });

  const room = useMaybeRoomContext();

  const cameraOnChange = useCallback(
    (enabled: boolean, isUserInitiated: boolean) =>
      isUserInitiated ? saveVideoInputEnabled(enabled) : null,
    [saveVideoInputEnabled],
  );

  const handleError = useCallback(
    (e: Error) => {
      if (room) {
        // awkwardly emit the event from outside of the room, as we don't have other means to raise a MediaDeviceError
        room.emit(RoomEvent.MediaDevicesError, e);
      }
      console.log(e);
    },
    [room],
  );
  const { devices, activeDeviceId, setActiveMediaDevice } =
    useMediaDeviceSelect({
      kind: "videoinput",
      room,
      onError: handleError,
    });

  const handleActiveDeviceChange = async (deviceId: string) => {
    try {
      await setActiveMediaDevice(deviceId);
      saveVideoInputDeviceId(deviceId ?? "");
    } catch (e) {
      if (e instanceof Error) {
        console.log(e);
      } else {
        throw e;
      }
    }
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    onClick?.(event);
  };

  return (
    <div className="flex items-center">
      <button
        className={cn(
          "flex items-center gap-1 rounded bg-[#2A2A2A] px-4 py-2 text-sm font-semibold text-off-white hover:bg-[#363636]",
          enabled && "bg-[#363636]",
          devices.length > 0 && "rounded-r-none",
        )}
        {...buttonProps}
        onClick={handleClick}
      >
        {enabled ? (
          <CameraIcon className="size-5 translate-y-px text-off-white" />
        ) : (
          <CameraDisabledIcon
            className={cn("size-5 translate-y-px text-off-white")}
          />
        )}
        <span className="">Camera</span>
      </button>
      {devices.length > 0 ? (
        <Select value={activeDeviceId} onValueChange={handleActiveDeviceChange}>
          <SelectTrigger className="size-9 justify-center rounded rounded-l-none border-none bg-[#2A2A2A] p-0 pr-1 hover:bg-[#363636]  focus:ring-0 focus:ring-offset-0"></SelectTrigger>
          <SelectContent position="popper">
            {devices.map((device) => (
              <SelectItem key={device.deviceId} value={device.deviceId}>
                {device.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : null}
    </div>
  );
}

export function ScreenShareButton() {
  const browserSupportsScreenSharing = supportsScreenSharing();
  const [isScreenShareEnabled, setIsScreenShareEnabled] = useState(false);

  const {
    buttonProps: { className, onClick, ...buttonProps },
    enabled,
    toggle,
  } = useTrackToggle({
    source: Track.Source.ScreenShare,
    captureOptions: {
      audio: true,
      systemAudio: "include",
    },
    onChange: (enabled: boolean) => onScreenShareChange(enabled),
    onDeviceError: (error) => {
      console.log({ source: Track.Source.ScreenShare, error });
    },
  });

  const onScreenShareChange = useCallback(
    (enabled: boolean) => {
      setIsScreenShareEnabled(enabled);
    },
    [setIsScreenShareEnabled],
  );
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    onClick?.(event);
  };

  if (!browserSupportsScreenSharing) {
    return null;
  }

  return (
    <button
      className={cn(
        "flex items-center gap-1 rounded bg-[#2A2A2A] px-4 py-2 text-sm font-semibold text-off-white hover:bg-[#363636]",
        enabled && "bg-[#363636]",
      )}
      {...buttonProps}
      onClick={handleClick}
    >
      <ScreenShareOutlineIcon className="size-5 text-off-white" />
      <span className="">
        {isScreenShareEnabled ? "Stop screen share" : "Share screen"}
      </span>
    </button>
  );
}
