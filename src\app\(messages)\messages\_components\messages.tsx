"use client";

import { FC, useEffect, useMemo, useRef, useState } from "react";
import { usePara<PERSON>, usePathname, useSearchParams } from "next/navigation";

import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { DirectMessageRequests } from "@/app/(messages)/messages/_components/direct-message-requests";
import { DirectMessages } from "@/app/(messages)/messages/_components/direct-messages";
import { GroupMessages } from "@/app/(messages)/messages/_components/groupMessages";
import { MessagesTabNavigation } from "@/app/(messages)/messages/_components/messages-tab-navigation";
import { Rooms } from "@/app/(messages)/messages/_components/rooms";
import { SettingsIcon } from "@/components/icons/settings-icon";
import { ProgressBarLink } from "@/components/progress-bar";
import { SOCKET_MESSAGE } from "@/environments/socket-messages";
import useThrottle from "@/hooks/use-throttle";
import {
  useConversationsInfiniteQuery,
  useDirectMessagesInfiniteQuery,
  useGroupByIdQuery,
  useGroupConversationsInfiniteQuery,
  usePinConversation,
} from "@/queries";
import { Group, GroupsResponse } from "@/queries/types/chats";
import { useUser } from "@/stores";
import { useSocket } from "@/stores/socket";

import { Request } from "./requests";
import { Search } from "./search";

interface Typing {
  groupId: string;
  user: {
    id: string;
    name: string;
  };
  date: number;
}

export type TypingsType = Record<string, Typing>;

export const Messages: FC = () => {
  const params = useParams() as { groupId?: string };
  const searchParams = useSearchParams();
  const pathName = usePathname();
  const twitterHandle = searchParams.get("twitterHandle");
  const queryClient = useQueryClient();
  const { user } = useUser();
  const { socket, setSeen } = useSocket();
  const [typings, setTypings] = useState<TypingsType>({});
  const [selectedGroups, setSelectedGroups] = useState<Group[]>([]);
  const [search, setSearch] = useState("");
  const savedSearch = useRef("");
  const throttledSearch = useThrottle(search);

  const [currentPage, setCurrentPage] = useState<
    "messages" | "request" | "receivedRequest"
  >("messages");
  const [currentTab, setCurrentTab] = useState<
    "rooms" | "groups" | "directmessages"
  >(
    () =>
      (localStorage.getItem("currentTab") as
        | "rooms"
        | "groups"
        | "directmessages") || "rooms",
  );
  const { data } = useGroupByIdQuery({
    groupId: params.groupId || "",
    twitterHandle: twitterHandle || "",
    isCommunity: pathName.includes("community"),
  });

  useEffect(() => {
    if (pathName.includes("community") || data?.group?.isBadge) {
      setCurrentTab("groups");
    } else {
      if (currentTab === "rooms" && data?.group?.isDirect) {
        setCurrentTab("directmessages");
        if (currentPage !== "request" && data?.group?.isTemporary) {
          setCurrentPage("request");
        }
      }
    }
  }, [pathName]);

  useEffect(() => {
    localStorage.setItem("currentTab", currentTab);
  }, [currentTab]);

  const {
    data: roomsData,
    isLoading: roomsIsLoading,
    isFetchingNextPage: roomsIsFetchingNextPage,
    fetchNextPage: fetchNextRoomsPage,
  } = useConversationsInfiniteQuery();

  const {
    data: directMessagesData,
    isLoading: directMessagesIsLoading,
    isFetchingNextPage: directMessagesIsFetchingNextPage,
    fetchNextPage: fetchNextDirectMessagesPage,
  } = useDirectMessagesInfiniteQuery();

  const {
    data: groupsData,
    isLoading: groupsLoading,
    isFetchingNextPage: groupsIsFetchingNextPage,
    fetchNextPage: fetchNextGroupsPage,
  } = useGroupConversationsInfiniteQuery();

  const { mutateAsync: pinConversation } = usePinConversation({
    onMutate: async ({ groupId, isPinned }) => {
      const previousData = queryClient.getQueryData(["chat", "conversations"]);
      const previousSearchData = queryClient.getQueryData([
        "chat",
        "search-room-conversations",
        savedSearch.current,
      ]);

      queryClient.setQueryData(
        ["chat", "search-room-conversations", savedSearch.current],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === groupId) {
                    return {
                      ...g,
                      memberLink: {
                        ...g.memberLink,
                        isPinned,
                      },
                    };
                  }
                  return g;
                }),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["chat", "conversations"],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === groupId) {
                    return {
                      ...g,
                      memberLink: {
                        ...g.memberLink,
                        isPinned,
                      },
                    };
                  }
                  return g;
                }),
              };
            }),
          };
        },
      );
      return { previousData, previousSearchData };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(["chat", "conversations"], context.previousData);
      queryClient.setQueryData(
        ["chat", "search-room-conversations", savedSearch.current],
        context.previousSearchData,
      );
    },
  });

  const { mutateAsync: pinConversationForDMs } = usePinConversation({
    onMutate: async ({ groupId, isPinned }) => {
      const previousData = queryClient.getQueryData([
        "chat",
        "direct-messages",
      ]);
      const previousSearchData = queryClient.getQueryData([
        "chat",
        "search-dm-conversations",
        savedSearch.current,
      ]);

      queryClient.setQueryData(
        ["chat", "search-dm-conversations", savedSearch.current],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === groupId) {
                    return {
                      ...g,
                      memberLink: {
                        ...g.memberLink,
                        isPinned,
                      },
                    };
                  }
                  return g;
                }),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["chat", "direct-messages"],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === groupId) {
                    return {
                      ...g,
                      memberLink: {
                        ...g.memberLink,
                        isPinned,
                      },
                    };
                  }
                  return g;
                }),
              };
            }),
          };
        },
      );
      return { previousData, previousSearchData };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(
        ["chat", "direct-messages"],
        context.previousData,
      );
      queryClient.setQueryData(
        ["chat", "search-dm-conversations", savedSearch.current],
        context.previousSearchData,
      );
    },
  });

  const { mutateAsync: pinProjectConversation } = usePinConversation({
    onMutate: async ({ groupId, isPinned }) => {
      const previousData = queryClient.getQueryData([
        "chat",
        "group-conversations",
      ]);
      const previousSearchData = queryClient.getQueryData([
        "chat",
        "search-project-chats",
        savedSearch.current,
      ]);

      queryClient.setQueryData(
        ["chat", "search-project-chats", savedSearch.current],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === groupId) {
                    return {
                      ...g,
                      memberLink: {
                        ...g.memberLink,
                        isPinned,
                      },
                    };
                  }

                  return g;
                }),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["chat", "group-conversations"],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === groupId) {
                    return {
                      ...g,
                      memberLink: {
                        ...g.memberLink,
                        isPinned,
                      },
                    };
                  }
                  return g;
                }),
              };
            }),
          };
        },
      );
      return { previousData, previousSearchData };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(
        ["chat", "group-conversations"],
        context.previousData,
      );
      queryClient.setQueryData(
        ["chat", "search-project-chats", savedSearch.current],
        context.previousSearchData,
      );
    },
  });

  const rooms = useMemo(() => {
    return roomsData?.pages.map((page) => page.groups).flat();
  }, [roomsData]);

  const directMessages = useMemo(() => {
    return directMessagesData?.pages.map((page) => page.groups).flat();
  }, [directMessagesData]);

  const groups = useMemo(() => {
    return groupsData?.pages.map((page) => page.groups).flat();
  }, [groupsData]);

  useEffect(() => {
    socket?.on(SOCKET_MESSAGE.CHAT_MESSAGE_GROUP, (message) => {
      if (!message.group) return;
      if (message.group?.isDirect) {
        queryClient.setQueryData(
          ["chat", "direct-messages"],
          (oldData: InfiniteData<GroupsResponse>) => {
            if (!oldData) return;

            return {
              ...oldData,
              pages: oldData.pages.map((page) => {
                return {
                  ...page,
                  groups: page.groups.map((g) => {
                    if (g.id === message.group?.id) {
                      return {
                        ...g,
                        ...message.group,
                      };
                    }

                    return g;
                  }),
                };
              }),
            };
          },
        );
      }

      queryClient.setQueryData(
        ["chat", "conversations"],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === message.group?.id) {
                    return {
                      ...g,
                      ...message.group,
                    };
                  }

                  return g;
                }),
              };
            }),
          };
        },
      );
    });
  }, [socket, rooms, queryClient, user?.id]);

  useEffect(() => {
    socket?.on(SOCKET_MESSAGE.CHAT_MESSAGE_GROUP, (message) => {
      if (!message.group) return;
      if (!message.group?.isDirect) return;

      queryClient.setQueryData(
        ["chat", "direct-messages"],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === message.group?.id) {
                    return {
                      ...g,
                      ...message.group,
                    };
                  }

                  return g;
                }),
              };
            }),
          };
        },
      );
    });
  }, [socket, directMessages, queryClient, user?.id]);

  useEffect(() => {
    socket?.on(SOCKET_MESSAGE.LEAVE_GROUP, (message) => {
      if (!message.group) return;
      if (!message.group?.isDirect) return;

      queryClient.setQueryData(
        ["chat", "direct-messages"],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.filter((g) => g.id !== message.group.id),
              };
            }),
          };
        },
      );
    });

    socket?.on(SOCKET_MESSAGE.ACCEPT_GROUP, (message) => {
      if (!message.group) return;
      if (!message.group?.isDirect) return;

      queryClient.setQueryData(
        ["chat", "direct-messages"],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === message.group.id) {
                    return {
                      ...g,
                      isRequest: false,
                    };
                  }
                  return g;
                }),
              };
            }),
          };
        },
      );
    });
  }, [socket, rooms, directMessages, queryClient, user?.id]);

  useEffect(() => {
    socket?.on(SOCKET_MESSAGE.CHAT_TYPING, (data: { typing: Typing }) => {
      if (data.typing) {
        if (user?.id === data.typing.user.id) return;
        setTypings((prev) => {
          return {
            ...prev,
            [data.typing.groupId]: { ...data.typing, date: Date.now() },
          };
        });
      }
    });
  }, [socket, rooms, directMessages, queryClient, user?.id]);

  useEffect(() => {
    const interval = setInterval(() => {
      setTypings((prev) => {
        const now = Date.now();
        let hasChanged = false;
        const newTypings = { ...prev };
        for (const groupId in prev) {
          if (now - prev[groupId].date > 800) {
            delete newTypings[groupId];
            hasChanged = true;
          }
        }

        return hasChanged ? newTypings : prev;
      });
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  const directMessagesProps = {
    setSeen,
    setSelectedGroups,
    selectedGroups,
    typings,
    params,
    groups: directMessages,
    id: user?.id,
    fetchNextDirectMessagesPage: fetchNextDirectMessagesPage,
    isLoading: directMessagesIsLoading,
    isFetchingNextPage: directMessagesIsFetchingNextPage,
    setCurrentPage: setCurrentPage,
    throttledSearch,
    pinConversationForDMs,
  };

  let directMessagesNotification = 0;
  directMessages?.forEach((group) => {
    const isLastMessageMine = group?.lastUserId === user?.id;
    if (group?.isRequest && !isLastMessageMine) {
      directMessagesNotification += 1;
    } else {
      const lastMessageDate = group?.lastMessageOn
        ? new Date(+group?.lastMessageOn)
        : null;
      const lastSeenDate = group?.memberLink?.lastSeen
        ? new Date(+group?.memberLink?.lastSeen)
        : null;

      const isNotSeen = isLastMessageMine
        ? false
        : lastMessageDate && lastSeenDate
          ? lastMessageDate > lastSeenDate
          : false;

      if (isNotSeen) {
        directMessagesNotification += 1;
      }
    }
  });

  const roomsProps = {
    throttledSearch,
    isLoading: roomsIsLoading,
    setSeen,
    setSelectedGroups,
    selectedGroups,
    typings,
    params,
    isFetchingNextPage: roomsIsFetchingNextPage,
    fetchNextRoomsPage,
    groups: rooms,
    pinConversation,
  };

  const groupMessagesProps = {
    groups: groups,
    isLoading: groupsLoading,
    typings,
    params,
    isFetchingNextPage: groupsIsFetchingNextPage,
    fetchNextGroupsPage,
    pinProjectConversation,
    throttledSearch,
  };

  const tab =
    currentTab === "rooms" ? (
      <Rooms {...roomsProps} />
    ) : currentTab === "groups" ? (
      <GroupMessages {...groupMessagesProps} />
    ) : (
      <DirectMessages {...directMessagesProps} />
    );

  return (
    <>
      {currentPage === "request" ? (
        <Request setCurrentPage={setCurrentPage} />
      ) : currentPage === "receivedRequest" ? (
        <DirectMessageRequests {...directMessagesProps} />
      ) : (
        <>
          <div className="mt-[18px] flex items-center justify-between px-6">
            <h1 className="text-[26px] font-semibold leading-[30px] text-white">
              Messages
            </h1>
            <div className="flex items-center">
              <ProgressBarLink
                href="/messages/settings"
                className="flex size-10 items-center justify-center"
              >
                <SettingsIcon className="size-6" />
              </ProgressBarLink>
            </div>
          </div>
          <Search
            placeholder="Search Messages"
            search={search}
            inputOnChange={(e) => {
              setSearch(e.target.value);
              savedSearch.current = e.target.value;
            }}
            closeOnClick={() => {
              setSearch("");
              savedSearch.current = "";
            }}
          />
          <MessagesTabNavigation
            currentTab={currentTab}
            setCurrentTab={setCurrentTab}
          />
          {tab}
        </>
      )}
    </>
  );
};
