"use client";

import { useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useUnbanUserMutation } from "@/queries/user-mutations";
import { User } from "@/types";

interface UnbanUserModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: User;
}

export const UnbanUserModal = ({
  open,
  setOpen,
  user,
}: UnbanUserModalProps) => {
  const queryClient = useQueryClient();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const { mutateAsync: unbanUser, isPending } = useUnbanUserMutation({
    onMutate: async () => {
      toast.red(`${user.twitterName} is unbanned!`);

      await queryClient.cancelQueries({
        queryKey: ["user", "isBanned", user.id],
      });

      const previousIsBanned = queryClient.getQueryData([
        "user",
        "isBanned",
        user.id,
      ]);

      queryClient.setQueryData(["user", "isBanned", user.id], false);

      return { previousIsBanned };
    },
    onError: (err, variables, context) => {
      toast.red(`Failed to unban ${user.twitterName}.`);
      queryClient.setQueryData(
        ["user", "isBanned", user.id],
        context?.previousIsBanned,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["threads", "user", user.id],
      });
    },
  });

  const handleUnban = async () => {
    setOpen(false);
    await unbanUser({ userId: user.id });
  };

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-sm">
          <UnbanUserModalContent
            user={user}
            setOpen={setOpen}
            isPending={isPending}
            handleUnban={handleUnban}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="justify-start gap-6 text-left">
        <UnbanUserModalContent
          user={user}
          setOpen={setOpen}
          isPending={isPending}
          handleUnban={handleUnban}
        />
      </DrawerContent>
    </Drawer>
  );
};

interface UnbanUserModalContentProps {
  user: User;
  setOpen: (open: boolean) => void;
  isPending: boolean;
  handleUnban: () => void;
}

const UnbanUserModalContent = ({
  user,
  setOpen,
  isPending,
  handleUnban,
}: UnbanUserModalContentProps) => {
  return (
    <>
      <DialogHeader className="space-y-4">
        <DialogTitle>Unban @{user.twitterHandle}?</DialogTitle>
        <DialogDescription className="text-gray-text">
          Are you sure you want to proceed?
        </DialogDescription>
      </DialogHeader>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => {
            setOpen(false);
          }}
        >
          Cancel
        </Button>
        <Button
          variant="destructive"
          className="flex-1"
          onClick={handleUnban}
          disabled={isPending}
        >
          Unban
        </Button>
      </div>
    </>
  );
};
