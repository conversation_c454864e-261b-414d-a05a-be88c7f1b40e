interface MediaFormatType {
  url: string;
  duration: number;
  preview: string;
  dims: number[];
  size: number;
}

export interface GIFType {
  id: string;
  title: string;
  media_formats: {
    nanogifpreview: MediaFormatType;
    nanowebm: MediaFormatType;
    gifpreview: MediaFormatType;
    webppreview_transparent: MediaFormatType;
    mediumgif: MediaFormatType;
    tinygifpreview: MediaFormatType;
    nanowebp_transparent: MediaFormatType;
    tinywebppreview_transparent: MediaFormatType;
    tinygif: MediaFormatType;
    nanowebppreview_transparent: MediaFormatType;
    gif: MediaFormatType;
    tinywebm: MediaFormatType;
    mp4: MediaFormatType;
    webp_transparent: MediaFormatType;
    webm: MediaFormatType;
    tinymp4: MediaFormatType;
    nanogif: MediaFormatType;
    nanomp4: MediaFormatType;
    tinywebp_transparent: MediaFormatType;
    loopedmp4: MediaFormatType;
  };
  created: number;
  content_description: string;
  itemurl: string;
  url: string;
  tags: string[];
  flags: string[];
  hasaudio: boolean;
}

export interface ThreadsGIFResponse {
  results: GIFType[];
}
