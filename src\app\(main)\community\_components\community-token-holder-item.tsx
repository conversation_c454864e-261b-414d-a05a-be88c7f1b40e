"use client";

import { Address, formatEther } from "viem";

import { StarOutlineIcon } from "@/components/icons-v2/star-outline";
import { LockOutlineIcon } from "@/components/icons/lock-outline";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { GroupTokenHolder } from "@/types/community";
import { formatAddress } from "@/utils/format-token-price";

import { MAX_GROUP_SUPPLY } from "./consts";

const formatNumber = (num: number): number => {
  let digit = 1;
  if (num < 1) digit = 4;

  const factor = 10 ** digit;
  return Math.round(num * factor) / factor;
};

interface TokenHolderItemProps {
  holder: GroupTokenHolder;
  ownerAddress: string | null;
  usdPrice: number;
  lpPairAddress: string;
  feeWalletAddress: Address;
}

export const TokenHolderItem: React.FC<TokenHolderItemProps> = ({
  holder,
  ownerAddress,
  usdPrice,
  lp<PERSON><PERSON><PERSON>dd<PERSON>,
  feeWalletAddress,
}) => {
  const usdBalanceEquivalent =
    ((Number(formatEther(BigInt(MAX_GROUP_SUPPLY))) * holder.tokenRatio) /
      100) *
    usdPrice;

  const itemContent = (
    <div className="flex w-full justify-between gap-4 px-6 py-4">
      <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
        <Avatar className="size-[42px]">
          <AvatarImage
            src={
              holder.user
                ? holder.user.twitterPicture
                : `https://effigy.im/a/${holder.address}.png`
            }
          />
          <AvatarFallback />
        </Avatar>
        <div className="flex w-full min-w-0 flex-col gap-1 text-sm leading-4">
          <div className="flex items-center gap-1.5">
            <h4 className="truncate text-[#F4F4F4]">
              {holder.user
                ? holder.user.twitterName
                : formatAddress(holder.address, 6, 4, 3)}
            </h4>
            {ownerAddress && holder.user?.address === ownerAddress && (
              <>
                <StarOutlineIcon />
                <h4 className="text-brand-orange">Creator</h4>
              </>
            )}
            {!holder.user &&
              (holder.address === lpPairAddress ||
                holder.address === feeWalletAddress) && (
                <>
                  <LockOutlineIcon className="size-4 text-brand-orange" />
                </>
              )}
          </div>
          <div className="flex justify-start truncate text-[#808080]">
            {holder.user
              ? `@${holder.user?.twitterHandle}`
              : holder.address === lpPairAddress
                ? "Liquidity Pool"
                : holder.address === feeWalletAddress
                  ? "Champions Rewards Wallet"
                  : "External Account"}
          </div>
        </div>
      </div>
      <div className="flex flex-shrink-0 flex-col items-end justify-center gap-[2px]">
        <div className="flex items-center gap-[6px]">
          <span className="text-sm font-medium text-[#F4F4F4]">
            {holder.tokenRatio < 0.001
              ? "< 0.001%"
              : `${formatNumber(holder.tokenRatio)}%`}
          </span>
        </div>
        <span className="flex items-center gap-[2px] text-sm text-[#808080]">
          <span>
            {usdBalanceEquivalent < 0.01
              ? "< $0.01"
              : `$${formatNumber(usdBalanceEquivalent)}`}
          </span>
        </span>
      </div>
    </div>
  );

  return holder.user ? (
    <ProgressBarLink href={`/${holder.user.twitterHandle}`}>
      {itemContent}
    </ProgressBarLink>
  ) : (
    <a
      href={`https://snowtrace.io/address/${holder.address}`}
      target="_blank"
      rel="noopener noreferrer"
    >
      {itemContent}
    </a>
  );
};
