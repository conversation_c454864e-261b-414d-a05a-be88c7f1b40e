import React from "react";

import { http, HttpResponse } from "msw";
import { Toaster } from "sonner";

import { mockCurrencies } from "@/components/tipping/tip-currency-selector.stories";
import { DynamicProvider } from "@/mocks/dynamic-context-mock";

import { TipModal } from "./tip-modal";

export default {
  title: "Modals/TipModal (by user profile)",
  component: TipModal,
  decorators: [
    (Story: any) => (
      <DynamicProvider>
        <Story />
        <Toaster position="top-center" />
      </DynamicProvider>
    ),
  ],
  parameters: {
    msw: {
      handlers: [
        http.get("*/user/handle", ({ request }) => {
          const url = new URL(request.url);
          const handle = url.searchParams.get("handle");
          return HttpResponse.json({
            user: {
              id: "1",
              twitterName: "Jason Desimone ⚔️",
              twitterHandle: handle || "demo-user",
              twitterPicture:
                "https://static.starsarena.com/uploads/b4bebd39-ec9a-c996-2191-00334d0f30361745662820400.png",
              address: "0x123",
            },
          });
        }),
        http.get("/currency/tippable", () => {
          return HttpResponse.json({ currencies: mockCurrencies });
        }),
      ],
    },
  },
};

export const Default = () => {
  return <TipModal userHandle="demo-user" />;
};
