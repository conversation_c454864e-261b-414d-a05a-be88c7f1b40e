import { parseISO } from "date-fns";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useIsBlockedByUserQuery, useIsUserBlockedQuery } from "@/queries";
import { useFeatureFlagsStore } from "@/stores/flags";
import { Thread, UserFlaggedEnum } from "@/types";
import { checkContent, cn, formatTimeDistance } from "@/utils";

import { GroupIcon, OfficialGroupIcon } from "./icons-v2/group-logo";
import { ImagePreviewModal } from "./image-preview-modal";
import { PostLivestreamInfo } from "./livestream/post-livestream-info";
import { ProgressBarLink } from "./progress-bar";
import { PostStageInfo } from "./stages/post-stage-info";
import { Video } from "./video";

export const QuotePost = ({
  userId,
  userName,
  userHandle,
  createdDate,
  content,
  images,
  videos,
  user,
  threadType,
  stage,
  livestream,
  isDeleted,
  communityId,
  community,
}: Thread) => {
  const date = parseISO(createdDate);
  const isCommunityPost = !!(communityId && community);
  const [finalContent, isTruncated, youtubeEmbedUrl] = checkContent({
    content,
    characterLimit: 205,
  });
  const showStages = useFeatureFlagsStore((state) => state.stages);

  const { data: isUserBlocked, isLoading: isUserBlockedLoading } =
    useIsUserBlockedQuery(userId);
  const { data: isBlockedByUser, isLoading: isBlockedByUserLoading } =
    useIsBlockedByUserQuery(userId);
  const isSuspended = user?.flag === UserFlaggedEnum.SUSPENDED;

  const isStage = threadType === "stage";
  const isLivestream = threadType === "livestream";

  return (
    <div className="w-full rounded-lg border border-dark-gray bg-dark-bk p-3 transition-colors hover:bg-dark-gray/[0.08]">
      {isDeleted && (
        <div className="flex flex-col text-sm">
          <div className="text-gray-text">
            Post unavailable. This post has voilated The Arena&apos;s{" "}
            <ProgressBarLink
              href="/terms-of-use"
              className="font-semibold text-white"
            >
              terms of use.
            </ProgressBarLink>
          </div>
        </div>
      )}
      {isSuspended && !isDeleted && (
        <div className="flex flex-col text-sm">
          <div className="text-gray-text">
            Post unavailable. The owner was suspended for violating{" "}
            <ProgressBarLink
              href="/terms-of-use"
              className="font-semibold text-white"
            >
              terms of use.
            </ProgressBarLink>
          </div>
        </div>
      )}

      {isUserBlocked && !isSuspended && !isDeleted && !isUserBlockedLoading && (
        <div className="flex flex-col text-sm">
          <div
            dangerouslySetInnerHTML={{
              __html: "This post is from a blocked user account",
            }}
            className="overflow-visible text-gray-text"
          />
        </div>
      )}

      {isBlockedByUser &&
        !isSuspended &&
        !isDeleted &&
        !isBlockedByUserLoading && (
          <div className="flex flex-col text-sm">
            <div
              dangerouslySetInnerHTML={{
                __html:
                  "You&apos;re unable to view this post because this account owner limits who can view their posts",
              }}
              className="post-content select-none text-gray-text"
            />
          </div>
        )}

      {!(
        (isUserBlocked && !isUserBlockedLoading) ||
        (isBlockedByUser && !isBlockedByUserLoading) ||
        isSuspended ||
        isDeleted
      ) && (
        <div className="flex w-full flex-col gap-2">
          {isCommunityPost && (
            <div className=" flex items-center justify-end gap-1">
              {community?.isOfficial ? <OfficialGroupIcon /> : <GroupIcon />}
              <span className="text-sm font-semibold text-[#f3f3f3]">
                ${community.ticker}
              </span>
            </div>
          )}
          <div
            className="flex items-center text-sm leading-4 text-gray-text"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <ProgressBarLink href={`/${userHandle}`}>
              <Avatar className="mr-2 size-[17px] flex-shrink-0">
                <AvatarImage src={user?.twitterPicture} />
                <AvatarFallback />
              </Avatar>
            </ProgressBarLink>
            <span
              className={cn(
                "truncate font-semibold text-off-white",
                userName.length > 15 && "truncate",
              )}
            >
              {userName}
            </span>
            <span className="truncate">・@{userHandle}</span>
            <span className="flex-shrink-0">・{formatTimeDistance(date)}</span>
          </div>

          {isStage && stage != null && showStages ? (
            <PostStageInfo stage={stage} host={user} />
          ) : isLivestream && livestream != null ? (
            <PostLivestreamInfo
              livestream={livestream}
              host={{
                ...user,
                twitterName: user?.twitterName ?? userName,
              }}
            />
          ) : (
            <div className="flex flex-col text-sm">
              <div
                dangerouslySetInnerHTML={{ __html: finalContent }}
                className="post-content select-none text-off-white"
              />
              {isTruncated && (
                <div className="text-brand-orange">Show more</div>
              )}
            </div>
          )}

          {images &&
            images.length > 0 &&
            images.map((image) => (
              <div
                key={image.id}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <ImagePreviewModal url={image.url}>
                  <img
                    src={image.url}
                    alt=""
                    className="max-h-[510px] w-full rounded object-cover"
                  />
                </ImagePreviewModal>
              </div>
            ))}
          {videos &&
            videos.length > 0 &&
            videos.map((video) => (
              <Video
                src={video.url}
                key={video.id}
                className="max-h-[500px] rounded-2xl"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              />
            ))}
          {youtubeEmbedUrl && (
            <iframe
              src={youtubeEmbedUrl}
              title="YouTube video player"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              referrerPolicy="strict-origin-when-cross-origin"
              allowFullScreen
              className="aspect-video w-full rounded"
            />
          )}
        </div>
      )}
    </div>
  );
};
