"use client";

import { useState } from "react";

import { MailIcon } from "@/components/icons-v2/account-security";
import { Button } from "@/components/ui/button";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

interface IRegisterEmailModalProps {
  handleRegister: (email: string) => void;
  handleClose: () => void;
}

export const RegisterEmailModal = (props: IRegisterEmailModalProps) => {
  const [email, setEmail] = useState<string>("");
  const [isEmailValid, setIsEmailValid] = useState<boolean>(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const verifyAndSetEmail = (email: string) => {
    if (isValidEmail(email)) {
      setIsEmailValid(true);
      setEmail(email);
    } else {
      setIsEmailValid(false);
      setEmail("");
    }
  };

  const handleRegister = () => {
    if (isEmailValid) {
      props.handleRegister(email);
    }
  };

  return (
    <div
      className={
        isTablet
          ? "fixed inset-0 z-40 flex content-center items-center justify-center"
          : ""
      }
    >
      <div
        className="bg-black fixed inset-0 z-30 bg-[#020202CC]"
        onClick={props.handleClose}
      />
      <div
        className={`fixed z-50 px-[24px] pb-[48px] pt-[24px] ${isTablet ? "rounded-[20px]" : "bottom-[0px] left-0 right-0 rounded-t-[20px]"}`}
        style={{ backgroundColor: "rgba(15, 15, 15, 0.90)" }}
      >
        <div
          className={`inline-flex w-full flex-col items-start gap-[32px] ${isTablet ? "mx-auto max-w-[402px]" : ""}`}
        >
          <div className="font-inter flex flex-col items-center gap-[8px] self-stretch text-[16px] font-semibold leading-[22px] text-[#F4F4F4]">
            <MailIcon />
            <p>Stay Protected!</p>
          </div>
          <div className="flex flex-col items-start gap-[24px] self-stretch">
            <div className="font-inter text-[14px] font-normal leading-normal text-[var(--LIGHT-GRAY-TEXT,#B5B5B5)]">
              Please add an email for easy account recovery in case you lose
              access to your X account.
            </div>
            <input
              type="email"
              className="flex h-[44px] items-center gap-[8.397px] self-stretch rounded-[10px] border border-[var(--GRAY-TEXT,#808080)] bg-[var(--LIGHT-BACKGROUND,#1A1A1A)] px-[24px] py-[10px] text-[#F4F4F4] opacity-85"
              placeholder="<EMAIL>"
              style={{
                color: "#F4F4F4",
                textAlign: "left",
                fontSize: "14px",
                fontStyle: "normal",
                fontWeight: "400",
                lineHeight: "16.794px",
              }}
              onChange={(e) => verifyAndSetEmail(e.target.value)}
            />
          </div>
          <div className="flex w-full items-center gap-[8px]">
            <Button
              variant="outline"
              className="flex-grow basis-[34%] sm:w-[120px]"
              onClick={props.handleClose}
            >
              Close
            </Button>
            <Button
              className="flex-grow basis-[66%] sm:w-[234px]"
              onClick={handleRegister}
              disabled={!isEmailValid}
            >
              Send Confirmation
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
