import { Role } from "@/components/livestream/constants";

export interface CreateLivestreamParams {
  name: string;
  thumbnailUrl: string;
  type: "EASY" | "PRO";
  // record: boolean;
  privacyType: number;
  badgeTypes?: number[];
  scheduledStartTime?: string;
}

export interface LivestreamBadgeType {
  id: number;
  createdOn: string;
  livestreamId: string;
  badgeType: number;
}

export interface Livestream {
  id: string;
  createdOn: string;
  endedOn: string | null;
  name: string;
  thumbnailUrl: string;
  type: "EASY" | "PRO";
  hostId: string;
  isActive: boolean;
  isSuspended: boolean;
  threadId: string;
  isRecorded: boolean;
  isRecordingComplete: boolean;
  recordingUrl: string | null;
  privacyType: number;
  scheduledStartTime?: string;
  startedOn?: string;
  badgeTypes?: LivestreamBadgeType[];
}

export interface CreateLivestreamResponse {
  livestream: Livestream;
  token?: string;
}

export interface EndLivestreamParams {
  livestreamId: string;
}

export interface JoinLivestreamParams {
  twitterHandle: string;
}

export interface JoinLivestreamResponse {
  id: string;
  token: string;
}
export interface LivestreamOkResponse {
  success: boolean;
}

export interface LiveLivestreamsResponse {
  livestreams: Livestream[];
}

export interface LivestreamIdParams {
  livestreamId: string;
}

export interface LivestreamUserSimple {
  id: string;
  role: Role;
  user: {
    twitterPicture: string;
    twitterName: string;
    twitterHandle: string;
  };
}

export interface LivestreamSimpleInfoResponse {
  livestream: Livestream;
  live: LivestreamUserSimple[];
  host: LivestreamUserSimple;
  listenersCount: number;
  tunedInCount: number;
  canJoin: boolean;
  hasSetReminder: boolean;
}

export interface LivestreamIngressResponse {
  server: string;
  streamKey: string;
}

export interface EditLivestreamParams {
  livestreamId: string;
  name: string;
  thumbnailUrl: string;
  type: "EASY" | "PRO";
  privacyType: number;
  badgeTypes?: number[];
  scheduledStartTime?: string;
}

export interface EditLivestreamResponse {
  livestream: Livestream;
}

export interface PostPinPostParams {
  livestreamId: string;
  postId: string;
}

export interface DeletePinPostParams {
  livestreamId: string;
  postId: string;
}

export interface PostBlockUserParams {
  livestreamId: string;
  userId: string;
}

export interface BlockedViewersResponse {
  blockedViewers: LivestreamUserSimple[];
}

export type LiveParticipant = {
  id: string;
  name: string;
  avatar: string;
  username: string;
  role: Role;
};

export interface TippingStatsResponse {
  biggestTip: {
    id: string;
    amount: number;
    createdOn: Date;
    tipper: {
      id: string;
      twitterHandle: string;
      twitterName: string;
      twitterPicture: string;
    };
  } | null;
  totalTips: number;
  biggestTipEver: {
    id: string;
    amount: number;
    createdOn: Date;
    tipper: {
      id: string;
      twitterHandle: string;
      twitterName: string;
      twitterPicture: string;
    };
  } | null;
  totalTipsEver: number;
}

export interface TopTipper {
  user: {
    id: string;
    twitterHandle: string;
    twitterName: string;
    twitterPicture: string;
  };
  totalTipped: number;
  lastTip: {
    id: string;
    amount: number;
    createdOn: string;
  };
}

export interface TopTippersResponse {
  tippers: TopTipper[];
}

export interface TippingInfo {
  stats: {
    biggestTip: {
      id: string;
      amount: number;
      createdOn: Date;
      tipper: {
        id: string;
        twitterHandle: string;
        twitterName: string;
        twitterPicture: string;
      };
    } | null;
    totalTips: number;
    biggestTipEver: {
      id: string;
      amount: number;
      createdOn: Date;
      tipper: {
        id: string;
        twitterHandle: string;
        twitterName: string;
        twitterPicture: string;
      };
    } | null;
    totalTipsEver: number;
  };
  topStreamTippers: TopTipper[];
  topCreatorTippers: TopTipper[];
}

export interface GenerateLivestreamIngressParams {
  livestreamId?: string;
}
