import { User } from "@/types";

interface Trade {
  id: string;
  traderTwitterHandle: string;
  traderTwitterPicture: string;
  traderTwitterName: string;
  subjectTwitterHandle: string;
  subjectTwitterPicture: string;
  subjectTwitterName: string;
  createdOn: string;
  shareAmount: string;
  amount: number;
  isBuy: boolean;
  isToken: boolean;
}

export interface TradeTradesResponse {
  trades: Trade[];
}
