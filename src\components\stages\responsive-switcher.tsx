"use client";

import { memo } from "react";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

import { StageMobile } from "./stage-mobile";
import { StagePlayerDesktop } from "./stage-player-desktop";
import { StagePlayerMobile } from "./stage-player-mobile";
import { StageDesktop } from "./stages-desktop";

export const ResponsiveSwitcher = memo(() => {
  const isLaptop = useMediaQuery(BREAKPOINTS.lg);

  return (
    <>
      {isLaptop && <StageDesktop />}
      {!isLaptop && <StageMobile />}
    </>
  );
});

ResponsiveSwitcher.displayName = "ResponsiveSwitcher";

export const PlayerResponsiveSwitcher = () => {
  const isLaptop = useMediaQuery(BREAKPOINTS.lg);

  return (
    <>
      {isLaptop && <StagePlayerDesktop />}
      {!isLaptop && <StagePlayerMobile />}
    </>
  );
};
