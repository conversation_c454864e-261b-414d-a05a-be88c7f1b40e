import { axios } from "@/lib/axios";

export const getProfileAddresses = async () => {
  const response = await axios.get("/profile/addresses");
  return response.data;
};

export const postUpdateBio = async ({ bio }: { bio: string }) => {
  const response = await axios.post("/profile/bio", { bio });
  return response.data;
};

export const postUpdateBanner = async ({
  bannerUrl,
}: {
  bannerUrl: string;
}) => {
  const response = await axios.post("/profile/banner", { bannerUrl });
  return response.data;
};

export const postMigrateDynamic = async () => {
  const response = await axios.post("/profile/migrateDynamic", {});
  return response.data;
};
