"use client";

import { useEffect } from "react";

import { useStageRecordingPlayerStore } from "@/stores/stage-recording-player";

import { PlayerResponsiveSwitcher } from "./responsive-switcher";

export const StagePlayerContainer = () => {
  const url = useStageRecordingPlayerStore((state) => state.url);

  if (url) {
    return <StagePlayer />;
  }

  return null;
};

const StagePlayer = () => {
  const recordingURL = useStageRecordingPlayerStore((state) => state.url!);
  const audioRef = useStageRecordingPlayerStore((state) => state.audioRef);
  const actions = useStageRecordingPlayerStore((state) => state.actions);

  useEffect(() => {
    const audio = new Audio(recordingURL);
    audioRef.current = audio;

    const setAudioData = () => {
      actions.setDuration(audio.duration);
      actions.setCurrentTime(audio.currentTime);
      actions.setIsLoaded(true);
      actions.setIsPlaying(true);
      audio.play();
    };

    const setAudioTime = () => {
      const newProgress = (audio.currentTime / audio.duration) * 100;
      actions.setProgress(newProgress);
      actions.setCurrentTime(audio.currentTime);
    };

    audio.addEventListener("loadedmetadata", setAudioData);
    audio.addEventListener("timeupdate", setAudioTime);
    audio.addEventListener("ended", () => actions.setIsPlaying(false));
    audio.addEventListener("play", () => actions.setIsPlaying(true));
    audio.addEventListener("pause", () => actions.setIsPlaying(false));

    return () => {
      audio.removeEventListener("loadedmetadata", setAudioData);
      audio.removeEventListener("timeupdate", setAudioTime);
      audio.removeEventListener("ended", () => actions.setIsPlaying(false));
      audio.pause();
      audio.currentTime = 0;
    };
  }, [recordingURL]);

  return <PlayerResponsiveSwitcher />;
};
