import { EthereumWallet } from "@dynamic-labs/ethereum-core";
import {
  Account,
  Chain,
  encodeFunctionData,
  Hex,
  PublicClient,
  Transport,
  WalletClient,
} from "viem";

import {
  COMMUNITIES_CONTRACT,
  COMMUNITY_ABI,
  COMMUNITY_PRICES_ABY,
  COMMUNITY_PRICES_CONTRACT,
} from "@/environments/COMMUNITY_CONTRACT";
import { Me } from "@/types/me";

const calculatePurchaseAmountAndPrice = async (
  primaryWallet: EthereumWallet,
  publicClient: PublicClient<Transport, Chain>,
  bcGroupId: bigint,
  amount: bigint,
) => {
  const [tokens, price] = await publicClient.readContract({
    address: COMMUNITY_PRICES_CONTRACT.addressMainnet as Hex,
    abi: COMMUNITY_PRICES_ABY,
    functionName: "calculatePurchaseAmountAndPrice",
    args: [amount, bcGroupId],
    account: primaryWallet.address as Hex,
  });
  return { tokens, price };
};

const calculateRewardWithFees = async (
  primaryWallet: EthereumWallet,
  publicClient: PublicClient<Transport, Chain>,
  bcGroupId: bigint,
  amount: bigint,
) => {
  return await publicClient.readContract({
    address: COMMUNITIES_CONTRACT.addressMainnet as Hex,
    abi: COMMUNITY_ABI,
    functionName: "calculateRewardWithFees",
    args: [amount, bcGroupId],
    account: primaryWallet.address as Hex,
  });
};

const buy = async (
  primaryWallet: EthereumWallet,
  publicClient: PublicClient<Transport, Chain>,
  walletClient: WalletClient<Transport, Chain, Account>,
  user: Me,
  bcGroupId: bigint,
  tokens: bigint,
  avaxAmount: bigint,
) => {
  const txData = encodeFunctionData({
    abi: COMMUNITY_ABI,
    functionName: user.referrerAddress
      ? "buyWithReferrerAndCreateLpIfPossible"
      : "buyAndCreateLpIfPossible",
    args: user.referrerAddress
      ? [tokens, bcGroupId, user.referrerAddress as Hex]
      : [tokens, bcGroupId],
  });

  const swapHash = await walletClient.sendTransaction({
    account: primaryWallet.address as Hex,
    chain: walletClient.chain,
    to: COMMUNITIES_CONTRACT.addressMainnet as Hex,
    value: avaxAmount,
    data: txData,
  });

  await publicClient.waitForTransactionReceipt({
    hash: swapHash as Hex,
    confirmations: 1,
  });
};

const sell = async (
  primaryWallet: EthereumWallet,
  publicClient: PublicClient<Transport, Chain>,
  walletClient: WalletClient<Transport, Chain, Account>,
  user: Me,
  bcGroupId: bigint,
  tokens: bigint,
) => {
  const txData = encodeFunctionData({
    abi: COMMUNITY_ABI,
    functionName: user.referrerAddress ? "sellWithReferrer" : "sell",
    args: user.referrerAddress
      ? [tokens, bcGroupId, user.referrerAddress as Hex]
      : [tokens, bcGroupId],
  });

  const swapHash = await walletClient.sendTransaction({
    account: primaryWallet.address as Hex,
    chain: walletClient.chain,
    to: COMMUNITIES_CONTRACT.addressMainnet as Hex,
    data: txData,
  });

  await publicClient.waitForTransactionReceipt({
    hash: swapHash as Hex,
    confirmations: 1,
  });
};

const calculateAndBuy = async (
  primaryWallet: EthereumWallet,
  publicClient: PublicClient<Transport, Chain>,
  walletClient: WalletClient<Transport, Chain, Account>,
  user: Me,
  bcGroupId: bigint,
  avaxAmount: bigint,
) => {
  const { tokens } = await calculatePurchaseAmountAndPrice(
    primaryWallet,
    publicClient,
    bcGroupId,
    avaxAmount,
  );

  await buy(
    primaryWallet,
    publicClient,
    walletClient,
    user,
    bcGroupId,
    tokens,
    avaxAmount,
  );
};

export const useLaunchToken = () => {
  return {
    calculatePurchaseAmountAndPrice,
    calculateRewardWithFees,
    buy,
    sell,
    calculateAndBuy,
  };
};
