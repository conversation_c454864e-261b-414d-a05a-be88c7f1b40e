"use client";

import { useRef, useState } from "react";

import { MailIcon } from "@/components/icons-v2/account-security";
import { toast } from "@/components/toast";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

interface IVerifyEmailModalProps {
  email: string;
  verifyAction: (verificationToken: string) => void;
  resendOTP: (email: string) => void;
  closeAction: () => void;
}

export const VerifyEmailModal = (props: IVerifyEmailModalProps) => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const VERIFICATION_LETTER_COUNT = 6;
  const [verificationParts, setVerificationParts] = useState(
    Array(VERIFICATION_LETTER_COUNT).fill(""),
  );
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const handleInputChange = (
    index: number,
    event: React.FormEvent<HTMLInputElement>,
  ) => {
    const value = (event.target as HTMLInputElement).value.toUpperCase();
    if (value.length === 1) {
      inputRefs.current[index]!.value = value;
      if (index < inputRefs.current.length - 1) {
        inputRefs.current[index + 1]?.focus();
        inputRefs.current[index + 1]?.select();
      }
      const newVerificationParts = [...verificationParts];
      newVerificationParts[index] = value;
      setVerificationParts(newVerificationParts);
    } else {
      inputRefs.current[index]!.value = value.slice(-1);
      const newVerificationParts = [...verificationParts];
      newVerificationParts[index] = value.slice(-1);
      setVerificationParts(newVerificationParts);
    }
  };

  const resendOTP = () => {
    try {
      props.resendOTP(props.email);
      toast.green("Verification code re-sent successfully.");
    } catch (error) {
      toast.red(
        "Failed to resend another verification code, please try again later!",
      );
    }
  };

  const verify = () => {
    const verificationToken = verificationParts.join("");
    props.verifyAction(verificationToken);
  };

  return (
    <div
      className={
        isTablet
          ? "fixed inset-0 z-50 flex content-center items-center justify-center"
          : ""
      }
    >
      <div
        className="bg-black fixed inset-0 z-40 bg-[#020202CC]"
        onClick={props.closeAction}
      />
      <div
        className={`fixed z-50 px-[24px] pb-[48px] pt-[24px] ${isTablet ? "rounded-[20px]" : "bottom-[0px] w-full rounded-t-[20px]"}`}
        style={{ backgroundColor: "rgba(15, 15, 15, 0.90)" }}
      >
        <div className="justify-left flex flex-col items-center gap-[32px]">
          <div className="font-inter flex flex-col items-center gap-[8px] self-stretch text-[16px] font-semibold leading-[22px] text-[#F4F4F4]">
            <MailIcon />
            <p>Verify your email address</p>
          </div>
          <div className="flex flex-col items-start gap-[24px] self-stretch">
            <div className="font-inter self-stretch text-[14px] font-normal leading-[21px] text-[var(--LIGHT-GRAY-TEXT,#B5B5B5)]">
              Verification code sent to{" "}
              <span className="font-semibold text-[var(--OFF-WHITE,#F4F4F4)]">
                {props.email || "<EMAIL>"}.
              </span>
            </div>
            <div className="flex items-center justify-between self-stretch px-[8px]">
              {Array.from({ length: VERIFICATION_LETTER_COUNT }).map(
                (_, index) => (
                  <input
                    type="text"
                    inputMode="numeric"
                    pattern="\d*"
                    key={index}
                    ref={(el) => {
                      inputRefs.current[index] = el;
                    }}
                    maxLength={1}
                    className="selection-transparent flex h-[45px] w-[45px] items-center justify-between rounded-[10px] border border-[var(--GRAY-TEXT,#808080)] bg-[var(--LIGHT-BACKGROUND,#1A1A1A)] p-[16px] text-center caret-transparent opacity-85"
                    placeholder="-"
                    onInput={(event) => handleInputChange(index, event)}
                    onClick={(event) => event.currentTarget.select()}
                  />
                ),
              )}
            </div>
            <div
              className="flex flex-col items-center self-stretch"
              onClick={resendOTP}
            >
              <div>
                <span className="font-inter text-[14px] font-normal leading-[21px] text-[var(--LIGHT-GRAY-TEXT,#B5B5B5)]">
                  Didn’t receive a code?{" "}
                </span>
                <span className="font-semibold text-[var(--OFF-WHITE,#F4F4F4)] underline">
                  Re-send
                </span>
              </div>
            </div>
          </div>
          <div className="flex-space-around flex w-full items-center gap-[8px]">
            <button
              className="flex h-[44px] w-full items-center justify-center rounded-[40px] bg-[var(--ORANGE-GRADIENT,#D64C05)] px-[60px] py-[8px]"
              onClick={verify}
            >
              <p className="font-inter text-center text-[14px] font-semibold leading-[20px] text-[var(--OFF-WHITE,#F4F4F4)]">
                Continue
              </p>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
