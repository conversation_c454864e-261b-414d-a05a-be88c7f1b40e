"use client";

import { FC, memo, useEffect } from "react";
import { useRouter } from "next/navigation";

import {
  createCurrency,
  SystemCurrency,
  SystemCurrencyCategoryEnum,
} from "@/api/client/admin-currency";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";

import { CurrencyForm, CurrencyFormData } from "../_components/currency-form";
import { useAdmin } from "../../../context/admin-context";

export const CurrencyCreate: FC = memo(() => {
  const router = useRouter();
  const { setSelectedProject } = useAdmin();

  useEffect(() => {
    const newProject: SystemCurrency = {
      id: 0,
      name: "",
      symbol: "",
      address: "",
      systemRate: "0",
      image: "",
      decimals: 18,
      categories: [],
    };
    setSelectedProject(newProject);
  }, [setSelectedProject]);

  const handleSubmit = async (data: CurrencyFormData) => {
    try {
      const categoriesArray = Object.entries(data.categories)
        .filter(([, isSelected]) => isSelected)
        .map(([category]) => category as SystemCurrencyCategoryEnum);

      const submitData = {
        ...data,
        categories: categoriesArray,
      };

      await createCurrency(submitData);
      toast.green("Project created successfully");
      router.push("/moderation/admin");
    } catch (error) {
      toast.danger(
        error instanceof Error ? error.message : "Failed to save project",
      );
    }
  };

  return (
    <div className="flex h-screen flex-col">
      <header className="sticky top-0 z-10 flex items-center gap-4 bg-[#141414] p-4">
        <ProgressBarLink href="/moderation/admin" className="size-5">
          <ArrowBackOutlineIcon className="size-5 text-off-white" />
        </ProgressBarLink>
        <h1 className="flex-1 text-center font-semibold">
          Add new project to Supported Projects
        </h1>
        <div className="size-5" />
      </header>

      <div className="flex-1 overflow-y-auto p-8">
        <CurrencyForm onSubmit={handleSubmit} />
      </div>
    </div>
  );
});

CurrencyCreate.displayName = "CurrencyCreate";
