"use client";

import { useQuery } from "@tanstack/react-query";

import { stageQueries } from "@/queries/stage-queries";

import { LiveStreamInfoPill } from "../livestream/live-stream-info-pill";
import { LiveStageInfoPill } from "./live-stage-info-pill";

export const LiveStagesHeader = () => {
  const { data, isLoading } = useQuery({
    ...stageQueries.liveStages(),
    refetchInterval: 60 * 1000,
  });

  if (isLoading || !data || data?.live.length === 0) return null;

  return (
    <div className="hide-scrollbar grid snap-x snap-mandatory grid-flow-col gap-4 overflow-x-auto px-5 pb-3 pt-1">
      {data.live.map((item) => {
        if (item.type === "livestream") {
          return <LiveStreamInfoPill key={item.id} livestream={item} />;
        }
        return <LiveStageInfoPill key={item.id} stage={item} />;
      })}
    </div>
  );
};
