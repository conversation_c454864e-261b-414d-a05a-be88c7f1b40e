"use client";

import { useLayoutEffect, useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { isIOS } from "@/components/ui/vaul/browser";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

export const LoginPopupInstruction = () => {
  const [open, setOpen] = useState(true);
  const [IOS, setIOS] = useState(false);
  const [isPWA, setIsPWA] = useState(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  useLayoutEffect(() => {
    const ios = isIOS();
    if (ios) {
      setIOS(ios);
    }

    if (
      typeof window !== "undefined" &&
      ((window.matchMedia != null &&
        window.matchMedia("(display-mode: standalone)").matches) ||
        (window.navigator != null &&
          (window.navigator as any).standalone === true))
    ) {
      setIsPWA(true);
    }
  }, []);

  if (!isTablet || isPWA) return null;

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
      }}
    >
      <DialogContent className="gap-6">
        <div className="flex items-center gap-3">
          <h3 className="text-base font-semibold leading-[22px] text-off-white">
            Hey! Hold on a second there.
          </h3>
        </div>

        <p className="text-sm text-[#B5B5B5]">
          Please make sure to enable pop-ups on your browser now, it takes less
          than 1 minute to do it and everything will go smoothly!
        </p>

        <Button
          className="mt-4 w-full"
          type="submit"
          onClick={() => {
            setOpen(false);
          }}
        >
          I Already Enabled Pop-Ups
        </Button>
      </DialogContent>
    </Dialog>
  );
};
