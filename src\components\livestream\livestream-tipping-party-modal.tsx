import { useEffect, useMemo, useState } from "react";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { zodResolver } from "@hookform/resolvers/zod";
import { useParticipants } from "@livekit/components-react";
import * as RadioGroup from "@radix-ui/react-radio-group";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { RoomEvent } from "livekit-client";
import { useForm } from "react-hook-form";
import { parseUnits } from "viem";
import { z } from "zod";

import { postTippingPartyNotify } from "@/api/client/chat";
import { batchMultiSendDynamic } from "@/api/client/dynamic/send-funds-dynamic";
import { getUserAddressesForMultiSendBatched } from "@/api/client/user";
import { TipPartyContent } from "@/app/(messages)/messages/_components/TipPartyContent";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { TippingInfoModal } from "@/components/tipping-info";
import { TipFormContent } from "@/components/tipping/tip-form-content";
import { toast } from "@/components/toast";
import { ArenaDialogHeader } from "@/components/ui/arena-dialog-header";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { fallbackArena } from "@/environments/tokens";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import { livestreamQueries } from "@/queries";
import {
  useAvaxPriceQuery,
  useTippableCurrenciesQuery,
} from "@/queries/currency-queries";
import { useSendTipsMutation } from "@/queries/send-tips-mutation";
import { Tip } from "@/queries/types/send-tips-data";
import { useUser } from "@/stores";
import { useLivestreamStore } from "@/stores/livestream";
import { cn } from "@/utils";
import { formatPrice } from "@/utils/format-token-price";
import { formatNumericValue } from "@/utils/number";

import { Role } from "./constants";
import { useDataChannelsContext } from "./stores/data-channels-context";

const TIP_OPTION = {
  EVERY_VIEWER: "EVERY_VIEWER",
  TOP_10_SUPPORTERS: "TOP_10_SUPPORTERS",
} as const;

type Option = (typeof TIP_OPTION)[keyof typeof TIP_OPTION];

type FormattedParticipant = {
  id: string;
  name: string;
  avatar: string;
  username: string;
  role: Role;
};

interface LivestreamTippingPartyModalProps {
  children: React.ReactNode;
  livestreamId?: string;
  dataChannelsContext?: ReturnType<typeof useDataChannelsContext>;
}

export const LivestreamTippingPartyModal = ({
  children,
  livestreamId: propLivestreamId,
  dataChannelsContext,
}: LivestreamTippingPartyModalProps) => {
  const [open, setOpen] = useState(false);
  const queryClient = useQueryClient();
  const { user } = useUser();
  const id = propLivestreamId ?? useLivestreamStore((state) => state.id!);
  const [tipAmount, setTipAmount] = useState("0");
  const [singleTipUSD, setSingleTipUSD] = useState("0");
  const [option, setOption] = useState<Option>(TIP_OPTION.EVERY_VIEWER);
  const [isTippingDisabled, setIsTippingDisabled] = useState(false);
  const [participantsSnapshot, setParticipantsSnapshot] = useState<
    FormattedParticipant[]
  >([]);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [step, setStep] = useState<1 | 2>(1);

  const { data } = useQuery(livestreamQueries.livestreamSimpleInfo(id));

  const context = dataChannelsContext ?? useDataChannelsContext();
  const { send } = context.chat;
  const participants = useParticipants({
    updateOnlyOn: [
      RoomEvent.ParticipantConnected,
      RoomEvent.ParticipantDisconnected,
    ],
  });
  const currentParticipants = useMemo(() => {
    const currentParticipants = participants.map((participant) => ({
      id: participant.identity,
      name: participant.attributes?.name,
      avatar: participant.attributes?.avatar,
      username: participant.attributes?.username,
      role: participant.attributes?.role,
    })) as {
      id: string;
      name: string;
      avatar: string;
      username: string;
      role: Role;
    }[];

    return currentParticipants.filter(
      (participant) =>
        participant.id !== user?.id &&
        participant.id !== data?.host.id &&
        !participant.id.includes("-ingress"),
    );
  }, [participants, data]);

  const { data: topLivestreamTippersData, refetch } = useQuery({
    ...livestreamQueries.topLivestreamTippers(id),
    staleTime: 10 * 60 * 1000,
  });
  const speakers = useMemo(() => {
    return (
      topLivestreamTippersData?.tippers.filter(
        (tipper) => tipper.user.id !== user?.id,
      ) ?? []
    );
  }, [topLivestreamTippersData?.tippers, user]);

  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useTippableCurrenciesQuery();

  const { data: avaxPrice } = useAvaxPriceQuery();

  const { sortedCurrencies } = useWalletCurrencies({
    user,
    currenciesData,
    isCurrenciesLoading,
  });

  const [symbol, setSymbol] = useState("ARENA");
  const token = useMemo(
    () => sortedCurrencies.find((t) => t.symbol === symbol) ?? fallbackArena,
    [sortedCurrencies, symbol],
  );

  const sendTipsInput = z.object({
    currency: z.string(),
    amountPerTip: z
      .string()
      .min(1, {
        message: "Tip amount is required",
      })
      .refine((v) => !isNaN(parseFloat(v.replace(/,/g, ""))), {
        message: "Tip amount must be a number",
      })
      .refine(
        (v) =>
          calculateSendTips() * parseFloat(v.replace(/,/g, "")) <=
          parseFloat(
            token.isToken
              ? formatPrice(token.balance || "0").toString()
              : (token.balance || "0").toString().replace(/,/g, ""),
          ),
        {
          message: "Insufficient balance",
        },
      ),
    totalAmount: z.string(),
  });
  type sendTipsInputType = z.infer<typeof sendTipsInput>;

  const form = useForm<sendTipsInputType>({
    defaultValues: {
      currency: "ARENA",
      amountPerTip: "",
      totalAmount: "",
    },
    resolver: zodResolver(sendTipsInput),
    mode: "all",
  });

  const { mutateAsync: sendTips } = useSendTipsMutation({
    onSuccess: () => {
      toast.green("Tips sent!");
      setOpen(false);
      form.reset();
      setTipAmount("0");
      setSingleTipUSD("0");
      setOption(TIP_OPTION.EVERY_VIEWER);
      queryClient.invalidateQueries({
        queryKey: ["wallet", "balance", user?.address, token.symbol],
      });
      setSymbol("ARENA");
    },
  });

  const calculateSendTips = () => {
    if (option === TIP_OPTION.TOP_10_SUPPORTERS) {
      return speakers.length;
    } else {
      return participantsSnapshot.length;
    }
  };

  const { primaryWallet } = useDynamicContext();

  const [isPendingDynamic, setIsPendingDynamic] = useState(false);

  const onSubmit = async (values: sendTipsInputType) => {
    const tips: Tip[] =
      option === TIP_OPTION.TOP_10_SUPPORTERS
        ? speakers.map((speaker) => ({
            userId: speaker.user.id,
            amount: tipAmount.replace(/,/g, ""),
          })) ?? []
        : participantsSnapshot.map((participant) => ({
            userId: participant.id,
            amount: tipAmount.replace(/,/g, ""),
          }));

    const message = {
      type: "tip-party",
      data: {
        amount: Number(values.amountPerTip.replace(/,/g, "")),
        token: {
          name: values.currency,
          icon: sortedCurrencies.find(
            (token) => token.symbol === values.currency,
          )?.photoURL,
        },
        type: option,
      },
    };

    if (user) {
      if (user.address === user.dynamicAddress?.toLowerCase()) {
        setIsPendingDynamic(true);
        const userIds = tips.map((tip) => tip.userId);
        const toAddresses: string[] =
          await getUserAddressesForMultiSendBatched(userIds);

        const decimals = values.currency === "MEAT" ? 6 : 18;
        const amounts = tips.map((tip) => parseUnits(tip.amount, decimals));
        const tippedToken = sortedCurrencies.find(
          (token) => token.symbol === values.currency,
        );

        if (!primaryWallet) {
          return;
        }
        if (!isEthereumWallet(primaryWallet)) {
          toast.danger("This wallet is not an Ethereum wallet");
          setIsPendingDynamic(false);
          return;
        }

        if (toAddresses.length !== amounts.length) {
          toast.danger("Mismatch between number of addresses and amounts");
          setIsPendingDynamic(false);
          return;
        }

        try {
          const { txHash, txData } = await batchMultiSendDynamic(
            primaryWallet,
            toAddresses,
            amounts,
            values.currency,
            tippedToken?.isToken || false,
            tippedToken?.contractAddress,
          );

          try {
            if (txHash && txData && txHash.length && txData.length) {
              for (let i = 0; i < txHash.length; i++) {
                await postTippingPartyNotify({
                  currency: values.currency,
                  txHash: txHash[i],
                  txData: txData[i],
                });
              }
            }
          } catch (error) {
            console.error("Error in postTipNotify:", error);
          }
          await send(JSON.stringify(message));
          toast.green("Tips sent!");
        } catch (error) {
          toast.danger("Error occured while tipping, please try again!");
          console.error("Error in multiSendDynamic:", error);
        } finally {
          setIsPendingDynamic(false);
          setOpen(false);
          form.reset();
          setTipAmount("0");
          setSingleTipUSD("0");
          setOption(TIP_OPTION.EVERY_VIEWER);
          queryClient.invalidateQueries({
            queryKey: ["wallet", "balance", user?.address, token.symbol],
          });
          setSymbol("ARENA");
          queryClient.invalidateQueries({
            queryKey: ["currency", "system"],
          });
        }
      } else {
        try {
          await sendTips({
            currency: values.currency,
            tips,
          });
          await send(JSON.stringify(message));
          toast.green("Tips sent!");
        } catch (error) {
          console.error("Error in sendTips:", error);
        } finally {
          setOpen(false);
          form.reset();
          setTipAmount("0");
          setSingleTipUSD("0");
          setOption(TIP_OPTION.EVERY_VIEWER);
          queryClient.invalidateQueries({
            queryKey: ["wallet", "balance", user?.address, token.symbol],
          });
          setSymbol("ARENA");
        }
      }
    }
  };

  const tipValue = async (rate: string, value: string) => {
    setSingleTipUSD(
      formatNumericValue((Number(value) * Number(rate)).toFixed(2).toString()),
    );
  };

  useEffect(() => {
    const sanitizedValue = tipAmount.replace(/,/g, "");
    const requiredBalance = Number(sanitizedValue) * calculateSendTips();
    if (
      requiredBalance > parseFloat((token.balance || "0").replace(/,/g, "")) ||
      requiredBalance === 0
    ) {
      setIsTippingDisabled(true);
    } else {
      setIsTippingDisabled(false);
    }
    tipValue(
      token.isToken
        ? (
            Number(formatPrice(token.systemRate)) * (avaxPrice?.avax || 0)
          ).toString()
        : token.systemRate,
      sanitizedValue,
    );
  }, [token, tipAmount]);

  useEffect(() => {
    if (open) {
      setParticipantsSnapshot(currentParticipants);
      refetch();
    } else {
      setParticipantsSnapshot([]);
    }
  }, [open]);

  useEffect(() => {
    const handleResize = () => {
      const viewportHeight =
        window.visualViewport?.height || window.innerHeight;
      const windowHeight = window.innerHeight;
      if (viewportHeight < windowHeight) {
        setIsKeyboardVisible(true);
      } else {
        setIsKeyboardVisible(false);
      }
    };
    if (typeof visualViewport != "undefined") {
      window.visualViewport?.addEventListener("resize", handleResize);
    }
    return () => {
      if (typeof visualViewport != "undefined") {
        window.visualViewport?.removeEventListener("resize", handleResize);
      }
    };
  }, []);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        className={cn(
          "flex h-full w-full flex-grow flex-col bg-dark-bk  px-6 pt-0 backdrop-blur-sm sm:h-fit sm:w-[420px] sm:gap-0 sm:bg-[#1A1A1A] sm:p-6",
          isKeyboardVisible
            ? "flex-grow-0 justify-end pb-[calc(142px+env(safe-area-inset-bottom))]"
            : "flex-grow justify-between",
        )}
      >
        <ArenaDialogHeader
          className="mb-12 sm:mb-8"
          title="Tipping Party"
          showBack={true}
          onBack={step === 2 ? () => setStep(1) : () => setOpen(false)}
        />
        {step === 1 && (
          <TipPartyContent
            options={[
              {
                value: TIP_OPTION.EVERY_VIEWER,
                label: (
                  <>
                    Tip Every
                    <br />
                    Viewer
                  </>
                ),
                emoji: "📺",
              },
              {
                value: TIP_OPTION.TOP_10_SUPPORTERS,
                label: (
                  <>
                    Tip Top 10
                    <br />
                    Supporters
                  </>
                ),
                emoji: "👏",
              },
            ]}
            selected={option}
            setSelected={(v) => setOption(v as Option)}
            infoText={
              <div className="mt-2 text-center text-gray-text">
                <h4 className="text-sm">
                  {option === TIP_OPTION.EVERY_VIEWER
                    ? "Every current stream viewer will receive the same tip."
                    : "Top 10 stream supporters will receive the same tip."}
                </h4>
                <p className="mt-1 font-semibold leading-[18px] text-off-white">
                  {`${option === TIP_OPTION.EVERY_VIEWER ? participantsSnapshot.length : speakers.length} tips will be sent`}
                </p>
              </div>
            }
            onContinue={() => setStep(2)}
            continueLabel="Continue"
          />
        )}
        {step === 2 && (
          <TipFormContent
            recepients={
              option === TIP_OPTION.TOP_10_SUPPORTERS
                ? speakers.slice(0, 10).map((s) => {
                    const user = s.user as any;
                    return {
                      id: user.id,
                      address: user.address || user.id,
                      twitterName: user.twitterName,
                      twitterHandle: user.twitterHandle,
                      twitterPicture: user.twitterPicture,
                    };
                  })
                : participantsSnapshot.map((p) => {
                    const pp = p as any;
                    return {
                      id: pp.id,
                      address: pp.address || pp.id,
                      twitterName: pp.name,
                      twitterHandle: pp.username,
                      twitterPicture: pp.avatar,
                    };
                  })
            }
            setOpen={setOpen}
            sortedCurrencies={sortedCurrencies}
            setResetForm={undefined}
            distributionMode="equal"
            buttonLabel="Send tips"
            customRecipientsSelector={null}
            onPartyNotify={async ({ currency, txHash, txData, recipient }) => {
              await postTippingPartyNotify({
                currency,
                txHash,
                txData,
              });
            }}
            onPartyMessage={async ({ message }) => {
              await send(JSON.stringify(message));
            }}
          />
        )}
        <TippingInfoModal />
      </DialogContent>
    </Dialog>
  );
};
