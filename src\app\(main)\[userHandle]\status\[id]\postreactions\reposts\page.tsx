"use client";

import { useRouter } from "next/navigation";

import { motion, useTransform } from "framer-motion";
import { Virtuoso } from "react-virtuoso";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { useBoundedScroll } from "@/hooks";
import { usePostReactionsRepostsInfiniteQuery } from "@/queries/postreactions-queries";
import { cn } from "@/utils";

import { UserListItem } from "../_components/user-list-item";
import { UserListItemLoadingSkeleton } from "../_components/user-list-item-loading-skeleton";

function PostReactionsPage({
  params,
}: {
  params: { userHandle: string; id: string };
}) {
  const router = useRouter();
  const pathname =
    typeof window !== "undefined" ? window.location.pathname : "";
  const postUrl = pathname.split("/postreactions")[0];

  const { scrollYBoundedProgress } = useBoundedScroll(300);
  const scrollYBoundedProgressDelayed = useTransform(
    scrollYBoundedProgress,
    [0, 0.3, 1],
    [0, 1, 1],
  );

  const { data, isLoading, isFetchingNextPage, fetchNextPage, hasNextPage } =
    usePostReactionsRepostsInfiniteQuery({
      threadId: params.id,
    });
  const reposts = data?.pages.flatMap((page) => page.repostedUsers) || [];

  return (
    <div className="flex h-full flex-col">
      <motion.div
        className="pt-pwa sticky top-0 z-50 bg-dark-bk/65 backdrop-blur-md"
        style={{
          y: useTransform(
            scrollYBoundedProgressDelayed,
            [0, 1],
            ["0%", "-100%"],
          ),
        }}
      >
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex flex-1 items-center">
            <button
              onClick={() => {
                router.replace(postUrl);
              }}
            >
              <ArrowBackOutlineIcon className="size-5" />
            </button>
          </div>
          <div className="text-base font-semibold leading-5">
            Post Reactions
          </div>
          <div className="flex-1" />
        </div>
        <div className="hide-scrollbar inline-flex h-10 w-full items-center justify-center overflow-x-auto border-b border-white/10 px-5">
          <ProgressBarLink
            href={`${postUrl}/postreactions/likes`}
            className={cn(
              "relative -mt-[1px] inline-flex items-center justify-center whitespace-nowrap px-8 pb-3 pt-2 text-sm font-medium text-dark-gray transition-all disabled:pointer-events-none disabled:opacity-50 sm:px-14",
            )}
            replace
          >
            Likes
          </ProgressBarLink>
          <ProgressBarLink
            href={`${postUrl}/postreactions/reposts`}
            className={cn(
              "relative -mt-[1px] inline-flex items-center justify-center whitespace-nowrap px-8 pb-3 pt-2 text-sm font-medium text-dark-gray transition-all disabled:pointer-events-none disabled:opacity-50 sm:px-14",
              "text-off-white after:absolute after:bottom-0 after:h-px after:w-full after:bg-brand-orange",
            )}
            replace
          >
            Reposts
          </ProgressBarLink>
          <ProgressBarLink
            href={`${postUrl}/postreactions/quotes`}
            className={cn(
              "relative -mt-[1px] inline-flex items-center justify-center whitespace-nowrap px-8 pb-3 pt-2 text-sm font-medium text-dark-gray transition-all disabled:pointer-events-none disabled:opacity-50 sm:px-14",
            )}
            replace
          >
            Quotes
          </ProgressBarLink>
        </div>
      </motion.div>
      {reposts.length === 0 && !isLoading && (
        <div className="flex h-full flex-grow items-center justify-center text-center">
          <div className="text-sm font-semibold text-off-white">
            No reposts yet
          </div>
        </div>
      )}
      <Virtuoso
        useWindowScroll
        data={reposts}
        increaseViewportBy={500}
        itemContent={(index, user) => {
          return <UserListItem user={user} threadId={params.id} />;
        }}
        endReached={() => {
          if (hasNextPage) {
            fetchNextPage();
          }
        }}
        components={{
          Footer: () => {
            if (isLoading || isFetchingNextPage) {
              return (
                <>
                  {Array.from({ length: 10 }).map((_, i) => (
                    <UserListItemLoadingSkeleton key={i} />
                  ))}
                </>
              );
            }
            return null;
          },
        }}
      />
    </div>
  );
}

export default PostReactionsPage;
