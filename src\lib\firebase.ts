import { initializeApp } from "firebase/app";
import { getMessaging, getToken } from "firebase/messaging";

import { postCloudMessagingPushToken } from "@/api/client/cloud-messaging";
import { env } from "@/env";

const firebaseConfig = {
  apiKey: "AIzaSyCN83XNCAEhZtViYjmRmVbML6FxIhIlJQA",
  authDomain: "imposing-yen-405409.firebaseapp.com",
  projectId: "imposing-yen-405409",
  storageBucket: "imposing-yen-405409.appspot.com",
  messagingSenderId: "277531163698",
  appId: "1:277531163698:web:814febc98e3d891494102b",
  measurementId: "G-C5C8N1GE1X",
};

const app = initializeApp(firebaseConfig);

export const generateToken = async (deviceType: string) => {
  try {
    const messaging = getMessaging(app);
    const permission = await Notification.requestPermission();

    if (permission === "granted" && messaging) {
      const raw = localStorage.getItem("fcm_token");
      if (raw) {
        try {
          const parsed = JSON.parse(raw);
          const { token, timestamp } = parsed;

          const ninetyDays = 90 * 24 * 60 * 60 * 1000;
          const isExpired = Date.now() - timestamp > ninetyDays;

          if (!isExpired && token) {
            return token;
          } else {
            console.log("FCM token expired, generating a new one...");
          }
        } catch (err) {
          console.warn(
            "Failed to parse FCM token from localStorage, regenerating...",
          );
        }
      }
      const token = await getToken(messaging, {
        vapidKey: env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
      });
      try {
        await postCloudMessagingPushToken({
          notificationToken: token,
          deviceType,
        });
        localStorage.setItem(
          "fcm_token",
          JSON.stringify({
            token,
            timestamp: Date.now(),
          }),
        );

        return token;
      } catch (error) {
        console.error(error);
      }
    }
  } catch (err) {
    console.error("Failed to initialize Firebase Messaging", err);
    return "error";
  }
};
