"use client";

import { useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";

import { useQueryClient } from "@tanstack/react-query";

import { setTutorialShown } from "@/api/client/user";
import { useFollowTopUserMutation } from "@/queries";
import { useUser } from "@/stores";
import { useTutorialStore } from "@/stores/tutorial";

import { UserAddOutlineIcon } from "../icons";
import { toast } from "../toast";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
} from "../ui/alert-dialog";
import { Button } from "../ui/button";

export const FollowSuggestionsDialog = () => {
  const pathname = usePathname();
  const { user, updateUser } = useUser();
  const queryClient = useQueryClient();
  const isFollowTutorialOpen = useTutorialStore(
    (state) => state.isFollowTutorialOpen,
  );
  const isOnHomePage = pathname === "/home";
  const isOpen = isOnHomePage && isFollowTutorialOpen;

  const actions = useTutorialStore((state) => state.actions);

  useEffect(() => {
    if (user?.tutorial?.followTutorialShown) {
      actions.setIsFollowTutorialOpen(false);
    }
  }, [user, actions]);

  const { mutateAsync: followTopUsers, isPending } = useFollowTopUserMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
    },
  });

  const onFollowTopUsers = async () => {
    await followTopUsers();
    actions.setIsFollowTutorialOpen(false);
    setTutorialShown("follow");
    handleUpdate();
    queryClient.invalidateQueries({
      queryKey: ["home", "threads", "my-feed"],
    });
    toast.green("Followed top users successfully!");
  };

  const handleUpdate = () => {
    if (!user) return;
    updateUser({
      ...user,
      tutorial: {
        ...user.tutorial,
        followTutorialShown: true,
      },
    });
  };

  return (
    <>
      <AlertDialog
        open={isOpen && !user?.tutorial?.followTutorialShown}
        onOpenChange={actions.setIsFollowTutorialOpen}
      >
        <AlertDialogContent
          overlayNoBG
          className="md:bottom-25 bottom-[calc(70px+env(safe-area-inset-bottom))] top-auto w-11/12 max-w-md translate-y-0 rounded-[10px] border border-off-white/20 p-6 text-off-white shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)] md:bottom-[30vh]"
        >
          <div className="flex flex-col items-center text-center">
            <UserAddOutlineIcon className="h-6 w-6 text-off-white" />
            <h2 className="mt-2 text-base font-semibold text-off-white">
              Follow some popular accounts!
            </h2>
            <p className="mt-4 text-sm text-gray-text ">
              By following some of the top 100 accounts, you&apos;ll get the
              pulse of our community.
            </p>
          </div>
          <AlertDialogFooter className="mt-2 flex-row items-baseline gap-2 pb-2">
            <Button
              onClick={() => {
                actions.setIsFollowTutorialOpen(false);
                setTutorialShown("follow");
                handleUpdate();
              }}
              variant="outline"
              className="h-9 w-1/3 text-off-white"
            >
              Close
            </Button>
            <Button
              onClick={() => {
                onFollowTopUsers();
              }}
              className="h-9 w-2/3 bg-orange-gradient"
              loading={isPending}
            >
              Follow all
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      {isOpen && <div className="absolute inset-0 z-20 bg-dark-bk/65" />}
    </>
  );
};
