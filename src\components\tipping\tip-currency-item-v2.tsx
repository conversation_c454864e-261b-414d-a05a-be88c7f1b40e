import { FC } from "react";

import { SystemCurrency } from "@/api/client/currency";
import { formatPrice } from "@/utils/format-token-price";

const numberFormatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 20,
});

export const TippingCurrencyItemV2: FC<{ currency: SystemCurrency }> = ({
  currency,
}) => {
  return (
    <div className="flex w-full cursor-pointer items-center justify-between">
      <div className="flex flex-col">
        <div className="flex items-center gap-4">
          <img
            src={currency.photoURL}
            className="size-8 rounded-full"
            alt={`${currency.symbol} logo`}
          />
          <div className="flex flex-col">
            <span className="text-off-white">
              {currency.isToken ? `$${currency.symbol}` : currency.symbol}
            </span>
            <span className="text-sm text-light-gray-text">
              {currency.name}
            </span>
          </div>
        </div>
      </div>
      <div className="flex flex-col items-end">
        <span className="text-off-white">
          {numberFormatter.format(
            currency.isToken
              ? parseFloat(
                  formatPrice(currency.balance).toFixed(
                    formatPrice(currency.balance) > 1 ? 2 : 4,
                  ),
                )
              : Number(currency.balance),
          )}
        </span>
        <span className="text-light-gray-text">
          ${numberFormatter.format(Number(currency.balanceUsd || "0"))}
        </span>
      </div>
    </div>
  );
};
