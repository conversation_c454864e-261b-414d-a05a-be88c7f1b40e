/** @type {import('prettier').Config & import('prettier-plugin-tailwindcss').PluginOptions} */
/** @type {import('@ianvs/prettier-plugin-sort-imports').PrettierConfig} */

const config = {
  semi: true,
  singleQuote: false,
  trailingComma: "all",
  arrowParens: "always",
  jsxSingleQuote: false,
  bracketSpacing: true,
  tabWidth: 2,
  printWidth: 80,
  plugins: [
    "@ianvs/prettier-plugin-sort-imports",
    "prettier-plugin-packagejson",
    "prettier-plugin-tailwindcss",
  ],
  importOrder: [
    "",
    "^react$",
    "^next(/.*)?$",
    "",
    "<TYPES>",
    "<TYPES>^[.]",
    "",
    "<BUILTIN_MODULES>",
    "",
    "<THIRD_PARTY_MODULES>",
    "",
    "^@/(.*)$",
    "",
    "^[./]",
    "",
    "^(?!.*[.]css$)[./].*$",
    ".css$",
  ],
  importOrderTypeScriptVersion: "5.4.5",
};

module.exports = config;
