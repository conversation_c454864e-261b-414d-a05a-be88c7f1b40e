import { compareDesc, format, formatDistanceToNowStrict, sub } from "date-fns";

export function formatTimeDistance(
  date: Date | number | string,
  suffix?: string,
) {
  if (!date) return "";

  if (typeof date === "string" && !isNaN(+date)) {
    date = +date;
  }

  if (!(date instanceof Date)) {
    date = new Date(date);
  }

  const now = new Date();
  const weekAgo = sub(now, {
    days: 7,
  });

  const comparedToLastWeek = compareDesc(weekAgo, date);

  // format distance to now if it's within the last week
  if (comparedToLastWeek === 1) {
    return (
      formatDistanceToNowStrict(date)
        .replace("hours", "hrs")
        .replace("hour", "hr")
        .replace("minutes", "mins")
        .replace("minute", "min")
        .replace("seconds", "secs")
        .replace("second", "sec") + (suffix ? ` ${suffix}` : "")
    );
  }

  // otherwise, format the date
  return format(date, "MM/dd/yy");
}

export function formatTime(date: Date | number | string, formatStr: string) {
  if (!date) return "";

  if (typeof date === "string" && !isNaN(+date)) {
    date = +date;
  }

  if (!(date instanceof Date)) {
    date = new Date(date);
  }

  return format(date, formatStr);
}
