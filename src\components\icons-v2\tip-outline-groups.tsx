import { ComponentProps } from "react";

export const TipPlusOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M9.1875 3.45073C10.0869 3.15483 11.0355 3 12 3C14.3869 3 16.6761 3.94821 18.364 5.63604C20.0518 7.32387 21 9.61305 21 12C21 13.1819 20.7672 14.3522 20.3149 15.4442C19.8626 16.5361 19.1997 17.5282 18.364 18.364C17.5282 19.1997 16.5361 19.8626 15.4442 20.3149C14.3522 20.7672 13.1819 21 12 21C10.8181 21 9.64778 20.7672 8.55585 20.3149C7.46392 19.8626 6.47177 19.1997 5.63604 18.364C4.80031 17.5282 4.13738 16.5361 3.68508 15.4442C3.23279 14.3522 3 13.1819 3 12C3 11.1685 3.11506 10.3489 3.33636 9.56243"
      stroke="#F4F4F4"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.0047 8.4C10.5134 8.4 9.30469 9.2055 9.30469 10.2C9.30469 11.1945 10.5134 12 12.0047 12C13.496 12 14.7047 12.8055 14.7047 13.8C14.7047 14.7945 13.496 15.6 12.0047 15.6M12.0047 8.4V15.6M12.0047 8.4C13.0037 8.4 13.8767 8.7618 14.3438 9.3M12.0047 8.4V7.5M12.0047 15.6V16.5M12.0047 15.6C11.0057 15.6 10.1327 15.2382 9.66559 14.7"
      stroke="#F4F4F4"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.47461 4.125V8.8125"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M8.8125 6.46875L4.125 6.46875"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);
