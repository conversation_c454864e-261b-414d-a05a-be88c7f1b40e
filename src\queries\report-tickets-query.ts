import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import {
  getGroupedTickets,
  getReportTicketById,
  getReportTickets,
  getUnseenCount,
} from "../api/client/reports";

export const useReportTicketsInfiniteQuery = (
  status: "Active" | "Closed",
  searchString?: string,
) => {
  return useInfiniteQuery({
    queryKey: ["report-tickets", status, searchString],
    queryFn: ({ pageParam }) =>
      getReportTickets({
        status,
        searchString,
        page: pageParam.page,
        pageSize: pageParam.pageSize,
      }),
    initialPageParam: {
      page: 1,
      pageSize: 20,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (lastPage.numberOfPages > lastPageParam.page) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }
      return undefined;
    },
    staleTime: 60 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
};

export const useReportTicketQuery = (ticketId: number) => {
  return useQuery({
    queryKey: ["report-ticket", ticketId],
    queryFn: () => getReportTicketById(ticketId),
    enabled: !!ticketId,
  });
};

export const useGroupedTicketsInfiniteQuery = (
  status: "Active" | "Closed",
  sortBy: "date" | "count" | "score" | "live" = "date",
  searchString?: string,
) => {
  return useInfiniteQuery({
    queryKey: ["grouped-tickets", status, sortBy, searchString],
    queryFn: ({ pageParam }) =>
      getGroupedTickets({
        status,
        sortBy,
        searchString,
        page: pageParam.page,
        pageSize: pageParam.pageSize,
      }),
    initialPageParam: {
      page: 1,
      pageSize: 20,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const totalPages = Math.ceil(lastPage.count / lastPageParam.pageSize);
      if (lastPageParam.page < totalPages) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }
      return undefined;
    },
    staleTime: 60 * 60 * 1000,
  });
};

export const useUnseenCountQuery = () => {
  return useQuery({
    queryKey: ["reports", "unseen-count"],
    queryFn: async () => {
      const count = await getUnseenCount();
      return count;
    },
    staleTime: 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
  });
};
