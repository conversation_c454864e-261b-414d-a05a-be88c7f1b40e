import * as React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useState,
} from "react";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import {
  Address,
  encodeFunctionData,
  formatEther,
  Hex,
  parseUnits,
  PublicClient,
  WalletClient,
} from "viem";

import { RestakeSubmitModal } from "@/app/(wallet)/wallet/token-portal/_components/restake-submit-modal";
import { LargeBanner } from "@/components/banner";
import { LogoIcon } from "@/components/icons";
import { LocatedBigNumber, LocatedNumber } from "@/components/located-number";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { TextInput } from "@/components/ui/text-input";
import {
  stakingContractABI,
  staking<PERSON>ontractAddress,
  tokenContractABI,
  wAVAXContractAddress,
  wrappedAvaxContractAbi,
} from "@/environments/stakingABI";
import { ARENA, swapTokens } from "@/environments/tokens";
import { useSwap } from "@/hooks/use-swap";
import { useAPY, useRestakeAgreed } from "@/queries/token-portal-queries";
import { cn } from "@/utils";

import { ArenaTokensStakingInfoModal } from "./token-info";

interface Reward {
  reward: string;
  icon: string;
  token: string;
  contractValue: bigint;
}

export const StakeTab: React.FC<StakeTabContentProps> = () => {
  return <StakeTabContent />;
};

interface StakeInfoProps {
  userTotalStake?: string;
  rewards: Reward[];
  handleClaimAll?: MouseEventHandler<HTMLButtonElement>;
  handleRestakeRewards: () => Promise<void>;
  isRewardsActionsDisabled: boolean;
  isRewardsProceeding: boolean;
}

const StakeInfo = ({
  userTotalStake,
  rewards,
  handleClaimAll,
  handleRestakeRewards,
  isRewardsActionsDisabled,
  isRewardsProceeding,
}: StakeInfoProps) => {
  const { data, isLoading } = useRestakeAgreed();
  const [isRestakeSubmitModalOpen, setRestakeSubmitModalOpen] = useState(false);

  return (
    <div className="mb-4 flex flex-col rounded-xl border border-gray-text bg-transparent p-4">
      <div className="mb-4 flex items-center justify-between text-sm font-semibold">
        <div>Your Stake</div>
        <div className="flex items-center">
          <img
            src={ARENA.icon}
            className="mr-0.5 size-4 rounded-full shadow-[0px_4px_12px_0px_rgba(0,0,0,0.20)]"
            alt="Arena logo"
          />
          {LocatedNumber(userTotalStake || 0)}
        </div>
      </div>
      <div className="mb-2 text-sm font-semibold">Rewards</div>
      {rewards.length > 0 &&
        rewards.map((reward) => (
          <div
            key={reward.token}
            className="mb-2 flex items-center justify-between text-xs font-normal leading-5"
          >
            <div>{reward.token}</div>
            <div className="flex items-center text-sm">
              <img
                src={reward.icon}
                className="mr-0.5 size-4 rounded-full shadow-[0px_4px_12px_0px_rgba(0,0,0,0.20)]"
                alt="Arena logo"
              />
              {LocatedBigNumber(formatEther(reward.contractValue), 4)}
            </div>
          </div>
        ))}
      <div className="mt-1 flex gap-2">
        <Button
          onClick={handleClaimAll}
          variant={"outline"}
          className="flex flex-auto px-4 py-2 text-sm text-off-white"
          disabled={isRewardsActionsDisabled}
          loading={isRewardsProceeding}
        >
          Claim All
        </Button>
        <Button
          onClick={async () =>
            data?.isRestakeAgreed
              ? await handleRestakeRewards()
              : setRestakeSubmitModalOpen(true)
          }
          className="flex-auto px-4 py-2 text-sm text-off-white"
          disabled={isRewardsActionsDisabled}
          loading={isRewardsProceeding || isLoading}
        >
          Restake Rewards
        </Button>
      </div>
      <RestakeSubmitModal
        open={isRestakeSubmitModalOpen}
        onOpenChange={setRestakeSubmitModalOpen}
        handleRestakeRewards={handleRestakeRewards}
      />
    </div>
  );
};

interface StakeTabContentProps {
  isChampionsTab?: boolean;
  setStakedBalance?: React.Dispatch<React.SetStateAction<string>>;
}

export const StakeTabContent: React.FC<StakeTabContentProps> = ({
  isChampionsTab = false,
  setStakedBalance,
}) => {
  const [unstakeAmount, setUnstakeAmount] = useState("");
  const [stakeAmount, setStakeAmount] = useState("");
  const [isStaking, setIsStaking] = useState(false);
  const [isUnstaking, setIsUnstaking] = useState(false);
  const [totalStaked, setTotalStaked] = useState("0");
  const [userTotalStake, setUserTotalStake] = useState<string>("0");
  const [rewards, setRewards] = useState<Reward[]>([
    {
      token: "ARENA",
      icon: ARENA.icon,
      reward: "0",
      contractValue: 0n,
    },

    {
      token: "AVAX",
      icon: "/assets/coins/avax.png",
      reward: "0",
      contractValue: 0n,
    },
  ]);
  const [isRewardsProceeding, setIsRewardsProceeding] = useState(false);
  const [userTokenBalance, setUserTokenBalance] = useState<string>("0");

  const { primaryWallet } = useDynamicContext();
  const { data, isLoading: isAPYLoading } = useAPY();

  const { getRate, buildSwap } = useSwap();

  const isWalletReady = () => {
    if (!primaryWallet) {
      console.error("Wallet is not initialized");
      return false;
    }
    if (!isEthereumWallet(primaryWallet)) {
      console.error("This wallet is not an Ethereum wallet");
      return false;
    }
    return true;
  };

  const getClients = async () => {
    if (!primaryWallet) {
      throw new Error("Wallet is not connected");
    }
    if (!isEthereumWallet(primaryWallet)) {
      throw new Error("This wallet is not a Ethereum wallet");
    }
    const publicClient = await primaryWallet.getPublicClient();
    const walletClient = await primaryWallet.getWalletClient();
    return {
      publicClient,
      walletClient,
      account: primaryWallet.address as Address,
    };
  };

  const fetchStakingData = async () => {
    if (!isWalletReady()) return;
    const { publicClient, account } = await getClients();

    const internalArenaBalance = await publicClient.readContract({
      address: stakingContractAddress as Address,
      abi: stakingContractABI,
      functionName: "internalArenaBalance",
    });
    setTotalStaked(formatEther(internalArenaBalance));

    const userTokenBalance = await publicClient.readContract({
      address: ARENA.address as Address,
      abi: tokenContractABI,
      functionName: "balanceOf",
      args: [account],
    });
    setUserTokenBalance(formatEther(userTokenBalance));

    const [stake] = await publicClient.readContract({
      address: stakingContractAddress as Address,
      abi: stakingContractABI,
      functionName: "getUserInfo",
      args: [account, ARENA.address as Address],
    });
    setUserTotalStake(formatEther(stake));
    if (isChampionsTab && setStakedBalance) {
      setStakedBalance(stake.toString());
    }

    const rewardArenaToken = await publicClient.readContract({
      address: stakingContractAddress as Address,
      abi: stakingContractABI,
      functionName: "pendingReward",
      args: [account, ARENA.address as Address],
    });
    const rewardWavaxToken = await publicClient.readContract({
      address: stakingContractAddress as Address,
      abi: stakingContractABI,
      functionName: "pendingReward",
      args: [account, wAVAXContractAddress],
    });
    setRewards([
      {
        token: "ARENA",
        icon: ARENA.icon,
        reward: LocatedNumber(formatEther(rewardArenaToken)),
        contractValue: rewardArenaToken,
      },
      {
        token: "AVAX",
        icon: "/assets/coins/avax.png",
        reward: LocatedNumber(formatEther(rewardWavaxToken)),
        contractValue: rewardWavaxToken,
      },
    ]);
  };

  const approveIfNeeded = async (
    publicClient: PublicClient,
    walletClient: WalletClient,
    tokenAddress: Address,
    ownerAddress: Address,
    spenderAddress: Address,
    amount: bigint,
  ) => {
    const allowance = await publicClient.readContract({
      address: tokenAddress,
      abi: tokenContractABI,
      functionName: "allowance",
      args: [ownerAddress, spenderAddress],
    });

    if (allowance >= amount) {
      return;
    }

    const approvalTx = await walletClient.writeContract({
      address: tokenAddress,
      chain: walletClient.chain,
      abi: tokenContractABI,
      functionName: "approve",
      args: [spenderAddress, amount],
      account: ownerAddress,
    });
    await publicClient.waitForTransactionReceipt({
      hash: approvalTx,
    });
  };

  const stake = async (
    publicClient: PublicClient,
    walletClient: WalletClient,
    account: Address,
    amount: bigint,
  ) => {
    await approveIfNeeded(
      publicClient,
      walletClient,
      ARENA.address as Address,
      account,
      stakingContractAddress,
      amount,
    );

    const { request: depositRequest } = await publicClient.simulateContract({
      address: stakingContractAddress as Address,
      abi: stakingContractABI,
      functionName: "deposit",
      args: [amount],
      account,
    });
    const depositTx = await walletClient.writeContract(depositRequest);
    await publicClient.waitForTransactionReceipt({
      hash: depositTx,
    });
  };

  const claimAll = async (
    publicClient: PublicClient,
    walletClient: WalletClient,
    account: Address,
  ) => {
    const { request: claimAllRequest } = await publicClient.simulateContract({
      address: stakingContractAddress as Address,
      abi: stakingContractABI,
      functionName: "withdraw",
      args: [BigInt(0)],
      account,
    });
    const claimAllTx = await walletClient.writeContract(claimAllRequest);
    await publicClient.waitForTransactionReceipt({
      hash: claimAllTx,
    });

    const totalWavaxBalance = await publicClient.readContract({
      address: wAVAXContractAddress as Address,
      abi: wrappedAvaxContractAbi,
      functionName: "balanceOf",
      args: [account],
    });
    if (totalWavaxBalance > BigInt(0)) {
      const withdrawWavaxTx = await walletClient.sendTransaction({
        account,
        chain: walletClient.chain,
        to: wAVAXContractAddress as Address,
        data: encodeFunctionData({
          abi: wrappedAvaxContractAbi,
          functionName: "withdraw",
          args: [totalWavaxBalance],
        }),
      });
      await publicClient.waitForTransactionReceipt({
        hash: withdrawWavaxTx,
      });
    }
  };

  const buyArenaToken = async (
    publicClient: PublicClient,
    walletClient: WalletClient,
    account: Address,
    amount: bigint,
  ) => {
    const payToken = swapTokens["AVAX"];
    const buyToken = swapTokens["ARENA"];
    const rate = await getRate({
      srcToken: payToken,
      destToken: buyToken,
      srcAmount: amount.toString(),
    });

    const res = await buildSwap({
      srcToken: payToken,
      destToken: buyToken,
      srcAmount: amount.toString(),
      priceRoute: rate,
      userAddress: account,
    });

    const buyArenaTx = await walletClient.sendTransaction({
      account,
      chain: walletClient.chain,
      to: res.to as Hex,
      value: res.value as any,
      data: res.data as any,
      gas: res.gas as any,
      gasPrice: res.gasPrice as any,
    });
    await publicClient.waitForTransactionReceipt({
      hash: buyArenaTx,
      confirmations: 1,
    });

    return BigInt(rate.destAmount);
  };

  const handleAction = async (
    action: (...args: any[]) => Promise<void>,
    actionName: string,
    setIsProcessing: (value: boolean) => void,
  ) => {
    if (!isWalletReady()) return;
    setIsProcessing(true);
    try {
      await action();
      toast.green(`${actionName} successful!`);
    } catch (error) {
      console.error(`${actionName} error:`, error);
      toast.red(
        `${actionName} failed${
          error instanceof Error ? `: ${error.message}` : ". Please try again."
        } `,
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const handleStake = useCallback(async () => {
    if (!stakeAmount) {
      console.error("Stake amount is missing");
      return;
    }

    await handleAction(
      async () => {
        const { publicClient, walletClient, account } = await getClients();

        await stake(
          publicClient,
          walletClient,
          account,
          parseUnits(stakeAmount, 18),
        );
        setStakeAmount("");
      },
      "Staking",
      setIsStaking,
    );
  }, [stakeAmount]);

  const handleUnstake = useCallback(async () => {
    if (!unstakeAmount) {
      console.error("Unstake amount is missing");
      return;
    }

    await handleAction(
      async () => {
        const { publicClient, walletClient, account } = await getClients();

        const { request: unstakeRequest } = await publicClient.simulateContract(
          {
            address: stakingContractAddress as Address,
            abi: stakingContractABI,
            functionName: "withdraw",
            args: [parseUnits(unstakeAmount, 18)],
            account,
          },
        );
        const unstakeTx = await walletClient.writeContract(unstakeRequest);
        await publicClient.waitForTransactionReceipt({
          hash: unstakeTx,
        });

        setUnstakeAmount("");
      },
      "Unstaking",
      setIsUnstaking,
    );
  }, [unstakeAmount]);

  const handleClaimAll = useCallback(async () => {
    await handleAction(
      async () => {
        const { publicClient, walletClient, account } = await getClients();

        await claimAll(publicClient, walletClient, account);
      },
      "Claim All",
      setIsRewardsProceeding,
    );
  }, [rewards]);

  const handleRestakeRewards = useCallback(async () => {
    await handleAction(
      async () => {
        const { publicClient, walletClient, account } = await getClients();

        const arenaReward = rewards.find((reward) => reward.token === "ARENA");
        const avaxReward = rewards.find((reward) => reward.token === "AVAX");
        let stakeValue = arenaReward?.contractValue || 0n;

        await claimAll(publicClient, walletClient, account);

        if (avaxReward && avaxReward.contractValue > 0) {
          const boughtAmount = await buyArenaToken(
            publicClient,
            walletClient,
            account,
            avaxReward.contractValue,
          );

          stakeValue += boughtAmount;
        }

        await stake(publicClient, walletClient, account, stakeValue);
      },
      "Restake Rewards",
      setIsRewardsProceeding,
    );
  }, [rewards]);

  const handleSetMaxStakeAmount = () => {
    if (isStaking) return;
    setStakeAmount(userTokenBalance);
  };

  const handleSetMaxUnstakeAmount = () => {
    if (isUnstaking) return;
    setUnstakeAmount(userTotalStake);
  };

  const isStakeButtonDisabled = useMemo(
    () => !(Number(stakeAmount) > 0) || isStaking,
    [stakeAmount, isStaking],
  );
  const isUnstakeButtonDisabled = useMemo(
    () => !(Number(unstakeAmount) > 0) || isUnstaking,
    [unstakeAmount, isUnstaking],
  );
  const isRewardsActionsDisabled = useMemo(
    () =>
      !rewards.some((reward) => reward.contractValue > 0n) ||
      isRewardsProceeding,
    [rewards, userTotalStake, isRewardsProceeding],
  );

  const renderStakeContent = useCallback(
    () => (
      <div className={cn("flex flex-col", !isChampionsTab && "mb-4")}>
        <div className="mb-2 flex gap-1">
          <label className="text-sm text-light-gray-text">Balance:</label>
          <label className="text-sm text-off-white">
            {LocatedNumber(userTokenBalance)}
          </label>
        </div>
        <div className="relative">
          <TextInput
            placeholder="Enter amount"
            value={stakeAmount}
            onChange={(e) => setStakeAmount(e.target.value)}
            type="number"
            step="0.01"
            min="0"
            className={cn(
              "appearance-none",
              isChampionsTab
                ? "border-none bg-chat-bubble"
                : "border-gray-text",
            )}
            disabled={isStaking}
          />
          <div
            className="absolute right-4 top-1/2 -translate-y-1/2 cursor-pointer"
            onClick={handleSetMaxStakeAmount}
          >
            <span className="text-xs underline">Max</span>
          </div>
        </div>
        <Button
          className={cn(
            "mt-4 bg-brand-orange text-off-white",
            !isChampionsTab && "mb-8",
          )}
          onClick={handleStake}
          disabled={isStakeButtonDisabled}
        >
          {isStaking ? "Staking..." : "Stake ARENA"}
        </Button>
      </div>
    ),
    [stakeAmount, isStaking, isStakeButtonDisabled, userTokenBalance],
  );

  const renderUnstakeContent = useCallback(
    () => (
      <div className="mb-4 flex flex-col">
        <div className="mb-2 flex gap-1">
          <label className="text-sm text-light-gray-text">
            Staked Balance:
          </label>
          <label className="text-sm text-off-white">
            {LocatedNumber(userTotalStake)}
          </label>
        </div>
        <div className="relative">
          <TextInput
            placeholder="Enter amount"
            value={unstakeAmount}
            onChange={(e) => setUnstakeAmount(e.target.value)}
            type="number"
            step="0.01"
            min="0"
            className="appearance-none border-gray-text"
            disabled={isUnstaking}
          />
          <div
            className="absolute right-4 top-1/2 -translate-y-1/2 cursor-pointer"
            onClick={handleSetMaxUnstakeAmount}
          >
            <span className="text-xs underline">Max</span>
          </div>
        </div>
        <Button
          className={`mb-8 mt-4 ${unstakeAmount ? "bg-brand-orange" : "bg-orange-gradient"} text-off-white`}
          onClick={handleUnstake}
          disabled={isUnstakeButtonDisabled}
        >
          {isUnstaking ? "Unstaking..." : "Unstake ARENA"}
        </Button>
      </div>
    ),
    [unstakeAmount, isUnstaking, isUnstakeButtonDisabled, userTotalStake],
  );

  useEffect(() => {
    void fetchStakingData();
  }, [primaryWallet, isStaking, isUnstaking, isRewardsProceeding]);

  if (isChampionsTab) {
    return <div>{renderStakeContent()}</div>;
  }

  return (
    <div className="relative mb-9 ml-0 mt-0 flex flex-col overflow-y-hidden px-2">
      <LargeBanner />
      <div className="mb-4 rounded-xl bg-purple-gradient p-4 pt-4 text-off-white">
        <div className="mb-2 text-xs leading-5">Total Staked</div>
        <div className="flex items-center">
          <LogoIcon className="mr-2 h-[22px] w-[22px] rounded-full border border-brand-orange bg-orange-gradient p-0.5 text-white" />
          <div className="text-2xl font-semibold leading-5">
            {LocatedNumber(totalStaked)}
          </div>
        </div>
      </div>
      <div className="mb-4 flex items-center justify-between gap-9 rounded-[10px] border border-gray-text px-4 py-2">
        <div className="flex flex-col gap-1 font-semibold leading-5 text-off-white">
          <span className="text-xs">APY</span>
          {!isAPYLoading && data?.apy ? (
            <span className="text-2xl">{LocatedNumber(data.apy, 0)}%</span>
          ) : (
            <span className="text-2xl">TBA</span>
          )}
        </div>
      </div>
      <Tabs defaultValue="stake" className="flex flex-grow flex-col">
        <TabsList className="mb-4 flex px-0">
          <TabsTrigger
            value="stake"
            className="relative flex-1 p-2 pb-3 text-sm font-light text-off-white opacity-50 data-[state=active]:font-medium data-[state=active]:text-off-white data-[state=active]:opacity-100 data-[state=active]:after:bg-purple-gradient"
          >
            Stake
          </TabsTrigger>
          <TabsTrigger
            value="unstake"
            className="relative flex-1 p-2 pb-3 text-sm font-light text-off-white opacity-50 data-[state=active]:font-medium data-[state=active]:text-off-white data-[state=active]:opacity-100 data-[state=active]:after:bg-purple-gradient"
          >
            Unstake
          </TabsTrigger>
        </TabsList>
        <TabsContent value="stake" className="flex flex-col">
          {renderStakeContent()}
        </TabsContent>
        <TabsContent value="unstake" className="flex flex-col">
          {renderUnstakeContent()}
        </TabsContent>
      </Tabs>
      <StakeInfo
        userTotalStake={userTotalStake}
        rewards={rewards}
        handleClaimAll={handleClaimAll}
        handleRestakeRewards={handleRestakeRewards}
        isRewardsActionsDisabled={isRewardsActionsDisabled}
        isRewardsProceeding={isRewardsProceeding}
      />
      <ArenaTokensStakingInfoModal />
    </div>
  );
};
