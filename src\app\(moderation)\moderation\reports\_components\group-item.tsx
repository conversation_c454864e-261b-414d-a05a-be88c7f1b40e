"use client";

import { FC } from "react";

import { format } from "date-fns";

import { GroupedTicket } from "@/queries/types/report-tickets";
import { cn } from "@/utils";

interface GroupItemProps {
  group: GroupedTicket;
  isSelected?: boolean;
  onClick?: () => void;
}

export const GroupItem: FC<GroupItemProps> = ({
  group,
  isSelected = false,
  onClick,
}) => {
  const {
    reportedUser,
    lastReportTimestamp,
    reportCount,
    type,
    content,
    score,
  } = group;

  return (
    <div
      className={cn(
        "group relative flex cursor-pointer items-start gap-3 rounded-[10px] p-6",
        "hover:bg-accent/50 transition-colors",
        isSelected && "bg-[#111] bg-opacity-[0.88]",
      )}
      onClick={onClick}
    >
      {group.liveStatus.isLive && (
        <div className="absolute left-3 top-8 h-1 w-1 -translate-y-1/2 rounded-full bg-red-600 " />
      )}
      {group.tickets[0].status === "Active" && (
        <div
          className={cn(
            "mt-[3px] rounded p-2",
            score === 1 && "bg-[#28A745]",
            score === 2 && "bg-[#FFC107]",
            score === 4 && "bg-[#DC3545]",
          )}
        ></div>
      )}
      <div className="flex min-w-0 flex-1 justify-between gap-2">
        <div className="flex min-w-0 flex-1 flex-col gap-2">
          <div className="flex min-w-0 items-center gap-1 text-sm">
            <div className="overflow-hidden text-ellipsis whitespace-nowrap">
              Reported User: @{reportedUser.twitterHandle}
            </div>
            {reportCount > 1 && (
              <div className="flex-shrink-0 rounded bg-dark-gray px-[3px] py-1 text-xs font-semibold leading-none text-off-white">
                {reportCount}
              </div>
            )}
          </div>
          <div className="max-w-[252px] overflow-hidden text-ellipsis whitespace-nowrap text-sm text-gray-text">
            {reportCount === 1
              ? `${type} #${group.tickets[0].id} - ${content}`
              : `Multiple Reports - ${group.tickets.map((t) => `#${t.id}`).join(", ")}`}
          </div>
        </div>

        <div className="flex-shrink-0 text-xs text-gray-text">
          <div className="hidden sm:block">
            {format(new Date(lastReportTimestamp), "MM/dd/yyyy - hh:mm a")}
          </div>
          <div className="flex flex-col items-end sm:hidden">
            <div>{format(new Date(lastReportTimestamp), "MM/dd/yyyy")}</div>
            <div>{format(new Date(lastReportTimestamp), "hh:mm a")}</div>
          </div>
        </div>
      </div>
      {isSelected && (
        <div className="absolute inset-y-0 right-0 w-1.5 rounded-r-[10px] bg-brand-orange" />
      )}
    </div>
  );
};
