"use client";

import { useEffect, useMemo, useRef } from "react";

import ReactCanvasConfetti from "react-canvas-confetti";
import { TCanvasConfettiInstance } from "react-canvas-confetti/dist/types";

import { useStageStore } from "@/stores/stage";

import { useDataChannelsContext } from "./stores/data-channels-context";

export function TipPartyConfetti() {
  const { chatMessages } = useDataChannelsContext().chat;
  const instance = useRef<TCanvasConfettiInstance>();
  const lastConfettiId = useStageStore((state) => state.lastConfettiId);

  const lastMessage = useMemo(() => {
    if (chatMessages.length === 0) {
      return null;
    }

    return chatMessages[chatMessages.length - 1];
  }, [chatMessages]);

  const fire = () => {
    instance.current?.({
      particleCount: 250,
      gravity: 1.5,
      origin: { x: 0.5, y: 1 },
      startVelocity: 80,
    });
  };

  useEffect(() => {
    let timoutRef: NodeJS.Timeout;
    if (lastMessage && lastMessage.message) {
      const messageData = JSON.parse(lastMessage.message);
      if (
        messageData.type === "tip-party" &&
        lastConfettiId.current !== lastMessage.id
      ) {
        fire();
        lastConfettiId.current = lastMessage.id;
      }
    }

    return () => {
      clearTimeout(timoutRef);
    };
  }, [lastMessage]);

  return (
    <ReactCanvasConfetti
      onInit={({ confetti }) => {
        instance.current = confetti;
      }}
      style={{
        position: "absolute",
        pointerEvents: "none",
        width: "100%",
        height: "100%",
        top: 0,
        left: 0,
        zIndex: 100,
      }}
    />
  );
}
