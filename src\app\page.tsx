"use client";

import { ComponentProps, useState } from "react";

import { useSocialAccounts } from "@dynamic-labs/sdk-react-core";
import { ProviderEnum } from "@dynamic-labs/types";

import { EmailLogin } from "@/components/email-login/email-login.component";
import { ProgressBarLink } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";

import { PWAInstruction } from "./_components/pwa-instruction";

export default function LoginPage() {
  const { error, isProcessing, signInWithSocialAccount } = useSocialAccounts();
  const [emailLoginVisible, setEmailLoginVisible] = useState(false);

  return (
    <main className="w-full sm:mx-auto sm:flex sm:justify-end">
      <div className="relative hidden h-screen flex-grow brightness-[0.35] sm:block sm:w-1/3">
        <img
          src="/images/hero-2.jpeg"
          alt=""
          className="absolute inset-0 h-full w-full -scale-x-100 transform object-cover object-left"
        />
        <ArenaLogo className="absolute left-12 top-12 hidden text-light-gray-text sm:inline-block" />
      </div>
      <div className="flex min-h-[100svh] flex-col pb-8 sm:w-2/3 sm:items-center sm:justify-center sm:gap-20">
        <div className="relative mt-[71px] h-[238px] w-full flex-shrink-0 overflow-hidden sm:mt-0 sm:h-auto sm:w-auto ">
          <div className="absolute left-1/2 flex w-full translate-x-[-50%] items-end justify-center gap-6 sm:relative">
            <div className="relative h-[196px] w-[121px] flex-shrink-0 overflow-hidden rounded-t-full brightness-75 sm:hidden">
              <img
                src="/images/hero-2.jpeg"
                alt=""
                className="absolute h-full w-full object-cover object-[65%]"
              />
            </div>
            <img
              src="/icons/logo.svg"
              alt="arena logo"
              className="h-[238px] w-[182px] flex-shrink-0 lg:h-[264px] lg:w-[202px]"
            />
            <div className="relative h-[196px] w-[121px] flex-shrink-0 overflow-hidden rounded-t-full brightness-75 sm:hidden">
              <img
                src="/images/hero-3.jpeg"
                alt=""
                className="absolute h-full w-full object-cover object-[10%]"
              />
            </div>
          </div>
        </div>
        <div className="mt-14 flex h-full w-full flex-col items-center justify-start sm:mt-0 sm:h-auto sm:w-auto">
          <div className="flex flex-col items-center">
            <div className="text-base text-light-gray-text lg:text-lg">
              A Next Gen SocialFi Experience
            </div>
            <p className="mt-5 flex flex-col gap-2 text-center text-4xl font-semibold text-off-white lg:text-5xl">
              <span>Connect.</span>
              <span>Engage.</span>
              <span>Monetize.</span>
            </p>
          </div>
          <div className="mt-14 flex w-full flex-col gap-[16px] px-7 sm:pb-0">
            <Button
              className="w-full gap-2 bg-gray-gradient sm:px-12"
              variant="secondary"
              onClick={() => signInWithSocialAccount(ProviderEnum.Twitter)}
            >
              <span>Enter The Arena With</span>
              <img
                src={"/icons/twitter.svg"}
                alt="X logo"
                width={28}
                height={24}
                className="sm:h-auto sm:w-5"
              />
            </Button>
            <p className=" text-center text-xs text-gray-text">
              Can&#39;t access your X account?{" "}
              <span
                className="font-inter decoration-skip-ink-none underline-from-font cursor-pointer text-[12px] font-normal leading-normal text-[var(--OFF-WHITE,#F4F4F4)] underline decoration-solid decoration-auto  underline-offset-auto"
                onClick={() => {
                  setEmailLoginVisible(true);
                }}
              >
                Log-in using email
              </span>
              .
            </p>
          </div>
          <div className="mt-6 px-7">
            <p className=" text-center text-xs text-gray-text">
              By clicking on &quot;Enter The Arena With X&quot; <br /> you agree
              to our{" "}
              <ProgressBarLink href="/terms-of-use" className="text-off-white">
                terms of use
              </ProgressBarLink>{" "}
              and{" "}
              <ProgressBarLink
                href="/privacy-policy"
                className="text-off-white"
              >
                privacy policy
              </ProgressBarLink>
              .
            </p>
          </div>
        </div>
      </div>
      <PWAInstruction />
      {emailLoginVisible && (
        <div>
          <EmailLogin
            visible={emailLoginVisible}
            setVisible={setEmailLoginVisible}
          />
        </div>
      )}
    </main>
  );
}

const ArenaLogo = (props: ComponentProps<"svg">) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="71"
      height="93"
      viewBox="0 0 71 93"
      fill="none"
      {...props}
    >
      <path
        d="M29.9561 71.5781H22.5965L22.1616 72.6707H24.8676L23.0351 78.5054H25.8265L27.239 72.6707H29.7777L29.9561 71.5781Z"
        fill="currentColor"
      />
      <path
        d="M38.8088 71.5781H36.5154L36.6232 74.0792H33.5828L33.6943 71.5781H31.4604L30.6538 78.5054H33.3783L33.5233 75.3874H36.6158L36.7459 78.5054H39.6117L38.8088 71.5781Z"
        fill="currentColor"
      />
      <path
        d="M47.6855 71.5781H40.3408L41.4819 78.5054H50.4323L49.8228 76.9594H43.8385L43.4593 75.3428H47.9717L47.4997 74.0383H43.1545L42.8312 72.6707H48.1204L47.6855 71.5781Z"
        fill="currentColor"
      />
      <path
        d="M7.64271 85.6225L9.11834 83.1697C9.22242 82.9988 9.37481 82.8353 9.55694 82.6903C9.73907 82.5454 9.95837 82.4153 10.1925 82.3075C10.4267 82.1998 10.6832 82.1143 10.9434 82.0511C11.2036 81.9917 11.4675 81.9582 11.7276 81.9582C11.9878 81.9582 12.2108 81.9917 12.4041 82.0511C12.5974 82.1106 12.7535 82.1961 12.865 82.3075C12.9803 82.4153 13.0509 82.5454 13.0695 82.6903C13.0918 82.8353 13.062 82.9988 12.9728 83.1697L11.7128 85.6225H7.64271ZM12.8873 79.9365C12.248 79.9365 11.5901 80.0146 10.9471 80.1558C10.3003 80.297 9.66473 80.5051 9.07374 80.7653C8.47531 81.0291 7.91405 81.345 7.43457 81.7055C6.94393 82.0697 6.53135 82.4785 6.23771 82.917L0.0712891 92.0889H3.85514L6.40497 87.8375H10.5865L8.40841 92.0889H12.1848L16.2214 82.917C16.4147 82.4785 16.4556 82.0697 16.3738 81.7055C16.2921 81.3487 16.0876 81.0291 15.7791 80.7653C15.4743 80.5051 15.0729 80.297 14.5823 80.1558C14.0991 80.0146 13.5266 79.9365 12.8873 79.9365Z"
        fill="currentColor"
      />
      <path
        d="M58.4796 85.6225L57.2195 83.1549C57.1303 82.9839 57.1043 82.8204 57.1266 82.6755C57.1489 82.5305 57.2195 82.4005 57.3347 82.2927C57.45 82.1849 57.6061 82.0994 57.7994 82.04C57.9926 81.9805 58.2194 81.9471 58.4796 81.9471C58.7397 81.9471 58.9999 81.9805 59.2601 82.04C59.5203 82.0994 59.7731 82.1849 60.0072 82.2927C60.2414 82.4005 60.457 82.5305 60.6428 82.6755C60.8287 82.8204 60.9773 82.9839 61.0814 83.1549L62.5645 85.6225H58.4796ZM57.3942 79.9365C56.7549 79.9365 56.1825 80.0146 55.6993 80.1558C55.2124 80.297 54.8072 80.5051 54.5024 80.7653C54.1939 81.0291 53.9895 81.345 53.9077 81.7055C53.8222 82.0697 53.8668 82.4785 54.0601 82.917L58.0967 92.0889H61.8806L59.6987 87.8375H63.8803L66.4301 92.0889H70.2065L64.0401 82.917C63.7465 82.4785 63.3339 82.0697 62.8433 81.7055C62.3601 81.3487 61.8025 81.0291 61.2041 80.7653C60.6131 80.5051 59.9775 80.297 59.3307 80.1558C58.6914 80.0146 58.0372 79.9365 57.3942 79.9365Z"
        fill="currentColor"
      />
      <path
        d="M20.8938 84.2395L21.6298 81.9577H24.4621C24.6033 81.9577 24.7223 81.9688 24.8226 81.9874C24.923 82.006 25.0048 82.0357 25.0642 82.0766C25.1237 82.1175 25.1646 82.1695 25.1832 82.229C25.2018 82.2921 25.2018 82.3627 25.1832 82.4482L24.9007 83.6523C24.8784 83.7489 24.8338 83.8344 24.7706 83.905C24.7074 83.9794 24.6256 84.0388 24.5216 84.0908C24.4175 84.1392 24.2948 84.1763 24.1499 84.2023C24.0049 84.2283 23.8414 84.2395 23.6592 84.2395H20.8938ZM26.2351 80.1738H19.3587L14.6865 92.0884H18.3663L20.1727 86.473H23.1835C23.373 86.473 23.5329 86.4879 23.6667 86.5213C23.8005 86.5548 23.9083 86.6068 23.9863 86.6737C24.0681 86.7406 24.1201 86.8261 24.1424 86.9264C24.1685 87.0305 24.1647 87.1494 24.1313 87.2869L23.0162 92.0847H26.7926L27.6438 86.7555C27.6698 86.5845 27.6735 86.4284 27.6512 86.2872C27.6289 86.146 27.5806 86.0159 27.51 85.9007C27.4394 85.7855 27.3427 85.6814 27.2201 85.5885C27.0974 85.4956 26.9525 85.4176 26.7815 85.3507C26.9785 85.2726 27.1532 85.1797 27.3093 85.0757C27.4617 84.9716 27.5955 84.8527 27.707 84.7189C27.8185 84.5888 27.9077 84.4402 27.9783 84.2841C28.049 84.128 28.0973 83.9571 28.1233 83.775L28.4801 81.5377C28.495 81.4337 28.4987 81.337 28.4801 81.2441C28.4653 81.1512 28.4318 81.0658 28.3835 80.984C28.3352 80.9022 28.2683 80.8242 28.1865 80.7536C28.1047 80.683 28.0044 80.6161 27.8891 80.5529C27.7739 80.4897 27.6512 80.434 27.5249 80.3894C27.3985 80.3411 27.2647 80.3039 27.1272 80.2742C26.9896 80.2444 26.8447 80.2184 26.696 80.2036C26.5547 80.1813 26.3986 80.1738 26.2351 80.1738Z"
        fill="currentColor"
      />
      <path
        d="M39.9953 80.1738H30.6546L29.3276 92.0884H41.4337L41.0992 89.3011H33.1226L33.2379 86.473H39.1664L38.9731 84.2395H33.3308L33.4237 81.9577H40.2109L39.9953 80.1738Z"
        fill="currentColor"
      />
      <path
        d="M50.9223 80.1738H47.8595L49.4801 85.3098L45.0867 80.1738H41.9272L43.9381 92.0884H47.9153L46.2872 85.451L51.6694 92.0884H55.5945L50.9223 80.1738Z"
        fill="currentColor"
      />
      <path
        d="M63.9993 29.3528V78.847H62.7467V34.3662C62.7467 19.1441 50.3692 6.76867 35.152 6.76867C19.9349 6.76867 7.54998 19.1441 7.54998 34.3662V78.8507H6.29736V29.3528C6.29736 13.4172 19.2138 0.50293 35.152 0.50293C51.0829 0.50293 63.9993 13.4172 63.9993 29.3528Z"
        fill="currentColor"
      />
      <path
        d="M58.9886 32.1772V74.1681H57.736V36.4361C57.736 23.979 47.6036 13.8482 35.1518 13.8482C22.7001 13.8482 12.5602 23.979 12.5602 36.4361V74.1681H11.3076V32.1772C11.3076 19.0102 21.9827 8.33691 35.1518 8.33691C48.3136 8.33691 58.9886 19.0102 58.9886 32.1772Z"
        fill="currentColor"
      />
      <path
        d="M53.9787 35.0018V69.4894H52.7261V38.51C52.7261 28.8178 44.8424 20.9354 35.1523 20.9354C25.4622 20.9354 17.5749 28.8178 17.5749 38.51V69.4894H16.3223V35.0018C16.3223 24.6035 24.756 16.1748 35.1523 16.1748C45.5449 16.1711 53.9787 24.6035 53.9787 35.0018Z"
        fill="currentColor"
      />
      <path
        d="M48.9648 37.8261V64.8104H47.7122V40.5836C47.7122 33.6527 42.0773 28.0187 35.1526 28.0187C28.2279 28.0187 22.5856 33.6527 22.5856 40.5836V64.8104H21.333V37.8261C21.333 30.1965 27.5217 24.0088 35.1526 24.0088C42.7761 24.0051 48.9648 30.1965 48.9648 37.8261Z"
        fill="currentColor"
      />
      <path
        d="M43.9542 40.6466V60.1276H42.7016V42.6534C42.7016 38.4874 39.3154 35.0981 35.1524 35.0981C30.9894 35.0981 27.5959 38.4837 27.5959 42.6534V60.1276H26.3433V40.6466C26.3433 35.7856 30.2907 31.8389 35.1524 31.8389C40.0068 31.8426 43.9542 35.7856 43.9542 40.6466Z"
        fill="currentColor"
      />
      <path
        d="M38.9427 43.4711V55.4489H37.6901V44.7273C37.6901 43.3225 36.549 42.1853 35.1514 42.1853C33.7539 42.1853 32.6091 43.3262 32.6091 44.7273V55.4489H31.3564V43.4711C31.3564 41.3788 33.0588 39.6768 35.1514 39.6768C37.2441 39.6768 38.9427 41.3788 38.9427 43.4711Z"
        fill="currentColor"
      />
    </svg>
  );
};
