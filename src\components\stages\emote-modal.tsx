"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";

import {
  useLocalParticipant,
  useParticipantAttribute,
  useRoomInfo,
} from "@livekit/components-react";
import { useQuery } from "@tanstack/react-query";
import { Emoji } from "emoji-picker-react";

import { useToggleRaisedHandMutation } from "@/queries";
import { stageQueries } from "@/queries/stage-queries";
import { useStageStore } from "@/stores/stage";
import { cn } from "@/utils";

import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Role } from "./constants";
import { useDataChannelsContext } from "./stores/data-channels-context";

// [ "❤️", "🔥", "💯", "👏", "🤯"], "✋", "270b",
// ["⚔️", "🤣", "🫡", "👍",  "🤡"],
const defaultEmotes = [
  ["2764-fe0f", "1f525", "1f4af", "1f44f", "1f92f"],
  ["2694-fe0f", "1f923", "1fae1", "1f44d", "1f921"],
];

export const EmoteModal = ({ children }: { children: React.ReactNode }) => {
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild>{children}</PopoverTrigger>
      <PopoverContent
        className="z-50 w-auto max-w-80 px-4"
        collisionPadding={16}
      >
        <EmoteModalContent setOpen={setOpen} />
      </PopoverContent>
    </Popover>
  );
};

const EmoteModalContent = ({
  setOpen,
}: {
  setOpen: (open: boolean) => void;
}) => {
  const currentParticipant = useLocalParticipant();
  const { sendEmote } = useDataChannelsContext();
  const [isHandRaised, setIsHandRaised] = useState(false);
  const local = useLocalParticipant();
  const role = useParticipantAttribute("role", {
    participant: local.localParticipant,
  }) as Role;
  const stageId = useStageStore((state) => state.id!);
  const roomInfo = useRoomInfo();

  const { data } = useQuery(stageQueries.emotes());

  const { mutateAsync: toggleRaisedHand } = useToggleRaisedHandMutation({
    onMutate: () => {
      setIsHandRaised((prev) => !prev);
    },
    onError: () => {
      setIsHandRaised((prev) => !prev);
    },
  });

  const emotes =
    data?.emotes && data?.emotes.length > 0 ? data?.emotes : defaultEmotes;

  const metadata = roomInfo.metadata
    ? JSON.parse(roomInfo.metadata)
    : { raisedHands: [] };

  const handleEmote = useCallback((emote: string) => {
    setOpen(false);
    sendEmote({
      emote,
      id: Date.now(),
      from: {
        id: currentParticipant.localParticipant.identity,
        name: currentParticipant.localParticipant.attributes.name,
        avatar: currentParticipant.localParticipant.attributes.avatar,
        username: currentParticipant.localParticipant.attributes.username,
      },
    });
    // const message = {
    //   type: "emote",
    //   emote,
    // };
    // chat.send(JSON.stringify(message));
  }, []);

  const handleToggleRaisedHand = useCallback(async () => {
    setOpen(false);
    await toggleRaisedHand({
      stageId,
    });
  }, [stageId]);

  useEffect(() => {
    setIsHandRaised(
      metadata?.raisedHands?.includes(local.localParticipant.identity) ?? false,
    );
  }, [roomInfo.metadata]);

  return (
    <div className="flex items-center justify-center gap-3">
      <div className="flex flex-col items-center justify-center gap-3">
        {emotes.map((row, i) => {
          return (
            <div className="flex items-center gap-3" key={i + "emote"}>
              {row.map((emote, j) => (
                <button
                  key={i + j}
                  className="flex size-8 items-center justify-center"
                  onClick={() => {
                    handleEmote(emote);
                  }}
                >
                  <Emoji unified={emote} size={32} />
                </button>
              ))}
            </div>
          );
        })}
      </div>
      {role !== "LISTENER" ? (
        <button
          className={cn(
            "h-14 w-[52px] flex-shrink-0 rounded pl-1",
            isHandRaised && "bg-dark-gray",
          )}
          onClick={handleToggleRaisedHand}
        >
          <Emoji unified="270b" size={46} />
        </button>
      ) : null}
    </div>
  );
};
