import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import {
  deleteDeclineInvitation,
  deletePinPost,
  deleteStage,
  postAcceptInvitation,
  postBlockUser,
  postCancelInvitation,
  postCreateStage,
  postDropDownToListener,
  postEditStage,
  postEndStage,
  postInvite,
  postJoinStage,
  postLeaveStage,
  postPinPost,
  postRemindStage,
  postRequestToSpeak,
  postStartStage,
  postTip,
  postToggleMute,
  postToggleRaisedHand,
  postUpdateRole,
  putUpdateEmotes,
} from "@/api/client/stage";

import {
  CreateStageParams,
  CreateStageResponse,
  DeletePinPostParams,
  DeletePinPostResponse,
  DeleteStageParams,
  DeleteStageResponse,
  EditStageResponse,
  EndStageResponse,
  JoinStageResponse,
  PostAcceptInvitationParams,
  PostAcceptInvitationResponse,
  PostBlockUserParams,
  PostBlockUserResponse,
  PostCancelInvitationParams,
  PostCancelInvitationResponse,
  PostDropDownToListenerParams,
  PostDropDownToListenerResponse,
  PostInviteParams,
  PostInviteResponse,
  PostLeaveStageParams,
  PostLeaveStageResponse,
  PostPinPostParams,
  PostPinPostResponse,
  PostRemindStageParams,
  PostRemindStageResponse,
  PostStartStageParams,
  PostStartStageResponse,
  PostTipParams,
  PostTipResponse,
  PostToggleMuteParams,
  PostToggleMuteResponse,
  PostToggleRaisedHandParams,
  PostToggleRaisedHandResponse,
  PutUpdateEmotesParams,
  PutUpdateEmotesResponse,
  RequestToSpeakResponse,
  UpdateRoleResponse,
} from "./types/stage";

type CreateStageMutationType = MutationOptions<
  CreateStageResponse,
  DefaultError,
  CreateStageParams,
  any
>;

export const useCreateStageMutation = (options?: CreateStageMutationType) => {
  return useMutation({
    mutationFn: postCreateStage,
    ...options,
  });
};

type JoinStageMutationType = MutationOptions<
  JoinStageResponse,
  DefaultError,
  any,
  any
>;

export const useJoinStageMutation = (options?: JoinStageMutationType) => {
  return useMutation({
    mutationFn: postJoinStage,
    ...options,
  });
};

type EndStageMutationType = MutationOptions<
  EndStageResponse,
  DefaultError,
  any,
  any
>;

export const useEndStageMutation = (options?: EndStageMutationType) => {
  return useMutation({
    mutationFn: postEndStage,
    ...options,
  });
};

type UpdateRoleMutationType = MutationOptions<
  UpdateRoleResponse,
  DefaultError,
  any,
  any
>;

export const useUpdateRoleMutation = (options?: UpdateRoleMutationType) => {
  return useMutation({
    mutationFn: postUpdateRole,
    ...options,
  });
};

type RequestToSpeakMutationType = MutationOptions<
  RequestToSpeakResponse,
  DefaultError,
  any,
  any
>;

export const useRequestToSpeakMutation = (
  options?: RequestToSpeakMutationType,
) => {
  return useMutation({
    mutationFn: postRequestToSpeak,
    ...options,
  });
};

type PostTipMutationType = MutationOptions<
  PostTipResponse,
  DefaultError,
  PostTipParams,
  any
>;

export const usePostTipStageMutation = (options?: PostTipMutationType) => {
  return useMutation({
    mutationFn: postTip,
    ...options,
  });
};

type InviteMutationType = MutationOptions<
  PostInviteResponse,
  DefaultError,
  PostInviteParams,
  any
>;

export const useInviteStageMutation = (options?: InviteMutationType) => {
  return useMutation({
    mutationFn: postInvite,
    ...options,
  });
};

type AcceptInvitationMutationType = MutationOptions<
  PostAcceptInvitationResponse,
  DefaultError,
  PostAcceptInvitationParams,
  any
>;

export const useAcceptInvitationMutation = (
  options?: AcceptInvitationMutationType,
) => {
  return useMutation({
    mutationFn: postAcceptInvitation,
    ...options,
  });
};

type DeclineInvitationMutationType = MutationOptions<
  PostAcceptInvitationResponse,
  DefaultError,
  PostAcceptInvitationParams,
  any
>;

export const useDeclineInvitationMutation = (
  options?: DeclineInvitationMutationType,
) => {
  return useMutation({
    mutationFn: deleteDeclineInvitation,
    ...options,
  });
};

type CancelInvitationMutationType = MutationOptions<
  PostCancelInvitationResponse,
  DefaultError,
  PostCancelInvitationParams,
  any
>;

export const useCancelInvitationMutation = (
  options?: CancelInvitationMutationType,
) => {
  return useMutation({
    mutationFn: postCancelInvitation,
    ...options,
  });
};

type LeaveStageMutationType = MutationOptions<
  PostLeaveStageResponse,
  DefaultError,
  PostLeaveStageParams,
  any
>;

export const useLeaveStageMutation = (options?: LeaveStageMutationType) => {
  return useMutation({
    mutationFn: postLeaveStage,
    ...options,
  });
};

type BlockUserMutationType = MutationOptions<
  PostBlockUserResponse,
  DefaultError,
  PostBlockUserParams,
  any
>;

export const useBlockStageUserMutation = (options?: BlockUserMutationType) => {
  return useMutation({
    mutationFn: postBlockUser,
    ...options,
  });
};

type ToggleMuteMutationType = MutationOptions<
  PostToggleMuteResponse,
  DefaultError,
  PostToggleMuteParams,
  any
>;

export const useToggleMuteMutation = (options?: ToggleMuteMutationType) => {
  return useMutation({
    mutationFn: postToggleMute,
    ...options,
  });
};

type PinPostMutationType = MutationOptions<
  PostPinPostResponse,
  DefaultError,
  PostPinPostParams,
  any
>;

export const usePinPostToStageMutation = (options?: PinPostMutationType) => {
  return useMutation({
    mutationFn: postPinPost,
    ...options,
  });
};

type StartStageMutationType = MutationOptions<
  PostStartStageResponse,
  DefaultError,
  PostStartStageParams,
  any
>;

export const useStartStageMutation = (options?: StartStageMutationType) => {
  return useMutation({
    mutationFn: postStartStage,
    ...options,
  });
};

type DeletePinPostMutationType = MutationOptions<
  DeletePinPostResponse,
  DefaultError,
  DeletePinPostParams,
  any
>;

export const useDeletePinPostFromStageMutation = (
  options?: DeletePinPostMutationType,
) => {
  return useMutation({
    mutationFn: deletePinPost,
    ...options,
  });
};

type RemindStageMutationType = MutationOptions<
  PostRemindStageResponse,
  DefaultError,
  PostRemindStageParams,
  any
>;

export const useRemindStageMutation = (options?: RemindStageMutationType) => {
  return useMutation({
    mutationFn: postRemindStage,
    ...options,
  });
};

type EditStageMutationType = MutationOptions<
  EditStageResponse,
  DefaultError,
  any,
  any
>;

export const useEditStageMutation = (options?: EditStageMutationType) => {
  return useMutation({
    mutationFn: postEditStage,
    ...options,
  });
};

type DeleteStageMutationType = MutationOptions<
  DeleteStageResponse,
  DefaultError,
  DeleteStageParams,
  any
>;

export const useDeleteStageMutation = (options?: DeleteStageMutationType) => {
  return useMutation({
    mutationFn: deleteStage,
    ...options,
  });
};

type ToggleRaisedHandMutationType = MutationOptions<
  PostToggleRaisedHandResponse,
  DefaultError,
  PostToggleRaisedHandParams,
  any
>;

export const useToggleRaisedHandMutation = (
  options?: ToggleRaisedHandMutationType,
) => {
  return useMutation({
    mutationFn: postToggleRaisedHand,
    ...options,
  });
};

type DropDownToListenerMutationType = MutationOptions<
  PostDropDownToListenerResponse,
  DefaultError,
  PostDropDownToListenerParams,
  any
>;

export const useDropDownToListenerMutation = (
  options?: DropDownToListenerMutationType,
) => {
  return useMutation({
    mutationFn: postDropDownToListener,
    ...options,
  });
};

type UpdateEmotesMutationType = MutationOptions<
  PutUpdateEmotesResponse,
  DefaultError,
  PutUpdateEmotesParams,
  any
>;

export const useUpdateEmotesMutation = (options?: UpdateEmotesMutationType) => {
  return useMutation({
    mutationFn: putUpdateEmotes,
    ...options,
  });
};
