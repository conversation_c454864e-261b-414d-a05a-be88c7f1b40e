import * as React from "react";

import { cva, VariantProps } from "class-variance-authority";

import { cn } from "@/utils";

const inputVariants = cva(
  "flex w-full p-4 pr-12 text-sm outline-none placeholder:text-gray-text focus-visible:ring-1  focus-visible:ring-offset-2 focus-visible:ring-offset-[#0F0F0F] disabled:cursor-not-allowed disabled:opacity-50 h-[52px] rounded-lg bg-transparent",
  {
    variants: {
      variant: {
        default: "border border-gray-text focus-visible:ring-off-white",
        error: "border border-danger focus-visible:ring-light-danger",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {
  onMaxClick?: () => void;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, variant, type, onMaxClick, children, ...props }, ref) => {
    if (onMaxClick) {
      return (
        <div className="relative w-full">
          <input
            type={type}
            className={cn(
              "w-full pr-16", // ensure padding for the button
              inputVariants({ variant }),
              className,
            )}
            ref={ref}
            {...props}
          />
          <div
            className={cn(
              "absolute right-2 top-1/2 -translate-y-1/2 transform text-[12px]",
              "px-2 py-1 text-[F4F4F4] underline decoration-[0.5px] underline-offset-2",
              "cursor-pointer font-extralight",
            )}
            onClick={() => onMaxClick()}
          >
            Max
          </div>
        </div>
      );
    }

    return (
      <div className="relative w-full">
        <input
          type={type}
          className={cn(inputVariants({ variant }), className)}
          ref={ref}
          {...props}
        />
        {children}
      </div>
    );
  },
);
Input.displayName = "Input";

export { Input };
