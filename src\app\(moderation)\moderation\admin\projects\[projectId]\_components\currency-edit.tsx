"use client";

import {
  FC,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useRouter } from "next/navigation";

import {
  deleteCurrency,
  SystemCurrencyCategoryEnum,
  updateCurrency,
} from "@/api/client/admin-currency";
import {
  ArrowBackOutlineIcon,
  ExclamationCircleOutlineIcon,
} from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useCurrencyByIdQuery } from "@/queries/currency-by-id-query";

import { useAdmin } from "../../../context/admin-context";
import { CurrencyForm, CurrencyFormData } from "./currency-form";

interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
}

const DeleteConfirmationDialog: FC<DeleteConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[342px] border-dark-gray bg-gray-bg px-4 py-6 shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)] sm:rounded-[10px]">
        <div className="flex justify-center">
          <div className="bg-red flex items-center justify-center rounded-full">
            <ExclamationCircleOutlineIcon className="size-6 text-red-600" />
          </div>
        </div>
        <DialogHeader className="mb-4">
          <DialogTitle className="text-center">
            You&apos;re about to delete this project
          </DialogTitle>
        </DialogHeader>
        <p className="mb-4 text-center text-sm text-gray-text">
          That means this project will be removed from the following interfaces;
          supported coins, tipping coins and social exchange.
        </p>
        <div className="flex justify-between gap-2">
          <Button variant="outline" onClick={onClose} className="w-1/3 py-2">
            Close
          </Button>
          <Button variant="default" onClick={onConfirm} className="w-2/3 py-2">
            Delete Project
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

interface CurrencyEditProps {
  projectId: string;
}

export const CurrencyEdit: FC<CurrencyEditProps> = memo(({ projectId }) => {
  const router = useRouter();
  const { setSelectedProject } = useAdmin();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const previousProjectIdRef = useRef(projectId);

  const timestamp = useMemo(() => {
    if (previousProjectIdRef.current !== projectId) {
      previousProjectIdRef.current = projectId;
      return Date.now();
    }
    return previousProjectIdRef.current === projectId ? undefined : Date.now();
  }, [projectId]);

  const queryKey = useMemo(
    () =>
      timestamp ? ["currency", projectId, timestamp] : ["currency", projectId],
    [projectId, timestamp],
  );

  const {
    data: existingProject,
    isLoading,
    refetch,
  } = useCurrencyByIdQuery(projectId, queryKey);

  useEffect(() => {
    if (previousProjectIdRef.current !== projectId) {
      refetch();
    }
  }, [projectId, refetch]);

  useEffect(() => {
    if (existingProject) {
      setSelectedProject(existingProject);
    }
  }, [existingProject, setSelectedProject]);

  const formattedProject = useMemo(() => {
    if (!existingProject) return undefined;

    return {
      name: existingProject.name,
      symbol: existingProject.symbol,
      address: existingProject.address || "",
      decimals: existingProject.decimals || 18,
      image: existingProject.image,
      systemRate: existingProject.systemRate,
      categories: {
        [SystemCurrencyCategoryEnum.SUPPORTED]:
          existingProject.categories?.includes(
            SystemCurrencyCategoryEnum.SUPPORTED,
          ) || false,
        [SystemCurrencyCategoryEnum.TIPPING]:
          existingProject.categories?.includes(
            SystemCurrencyCategoryEnum.TIPPING,
          ) || false,
        [SystemCurrencyCategoryEnum.EXCHANGE]:
          existingProject.categories?.includes(
            SystemCurrencyCategoryEnum.EXCHANGE,
          ) || false,
      },
    };
  }, [existingProject]);

  const handleSubmit = async (data: CurrencyFormData) => {
    try {
      const categoriesArray = Object.entries(data.categories)
        .filter(([, isSelected]) => isSelected)
        .map(([category]) => category as SystemCurrencyCategoryEnum);

      const submitData = {
        ...data,
        categories: categoriesArray,
      };

      await updateCurrency(projectId, submitData);
      toast.green("Project updated successfully");
      router.push("/moderation/admin");
    } catch (error) {
      toast.danger(
        error instanceof Error ? error.message : "Failed to save project",
      );
    }
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteCurrency(projectId);
      toast.green("Project removed successfully");
      router.push("/moderation/admin");
    } catch (error) {
      toast.danger(
        error instanceof Error ? error.message : "Failed to remove project",
      );
    }
  };

  const handleRemoveProject = useCallback(() => {
    setIsDeleteDialogOpen(true);
  }, []);

  if (isLoading || !existingProject) {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="flex h-screen flex-col">
      <header className="sticky top-0 z-10 flex items-center gap-4 bg-[#141414] p-4">
        <ProgressBarLink href="/moderation/admin" className="size-5">
          <ArrowBackOutlineIcon className="size-5 text-off-white" />
        </ProgressBarLink>
        <h1 className="flex-1 text-center font-semibold">Edit Project</h1>
        <div className="size-5" />
      </header>

      <div className="flex-1 overflow-y-auto p-8">
        <CurrencyForm
          defaultValues={formattedProject}
          onSubmit={handleSubmit}
          existingImage={existingProject.image}
        >
          <div className="mt-6">
            <h2 className="mb-4 text-sm font-medium uppercase">Actions</h2>
            <Button
              type="button"
              variant="destructive"
              onClick={handleRemoveProject}
              className="mb-4 w-full"
            >
              Remove Project
            </Button>
          </div>
        </CurrencyForm>
      </div>

      <DeleteConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleDeleteConfirm}
      />
    </div>
  );
});

CurrencyEdit.displayName = "CurrencyEdit";
