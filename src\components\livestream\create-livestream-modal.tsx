"use client";

import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { AnimatePresence, motion, useMotionTemplate } from "framer-motion";
import <PERSON><PERSON><PERSON>, { Area } from "react-easy-crop";
import { v4 } from "uuid";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import {
  livestreamQueries,
  useCreateLivestreamMutation,
  useEditLivestreamMutation,
} from "@/queries";
import { useUser } from "@/stores";
import { useLivestreamStore } from "@/stores/livestream";
import { ThreadPrivacyTypeEnum } from "@/types";
import { cn, upload } from "@/utils";
import getCroppedImg from "@/utils/crop-image";

import {
  ArrowBackOutlineIcon,
  CalendarOutlineIcon,
  EyeOutlineIcon,
  PencilAltOutlineIcon,
  TrashOutlineIcon,
} from "../icons";
import { toast } from "../toast";
import { AlertDialog, AlertDialogContent } from "../ui/alert-dialog";
import { Button } from "../ui/button";
import { Drawer, DrawerContent } from "../ui/drawer";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Switch } from "../ui/switch";
import { DeleteLivestreamModal } from "./delete-livestream-modal";
import { useLivestreamEditor } from "./hooks/use-livestream-editor";
import { IngressControls } from "./ingress-controls";
import { LiveStreamScheduleModal } from "./livestream-schedule-modal";

type Step = "LIVESTREAM" | "SETTINGS" | "BADGES" | "THUMBNAIL";

interface Badge {
  name: string;
  type: number;
  image: string;
}

interface StepState {
  currentStep: Step;
  data: {
    livestream?: LivestreamStepData;
    thumbnailUrl?: string;
    previewURL?: string;
    settings?: SettingsStepData;
    badges?: Badge[];
  };
}

const MAX_LENGTH = 60;

export function CreateLivestreamModal() {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { user } = useUser();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const actions = useLivestreamStore((state) => state.actions);
  const [{ composeLivestream: open, livestreamId }, setLivestreamEditor] =
    useLivestreamEditor();

  const { data: livestreamData, isLoading } = useQuery({
    ...livestreamQueries.livestreamSimpleInfo(livestreamId ?? ""),
    enabled: !!livestreamId,
  });

  const [state, setState] = useState<StepState>({
    currentStep: "LIVESTREAM",
    data: {
      livestream: {
        name: livestreamData?.livestream.name ?? "",
        type: livestreamData?.livestream.type ?? "EASY",
      },
      thumbnailUrl: livestreamData?.livestream.thumbnailUrl,
      badges:
        livestreamData?.livestream.badgeTypes?.map((badge) => {
          const badgeInfo = badges.find((b) => b.type === badge.badgeType);
          return {
            name: badgeInfo?.name ?? "",
            type: badge.badgeType,
            image: badgeInfo?.image ?? "",
          };
        }) ?? [],
      settings: {
        badges:
          livestreamData?.livestream.badgeTypes?.map((badge) => {
            const badgeInfo = badges.find((b) => b.type === badge.badgeType);
            return {
              name: badgeInfo?.name ?? "",
              type: badge.badgeType,
              image: badgeInfo?.image ?? "",
            };
          }) ?? [],
        gateType:
          livestreamData?.livestream.privacyType ===
          ThreadPrivacyTypeEnum.SHAREHOLDERS
            ? "TICKET"
            : livestreamData?.livestream.privacyType ===
                ThreadPrivacyTypeEnum.BADGEHOLDERS
              ? "BADGE"
              : null,
        scheduledDate: livestreamData?.livestream.scheduledStartTime ?? null,
      },
    },
  });

  const { mutateAsync: createLivestream, isPending } =
    useCreateLivestreamMutation({
      onSuccess: (data) => {
        toast.green("You created a new Stream!");
        reset();
        queryClient.resetQueries({
          queryKey: ["home", "threads", "livestreams-feed"],
        });
        const url = new URL(window.location.href);
        url.search = "";
        window.history.replaceState({}, "", url);
        if (data.token && !data.livestream.scheduledStartTime) {
          router.push(`/live/${user?.twitterHandle}`);
        } else {
          router.push(
            `/${user?.twitterHandle}/status/${data.livestream.threadId}`,
          );
        }
      },
    });

  const { mutateAsync: editLivestream, isPending: isEditingPending } =
    useEditLivestreamMutation({
      onSuccess: (data) => {
        toast.green("Stream updated successfully!");
        reset();
        queryClient.resetQueries({
          queryKey: ["home", "threads", "livestreams-feed"],
        });
        // const url = new URL(window.location.href);
        // url.search = "";
        // window.history.replaceState({}, "", url);
        router.push(
          `/${user?.twitterHandle}/status/${data.livestream.threadId}`,
        );
      },
    });

  const handleCloseModal = () => {
    reset();
    setLivestreamEditor({ composeLivestream: false });
  };

  const handleGoToThumbnailStep = (
    previewURL: string,
    data?: LivestreamStepData,
  ) => {
    setState((prev) => ({
      ...prev,
      data: { ...prev.data, previewURL, livestream: data },
      currentStep: "THUMBNAIL",
    }));
  };

  const handleGoToSettingsStep = (data: LivestreamStepData) => {
    setState((prev) => ({
      ...prev,
      data: { ...prev.data, livestream: data },
      currentStep: "SETTINGS",
    }));
  };

  const handleAddThumbnail = (url: string) => {
    setState((prev) => ({
      ...prev,
      data: {
        ...prev.data,
        thumbnailUrl: url,
      },
      currentStep: "LIVESTREAM",
    }));
  };

  const handleGoToLivestreamStep = (data: SettingsStepData) => {
    setState((prev) => ({
      ...prev,
      data: { ...prev.data, settings: data },
      currentStep: "LIVESTREAM",
    }));
  };

  const handleCloseThumbnailStep = () => {
    setState((prev) => ({
      ...prev,
      currentStep: "LIVESTREAM",
    }));
  };

  const handleConfirmStart = (data: SettingsStepData) => {
    setState((prev) => ({
      ...prev,
      data: { ...prev.data, settings: data },
    }));
    handleStartorScheduleStream({
      ...state,
      data: { ...state.data, settings: data },
    });
  };

  const handleGoToBadgesStep = (data: SettingsStepData) => {
    setState((prev) => ({
      ...prev,
      data: { ...prev.data, settings: data },
      currentStep: "BADGES",
    }));
  };

  const handleStartorScheduleStream = async (state: StepState) => {
    const name = state.data.livestream?.name;
    const type = state.data.livestream?.type;
    const thumbnailUrl = state.data.thumbnailUrl;
    if (!name || !type || !thumbnailUrl) {
      toast.danger("Please enter a name, type and thumbnail before proceeding");
      return;
    }

    if (
      state.data.settings?.gateType === "BADGE" &&
      !state.data.badges?.length
    ) {
      toast.danger("Please select at least one badge");
      return;
    }

    const gateType =
      state.data.settings?.gateType === "TICKET"
        ? ThreadPrivacyTypeEnum.SHAREHOLDERS
        : state.data.settings?.gateType === "BADGE"
          ? ThreadPrivacyTypeEnum.BADGEHOLDERS
          : ThreadPrivacyTypeEnum.PUBLIC;

    const badgeTypes =
      state.data.settings?.gateType === "BADGE" &&
      state.data.badges?.length &&
      state.data.badges.length > 0
        ? state.data.badges.map((badge) => badge.type)
        : undefined;

    if (livestreamId) {
      editLivestream({
        livestreamId: livestreamId,
        name: name,
        thumbnailUrl: thumbnailUrl,
        type: type,
        privacyType: gateType,
        badgeTypes: badgeTypes,
        scheduledStartTime: state.data.settings?.scheduledDate ?? undefined,
      });
    } else {
      createLivestream({
        name: name,
        thumbnailUrl: thumbnailUrl,
        type: type,
        privacyType: gateType,
        badgeTypes,
        scheduledStartTime: state.data.settings?.scheduledDate ?? undefined,
      });
    }
  };

  const reset = () => {
    setState({
      currentStep: "LIVESTREAM",
      data: {},
    });
  };

  const renderStep = () => {
    switch (state.currentStep) {
      case "LIVESTREAM": {
        return (
          <LivestreamStep
            livestreamId={livestreamId}
            initialData={state.data.livestream}
            thumbnailUrl={state.data.thumbnailUrl}
            onClose={handleCloseModal}
            onConfirm={handleGoToSettingsStep}
            handleGoToThumbnailStep={handleGoToThumbnailStep}
          />
        );
      }

      case "THUMBNAIL": {
        if (!state.data.previewURL) {
          return null;
        }

        return (
          <ThumbnailStep
            title={state.data.livestream?.name}
            previewURL={state.data.previewURL}
            onClose={handleCloseThumbnailStep}
            onConfirm={handleAddThumbnail}
          />
        );
      }

      case "SETTINGS": {
        return (
          <SettingsStep
            livestreamId={livestreamId}
            initialData={{
              settings: state.data.settings,
              badges: state.data.badges,
            }}
            onClose={handleGoToLivestreamStep}
            onConfirm={handleConfirmStart}
            isMutationPending={isPending || isEditingPending}
            onOpenBadges={handleGoToBadgesStep}
          />
        );
      }

      case "BADGES": {
        if (!isTablet) {
          return (
            <SettingsStep
              livestreamId={livestreamId}
              initialData={{
                settings: state.data.settings,
                badges: state.data.badges,
              }}
              onClose={handleGoToLivestreamStep}
              onConfirm={handleConfirmStart}
              isMutationPending={isPending || isEditingPending}
              onOpenBadges={handleGoToBadgesStep}
            />
          );
        }

        return (
          <EditBadges
            onClose={() => {
              setState((prev) => ({
                ...prev,
                currentStep: "SETTINGS",
              }));
            }}
            initialBadges={state.data.badges}
            onConfirm={(badges) => {
              setState((prev) => ({
                ...prev,
                data: { ...prev.data, badges },
                currentStep: "SETTINGS",
              }));
            }}
          />
        );
      }
    }
  };

  useEffect(() => {
    if (livestreamId && !isLoading) {
      if (livestreamData) {
        if (livestreamData.livestream.hostId !== user?.id) {
          toast.danger("You are not the host of this stream");
          router.replace("/live?streamTab=livestreams");
          return;
        }

        setState((prev) => ({
          ...prev,
          data: {
            livestream: {
              name: livestreamData?.livestream.name ?? "",
              type: livestreamData?.livestream.type ?? "EASY",
            },
            badges:
              livestreamData?.livestream.badgeTypes?.map((badge) => {
                const badgeInfo = badges.find(
                  (b) => b.type === badge.badgeType,
                );
                return {
                  name: badgeInfo?.name ?? "",
                  type: badge.badgeType,
                  image: badgeInfo?.image ?? "",
                };
              }) ?? [],
            settings: {
              badges:
                livestreamData?.livestream.badgeTypes?.map((badge) => {
                  const badgeInfo = badges.find(
                    (b) => b.type === badge.badgeType,
                  );
                  return {
                    name: badgeInfo?.name ?? "",
                    type: badge.badgeType,
                    image: badgeInfo?.image ?? "",
                  };
                }) ?? [],
              gateType:
                livestreamData?.livestream.privacyType ===
                ThreadPrivacyTypeEnum.SHAREHOLDERS
                  ? "TICKET"
                  : livestreamData?.livestream.privacyType ===
                      ThreadPrivacyTypeEnum.BADGEHOLDERS
                    ? "BADGE"
                    : null,
              scheduledDate:
                livestreamData?.livestream.scheduledStartTime ?? null,
            },
          },
        }));
      } else {
        router.replace("/live?streamTab=livestreams");
      }
    }
  }, [livestreamId, livestreamData?.livestream.scheduledStartTime, isLoading]);

  useEffect(() => {
    if (!open) {
      reset();
      setLivestreamEditor({ livestreamId: null });
    }
  }, [open]);

  return (
    <AlertDialog
      open={open}
      onOpenChange={(open) => {
        setLivestreamEditor({ composeLivestream: open });
      }}
    >
      <AlertDialogContent
        className={cn(
          "pt-pwa flex h-full w-full flex-col overflow-y-auto bg-dark-bk/75 px-0 py-2 backdrop-blur-sm transition-colors sm:h-auto sm:max-h-screen sm:bg-dark-bk/75",
          state.currentStep === "BADGES" &&
            isTablet &&
            "max-w-sm bg-[#0F0F0F]/90 sm:bg-[#0F0F0F]/90",
        )}
      >
        {renderStep()}
        <Drawer
          open={state.currentStep === "BADGES" && !isTablet}
          onOpenChange={() => {
            setState((prev) => ({
              ...prev,
              currentStep: "SETTINGS",
            }));
          }}
        >
          <DrawerContent className="p-0">
            <EditBadges
              onClose={() => {
                setState((prev) => ({
                  ...prev,
                  currentStep: "SETTINGS",
                }));
              }}
              initialBadges={state.data.badges}
              onConfirm={(badges) => {
                setState((prev) => ({
                  ...prev,
                  data: { ...prev.data, badges },
                  currentStep: "SETTINGS",
                }));
              }}
            />
          </DrawerContent>
        </Drawer>
      </AlertDialogContent>
    </AlertDialog>
  );
}

interface LivestreamStepData {
  name: string;
  type: "EASY" | "PRO";
}

interface LivestreamStepProps {
  initialData?: LivestreamStepData;
  livestreamId: string | null;
  thumbnailUrl?: string;
  onClose: () => void;
  onConfirm: (data: LivestreamStepData) => void;
  handleGoToThumbnailStep: (
    previewURL: string,
    data?: LivestreamStepData,
  ) => void;
}

function LivestreamStep({
  initialData,
  onClose,
  onConfirm,
  livestreamId,
  thumbnailUrl,
  handleGoToThumbnailStep,
}: LivestreamStepProps) {
  const { user } = useUser();
  const inputRef = useRef<HTMLInputElement>(null);
  const [name, setName] = useState(initialData?.name || "");
  const [type, setType] = useState<"EASY" | "PRO">(initialData?.type || "EASY");
  const { data: livestreamData } = useQuery({
    ...livestreamQueries.livestreamSimpleInfo(livestreamId ?? ""),
    enabled: !!livestreamId,
  });

  const isButtonDisabled = name.trim().length === 0 || !thumbnailUrl;

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.danger("Uploaded image file cannot exceed 5 MB");
        return;
      }

      const previewURL = URL.createObjectURL(file);
      handleGoToThumbnailStep(previewURL, { name, type });
    }
  };

  return (
    <div className="flex h-full flex-col">
      <div className="flex items-center p-6">
        <div className="flex flex-1 justify-start">
          <button onClick={onClose}>
            <ArrowBackOutlineIcon className="size-5 text-off-white" />
          </button>
        </div>
        <h3 className="text-base font-semibold leading-5 text-white">
          Create your Live Stream 1/2
        </h3>
        {livestreamId &&
        livestreamData &&
        livestreamData.livestream.hostId === user?.id ? (
          <div className="flex flex-1 justify-end">
            <DeleteLivestreamModal>
              <button>
                <TrashOutlineIcon className="size-5 text-brand-orange" />
              </button>
            </DeleteLivestreamModal>
          </div>
        ) : (
          <div className="flex-1" />
        )}
      </div>
      <div className="flex flex-grow flex-col sm:flex-grow-0">
        <div className="flex flex-col gap-6 p-6">
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between gap-2">
              <Label htmlFor="livestream-editor" className="text-off-white">
                THUMBNAIL IMAGE
              </Label>
            </div>
            <LivestreamCardPreview
              title={name}
              type="THUMBNAIL"
              url={thumbnailUrl}
            >
              <Button
                variant="secondary"
                className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 py-2 text-xs md:py-3 md:text-sm"
                onClick={() => inputRef.current?.click()}
              >
                <PencilAltOutlineIcon className="size-6 text-dark-bk" />
                <span>Edit Thumbnail</span>
              </Button>
            </LivestreamCardPreview>
            <input
              ref={inputRef}
              style={{ display: "none" }}
              type="file"
              accept="image/*"
              onChange={handleChange}
            />
          </div>
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between gap-2">
              <Label htmlFor="livestream-editor" className="text-off-white">
                Name
              </Label>
            </div>
            <Input
              id="livestream-editor"
              type="text"
              placeholder="Name your Live Stream!"
              className="min-h-[50px] w-full bg-transparent p-4 pr-10 text-sm text-off-white placeholder:text-gray-text focus:outline-none"
              value={name}
              onChange={(e) => setName(e.target.value)}
              maxLength={MAX_LENGTH}
            >
              <span className="pointer-events-none absolute right-4 top-4 text-sm text-gray-text">
                {MAX_LENGTH - name.length}
              </span>
            </Input>
          </div>
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between gap-4">
              <Button
                className="flex-1 justify-start rounded-[10px] p-4 text-left text-sm"
                onClick={() => setType("EASY")}
                variant={type === "EASY" ? "default" : "outline"}
              >
                Easy <br /> Live Stream
              </Button>
              <Button
                className="flex-1 justify-start rounded-[10px] p-4 text-left text-sm"
                onClick={() => setType("PRO")}
                variant={type === "PRO" ? "default" : "outline"}
              >
                Pro <br /> Live Stream
              </Button>
            </div>
            {type === "EASY" && (
              <p className="text-xs text-off-white">
                Just share your cam and your screen, like a video call!
              </p>
            )}
            {type === "PRO" && (
              <>
                <p className="text-xs text-off-white">
                  Recommended for seasoned streamers! Set your streaming
                  software server and streaming key and you’ll be good to go.
                </p>
                <IngressControls />
              </>
            )}
          </div>
        </div>
      </div>
      <div className="px-6 py-4">
        <Button
          className="w-full flex-grow"
          onClick={() => onConfirm({ name, type })}
          disabled={isButtonDisabled}
        >
          Continue {type === "PRO" && "(I already finished the setup)"}
        </Button>
      </div>
    </div>
  );
}

interface ThumbnailStepProps {
  previewURL: string;
  title?: string;
  onClose: () => void;
  onConfirm: (url: string) => void;
}

function ThumbnailStep({
  title,
  previewURL,
  onClose,
  onConfirm,
}: ThumbnailStepProps) {
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const width = useMotionTemplate`${progress}%`;

  const resetUploading = () => {
    setIsUploading(false);
    setProgress(0);
  };

  const handleApply = async () => {
    if (!croppedAreaPixels) return;

    const blob = await getCroppedImg(previewURL, croppedAreaPixels);

    if (!blob) return;

    // @ts-expect-error
    blob.name = "image.jpeg";
    // @ts-expect-error
    blob.lastModified = new Date();

    const file = new File([blob], `${v4()}.jpg`, { type: "image/jpeg" });

    setIsUploading(true);
    try {
      const res = await upload({
        file,
        onProgressChange: (progress) => {
          setProgress(progress);
        },
      });

      onConfirm(res.url);
    } catch (error) {
      console.error(error);
      toast.danger("File upload failed");
      resetUploading();
    }
  };

  return (
    <div className="flex h-full flex-col gap-8">
      <div className="flex items-center p-6">
        <div className="flex flex-1 justify-start">
          <button onClick={onClose}>
            <ArrowBackOutlineIcon className="size-5 text-off-white" />
          </button>
        </div>
        <h3 className="text-base font-semibold leading-5 text-white">
          Edit a Thumbnail
        </h3>
        <div className="flex-1" />
      </div>
      {previewURL ? (
        <div className="px-6">
          <div className="relative aspect-square w-full">
            <Cropper
              showGrid={false}
              maxZoom={2}
              image={previewURL || undefined}
              crop={crop}
              zoom={zoom}
              style={{
                cropAreaStyle: {
                  borderRadius: "10px",
                  border: "none",
                  color: "transparent",
                },
              }}
              aspect={16 / 9}
              onCropChange={setCrop}
              onZoomChange={setZoom}
              onCropComplete={(croppedArea, croppedAreaPixels) => {
                setCroppedAreaPixels(croppedAreaPixels);
              }}
              objectFit="horizontal-cover"
            />
            <LivestreamCardPreview title={title} type="PLACEHOLDER">
              <Button
                variant="secondary"
                className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 py-2 text-xs disabled:opacity-80 md:py-3 md:text-sm"
                disabled
              >
                Join Live Stream
              </Button>
            </LivestreamCardPreview>
            <AnimatePresence>
              {isUploading && (
                <motion.div
                  style={{ width }}
                  exit={{ opacity: 0 }}
                  className="absolute bottom-0 left-0 z-10 h-1 min-w-4 bg-brand-orange"
                />
              )}
            </AnimatePresence>
          </div>
        </div>
      ) : null}

      <div className="px-6 py-4">
        <Button
          className="w-full flex-grow sm:px-8 sm:py-2"
          onClick={handleApply}
        >
          Save
        </Button>
      </div>
    </div>
  );
}

interface LivestreamCardPreviewProps {
  title?: string;
  url?: string;
  children: React.ReactNode;
  type?: "THUMBNAIL" | "PLACEHOLDER";
}

function LivestreamCardPreview({
  title,
  url,
  children,
  type = "THUMBNAIL",
}: LivestreamCardPreviewProps) {
  return (
    <div
      className={cn(
        "group isolate flex aspect-video w-full flex-col justify-between gap-2 overflow-hidden rounded-[10px] border border-brand-orange bg-cover bg-center",
        type === "PLACEHOLDER" &&
          "pointer-events-none absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 border-dashed",
        type === "THUMBNAIL" && "relative",
      )}
      style={{
        backgroundImage: url ? `url(${url})` : undefined,
      }}
    >
      <div className="p-4">
        <h5
          className={cn(
            "line-clamp-2 text-sm font-semibold text-off-white md:text-lg",
            type === "PLACEHOLDER" && "opacity-80",
          )}
        >
          {title || "Name for your Live Stream"}
        </h5>
      </div>
      <div
        className={cn(
          "flex items-center justify-between p-4",
          type === "PLACEHOLDER" && "opacity-80",
        )}
      >
        <div className="rounded-md bg-[#E74141] p-1 text-xs font-semibold leading-none text-off-white">
          LIVE
        </div>
        <div className="flex items-center gap-1">
          <EyeOutlineIcon
            className="size-5 text-brand-orange md:size-6"
            strokeWidth={1.5}
          />
          <span className="text-xs font-semibold text-off-white">–</span>
        </div>
      </div>
      <div className="pointer-events-none absolute inset-0 -z-10 bg-dark-bk/50" />
      {children}
    </div>
  );
}

interface SettingsStepData {
  gateType: "TICKET" | "BADGE" | null;
  badges: { name: string; type: number; image: string }[];
  scheduledDate: string | null;
}

interface SettingsStepProps {
  initialData?: {
    settings?: SettingsStepData;
    badges?: { name: string; type: number; image: string }[];
  };
  livestreamId: string | null;
  onClose: (data: SettingsStepData) => void;
  onConfirm: (data: SettingsStepData) => void;
  isMutationPending: boolean;
  onOpenBadges: (data: SettingsStepData) => void;
}

const SettingsStep = ({
  initialData,
  onClose,
  onConfirm,
  livestreamId,
  isMutationPending,
  onOpenBadges,
}: SettingsStepProps) => {
  const { user } = useUser();
  const [gateType, setGateType] = useState<"TICKET" | "BADGE" | null>(
    initialData?.settings?.gateType || null,
  );
  const [scheduledDate, setScheduledDate] = useState<string | null>(
    initialData?.settings?.scheduledDate || null,
  );

  const { data: livestreamData } = useQuery({
    ...livestreamQueries.livestreamSimpleInfo(livestreamId ?? ""),
    enabled: !!livestreamId,
  });

  const handleClose = () => {
    onClose({
      gateType,
      badges: initialData?.badges || [],
      scheduledDate,
    });
  };

  return (
    <div className="flex h-full flex-col">
      <div className="flex items-center p-6">
        <div className="flex flex-1 justify-start">
          <button onClick={handleClose}>
            <ArrowBackOutlineIcon className="size-5 text-off-white" />
          </button>
        </div>
        <h3 className="text-base font-semibold leading-5 text-white">
          Create your Live Stream 2/2
        </h3>
        {livestreamId &&
        livestreamData &&
        livestreamData.livestream.hostId === user?.id ? (
          <div className="flex flex-1 justify-end">
            <DeleteLivestreamModal>
              <button>
                <TrashOutlineIcon className="size-5 text-brand-orange" />
              </button>
            </DeleteLivestreamModal>
          </div>
        ) : (
          <div className="flex-1" />
        )}
      </div>
      <div className="mb-6 mt-2 flex w-full flex-col gap-6 px-6 sm:justify-start">
        <Label className="flex w-full items-center justify-between gap-2">
          <div className="flex flex-col gap-1.5">
            <div className="text-sm font-medium normal-case text-off-white">
              Gate my Stream
            </div>
            <p className="max-w-48 text-xs font-normal normal-case text-gray-text">
              Select who can join your Stream
            </p>
          </div>
          <Switch
            checked={gateType !== null}
            onCheckedChange={(checked) => {
              setGateType(checked ? "TICKET" : null);
            }}
          />
        </Label>
        {gateType !== null ? (
          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between gap-4">
              <Button
                className="flex-1 justify-start rounded-[10px] p-4 text-left text-sm"
                onClick={() => setGateType("TICKET")}
                variant={gateType === "TICKET" ? "default" : "outline"}
              >
                Ticket Gated <br /> Stream
              </Button>
              <Button
                className="flex-1 justify-start rounded-[10px] p-4 text-left text-sm"
                onClick={() => setGateType("BADGE")}
                variant={gateType === "BADGE" ? "default" : "outline"}
              >
                Badge Gated <br /> Stream
              </Button>
            </div>
            {gateType === "BADGE" ? (
              <div
                className={cn(
                  "flex flex-wrap items-center justify-center gap-2 rounded-[10px]",
                  initialData?.badges?.length && initialData?.badges?.length > 0
                    ? "bg-light-background px-4 py-6"
                    : "px-4",
                )}
              >
                {initialData?.badges?.map(({ name, image, type }) => {
                  return (
                    <Button
                      key={type}
                      variant="outline"
                      className={cn(
                        "flex-shrink-0 cursor-default gap-1.5 border-brand-orange py-1.5 text-base font-medium leading-5 hover:bg-transparent",
                      )}
                    >
                      <div className="w-max">
                        <img
                          src={image}
                          className="h-[18px] w-auto"
                          alt={`${name} logo`}
                        />
                      </div>
                      <span>{name}</span>
                    </Button>
                  );
                })}
                <Button
                  variant="outline"
                  className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5"
                  onClick={() => {
                    onOpenBadges({
                      badges: initialData?.badges || [],
                      scheduledDate: scheduledDate,
                      gateType,
                    });
                  }}
                >
                  <PencilAltOutlineIcon className="size-5 text-off-white" />
                  <span>Edit</span>
                </Button>
              </div>
            ) : null}
          </div>
        ) : null}
      </div>
      <div className="mt-auto flex flex-shrink-0 flex-col gap-4 px-6 py-4 sm:mt-0 sm:items-end sm:justify-between sm:gap-4">
        <div className="flex w-full gap-2">
          <Button
            className="w-full flex-grow sm:w-auto sm:px-8 sm:py-2"
            onClick={() =>
              onConfirm({
                gateType,
                badges: initialData?.badges || [],
                scheduledDate,
              })
            }
            loading={isMutationPending}
          >
            {scheduledDate ? "Save Scheduled Stream" : "Start Now"}
          </Button>
          <LiveStreamScheduleModal
            scheduledDate={scheduledDate}
            setScheduledDate={setScheduledDate}
          >
            <Button variant="outline" className="size-11">
              <CalendarOutlineIcon className="size-6 text-off-white" />
            </Button>
          </LiveStreamScheduleModal>
        </div>
        {scheduledDate ? (
          <div className="w-full rounded-[10px] bg-chat-bubble p-2 text-center text-xs text-gray-text">
            Starting on{" "}
            {format(new Date(scheduledDate), "EEEE, MMM d 'at' h:mm a")}
          </div>
        ) : null}
      </div>
    </div>
  );
};

interface EditBadgesProps {
  onClose: () => void;
  initialBadges?: { name: string; type: number; image: string }[];
  onConfirm: (
    badges: {
      name: string;
      type: number;
      image: string;
    }[],
  ) => void;
}

const EditBadges = ({ onClose, initialBadges, onConfirm }: EditBadgesProps) => {
  const [selectedBadges, setSelectedBadges] = useState<
    {
      name: string;
      type: number;
      image: string;
    }[]
  >(initialBadges || []);

  return (
    <div className="flex h-full flex-col gap-8 p-6">
      <div className="flex h-full flex-col gap-2">
        <div className="flex items-center gap-2">
          <div className="flex justify-start">
            <button onClick={onClose}>
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </button>
          </div>
          <h3 className="text-xl font-semibold leading-6 text-white">
            Select Badges
          </h3>
        </div>
        <p className="text-xs text-off-white">
          Pick up to 3 badges to allow them to join your Stream.
        </p>
      </div>
      <div className="flex flex-wrap items-center justify-center gap-[14px] rounded-[10px]">
        {badges.map(({ name, image, type }) => {
          const isSelected = selectedBadges.some(
            (badge) => badge.type === type,
          );
          return (
            <Button
              key={type}
              variant="outline"
              className={cn(
                "flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5",
                isSelected && "border-brand-orange",
              )}
              onClick={() => {
                if (isSelected) {
                  setSelectedBadges((prev) =>
                    prev.filter((badge) => badge.type !== type),
                  );
                } else {
                  if (selectedBadges.length >= 3) {
                    toast.danger("You can only select up to 3 badges");
                  } else {
                    setSelectedBadges((prev) => [
                      ...prev,
                      { name, type, image },
                    ]);
                  }
                }
              }}
            >
              <div className="w-max">
                <img
                  src={image}
                  className={cn("h-[18px] w-auto", !isSelected && "grayscale")}
                  alt={`${name} logo`}
                />
              </div>
              <span>{name}</span>
            </Button>
          );
        })}
      </div>
      <Button
        className="w-full flex-grow sm:w-auto sm:px-8 sm:py-2"
        onClick={() => {
          onConfirm(selectedBadges);
        }}
      >
        Confirm
      </Button>
    </div>
  );
};

const badges = [
  {
    name: "Arena OGs",
    type: 1,
    image: "/assets/badges/badge-type-1.png",
  },
  {
    name: "DeGods",
    type: 2,
    image: "/assets/badges/badge-type-2.png",
  },
  {
    name: "Dokyo",
    type: 3,
    image: "/assets/badges/badge-type-3.png",
  },
  {
    name: "Sappy Seals",
    type: 4,
    image: "/assets/badges/badge-type-4.png",
  },
  {
    name: "GURS",
    type: 5,
    image: "/assets/badges/badge-type-5.png",
  },
  {
    name: "Nochill",
    type: 6,
    image: "/assets/badges/badge-type-6.png",
  },
  {
    name: "Steady",
    type: 8,
    image: "/assets/badges/badge-type-8.png",
  },
  {
    name: "Smol Joe",
    type: 9,
    image: "/assets/badges/badge-type-9.png",
  },
  {
    name: "Gogonauts",
    type: 11,
    image: "/assets/badges/badge-type-11.png",
  },
  {
    name: "Bodoggos",
    type: 12,
    image: "/assets/badges/badge-type-12.png",
  },
  {
    name: "Pudgy Penguins",
    type: 13,
    image: "/assets/badges/badge-type-13.png",
  },
  {
    name: "COQ",
    type: 14,
    image: "/assets/badges/badge-type-14.png",
  },
  {
    name: "MOG",
    type: 15,
    image: "/assets/badges/badge-type-15.png",
  },
  {
    name: "Mad Lads",
    type: 16,
    image: "/assets/badges/badge-type-16.png",
  },
  {
    name: "Nochillio",
    type: 18,
    image: "/assets/badges/badge-type-18.png",
  },
  {
    name: "Arena Champion",
    type: 19,
    image: "/assets/badges/badge-type-19.png",
  },
  {
    name: "AuTistiC BoYs CLub",
    type: 20,
    image: "/assets/badges/badge-type-20.png",
  },
  {
    name: "MU Doggy",
    type: 21,
    image: "/assets/badges/badge-type-21.png",
  },
  {
    name: "MU",
    type: 22,
    image: "/assets/badges/badge-type-22.png",
  },
  {
    name: "Bad Bois",
    type: 23,
    image: "/assets/badges/badge-type-23.png",
  },
  {
    name: "Boi",
    type: 24,
    image: "/assets/badges/badge-type-24.png",
  },
  {
    name: "LOK",
    type: 25,
    image: "/assets/badges/badge-type-25.png",
  },
  {
    name: "EROL",
    type: 26,
    image: "/assets/badges/badge-type-26.png",
  },
  {
    name: "Doomercorp",
    type: 27,
    image: "/assets/badges/badge-type-27.png",
  },
];
