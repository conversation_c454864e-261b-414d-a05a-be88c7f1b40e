import {
  DefaultError,
  MutationOptions,
  UndefinedInitialDataOptions,
  useInfiniteQuery,
  useMutation,
  useQuery,
} from "@tanstack/react-query";

import {
  banCommunity,
  getCommunityNameCheck,
  getCommunityStats,
  getFeedEligibleCommunities,
  getGroupsSearch,
  getGroupTokenHolders,
  getIsUserBannedFromCommunity,
  getTrendingGroups,
  postCreateCommunity,
  updateCommunity,
} from "@/api/client/group";
import {
  CreateCommunityFormState,
  EditCommunityFormState,
} from "@/app/(create-community)/create-community/_components/create-community-form-input";
import { CommunityExtended } from "@/types/community";

import { CreateCommunityResponse, TradesGroupsTrendingResponse } from "./types";

type CreateGroupMutation = MutationOptions<
  CreateCommunityResponse,
  DefaultError,
  CreateCommunityFormState,
  any
>;

export const useCreateGroupMutation = (options?: CreateGroupMutation) => {
  return useMutation({
    mutationFn: postCreateCommunity,
    ...options,
  });
};

type EditGroupMutation = MutationOptions<
  unknown,
  DefaultError,
  EditCommunityFormState,
  any
>;

export const useEditGroupMutation = (options?: EditGroupMutation) => {
  return useMutation({
    mutationFn: updateCommunity,
    ...options,
  });
};

export interface CommunitiesResponse {
  communities: CommunityExtended[];
  numberOfPages: number;
  numberOfResults: number;
  pageSize: number;
}

export const useGroupsSearchQuery = (searchString: string) => {
  return useQuery({
    queryKey: ["community", "search-communities", searchString],
    queryFn: () => {
      return getGroupsSearch({ searchString });
    },
    enabled: !!searchString,
  });
};

export const useFeedEligibleCommunitiesQuery = (searchString: string) => {
  return useInfiniteQuery({
    queryKey: ["communities", "feed-eligible", searchString],
    queryFn: ({ pageParam }) => {
      return getFeedEligibleCommunities({ ...pageParam, searchString });
    },
    initialPageParam: {
      page: 1,
      pageSize: 20,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (lastPage.numberOfPages > lastPageParam.page) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
  });
};

export const useTokenHoldersQuery = (contractAddress: string) => {
  return useInfiniteQuery({
    queryKey: ["community", "token-holders", contractAddress],
    queryFn: ({ pageParam }) => {
      return getGroupTokenHolders({ ...pageParam, contractAddress });
    },
    initialPageParam: {
      page: 1,
      pageSize: 25,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (lastPage.numberOfPages > lastPageParam.page) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }
      return undefined;
    },
    enabled: !!contractAddress,
  });
};

type TrendingGroupsQueryOptions = Omit<
  UndefinedInitialDataOptions<TradesGroupsTrendingResponse>,
  "queryKey"
>;

export const useTrendingGroupsQuery = (
  options?: TrendingGroupsQueryOptions,
) => {
  return useQuery<TradesGroupsTrendingResponse>({
    queryKey: ["trade", "groups", "trending"],
    queryFn: getTrendingGroups,
    // cache for 1 minute to prevent unnecessary requests
    staleTime: 1000 * 60,
    ...options,
  });
};

export const useCommunityStatsQuery = (contractAddress: string) => {
  return useQuery({
    queryKey: ["community", "stats", contractAddress],
    queryFn: async () => {
      if (!contractAddress) return null;
      return await getCommunityStats(contractAddress);
    },
    enabled: !!contractAddress,
  });
};

export const useCommunityNameTakenCheckQuery = (
  name?: string,
  communityId?: string,
) => {
  return useQuery({
    queryKey: ["community", "name-check", name, communityId],
    queryFn: () => {
      if (!name) return null;
      return getCommunityNameCheck({ name, communityId });
    },
    enabled: !!name,
  });
};

type BanCommunityMutationType = MutationOptions<
  unknown,
  DefaultError,
  string,
  any
>;

export const useBanCommunityMutation = (options?: BanCommunityMutationType) => {
  return useMutation({
    mutationFn: banCommunity,
    ...options,
  });
};

export const useIsUserBannedQuery = (communityId: string, userId: string) => {
  return useQuery({
    queryKey: ["community", "isUserBanned", communityId, userId],
    queryFn: () => {
      if (!communityId || !userId) return false;
      return getIsUserBannedFromCommunity({ communityId, userId });
    },
    enabled: Boolean(communityId && userId),
  });
};
