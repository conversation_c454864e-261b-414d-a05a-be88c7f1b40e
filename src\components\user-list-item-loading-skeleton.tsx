import Skeleton from "react-loading-skeleton";

import { cn } from "@/utils";

export const UserListItemLoadingSkeleton = ({
  className,
}: {
  className?: string;
}) => {
  return (
    <div
      className={cn("flex w-full justify-between gap-4 px-6 py-4", className)}
    >
      <div className="flex items-center gap-[10px]">
        <Skeleton circle className="size-[42px]" />
        <div className="flex w-full flex-col gap-1 leading-4">
          <Skeleton className="mt-1 h-[14px] w-28" />
          <Skeleton className="h-[12px] w-20" />
        </div>
      </div>
      <div className="flex flex-col items-end justify-center gap-1 leading-4">
        <Skeleton className="mt-1 h-[14px] w-16" />
        <Skeleton className="h-[12px] w-14" />
      </div>
    </div>
  );
};
