import React from "react"; // Removed useState as it's no longer needed

import { SystemCurrency } from "@/api/client/currency";

interface TipAmountInputV2Props {
  name: string;
  value: string;
  onValueChange: (value: string) => void;
  onBlur?: () => void;
  ref?: React.Ref<any>;
  errorMessage?: string;
  token: SystemCurrency;
  onPresetClick?: (amount: string) => void;
}

const PRESETS = [
  { label: "$1", value: "1", emoji: "😘" },
  { label: "$10", value: "10", emoji: "🤑" },
  { label: "$100", value: "100", emoji: "💸" },
  { label: "Max", value: "max", emoji: "👑" },
];

export const TipAmountInputV2: React.FC<TipAmountInputV2Props> = ({
  name,
  value,
  onValueChange,
  onBlur,
  ref,
  errorMessage,
  token,
  onPresetClick,
}) => {
  const isPresetValue = PRESETS.some((p) => p.value === value);
  const isCustomMode = value !== "" && !isPresetValue;

  const getCustomEmoji = () => {
    const numericValue = parseFloat(value);
    if (isNaN(numericValue) || numericValue <= 0) return "💖";
    if (numericValue >= 100) return PRESETS[2].emoji;
    if (numericValue >= 10) return PRESETS[1].emoji;
    return PRESETS[0].emoji;
  };

  const buttons = isCustomMode
    ? [
        {
          label: "Custom",
          value: "custom",
          emoji: getCustomEmoji(),
        },
        PRESETS[3], // Max button
      ]
    : PRESETS;

  return (
    <div className="flex w-full flex-col items-center">
      <div className="mb-2 flex flex-row justify-center">
        <div className="text-2xl text-light-gray-text">Tip amount</div>
      </div>
      <div className="mb-4 flex w-full items-center justify-center">
        <div className="flex items-center justify-center">
          <span className="select-none text-4xl font-bold text-off-white">
            $
          </span>
          <input
            className="border-none bg-transparent text-center text-4xl font-bold text-off-white outline-none"
            type="text"
            inputMode="numeric"
            name={name}
            value={value}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              onValueChange(e.target.value);
            }}
            // REMOVED: All onFocus logic that was causing the problem.
            onBlur={onBlur}
            ref={ref as any}
            placeholder="1"
            style={{
              fontVariantNumeric: "tabular-nums",
              width: `${Math.max(value.length, 1) + 1}ch`,
              minWidth: "2ch",
              maxWidth: "8ch",
            }}
          />
        </div>
      </div>
      <div className="mb-2 flex w-full flex-row justify-center gap-2">
        {buttons.map((preset) => {
          const isActive = isCustomMode
            ? preset.value === "custom"
            : value === preset.value;

          return (
            <button
              key={preset.label}
              type="button"
              className={`flex items-center justify-center gap-2 border px-4 py-1 text-sm text-off-white transition-all duration-200
                h-10 min-w-20 whitespace-nowrap rounded-full
                ${
                  isActive
                    ? "border-brand-orange bg-[#1a1a1a]"
                    : "border-gray-700 bg-transparent"
                }
              `}
              onClick={() => {
                if (preset.value === "custom") {
                  return;
                }
                if (onPresetClick) {
                  onPresetClick(preset.value);
                }
              }}
            >
              <span className="flex h-full items-center gap-2">
                <span className="leading-none">{preset.label}</span>
                <span
                  className={`transition-all duration-200 ${
                    isActive ? "text-base" : "text-sm"
                  }`}
                  style={{ lineHeight: "1" }}
                >
                  {preset.emoji}
                </span>
              </span>
            </button>
          );
        })}
      </div>
      {errorMessage && (
        <div className="text-red-500 mt-1 text-xs">{errorMessage}</div>
      )}
    </div>
  );
};