"use client";

import { useEffect, useState } from "react";
import { useSelectedLayoutSegments } from "next/navigation";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

import { Reports } from "./_components/reports";

function ReportsLayout({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);
  const isLargeTablet = useMediaQuery(BREAKPOINTS.lg);
  const segments = useSelectedLayoutSegments();

  const hasTicketId = segments.length > 0;

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  if (!isLargeTablet) {
    return <>{children}</>;
  }

  return <>{hasTicketId ? <Reports /> : children}</>;
}

export default ReportsLayout;
