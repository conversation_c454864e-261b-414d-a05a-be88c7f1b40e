import { useMemo } from "react";

import { MIN_TOKEN_PRICE } from "@/app/(main)/community/_components/consts";
import { CommunityStats } from "@/types/community";
import { abbreviateNumber, divideBigInt } from "@/utils";

export const useTokenPriceChange = (stats?: CommunityStats) => {
  return useMemo(() => {
    let keyPrice = BigInt(MIN_TOKEN_PRICE);
    let lastKeyPrice = BigInt(MIN_TOKEN_PRICE);

    if (stats?.price && BigInt(stats.price) > keyPrice)
      keyPrice = BigInt(stats.price);

    if (stats?.lastPrice && BigInt(stats.lastPrice) > lastKeyPrice)
      lastKeyPrice = BigInt(stats.lastPrice);

    const percentage = lastKeyPrice
      ? 100 * (divideBigInt(keyPrice, lastKeyPrice) - 1)
      : keyPrice
        ? 100
        : 0;

    return [percentage < 0, abbreviateNumber(percentage, 2, false)];
  }, [stats]);
};
