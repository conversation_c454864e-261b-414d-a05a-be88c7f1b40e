"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import { useParams, useSelectedLayoutSegments } from "next/navigation";

import { InfiniteData, useQueryClient } from "@tanstack/react-query";
import <PERSON>Handler from "@tiptap-pro/extension-file-handler";
import Document from "@tiptap/extension-document";
import HardBreak from "@tiptap/extension-hard-break";
import History from "@tiptap/extension-history";
import Mention from "@tiptap/extension-mention";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, nodePasteRule, useEditor } from "@tiptap/react";
import EmojiPicker, { Theme } from "emoji-picker-react";
import { AnimatePresence, motion, useMotionTemplate } from "framer-motion";
import DOMPurify from "isomorphic-dompurify";
import { v4 } from "uuid";

import { proxyFile } from "@/actions/proxy-file";
import { GIFsModal } from "@/app/compose/post/_components/gifs-modal";
import { UrlHighlighter } from "@/app/compose/post/_components/url-highlighter";
import {
  GIFOutlineIcon,
  ImageOutlineIcon,
  SmileOutlineIcon,
  XCircleOutlineIcon,
} from "@/components/icons";
import { suggestion } from "@/components/mention/suggestion";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import {
  usePostThreadAnswerMutation,
  useThreadByIdQuery,
  useThreadNestedAnswersInfiniteQuery,
  useTotalUploadedQuery,
} from "@/queries";
import { ThreadsResponse } from "@/queries/types";
import { FileType, Thread, ThreadResponse } from "@/types";
import { cn, upload } from "@/utils";
import { compressImageToJpeg } from "@/utils/compress-jpeg";
import { removeMentionSpans } from "@/utils/mention";
import { IS_ANDROID, IS_IOS } from "@/utils/window-environment";

import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";

export function ReplyEditor() {
  const segments = useSelectedLayoutSegments();
  const queryClient = useQueryClient();
  const editorRef = useRef<HTMLDivElement>(null);
  const params = useParams() as { userHandle: string; id: string };
  const [isFocused, setIsFocused] = useState(false);
  const [files, setFiles] = useState<FileType[]>([]);
  const [isDragging, setIsDragging] = useState(false);

  const inputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isUploaded, setIsUploaded] = useState(false);
  const [preview, setPreview] = useState<{
    url: string;
    type: "video" | "image";
  } | null>(null);
  const [progress, setProgress] = useState(0);
  const width = useMotionTemplate`${progress}%`;

  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const { data: nestedAnswersData, isLoading: isAnswersLoading } =
    useThreadNestedAnswersInfiniteQuery(params.id);
  const { data, isLoading } = useThreadByIdQuery(params.id);

  const { data: totalUploaded, isLoading: isUploadDataLoading } =
    useTotalUploadedQuery();

  const usersHandles = useMemo(() => {
    if (!nestedAnswersData || isAnswersLoading) return [];

    return Array.from(
      new Set(
        nestedAnswersData.pages
          .reduce((prev, current) => {
            return [...prev, ...(current?.threads ?? [])];
          }, [] as Thread[])
          .reverse()
          .map((thread) => thread?.user?.twitterHandle),
      ),
    );
  }, [nestedAnswersData, isAnswersLoading]);

  const isBanned = useMemo(() => {
    if (!nestedAnswersData || isLoading) return false;

    // Check if any thread is deleted
    return nestedAnswersData.pages
      .reduce((prev, current) => {
        return [...prev, ...(current?.threads ?? [])];
      }, [] as Thread[])
      .some((thread) => thread?.isDeleted === true);
  }, [nestedAnswersData, isLoading]);

  const { mutateAsync: postReply, isPending: isReplyPending } =
    usePostThreadAnswerMutation({
      onSuccess: (data) => {
        toast.green("Your post was sent");
        localStorage.removeItem(EDITOR_DRAFT_KEY);
        localStorage.removeItem(FILE_DRAFT_KEY);
        queryClient.setQueryData(
          ["threads", params.id],
          (old: ThreadResponse) => {
            if (!old) return old;

            return {
              ...old,
              thread: {
                ...old.thread,
                answerCount: old.thread.answerCount + 1,
              },
            };
          },
        );

        queryClient.setQueryData(
          ["threads", "answers", params.id],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) {
              queryClient.invalidateQueries({
                queryKey: ["threads", "answers", params.id],
              });
              return old;
            }

            return {
              ...old,
              pages: old.pages.map((page, index) => {
                if (index === 0) {
                  return {
                    ...page,
                    threads: [data.thread, ...page.threads],
                  };
                }
                return page;
              }),
            };
          },
        );

        editor?.commands.clearContent();
        setFiles([]);
        resetUploading();
        if (segments.length === 3 && segments[1] === "status") {
          window.scrollTo({ top: 0, behavior: "smooth" });
        }
      },
    });

  const [mentionCountExceeded, setMentionCountExceeded] = useState(false);
  const [mentionWarningShown, setMentionWarningShown] = useState(false);

  const EDITOR_DRAFT_KEY = "thread_post_draft";
  const FILE_DRAFT_KEY = "thread_file_draft";

  const loadDraft = () => {
    const savedContent = localStorage.getItem(EDITOR_DRAFT_KEY) || "";
    editor?.commands.setContent(savedContent);

    if (!localStorage.getItem(FILE_DRAFT_KEY)) {
      return;
    }
    const savedFile = JSON.parse(localStorage.getItem(FILE_DRAFT_KEY) || "");

    if (savedFile && savedFile.url) {
      setFiles([
        {
          id: savedFile.id,
          isLoading: false,
          previewUrl: savedFile.previewUrl,
          url: savedFile.url,
          fileType: savedFile.fileType,
          size: savedFile.size,
        },
      ]);

      setPreview({
        url: savedFile.previewUrl,
        type: savedFile.fileType.includes("image") ? "image" : "video",
      });
    }
  };

  useEffect(() => {
    if (files.length > 0) {
      setIsUploaded(true);
      localStorage.setItem(FILE_DRAFT_KEY, JSON.stringify(files[0]));
    } else if (isUploaded && files.length === 0) {
      setIsUploaded(false);
      localStorage.removeItem(FILE_DRAFT_KEY);
    }
  }, [files]);

  const editor = useEditor({
    extensions: [
      Document,
      Text,
      Paragraph,
      HardBreak.extend({
        addPasteRules() {
          return [
            nodePasteRule({
              find: /<br>$/g,
              type: this.type,
            }),
          ];
        },
      }),
      History,
      Placeholder.configure({
        placeholder: ({ editor }) => {
          return (
            editor.options.element.getAttribute("data-placeholder") ??
            "What's happening?"
          );
        },
      }),
      Mention.configure({
        HTMLAttributes: {
          class: "text-brand-orange",
        },
        suggestion,
      }),
      FileHandler.configure({
        allowedMimeTypes: [
          "image/png",
          "image/jpeg",
          "image/gif",
          "image/webp",
          "video/mp4",
          "video/quicktime",
        ],

        onDrop: (currentEditor, files, pos) => {
          setIsDragging(false);
          files.forEach((file) => {
            handleUpload(file);
          });
        },
        onPaste: (currentEditor, files, htmlContent) => {
          if (htmlContent) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlContent, "text/html");
            const imgElement = doc.querySelector("img");

            if (imgElement && imgElement.src) {
              handleUpload(imgElement.src);
              return false; // Prevent default paste behavior
            }
          }

          files.forEach((file) => handleUpload(file));
        },
      }),
      UrlHighlighter,
    ],
    editorProps: {
      attributes: {
        class:
          "focus:outline-none px-1 w-full select-text pt-2 pb-3 text-base leading-5 text-off-white",
      },
    },
    onCreate: ({ editor }) => {
      loadDraft();

      let mentionCount = 0;
      editor.state.doc.descendants((node) => {
        if (node.type.name === "mention") {
          mentionCount++;
        }
      });
      if (mentionCount && mentionCount > 20) {
        setMentionCountExceeded(true);
        if (!mentionWarningShown) {
          setMentionWarningShown(true);
          toast.danger("This post exceeds the number of @mentions allowed.");
        }
      } else {
        setMentionWarningShown(false);
        setMentionCountExceeded(false);
      }
    },
    onUpdate: ({ editor }) => {
      const content = editor.getHTML();
      localStorage.setItem(EDITOR_DRAFT_KEY, content);

      let mentionCount = 0;
      editor.state.doc.descendants((node) => {
        if (node.type.name === "mention") {
          mentionCount++;
        }
      });
      if (mentionCount && mentionCount > 20) {
        setMentionCountExceeded(true);
        if (!mentionWarningShown) {
          setMentionWarningShown(true);
          toast.danger("This post exceeds the number of @mentions allowed.");
        }
      } else {
        setMentionWarningShown(false);
        setMentionCountExceeded(false);
      }
    },
    onFocus() {
      setIsFocused(true);
    },
  });

  const handleUpload = async (file: File | string) => {
    let fileToUpload: File;
    let previewURL: string;

    if (typeof file === "string") {
      try {
        const { data, contentType } = await proxyFile(file);
        const blob = new Blob([Buffer.from(data, "base64")], {
          type: contentType,
        });
        const fileName = file.split("/").pop() || "";
        fileToUpload = new File([blob], fileName, { type: contentType });
        previewURL = URL.createObjectURL(blob);
      } catch (error) {
        console.error("Error fetching external image:", error);
        resetUploading();
        return;
      }
    } else {
      fileToUpload = file;
      previewURL = URL.createObjectURL(file);
    }

    if (fileToUpload.type.includes("video/ogg")) {
      toast.danger("Please upload video in either mp4 or webm format");
      return;
    }
    if (
      fileToUpload.type.includes("image") &&
      !(
        fileToUpload.type.includes("image/jpeg") ||
        fileToUpload.type.includes("image/gif") ||
        fileToUpload.type.includes("image/png")
      )
    ) {
      toast.danger("Please upload image in JPEG, JPG, PNG or GIF format");
      return;
    }
    if (
      fileToUpload.type.includes("image") &&
      fileToUpload.size > 10 * 1024 * 1024
    ) {
      toast.danger("Uploaded image file cannot exceed 10 MB");
      return;
    }
    if (
      fileToUpload.type.includes("video") &&
      fileToUpload.size > 100 * 1024 * 1024
    ) {
      toast.danger("Uploaded video file cannot exceed 100 MB");
      return;
    }
    if (
      fileToUpload.type.includes("video") &&
      fileToUpload.size + (totalUploaded || 0) > 300 * 1024 * 1024
    ) {
      toast.danger("You have reached your daily video upload limit of 300 MB.");
      resetUploading();
      return;
    }

    if (fileToUpload.type.includes("video")) {
      previewURL += "#t=0.001";
    }
    setPreview({
      url: previewURL,
      type: fileToUpload.type.includes("image") ? "image" : "video",
    });
    setIsUploading(true);
    setProgress(0);

    if (
      fileToUpload.type.includes("image") &&
      (fileToUpload.type.includes("image/jpeg") ||
        fileToUpload.type.includes("image/png")) &&
      fileToUpload.size > 500 * 1024
    ) {
      const compressed = await compressImageToJpeg(fileToUpload, (progress) => {
        setProgress(progress);
      });
      if (compressed) {
        fileToUpload = compressed;
      }
    }

    try {
      const res = await upload({
        file: fileToUpload,
        onProgressChange: (progress) => {
          setProgress(progress);
        },
      });

      setFiles([
        {
          id: res.id,
          isLoading: false,
          previewUrl: res.previewUrl,
          url: res.url,
          fileType: fileToUpload.type.includes("image") ? "image" : "video",
          size: fileToUpload.size,
        },
      ]);
    } catch (error) {
      console.error(error);
      toast.danger("File upload failed");
      resetUploading();
    } finally {
      setIsUploading(false);
    }
  };

  const handleReply = async () => {
    if (!data) return;
    if (!editor || (editor?.getText().trim() === "" && files.length === 0))
      return;

    const text = editor.getHTML();
    const plainText = editor.getText();

    let formattedContent = "";
    if (plainText.trim().length > 0) {
      const updatedContent = DOMPurify.sanitize(text);
      formattedContent = removeMentionSpans(updatedContent);
    }
    editor.chain().clearContent().run();
    setIsFocused(false);

    await postReply({
      content: formattedContent,
      threadId: data?.thread?.id,
      userId: data?.thread?.userId,
      files,
    });
  };

  const resetUploading = () => {
    setIsUploading(false);
    setProgress(0);
    setPreview(null);
  };

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    await handleUpload(file);
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  if (isBanned) {
    return null;
  }

  return (
    <div className="flex flex-grow flex-col border-t border-dark-gray bg-dark-bk pb-4 pt-2 sm:flex-grow-0 sm:border-y">
      <div className="flex min-w-0 flex-grow flex-col gap-3 px-6">
        {isFocused && (
          <div className="truncate text-sm text-gray-text">
            Replying to{" "}
            <span className="text-brand-orange">
              {usersHandles.length > 0 ? (
                <>
                  {usersHandles.length <= 3 ? (
                    usersHandles.map((userHandle, index) => (
                      <span key={userHandle}>
                        @{userHandle}
                        {index !== usersHandles.length - 1 ? ", " : ""}
                      </span>
                    ))
                  ) : (
                    <>
                      {usersHandles.slice(0, 2).map((userHandle, index) => (
                        <span key={userHandle}>
                          @{userHandle}
                          {index !== 1 ? ", " : " "}
                        </span>
                      ))}
                      <span>and {usersHandles.length - 2} others</span>
                    </>
                  )}
                </>
              ) : null}
            </span>
          </div>
        )}
        {(preview || files.length > 0) && (
          <div className="relative w-20">
            {preview && preview.type === "video" && (
              <video
                src={
                  isUploading && !files[0]?.url ? preview.url : files[0]?.url
                }
                className="w-full rounded-md"
                autoPlay
                loop
                muted
                controls
                playsInline
                webkit-playsinline="true"
              />
            )}
            {preview && preview.type === "image" && (
              <img
                src={
                  isUploading && !files[0]?.url ? preview.url : files[0]?.url
                }
                alt="preview"
                className="max-h-[510px] w-full rounded-md object-cover"
              />
            )}
            {!preview && files.length > 0 && (
              <img
                src={files[0].url}
                alt="preview"
                className="max-h-[510px] w-full rounded-md object-cover"
              />
            )}
            <button
              className="absolute right-1 top-1 z-10"
              onClick={() => {
                setFiles([]);
                resetUploading();
              }}
            >
              <XCircleOutlineIcon className="size-6 fill-dark-bk/75 text-off-white" />
            </button>
            <AnimatePresence>
              {isUploading && (
                <div className="absolute inset-0 overflow-hidden rounded-[10px]">
                  <motion.div
                    style={{ width }}
                    exit={{ opacity: 0 }}
                    className="absolute bottom-0 left-0 h-1 min-w-4 bg-brand-orange"
                  />
                </div>
              )}
            </AnimatePresence>
          </div>
        )}
        <div
          ref={editorRef}
          className={cn(
            "relative rounded-[10px] border border-dashed",
            isDragging ? "border-brand-orange" : "border-transparent",
          )}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={() => {
            setIsDragging(false);
          }}
        >
          <EditorContent
            editor={editor}
            data-placeholder={
              isFocused ? "" : isTablet ? "Post your reply" : "Tap to reply"
            }
            className="post-content w-full border-b border-brand-orange "
          />
        </div>
        {isFocused && (
          <div className="flex items-center justify-between sm:gap-4">
            <div className="flex items-center gap-4">
              {!(IS_ANDROID || IS_IOS) && (
                <>
                  <Popover modal>
                    <PopoverTrigger>
                      <SmileOutlineIcon
                        className="size-7 text-[#E0E0E0]"
                        strokeWidth={1.5}
                      />
                    </PopoverTrigger>
                    <PopoverContent
                      align="center"
                      collisionPadding={{
                        right: 12,
                        left: 12,
                      }}
                      className="flex max-h-[420px] w-full flex-col overflow-y-auto rounded-lg border-none bg-chat-bubble p-0 text-left shadow-[4px_4px_4px_0px_rgba(0,0,0,0.50)] sm:w-[420px]"
                    >
                      <EmojiPicker
                        reactionsDefaultOpen={false}
                        theme={Theme.DARK}
                        height={300}
                        width="100%"
                        onEmojiClick={(emoji) => {
                          editor?.chain().insertContent(emoji.emoji).run();
                        }}
                        previewConfig={{ showPreview: false }}
                        className="!bg-chat-bubble [&_h2]:bg-chat-bubble/95"
                      />
                    </PopoverContent>
                  </Popover>
                </>
              )}
              <div className="flex items-center">
                <button
                  disabled={files.length > 0}
                  onClick={() => {
                    !isUploading &&
                      inputRef.current &&
                      inputRef.current.click();
                  }}
                >
                  <ImageOutlineIcon
                    className={cn(
                      "flex size-8 items-center justify-center text-off-white",
                      files.length > 0 && "opacity-50",
                    )}
                  />
                </button>
                <input
                  ref={inputRef}
                  className="hidden"
                  type="file"
                  accept="image/*,video/*"
                  onChange={handleChange}
                  disabled={isUploadDataLoading}
                />
              </div>
              <GIFsModal
                onSelect={(gif) => {
                  setFiles([
                    {
                      id: v4(),
                      isLoading: false,
                      previewUrl: gif.media_formats.gifpreview.url,
                      url: gif.media_formats.gif.url,
                      fileType: "image",
                      size: 0,
                    },
                  ]);
                }}
              >
                <button disabled={files.length > 0}>
                  <GIFOutlineIcon
                    className={cn(
                      "size-8 text-off-white",
                      files.length > 0 && "opacity-50",
                    )}
                  />
                </button>
              </GIFsModal>
            </div>
            <Button
              className="w-auto px-8 py-2"
              onClick={handleReply}
              disabled={
                mentionCountExceeded ||
                isReplyPending ||
                isUploading ||
                !data ||
                !editor ||
                (editor?.getText().trim() === "" && files.length === 0)
              }
            >
              Reply
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
