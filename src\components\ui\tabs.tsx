"use client";

import * as React from "react";

import * as TabsPrimitive from "@radix-ui/react-tabs";

import { cn } from "@/utils";

const Tabs = TabsPrimitive.Root;

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "hide-scrollbar inline-flex h-10 items-center justify-start overflow-x-auto border-b border-white/10 px-5",
      className,
    )}
    {...props}
  />
));
TabsList.displayName = TabsPrimitive.List.displayName;

/**
 * ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2
 */
const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "relative -mt-[1px] inline-flex items-center justify-center whitespace-nowrap px-5 pb-3 pt-2 text-sm font-medium text-dark-gray outline-none transition-all disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-off-white data-[state=active]:after:absolute data-[state=active]:after:bottom-0 data-[state=active]:after:h-px data-[state=active]:after:w-full data-[state=active]:after:bg-brand-orange sm:px-14 ",
      className,
    )}
    {...props}
  />
));
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

const TabsFlatTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "relative -mt-[1px] inline-flex items-center justify-center whitespace-nowrap p-0 pb-2 text-sm font-medium text-dark-gray transition-all disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-off-white data-[state=active]:after:absolute data-[state=active]:after:bottom-0 data-[state=active]:after:h-px data-[state=active]:after:w-full data-[state=active]:after:bg-brand-orange",
      className,
    )}
    {...props}
  />
));
TabsFlatTrigger.displayName = TabsPrimitive.Trigger.displayName;

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content ref={ref} className={cn("", className)} {...props} />
));
TabsContent.displayName = TabsPrimitive.Content.displayName;

export { Tabs, TabsList, TabsTrigger, TabsContent, TabsFlatTrigger };
