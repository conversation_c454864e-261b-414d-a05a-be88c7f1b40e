import { useState } from "react";

import { StakeTabContent } from "@/app/(wallet)/wallet/token-portal/_components/stake-tab";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatMarketCap } from "@/utils/format-market-cap";
import { formatPrice } from "@/utils/format-token-price";

interface ChampionsUserCardProps {
  rank: number;
  stakedBalance: string;
  twitterPicture: string;
  twitterName: string;
}

export const ChampionsUserCard = ({
  rank,
  stakedBalance,
  twitterPicture,
  twitterName,
}: ChampionsUserCardProps) => {
  const [stakedCurrentBalance, setStakedBalance] = useState(stakedBalance);
  return (
    <div className="p-[16px]">
      <div className="flex w-full justify-between gap-4">
        <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
          <Avatar className="size-[42px]">
            <AvatarImage src={twitterPicture} />
            <AvatarFallback />
          </Avatar>
          <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
            <div className="flex gap-1.5">
              <h4 className="truncate text-[#808080]">{twitterName}</h4>
              {rank !== 0 && rank <= 500 && (
                <img
                  src="/icons/arena-champion-user.svg"
                  className="h-4 w-4 rounded-full"
                  alt="Arena logo"
                />
              )}
            </div>
            <div className="truncate text-[#F4F4F4]">
              Rank {rank === 0 ? "N/A" : rank}
            </div>
          </div>
        </div>
        <div className="flex flex-shrink-0 flex-col items-end justify-center gap-[2px]">
          <div className="flex items-center text-sm font-medium text-[#808080]">
            Staked Balance
          </div>
          <div className="flex items-center gap-[6px]">
            <img
              src="/assets/coins/arena.png"
              className="h-4 w-4 rounded-full"
              alt="ARENA logo"
            />
            <span className="text-sm font-medium text-[#F4F4F4]">
              {formatMarketCap(formatPrice(stakedCurrentBalance))}
            </span>
          </div>
        </div>
      </div>
      <div className="mt-[24px]">
        <StakeTabContent
          isChampionsTab={true}
          setStakedBalance={setStakedBalance}
        />
      </div>
    </div>
  );
};
