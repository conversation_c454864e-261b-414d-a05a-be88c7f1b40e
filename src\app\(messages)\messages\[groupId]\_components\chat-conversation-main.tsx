"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { useParams } from "next/navigation";

import {
  InfiniteData,
  useInfiniteQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { format, isThisWeek, isThisYear, isToday, isYesterday } from "date-fns";
import { Loader2Icon } from "lucide-react";
import { useInView } from "react-intersection-observer";

import {
  acceptChat,
  getMessagesAfter,
  getMessagesAround,
  getMessagesBefore,
  leaveChat,
} from "@/api/client/chat";
import { ArrowDownFilledIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import { SOCKET_MESSAGE } from "@/environments/socket-messages";
import {
  useIsBlockedByUserQuery,
  useIsUserBlockedQuery,
  useMessagesAroundQuery,
  useUserByIdQuery,
} from "@/queries";
import {
  ChatMessagesResponse,
  MessageType,
  Reaction,
} from "@/queries/types/chats";
import { useUser } from "@/stores";
import { useSocket } from "@/stores/socket";
import { UserFlaggedEnum } from "@/types";
import { cn } from "@/utils";
import { isSafari } from "@/utils/is-safari";

import { useGroup, useGroupStore } from "../context/group-context";
import { BlockedUserForm } from "./blocked-by-user.form";
import { BlockedForm } from "./blocked-user.form";
import { ChatInput } from "./chat-input";
import { FollowForm } from "./follow-form";
import { HolderForm } from "./holder-form";
import { Message, MyMessage } from "./message";
import { NobodyForm } from "./nobody-form";
import { RequestForm } from "./request-form";

type QueryKeyType = (
  | string
  | {
      groupId: string;
      messageId?: string | null;
    }
)[];

export const ChatConversation = () => {
  const queryClient = useQueryClient();
  const { user } = useUser();
  const params = useParams() as { groupId: string };
  const [showScrollToBottomButton, setShowScrollToBottomButton] =
    useState(false);
  const prevMessagesLengthRef = useRef(0);
  const prevParentScrollHeightRef = useRef(0);
  const fetchingPreviousPageRef = useRef(false);
  const parentRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);
  const { ref: topLoadingRef, inView: topInView } = useInView({
    threshold: 0.8,
  });
  const { ref: bottomLoadingRef, inView: bottomInView } = useInView({
    threshold: 0.8,
  });

  const [reply, setReply] = useState<MessageType | null>(null);
  const { data: group, updateIsRequest } = useGroup();

  const [initialLoad, setInitialLoad] = useGroupStore((state) => [
    state.initialLoad,
    state.actions.setInitialLoad,
  ]);
  const messageId = useGroupStore((state) => state.messageId);
  const resetMessageId = useGroupStore((state) => state.actions.resetMessageId);

  const { socket, setSeen } = useSocket();

  const {
    data: messagesData,
    fetchPreviousPage,
    isFetchingPreviousPage,
    hasPreviousPage,
    fetchNextPage,
    hasNextPage,
  } = useInfiniteQuery<
    ChatMessagesResponse,
    Error,
    InfiniteData<ChatMessagesResponse>,
    QueryKeyType,
    {
      timeFrom: number;
      messageId: string | null;
      initialLoad?: boolean;
    }
  >({
    queryKey: [
      "chat",
      "group-infinite-messages",
      { groupId: params.groupId, messageId },
    ],
    queryFn: async ({ pageParam, direction }) => {
      if (pageParam.messageId) {
        const messagesData = await getMessagesAround({
          groupId: params.groupId,
          messageId: pageParam.messageId,
        });

        return messagesData;
      }

      if (
        direction === "forward" &&
        pageParam.messageId == null &&
        !pageParam.initialLoad
      ) {
        const messagesData = await getMessagesAfter({
          groupId: params.groupId,
          timeFrom: pageParam.timeFrom,
        });

        return messagesData;
      }

      const messagesData = await getMessagesBefore({
        groupId: params.groupId,
        timeFrom: pageParam.timeFrom,
      });

      messagesData.messages = messagesData.messages.reverse();

      return messagesData;
    },
    initialPageParam: {
      timeFrom: 0,
      messageId,
      initialLoad: true,
    },
    getPreviousPageParam: (firstPage, pages, lastPageParam) => {
      if (firstPage.messages.length >= 40) {
        return {
          timeFrom: +firstPage.messages[0].createdOn,
          messageId: null,
        };
      }

      return undefined;
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (lastPageParam.initialLoad && !messageId) return undefined;

      if (lastPage.messages.length >= 40) {
        return {
          timeFrom: +lastPage.messages[lastPage.messages.length - 1].createdOn,
          messageId: null,
        };
      }

      return undefined;
    },
  });

  const [groupedMessages, messages] = useMemo(() => {
    const flatMessages =
      messagesData?.pages.map((page) => page.messages).flat() ?? [];

    const groupedByDate = flatMessages.reduce(
      (acc, message) => {
        const date = new Date(+message.createdOn);
        const formattedDate = format(date, "MM/dd/yyyy");

        if (!acc[formattedDate]) {
          acc[formattedDate] = [];
        }

        acc[formattedDate].push(message);
        return acc;
      },
      {} as Record<string, MessageType[]>,
    );

    return [Object.entries(groupedByDate), flatMessages];
  }, [messagesData]);

  const handleScrollToBottom = useCallback(() => {
    setTimeout(() => {
      bottomRef.current?.scrollIntoView({
        behavior: "smooth",
      });
    }, 0);
  }, []);

  const handleScrollToBottomIfAtBottom = useCallback(() => {
    setTimeout(() => {
      const scrollElement = parentRef.current;
      if (!scrollElement) return;

      const scroll =
        scrollElement?.scrollTop + scrollElement?.clientHeight >=
        scrollElement?.scrollHeight - 50;

      if (scroll) {
        bottomRef.current?.scrollIntoView();
      }
    }, 0);
  }, []);

  const handleScrollToBottomButton = useCallback(() => {
    if (messageId && hasNextPage) {
      resetMessageId();
    } else {
      setTimeout(() => {
        bottomRef.current?.scrollIntoView({
          behavior: "smooth",
        });
      }, 0);
    }
  }, [hasNextPage, messageId, resetMessageId]);

  useEffect(() => {
    if (initialLoad && messages.length > 0) {
      if (messageId) {
        const element = document.getElementById(`message-${messageId}`);

        if (element) {
          element.scrollIntoView({
            block: "center",
          });

          // Create an Intersection Observer
          const observer = new IntersectionObserver(
            (entries) => {
              if (entries[0].isIntersecting) {
                element.classList.add("animate-highlight-bg");
                // Remove the highlight class after the animation
                setTimeout(() => {
                  element.classList.remove("animate-highlight-bg");
                }, 1500); // 1500ms matches our animation duration

                // Disconnect the observer after triggering the animation
                observer.disconnect();
              }
            },
            { threshold: 1 },
          ); // Trigger when 100% of the element is visible

          // Start observing the element
          observer.observe(element);
        }
        setShowScrollToBottomButton(true);
      } else {
        bottomRef.current?.scrollIntoView();
      }
      setInitialLoad(false);
    }
  }, [messages, initialLoad, messageId, setInitialLoad]);

  useEffect(() => {
    const scrollElement = parentRef.current;
    if (
      topInView &&
      !isFetchingPreviousPage &&
      scrollElement &&
      !fetchingPreviousPageRef.current
    ) {
      const scrollHeight = scrollElement.scrollHeight;
      prevParentScrollHeightRef.current = scrollHeight;
      fetchingPreviousPageRef.current = true;
      fetchPreviousPage();
    }
  }, [topInView, isFetchingPreviousPage, fetchPreviousPage]);

  useEffect(() => {
    if (bottomInView) {
      fetchNextPage();
    }
  }, [bottomInView, fetchNextPage]);

  useEffect(() => {
    if (messages.length > prevMessagesLengthRef.current && !initialLoad) {
      const scrollElement = parentRef.current;
      if (scrollElement && fetchingPreviousPageRef.current) {
        const scrollHeight = scrollElement.scrollHeight;
        const newScrollTop = scrollHeight - prevParentScrollHeightRef.current;
        scrollElement.scrollTo({
          top: newScrollTop,
        });
        if (isSafari()) {
          scrollElement.style.display = "none";
          scrollElement.offsetHeight; // Force a reflow
          scrollElement.style.display = "";
        }
        fetchingPreviousPageRef.current = false;
      }
    }
    prevMessagesLengthRef.current = messages.length;
  }, [messages, initialLoad]);

  useEffect(() => {
    const scrollElement = parentRef.current;
    if (scrollElement) {
      const handleScroll = () => {
        if (hasNextPage && messageId) return;

        setShowScrollToBottomButton((prev) => {
          const show =
            scrollElement?.scrollTop + scrollElement?.clientHeight <=
            scrollElement?.scrollHeight - 50;

          if (show !== prev) {
            return show;
          }

          return prev;
        });
      };
      scrollElement.addEventListener("scroll", handleScroll);
      return () => scrollElement.removeEventListener("scroll", handleScroll);
    }
  }, [hasNextPage, messageId]);

  useEffect(() => {
    if (socket && params.groupId) {
      socket.emit("subscribe", `group-${params.groupId}`);
    }

    return () => {
      if (socket && params.groupId) {
        socket.emit("unsubscribe", `group-${params.groupId}`);
      }
    };
  }, [socket, params]);

  useEffect(() => {
    socket?.on(
      SOCKET_MESSAGE.CHAT_MESSAGE,
      async (data: { message: MessageType }) => {
        if (!data.message || !parentRef.current) return;

        if (data.message.groupId !== params.groupId) return;

        const isAtBottom =
          parentRef.current?.scrollHeight -
            parentRef.current?.scrollTop -
            parentRef.current?.clientHeight ===
          0;

        if (!hasNextPage) {
          queryClient.setQueryData(
            [
              "chat",
              "group-infinite-messages",
              {
                groupId: params.groupId,
                messageId,
              },
            ],
            (
              old: InfiniteData<{
                messages: MessageType[];
              }>,
            ) => {
              if (!old) return old;

              return {
                ...old,
                pages: old.pages.map((page, index) => {
                  if (index === old.pages.length - 1) {
                    const indexToReplace = page.messages.findLastIndex((m) =>
                      m.id.includes("remove"),
                    );

                    if (indexToReplace !== -1) {
                      page.messages[indexToReplace] = data.message;
                      return {
                        ...page,
                        messages: page.messages,
                      };
                    }

                    return {
                      ...page,
                      messages: [...page.messages, data.message],
                    };
                  }
                  return page;
                }),
              };
            },
          );

          if (isAtBottom === true) {
            handleScrollToBottom();
          }
        }

        setSeen(params.groupId, Date.now());

        queryClient.invalidateQueries({
          queryKey: ["chat", "conversations"],
        });
        queryClient.invalidateQueries({
          queryKey: ["chat", "direct-messages"],
        });
      },
    );
    socket?.on(`chat-message-reaction`, async (data: Reaction) => {
      queryClient.setQueryData(
        [
          "chat",
          "group-infinite-messages",
          { groupId: params.groupId, messageId },
        ],
        (old: InfiniteData<ChatMessagesResponse>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page, index) => {
              if (
                page.messages.some((message) => message.id === data.messageId)
              ) {
                return {
                  ...page,
                  messages: page.messages.map((message) => {
                    if (message.id === data.messageId) {
                      const filteredReactions = message.reactions?.filter(
                        (reaction) => reaction.userId !== data.userId,
                      );
                      return {
                        ...message,
                        reactions: [...filteredReactions, data],
                      };
                    }
                    return message;
                  }),
                };
              }

              return page;
            }),
          };
        },
      );
      handleScrollToBottomIfAtBottom();
    });
    socket?.on(`add-chat-reaction`, async (data: Reaction) => {
      queryClient.setQueryData(
        [
          "chat",
          "group-infinite-messages",
          { groupId: params.groupId, messageId },
        ],
        (old: InfiniteData<ChatMessagesResponse>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page, index) => {
              if (
                page.messages.some((message) => message.id === data.messageId)
              ) {
                return {
                  ...page,
                  messages: page.messages.map((message) => {
                    if (message.id === data.messageId) {
                      const filteredReactions = message.reactions?.filter(
                        (reaction) => reaction.userId !== data.userId,
                      );
                      return {
                        ...message,
                        reactions: [...filteredReactions, data],
                      };
                    }
                    return message;
                  }),
                };
              }

              return page;
            }),
          };
        },
      );
      handleScrollToBottomIfAtBottom();
    });
    socket?.on(`delete-chat-reaction`, async (data: Reaction) => {
      queryClient.setQueryData(
        [
          "chat",
          "group-infinite-messages",
          { groupId: params.groupId, messageId },
        ],
        (old: InfiniteData<ChatMessagesResponse>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page, index) => {
              if (
                page.messages.some((message) => message.id === data.messageId)
              ) {
                return {
                  ...page,
                  messages: page.messages.map((message) => {
                    if (message.id === data.messageId) {
                      const filteredReactions = message.reactions?.filter(
                        (reaction) => reaction.userId !== data.userId,
                      );
                      return {
                        ...message,
                        reactions: filteredReactions,
                      };
                    }
                    return message;
                  }),
                };
              }

              return page;
            }),
          };
        },
      );
      handleScrollToBottomIfAtBottom();
    });

    return () => {
      setSeen(params.groupId, Date.now());
      socket?.off(SOCKET_MESSAGE.CHAT_MESSAGE);
    };
  }, [
    params.groupId,
    queryClient,
    socket,
    setSeen,
    handleScrollToBottom,
    handleScrollToBottomIfAtBottom,
    hasNextPage,
    messageId,
  ]);

  const settings = group && group.chatMateSettings;
  const { data: isUserBlocked, isLoading: isUserBlockedLoading } =
    useIsUserBlockedQuery(settings?.chatMate?.id);
  const { data: isBlockedByUser, isLoading: isBlockedByUserLoading } =
    useIsBlockedByUserQuery(settings?.chatMate?.id);
  const owner = useUserByIdQuery(group?.group?.ownerUserId || "");
  const isHolderNeed =
    settings &&
    settings.chatMate &&
    settings.chatMate.id !== user?.id &&
    settings.holders &&
    !settings.isKeyholder;
  const holdersFulfilled =
    settings &&
    settings.chatMate &&
    settings.chatMate.id !== user?.id &&
    settings.holders &&
    settings.isKeyholder;
  const isFollowerNeed =
    settings && !settings.holders && settings.followers && !settings.isFollower;
  const followersFulfilled =
    settings && settings.followers && settings.isFollower;
  const isNobody = settings && !settings.holders && !settings.followers;
  const isRequest =
    group &&
    group.group &&
    group.group.isRequest &&
    group.group.lastUserId !== user?.id;
  const isTemporary = group && group.group && group.group.isTemporary;
  const isSuspended =
    group && group.group
      ? group.group.isDirect
        ? settings?.chatMate?.flag === UserFlaggedEnum.SUSPENDED
        : owner?.data?.user?.flag === UserFlaggedEnum.SUSPENDED
      : owner?.data?.user?.flag === UserFlaggedEnum.SUSPENDED;

  const ADMIN_ID = "d4b0b964-6122-4c9b-8759-19cc17c0e6a0";
  const isAdmin = user?.id === ADMIN_ID;

  const [isTicketPurchased, setIsTicketPurchased] = useState<boolean>(false);

  const handleDeleteGroup = () =>
    leaveChat({ groupId: group?.group?.id || "" });

  const handleAccept = () => {
    updateIsRequest && updateIsRequest(false);
    acceptChat({ groupId: group?.group?.id || "" });
  };

  if (group?.group?.isDirect) {
    if (
      group?.group?.chatMateId !== user?.id &&
      group?.group?.ownerUserId !== user?.id
    ) {
      return null;
    }
  }

  return (
    <>
      <div
        className="flex-grow overflow-y-auto overflow-x-hidden py-2"
        ref={parentRef}
      >
        {hasPreviousPage && (
          <div
            className="flex w-full items-center justify-center py-4"
            ref={topLoadingRef}
          >
            <Loader2Icon className="size-6 animate-spin text-brand-orange" />
          </div>
        )}
        {groupedMessages.map(([date, messages]) => {
          const formattedDate = formatTimeDistance(date);

          return (
            <div key={date} className="relative">
              <div className="sticky top-0 z-10 flex justify-center pb-1 pt-5">
                <div className="min-w-[100px] rounded-full border border-dark-gray/50  bg-dark-bk px-3 py-1 text-center text-xs text-off-white">
                  {formattedDate}
                </div>
              </div>
              {messages.map((message, index) => {
                const previousMessage = messages?.[index - 1];
                const nextMessage = messages?.[index + 1];

                if (user && message?.userId === user.id) {
                  const isPreviousSameUser =
                    previousMessage?.userId === user.id;
                  const isNextSameUser = nextMessage?.userId === user.id;

                  return (
                    <MyMessage
                      key={message.id}
                      message={message}
                      isPreviousSameUser={isPreviousSameUser}
                      isNextSameUser={isNextSameUser}
                      setReply={setReply}
                      scrollToBottom={handleScrollToBottomIfAtBottom}
                    />
                  );
                }

                const isPreviousSameUser =
                  previousMessage?.userId === message?.userId;
                const isNextSameUser = nextMessage?.userId === message?.userId;

                return (
                  <Message
                    key={message.id}
                    message={message}
                    isPreviousSameUser={isPreviousSameUser}
                    isNextSameUser={isNextSameUser}
                    setReply={setReply}
                    scrollToBottom={handleScrollToBottomIfAtBottom}
                  />
                );
              })}
            </div>
          );
        })}
        {hasNextPage && (
          <div
            className="flex w-full items-center justify-center py-4"
            ref={bottomLoadingRef}
          >
            <Loader2Icon className="size-6 animate-spin text-brand-orange" />
          </div>
        )}
        <div
          className="pointer-events-none invisible opacity-0"
          ref={bottomRef}
        />
      </div>
      {group?.group?.isDirect && !isAdmin ? (
        <div className="sticky bottom-0 z-10 flex-shrink-0">
          {isUserBlocked && !isUserBlockedLoading && (
            <BlockedForm subjectName={settings.chatMate.twitterHandle} />
          )}
          {isBlockedByUser && !isBlockedByUserLoading && <BlockedUserForm />}
          {isTemporary &&
            isNobody &&
            !isRequest &&
            !isUserBlocked &&
            !isBlockedByUser && <NobodyForm />}
          {!holdersFulfilled &&
            isFollowerNeed &&
            !isRequest &&
            isTemporary &&
            !isUserBlocked &&
            !isBlockedByUser && <FollowForm />}
          {!followersFulfilled &&
            isHolderNeed &&
            !isRequest &&
            isTemporary &&
            !isTicketPurchased &&
            !isUserBlocked &&
            !isBlockedByUser && (
              <HolderForm
                setIsTicketPurchased={setIsTicketPurchased}
                subjectName={settings.chatMate.twitterHandle}
              />
            )}
          {!(isNobody && isTemporary) &&
            (followersFulfilled ||
              holdersFulfilled ||
              !isTemporary ||
              isTicketPurchased) &&
            !isRequest &&
            !isUserBlocked &&
            !isBlockedByUser && (
              <div className="sticky bottom-0 z-10 flex-shrink-0">
                {!isSuspended && (
                  <ChatInput
                    reply={reply}
                    setReply={setReply}
                    scrollToBottom={handleScrollToBottomButton}
                    canOptimisticUpdate={!hasNextPage}
                  />
                )}
                <Button
                  onClick={handleScrollToBottomButton}
                  variant="outline"
                  className={cn(
                    "absolute -top-14 left-1/2 size-10 -translate-x-1/2 border-dark-gray/50 bg-dark-bk p-0 transition-opacity duration-200 hover:bg-dark-bk",
                    showScrollToBottomButton
                      ? "opacity-100"
                      : "pointer-events-none  opacity-0",
                  )}
                >
                  <ArrowDownFilledIcon className="size-5 text-off-white" />
                </Button>
              </div>
            )}
          {isRequest && !isUserBlocked && !isBlockedByUser && (
            <RequestForm
              chatMateName={settings.chatMate.twitterHandle || ""}
              handleDelete={handleDeleteGroup}
              handleAccept={handleAccept}
            />
          )}
        </div>
      ) : (
        <div className="sticky bottom-0 z-10 flex-shrink-0">
          {!isSuspended && (
            <ChatInput
              reply={reply}
              setReply={setReply}
              scrollToBottom={handleScrollToBottomButton}
              canOptimisticUpdate={!hasNextPage}
            />
          )}
          <Button
            onClick={handleScrollToBottomButton}
            variant="outline"
            className={cn(
              "absolute -top-14 left-1/2 size-10 -translate-x-1/2 border-dark-gray/50 bg-dark-bk p-0 transition-opacity duration-200 hover:bg-dark-bk",
              showScrollToBottomButton
                ? "opacity-100"
                : "pointer-events-none  opacity-0",
            )}
          >
            <ArrowDownFilledIcon className="size-5 text-off-white" />
          </Button>
        </div>
      )}
    </>
  );
};

export function formatTimeDistance(date: Date | number | string) {
  if (!date) return "";

  if (typeof date === "string" && !isNaN(+date)) {
    date = +date;
  }

  if (!(date instanceof Date)) {
    date = new Date(date);
  }

  if (isToday(date)) {
    return "Today";
  }

  if (isYesterday(date)) {
    return "Yesterday";
  }

  if (isThisWeek(date)) {
    return format(date, "EEEE");
  }

  if (isThisYear(date)) {
    return format(date, "MMMM d");
  }

  return format(date, "MMMM d, yyyy");
}
