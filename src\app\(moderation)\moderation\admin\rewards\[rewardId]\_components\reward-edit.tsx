"use client";

import {
  FC,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useRouter } from "next/navigation";

import {
  SystemCurrencyCategoryEnum,
  updateCurrency,
} from "@/api/client/admin-currency";
import { deleteReward, updateReward } from "@/api/client/admin-rewards";
import { CategoryCheckboxes } from "@/components/category-checkboxes";
import {
  ArrowBackOutlineIcon,
  ExclamationCircleOutlineIcon,
} from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { TextInput } from "@/components/ui/text-input";
import { useRewardByIdQuery } from "@/queries/reward-by-id-query";

import { useAdmin } from "../../../context/admin-context";
import { RewardForm, RewardFormData } from "./reward-form";

interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
}

const DeleteConfirmationDialog: FC<DeleteConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md border-dark-gray bg-gray-bg shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)]">
        <div className="mb-4 flex justify-center">
          <div className="bg-red flex size-12 items-center justify-center rounded-full">
            <ExclamationCircleOutlineIcon className="size-6 text-red-600" />
          </div>
        </div>
        <DialogHeader className="mb-4">
          <DialogTitle className="text-center text-xl">
            You&apos;re about to delete this reward
          </DialogTitle>
        </DialogHeader>
        <p className="mb-6 text-center text-gray-text">
          This means it will be removed from the Arena Champions rewards pool.
        </p>
        <div className="flex justify-between gap-4">
          <Button variant="outline" onClick={onClose} className="w-1/3">
            Close
          </Button>
          <Button variant="default" onClick={onConfirm} className="w-2/3">
            Delete Reward
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

interface PromoteToSupportedDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (
    contractAddress: string,
    categories: SystemCurrencyCategoryEnum[],
  ) => Promise<void>;
  contractAddress?: string;
}

const PromoteToSupportedDialog: FC<PromoteToSupportedDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  contractAddress: initialContractAddress = "",
}) => {
  const [contractAddress, setContractAddress] = useState(
    initialContractAddress,
  );
  const [categories, setCategories] = useState<
    Record<SystemCurrencyCategoryEnum, boolean>
  >({
    [SystemCurrencyCategoryEnum.SUPPORTED]: true,
    [SystemCurrencyCategoryEnum.TIPPING]: false,
    [SystemCurrencyCategoryEnum.EXCHANGE]: false,
  });

  useEffect(() => {
    if (isOpen) {
      setContractAddress(initialContractAddress);
    }
  }, [isOpen, initialContractAddress]);

  const handleConfirm = async () => {
    try {
      const selectedCategories = Object.entries(categories)
        .filter(([, isSelected]) => isSelected)
        .map(([category]) => category as SystemCurrencyCategoryEnum);

      await onConfirm(contractAddress, selectedCategories);
      onClose();
    } catch (error) {
      // Error handling is done in the onConfirm function
    }
  };

  const handleCategoryChange = (
    category: SystemCurrencyCategoryEnum,
    checked: boolean,
  ) => {
    if (category === SystemCurrencyCategoryEnum.SUPPORTED) {
      setCategories({
        ...categories,
        [SystemCurrencyCategoryEnum.SUPPORTED]: checked,
        [SystemCurrencyCategoryEnum.TIPPING]: checked
          ? categories[SystemCurrencyCategoryEnum.TIPPING]
          : false,
        [SystemCurrencyCategoryEnum.EXCHANGE]: checked
          ? categories[SystemCurrencyCategoryEnum.EXCHANGE]
          : false,
      });
    } else {
      if (!categories[SystemCurrencyCategoryEnum.SUPPORTED]) {
        return;
      }
      setCategories({
        ...categories,
        [category]: checked,
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md border-dark-gray bg-gray-bg shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)]">
        <DialogHeader className="mb-4">
          <DialogTitle className="text-center text-xl">
            Promote to Supported Project
          </DialogTitle>
        </DialogHeader>

        <p className="mb-6 text-center text-gray-text">
          Additional information required to promote the project to a supported
          token.
        </p>

        <div className="space-y-6">
          <div>
            <Label className="text-xs font-medium uppercase text-gray-text">
              CONTRACT ADDRESS
            </Label>
            <TextInput
              value={contractAddress}
              onChange={(e) => setContractAddress(e.target.value)}
              placeholder="0xExample"
              className="mt-2 bg-[#111] text-off-white"
            />
          </div>

          <div>
            <CategoryCheckboxes
              categories={categories}
              onCategoryChange={handleCategoryChange}
            />
          </div>
        </div>

        <div className="mt-6 flex justify-between gap-4">
          <Button variant="outline" onClick={onClose} className="w-1/3">
            Close
          </Button>
          <Button variant="default" onClick={handleConfirm} className="w-2/3">
            Promote Project
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

interface RewardEditProps {
  rewardId: string;
}

export const RewardEdit: FC<RewardEditProps> = memo(({ rewardId }) => {
  const router = useRouter();
  const { setSelectedReward } = useAdmin();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isPromoteDialogOpen, setIsPromoteDialogOpen] = useState(false);

  const previousRewardIdRef = useRef(rewardId);

  const timestamp = useMemo(() => {
    if (previousRewardIdRef.current !== rewardId) {
      previousRewardIdRef.current = rewardId;
      return Date.now();
    }
    return previousRewardIdRef.current === rewardId ? undefined : Date.now();
  }, [rewardId]);

  const queryKey = useMemo(
    () => (timestamp ? ["reward", rewardId, timestamp] : ["reward", rewardId]),
    [rewardId, timestamp],
  );

  const {
    data: existingReward,
    isLoading,
    refetch,
  } = useRewardByIdQuery(rewardId, queryKey);

  useEffect(() => {
    if (previousRewardIdRef.current !== rewardId) {
      refetch();
    }
  }, [rewardId, refetch]);

  useEffect(() => {
    if (existingReward) {
      setSelectedReward({
        id: existingReward.id,
        name: existingReward.systemCurrency.name,
        image: existingReward.systemCurrency.image,
        rewardAmount: existingReward.amount,
      });
    }
  }, [existingReward, setSelectedReward]);

  const formattedReward = useMemo(() => {
    if (!existingReward) return undefined;

    return {
      name: existingReward.systemCurrency.name,
      symbol: existingReward.systemCurrency.symbol,
      contractAddress: existingReward.systemCurrency.address || "",
      amount: existingReward.amount,
      decimals: existingReward.systemCurrency.decimals || 18,
      image: existingReward.systemCurrency.image,
      status: existingReward.status,
    };
  }, [existingReward]);

  const handleSubmit = async (data: RewardFormData) => {
    try {
      if (!existingReward || !existingReward.systemCurrency.id) {
        toast.danger("Invalid reward data");
        return;
      }

      const submitData = {
        systemCurrencyId: existingReward.systemCurrency.id,
        status: data.status,
        amount: data.amount || 0,
      };

      if (data.image && data.image !== existingReward.systemCurrency.image) {
        await updateCurrency(existingReward.systemCurrency.id, {
          image: data.image,
        });
      }

      await updateReward(rewardId, submitData);
      toast.green("Reward updated successfully");
      router.replace("/moderation/admin", { scroll: false });
    } catch (error) {
      toast.danger(
        error instanceof Error ? error.message : "Failed to save reward",
      );
    }
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteReward(rewardId);
      toast.green("Reward removed successfully");
      router.replace("/moderation/admin", { scroll: false });
    } catch (error) {
      toast.danger(
        error instanceof Error ? error.message : "Failed to remove reward",
      );
    }
  };

  const handlePromoteConfirm = async (
    contractAddress: string,
    categories: SystemCurrencyCategoryEnum[],
  ) => {
    try {
      if (!existingReward) return;

      await updateCurrency(existingReward.systemCurrency.id, {
        address: contractAddress,
        categories,
      });

      toast.green("Project promoted successfully");
      router.replace("/moderation/admin", { scroll: false });
    } catch (error) {
      toast.danger(
        error instanceof Error ? error.message : "Failed to promote project",
      );
    }
  };

  const handleRemoveReward = useCallback(() => {
    setIsDeleteDialogOpen(true);
  }, []);

  const handlePromoteProject = useCallback(() => {
    setIsPromoteDialogOpen(true);
  }, []);

  if (isLoading || !existingReward) {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="flex h-screen flex-col">
      <header className="sticky top-0 z-10 flex items-center gap-4 bg-[#141414] p-4">
        <ProgressBarLink href="/moderation/admin" className="size-5">
          <ArrowBackOutlineIcon className="size-5 text-off-white" />
        </ProgressBarLink>
        <h1 className="flex-1 text-center font-semibold">
          Edit Champions Reward
        </h1>
        <div className="size-5" />
      </header>

      <div className="flex-1 overflow-y-auto p-8">
        <RewardForm
          defaultValues={formattedReward}
          onSubmit={handleSubmit}
          existingImage={existingReward.systemCurrency.image}
        >
          <div className="mt-6">
            <h2 className="mb-4 text-sm font-medium uppercase">Actions</h2>
            <Button
              type="button"
              variant="secondary"
              className="mb-4 w-full"
              onClick={handlePromoteProject}
            >
              Promote to Supported Project
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleRemoveReward}
              className="mb-4 w-full"
            >
              Remove Reward
            </Button>
          </div>
        </RewardForm>
      </div>

      <DeleteConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleDeleteConfirm}
      />

      <PromoteToSupportedDialog
        isOpen={isPromoteDialogOpen}
        onClose={() => setIsPromoteDialogOpen(false)}
        onConfirm={handlePromoteConfirm}
        contractAddress={formattedReward?.contractAddress}
      />
    </div>
  );
});

RewardEdit.displayName = "RewardEdit";
