import { Thread, User } from "../../types";

export enum TicketType {
  USER_REPORT = "User Report",
  POST_REPORT = "Post Report",
}

export enum TicketStatus {
  ACTIVE = "Active",
  CLOSED = "Closed",
}

export interface LiveStatus {
  isLive: boolean;
  liveType?: "livestream" | "stage";
}

export interface ModeratorActionLog {
  action: string;
  timestamp: Date;
  moderator: User;
}

export interface ReportTicket {
  id: number;
  type: TicketType;
  timestamp: Date;
  reportedUser: User;
  reportedUserId: string;
  reportingUser: User;
  reportingUserId: string;
  reportType: string;
  content: string;
  status: TicketStatus;
  reportedThread: Thread;
  reportedThreadId?: string;
  isSeen: boolean;
  actionLogs?: ModeratorActionLog[];
}

export interface GroupedTicket {
  reportedUser: User;
  lastReportTimestamp: Date;
  score: number;
  reportCount: number;
  type: string;
  content: string;
  tickets: ReportTicket[];
  isSeen: boolean;
  liveStatus: LiveStatus;
}

export interface GroupedTicketsResponse {
  count: number;
  groups: GroupedTicket[];
  page: number;
  pageSize: number;
}
