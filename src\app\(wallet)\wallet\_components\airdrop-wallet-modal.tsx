"use client";

import { useEffect, useState } from "react";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useWeb3Modal, useWeb3ModalAccount } from "@web3modal/ethers/react";
import { AxiosError } from "axios";

import { postChainSetClaimAddress } from "@/api/client/chain";
import {
  ArrowBackOutlineIcon,
  CheckFilledIcon,
  InformationCircleOutlineIcon,
  XCircleOutlineIcon,
} from "@/components/icons";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useMeQuery } from "@/queries";
import { cn } from "@/utils";

interface AirdropWalletModalProps {
  open: boolean;
  setOpen: (value: boolean) => void;
}

export const AirdropWalletModal = ({
  open,
  setOpen,
}: AirdropWalletModalProps) => {
  const queryClient = useQueryClient();
  const { data } = useMeQuery();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [isWalletChanged, setIsWalletChanged] = useState(false);
  const [updatedWallet, setUpdatedWallet] = useState<string | null>(null);
  const [isError, setIsError] = useState(false);
  const currentWallet = data?.user?.ethereumAddress;
  const web3Modal = useWeb3Modal();
  const [connect, setConnect] = useState<boolean>(false);
  const { address, isConnected } = useWeb3ModalAccount();

  const connectWallet = useMutation({
    mutationFn: async () => {
      if (!isConnected) {
        toast.danger("No wallet connected");
        setIsError(true);
        return;
      }

      let account = address || "";

      if (account === currentWallet) {
        toast.danger("Connected wallet is the same as existing");
        setIsError(true);
        return;
      }

      if (data?.user.address === account) {
        toast.danger("Please connect a different wallet.");
        setIsError(true);
        return;
      }

      try {
        await postChainSetClaimAddress(account);
        setIsWalletChanged(true);
        setUpdatedWallet(account);
        setIsError(false);
        toast.green("Wallet connected! You are ready for the Airdrop.");
      } catch (error) {
        if (error instanceof AxiosError) {
          console.error(error.response?.data);
          toast.danger(
            error.response?.data.message ||
              "An unexpected error occurred. Please try again later.",
          );
        }
        setIsError(true);
      }
      return { account };
    },
  });

  useEffect(() => {
    if (isConnected && connect) {
      connectWallet.mutate();
      setConnect(false);
    }
  }, [isConnected, connect]);

  const handleConnectWallet = async () => {
    await web3Modal.open();
    setConnect(true);
  };

  const handleClose = () => {
    setOpen(false);
    setTimeout(() => {
      setIsWalletChanged(false);
      setUpdatedWallet(null);
      queryClient.invalidateQueries({
        queryKey: ["user", "me"],
      });
    }, 200);
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
        if (!open) {
          handleClose();
        }
      }}
    >
      <DialogContent className="flex h-full w-full flex-col bg-dark-bk p-0 px-6 backdrop-blur-sm sm:h-auto">
        <div className="flex items-center gap-2 pt-[calc(1rem+env(safe-area-inset-top))]">
          <div className="flex-1">
            <DialogClose className="flex flex-shrink-0">
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </DialogClose>
          </div>
          <h2 className="text-base font-semibold leading-[22px] text-off-white">
            Connect Airdrop Wallet
          </h2>
          <div className="flex-1" />
        </div>
        <p className="mt-12 text-sm text-off-white">
          Connect a Metamask wallet on the Avalanche C-Chain to receive your
          $ARENA tokens:
        </p>
        <p className="text-sm text-off-white">
          <span className="font-semibold">
            It can be a different wallet than your current Arena wallet.
          </span>{" "}
          (Hardware wallets supported)
        </p>
        <div className="mt-2 flex flex-col gap-2">
          <div className="flex items-center justify-between gap-2">
            <Label
              htmlFor="wallet-input"
              className={cn(
                isWalletChanged && updatedWallet && "text-light-gray-text",
              )}
            >
              Airdrop Wallet
            </Label>
          </div>
          <div
            className={cn(
              "flex  items-center justify-between gap-4 rounded-lg border border-light-gray-text px-4 py-3 text-sm",
              isError ? "text-gray-text" : "text-light-gray-text",
            )}
          >
            <span className="flex-grow truncate">
              {!currentWallet && !updatedWallet
                ? "No wallet connected"
                : isWalletChanged && updatedWallet
                  ? updatedWallet
                  : currentWallet ?? ""}
            </span>
            <div className="flex-shrink-0">
              {currentWallet || updatedWallet ? (
                <CheckFilledIcon className="size-5 text-light-gray-text" />
              ) : (
                <XCircleOutlineIcon className="size-6 text-light-gray-text" />
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex-1" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            className="flex-1"
            onClick={handleConnectWallet}
            disabled={connectWallet.isPending}
          >
            Connect Wallet
          </Button>
        </div>
        <div className="mt-auto">
          <HelpInfoModal />
        </div>
      </DialogContent>
    </Dialog>
  );
};

const HelpInfoModal = () => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [open, setOpen] = useState(false);

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <div className="flex w-full justify-center">
          <DialogTrigger asChild>
            <Button
              variant="outline"
              className="mb-4 mt-auto flex items-center gap-[10px] border-none text-off-white"
            >
              <InformationCircleOutlineIcon className="size-6" />
              <span className="text-sm font-semibold underline">
                Have trouble connecting? Ask us for help!
              </span>
            </Button>
          </DialogTrigger>
        </div>
        <DialogContent className="max-w-sm gap-4">
          <HelpInfoModalContent />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button
          variant="outline"
          className="mb-4 mt-auto flex w-full items-center gap-[10px] border-none text-off-white"
        >
          <InformationCircleOutlineIcon className="size-6" />
          <span className="text-sm font-semibold underline">
            How do you earn airdrop points?
          </span>
        </Button>
      </DrawerTrigger>
      <DrawerContent className="gap-4">
        <HelpInfoModalContent />
      </DrawerContent>
    </Drawer>
  );
};

const HelpInfoModalContent = () => {
  return (
    <>
      <h3 className="text-base font-semibold leading-[22px] text-off-white">
        Join our Discord Server
      </h3>
      <div className="flex flex-col gap-3 text-sm text-gray-text">
        <p>
          If you need help connecting your wallet, create a ticket in our
          Discord Server for help.
        </p>
        <p>We will reply in 24 hours at most.</p>
      </div>
      <Button className="mt-6 w-full" asChild>
        <a
          href="https://discord.gg/a5Fw3TFP5n"
          target="_blank"
          rel="noreferrer"
        >
          Join The Arena Discord
        </a>
      </Button>
    </>
  );
};
