"use client";

import { useState } from "react";

import { Widget } from "@typeform/embed-react";

import { ArrowBackOutlineIcon } from "@/components/icons";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";

export const BugReportModal = ({ children }: { children: React.ReactNode }) => {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <DialogContent className="h-full max-w-xl overflow-hidden bg-[rgba(15,15,15,0.90)] p-0 pt-[calc(1rem+env(safe-area-inset-top))] backdrop-blur-sm sm:h-auto sm:min-h-[600px]">
          <div className="flex flex-grow flex-col">
            <div className="flex items-center justify-start gap-2 px-6 pb-4 ">
              <DialogClose className="outline-none">
                <ArrowBackOutlineIcon className="size-5" />
              </DialogClose>
            </div>
            <div className="flex-grow">
              <Widget
                id="https://thearena.typeform.com/RequestForm"
                className="size-full"
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
