import { Dispatch, FC, SetStateAction, useEffect, useState } from "react";

import { useInView } from "react-intersection-observer";

import { ArrowBackOutlineIcon } from "@/components/icons";
import useThrottle from "@/hooks/use-throttle";
import { useUserByIdQuery } from "@/queries";
import { Group } from "@/queries/types/chats";
import { useUser } from "@/stores";

import { DirectMessageRequestItem } from "./message-item";
import { TypingsType } from "./messages";

interface DirectMessageRequestProps {
  groups: Group[] | undefined;
  isLoading: boolean;
  setSeen: (groupId: string, date: number) => void;
  setSelectedGroups: Dispatch<SetStateAction<Group[]>>;
  selectedGroups: Group[];
  typings: TypingsType;
  params: {
    groupId?: string | undefined;
  };
  isFetchingNextPage: boolean;
  id: string | undefined;
  setCurrentPage: Dispatch<
    SetStateAction<"messages" | "request" | "receivedRequest">
  >;
  fetchNextDirectMessagesPage: any;
}

export const DirectMessageRequests: FC<DirectMessageRequestProps> = ({
  groups,
  isLoading,
  setSeen,
  setSelectedGroups,
  selectedGroups,
  typings,
  params,
  isFetchingNextPage,
  id,
  fetchNextDirectMessagesPage,
  setCurrentPage,
}) => {
  const { ref, inView } = useInView();
  const { user } = useUser();
  const [search, setSearch] = useState("");
  const throttledSearch = useThrottle(search);

  useEffect(() => {
    if (inView) {
      fetchNextDirectMessagesPage();
    }
  }, [inView]);

  let directMessages: Group[] = [];
  groups?.forEach((group) => {
    if (!group.isTemporary && group.isDirect && group.isRequest) {
      directMessages.push(group);
    }
  });

  return (
    <div className="mt-[23px] px-2">
      <>
        <div className="from-blue-500 to-purple-600 relative flex h-[64px] items-center bg-gradient-to-r px-6 py-[12px] shadow-lg">
          <div className="flex items-center justify-start gap-2">
            <button
              onClick={() => setCurrentPage("messages")}
              className="hover:bg-blue-700 rounded-full p-2 transition-colors duration-200"
            >
              <ArrowBackOutlineIcon className="size-5 text-white" />
            </button>
          </div>
          <h4
            className="absolute text-lg font-semibold text-white"
            style={{ transform: "translateX(-50%)", left: "50%" }}
          >
            Message Requests
          </h4>
        </div>
      </>

      {!isLoading &&
        directMessages &&
        directMessages?.map((group) => {
          return (
            group.lastUserId !== user?.id && (
              <DirectMessageRequestItem
                key={group.id}
                group={group}
                setSeen={() => {
                  setSeen(group.id, Date.now());
                }}
                selectGroup={() => {
                  setSelectedGroups((prev) => {
                    if (prev.length === 1) {
                      return prev;
                    }

                    if (prev.find(({ id }) => id === group.id)) {
                      return prev.filter((g) => g.id !== group.id);
                    }

                    return [...prev, group];
                  });
                }}
                isSelected={Boolean(
                  selectedGroups.find(({ id }) => id === group.id),
                )}
                typingUser={
                  typings[group.id] ? typings[group.id].user.name : undefined
                }
                isConversationOpen={group.id === params?.groupId}
                search={throttledSearch}
              />
            )
          );
        })}
      {!isLoading && !isFetchingNextPage && throttledSearch === "" && (
        <div ref={ref} style={{ visibility: "hidden" }}>
          <p>.</p>
        </div>
      )}
      {selectedGroups.length > 0 && (
        <div className="top-pwa fixed inset-x-0 flex h-[54px] items-center justify-between bg-dark-bk px-6 pt-6 text-off-white">
          <div className="flex items-center gap-4">
            <button
              onClick={() => {
                setSelectedGroups([]);
              }}
            >
              <ArrowBackOutlineIcon className="size-5" />
            </button>
            <div className="text-base leading-5">{selectedGroups.length}</div>
          </div>
          <div></div>
        </div>
      )}
    </div>
  );
};
