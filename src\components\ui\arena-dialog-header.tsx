import React from "react";

import { cn } from "@/utils";

import { ArrowBackOutlineIcon } from "../icons";
import { Button } from "./button";
import { DialogClose } from "./dialog";

interface ModalHeaderProps {
  title: React.ReactNode;
  onBack?: () => void;
  showBack?: boolean;
  rightButton?: React.ReactNode;
  className?: string;
}

export const ArenaDialogHeader: React.FC<ModalHeaderProps> = ({
  title,
  onBack,
  showBack = true,
  rightButton,
  className,
}) => (
  <div
    className={cn(
      "sticky top-0 z-10 flex bg-dark-bk pt-[calc(1rem+env(safe-area-inset-top))] sm:bg-[#1A1A1A] sm:p-0",
      className,
    )}
  >
    <div className="flex w-full items-center">
      {showBack ? (
        onBack ? (
          <Button variant="ghost" size="icon" onClick={onBack}>
            <ArrowBackOutlineIcon className="size-5" />
          </Button>
        ) : (
          <DialogClose className="flex size-10 items-center justify-center">
            <ArrowBackOutlineIcon className="size-5" />
          </DialogClose>
        )
      ) : null}
      <h3 className="ml-2 text-base font-semibold leading-[22px] text-off-white">
        {title}
      </h3>
      <div className="flex-1" />
      {rightButton}
    </div>
  </div>
);
