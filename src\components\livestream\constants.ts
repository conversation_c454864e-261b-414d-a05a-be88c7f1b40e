export const ROLES = {
  HOST: "HOST",
  MODERATOR: "MODERATOR",
  VIEWER: "VIEWER",
} as const;

export const ROLE_NAMES = {
  [ROLES.HOST]: "Host",
  [ROLES.MODERATOR]: "Moderator",
  [ROLES.VIEWER]: "Viewer",
} as const;

export const ROLE_ORDER = [ROLES.HOST, ROLES.MODERATOR, ROLES.VIEWER] as const;

export const INVITED_ROLES = {
  [ROLES.MODERATOR]: ROLES.MODERATOR,
} as const;

export type Role = (typeof ROLES)[keyof typeof ROLES];

export type InvitedRole = (typeof INVITED_ROLES)[keyof typeof INVITED_ROLES];
