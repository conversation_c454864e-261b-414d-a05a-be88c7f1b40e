"use client";

import { memo, useMemo } from "react";
import Image from "next/image";

import { useQuery, useQueryClient } from "@tanstack/react-query";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  livestreamQueries,
  useFollowMutation,
  userQueries,
  useSharesStatsQuery,
  useUnfollowMutation,
} from "@/queries";
import { TradesUsersTrendingResponse } from "@/queries/types";
import { useLivestreamStore } from "@/stores/livestream";
import { User } from "@/types";
import { abbreviateNumber, cn, formatAvax } from "@/utils";

import { ProgressBarLink } from "../progress-bar";
import { toast } from "../toast";
import { TradeTicketsModal } from "../trade-tickets-modal";
import { Button } from "../ui/button";
import { useDataChannelsContext } from "./stores/data-channels-context";

export const HostInfo = memo(() => {
  const queryClient = useQueryClient();
  const id = useLivestreamStore((state) => state.id!);
  const didFollowHost = useLivestreamStore((state) => state.didFollowHost);
  const actions = useLivestreamStore((state) => state.actions);
  const { data } = useQuery(livestreamQueries.livestreamSimpleInfo(id));
  const { chat } = useDataChannelsContext();

  const {
    data: userData,
    isLoading: isUserDataLoading,
    refetch: refetchUserData,
  } = useQuery({
    ...userQueries.byHandle(data?.host.user.twitterHandle ?? ""),
  });
  const { data: statsData, isLoading: isStatsDataLoading } =
    useSharesStatsQuery({
      userId: data?.host.id,
    });

  const followersCount = abbreviateNumber(userData?.user?.followerCount ?? 0);

  const { mutateAsync: follow } = useFollowMutation({
    onMutate: async () => {
      toast.green(`You're now following ${data?.host.user.twitterName}!`);
      await queryClient.cancelQueries({
        queryKey: ["user", "handle", data?.host.user.twitterHandle],
      });
      await queryClient.cancelQueries({
        queryKey: ["trade", "users", "trending"],
      });

      const previousUser = queryClient.getQueryData([
        "user",
        "handle",
        data?.host.user.twitterHandle,
      ]);
      const previousTrendingUsers = queryClient.getQueryData([
        "trade",
        "users",
        "trending",
      ]);

      queryClient.setQueryData(
        ["user", "handle", data?.host.user.twitterHandle],
        (
          old:
            | {
                user: User;
              }
            | undefined,
        ) => {
          if (!old) {
            return old;
          }

          return {
            ...old,
            user: {
              ...old.user,
              following: true,
            },
          };
        },
      );
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        (old: TradesUsersTrendingResponse) => {
          if (!old) return old;

          return {
            ...old,
            users: old.users.map((u) => {
              if (u.id === data?.host.id) {
                return {
                  ...u,
                  following: true,
                };
              }
              return u;
            }),
          };
        },
      );

      return { previousUser, previousTrendingUsers };
    },
    onSuccess: () => {
      if (!didFollowHost) {
        chat.send(
          JSON.stringify({
            type: "followed",
            data: {
              followedUser: {
                id: data?.host.id,
                name: data?.host.user.twitterName,
                username: data?.host.user.twitterHandle,
              },
            },
          }),
        );
        actions.setDidFollowHost(true);
      }
      refetchUserData();
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to follow ${data?.host.user.twitterName}.`);
      queryClient.setQueryData(
        ["user", "handle", data?.host.user.twitterHandle],
        context.previousUser,
      );
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        context?.previousTrendingUsers,
      );
    },
  });
  const { mutateAsync: unfollow } = useUnfollowMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["user", "handle", data?.host.user.twitterHandle],
      });
      await queryClient.cancelQueries({
        queryKey: ["trade", "users", "trending"],
      });

      const previousUser = queryClient.getQueryData([
        "user",
        "handle",
        data?.host.user.twitterHandle,
      ]);
      const previousTrendingUsers = queryClient.getQueryData([
        "trade",
        "users",
        "trending",
      ]);

      queryClient.setQueryData(
        ["user", "handle", data?.host.user.twitterHandle],
        (
          old:
            | {
                user: User;
              }
            | undefined,
        ) => {
          if (!old) {
            return old;
          }

          return {
            ...old,
            user: {
              ...old.user,
              following: false,
            },
          };
        },
      );
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        (old: TradesUsersTrendingResponse) => {
          if (!old) return old;

          return {
            ...old,
            users: old.users.map((u) => {
              if (u.id === data?.host.id) {
                return {
                  ...u,
                  following: false,
                };
              }
              return u;
            }),
          };
        },
      );

      return { previousUser, previousTrendingUsers };
    },
    onSuccess: () => {
      refetchUserData();
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to unfollow ${data?.host.user.twitterName}.`);
      queryClient.setQueryData(
        ["user", "handle", data?.host.user.twitterHandle],
        context.previousUser,
      );
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        context?.previousTrendingUsers,
      );
    },
  });

  const ticketPrice = useMemo(() => {
    if (isUserDataLoading || isStatsDataLoading) return "";

    const formattedEther = formatAvax(
      statsData?.stats?.keyPrice ?? userData?.user?.keyPrice ?? "",
    );

    if (parseFloat(formattedEther) >= 1) {
      return parseFloat(formattedEther).toFixed(2);
    }

    return formattedEther;
  }, [
    statsData?.stats?.keyPrice,
    userData?.user?.keyPrice,
    isUserDataLoading,
    isStatsDataLoading,
  ]);

  const handleFollow = () => {
    if (!userData) return;

    if (userData?.user?.following) {
      unfollow({ userId: userData.user.id });
    } else {
      follow({ userId: userData.user.id });
    }
  };

  return (
    <div className="flex flex-grow gap-2">
      <div className="flex flex-grow items-center justify-between gap-2 overflow-hidden rounded-[10px] bg-lighter-background p-4">
        <div className="flex items-center gap-2">
          <Avatar className="size-9">
            <ProgressBarLink href={`/${data?.host.user.twitterHandle}`}>
              <AvatarImage src={data?.host.user.twitterPicture} />
              <AvatarFallback />
            </ProgressBarLink>
          </Avatar>
          <div className="flex w-full min-w-0  flex-col text-sm leading-4">
            <div className="flex gap-1.5">
              <ProgressBarLink
                href={`/${data?.host.user.twitterHandle}`}
                className="truncate text-sm text-off-white"
              >
                {data?.host.user.twitterName}
              </ProgressBarLink>
            </div>
            <ProgressBarLink
              href={`/${data?.host.user.twitterHandle}`}
              className="truncate text-xs text-gray-text"
            >
              @{data?.host.user.twitterHandle}
            </ProgressBarLink>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <TradeTicketsModal
            userHandle={data?.host.user.twitterHandle}
            onSuccess={(amount) => {
              chat.send(
                JSON.stringify({
                  type: "bought-ticket",
                  data: {
                    user: {
                      id: data?.host.id,
                      name: data?.host.user.twitterName,
                      username: data?.host.user.twitterHandle,
                    },
                    amount,
                  },
                }),
              );
            }}
          >
            <Button className="min-w-[160px] flex-1 gap-1 py-2">
              Buy{" "}
              <Image
                src="/assets/coins/avax.png"
                className="ml-0.5 size-3 rounded-full brightness-110 grayscale"
                alt={`AVAX logo`}
                width={12}
                height={12}
              />
              <span className="text-xs font-medium leading-5 text-off-white">
                {ticketPrice}
              </span>
            </Button>
          </TradeTicketsModal>
          <Button
            variant="outline"
            className={cn(
              "w-[120px] px-[10px] py-2",
              !userData?.user?.following && "border-brand-orange",
            )}
            onClick={handleFollow}
          >
            {userData?.user?.following ? "Unfollow" : "Follow"}
          </Button>
        </div>
      </div>
      <div className="flex flex-col items-center justify-center gap-1 overflow-hidden rounded-[10px] bg-lighter-background px-6 py-4 font-semibold">
        <div className="text-2xl leading-none text-off-white">
          {followersCount}
        </div>
        <div className="text-base leading-none text-gray-text">Followers</div>
      </div>
    </div>
  );
});

HostInfo.displayName = "HostInfo";
