"use client";

import { FC, memo, useEffect } from "react";
import { useRouter } from "next/navigation";

import {
  createReward,
  RewardCurrencyStatusEnum,
} from "@/api/client/admin-rewards";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";

import { useAdmin } from "../../../context/admin-context";
import { RewardForm, RewardFormData } from "./reward-form";

export const RewardCreate: FC = memo(() => {
  const router = useRouter();
  const { setSelectedReward } = useAdmin();

  useEffect(() => {
    setSelectedReward({
      id: "new",
      name: "",
      image: "",
      rewardAmount: 0,
    });
  }, [setSelectedReward]);

  const handleSubmit = async (data: RewardFormData) => {
    try {
      const submitData = {
        symbol: data.symbol,
        name: data.name,
        amount: data.amount || 0,
        status: data.status,
        decimals: data.decimals,
        contractAddress: data.contractAddress,
        image: data.image,
      };

      await createReward(submitData);
      toast.green("Reward created successfully");
      router.push("/moderation/admin");
    } catch (error) {
      toast.danger(
        error instanceof Error ? error.message : "Failed to save reward",
      );
    }
  };

  return (
    <div className="flex h-screen flex-col">
      <header className="sticky top-0 z-10 flex items-center gap-4 bg-[#141414] p-4">
        <ProgressBarLink href="/moderation/admin" className="size-5">
          <ArrowBackOutlineIcon className="size-5 text-off-white" />
        </ProgressBarLink>
        <h1 className="flex-1 text-center font-semibold">
          Add new Champions Reward
        </h1>
        <div className="size-5" />
      </header>

      <div className="flex-1 overflow-y-auto p-8">
        <RewardForm onSubmit={handleSubmit} />
      </div>
    </div>
  );
});

RewardCreate.displayName = "RewardCreate";
