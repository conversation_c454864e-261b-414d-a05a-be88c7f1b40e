import { useEffect, useState } from "react";

import { useIsUserBannedQuery } from "@/queries/groups-queries";
import { useUser } from "@/stores/user";
import { CommunityExtended } from "@/types/community";

type CommunityData =
  | {
      community: CommunityExtended;
      userTokenBalance?: string;
    }
  | undefined;

export const useCanPost = (communityData: CommunityData) => {
  const [hasEnoughTokens, setHasEnoughTokens] = useState(false);

  const { user } = useUser();

  const { data: isUserBannedFromCommunity } = useIsUserBannedQuery(
    communityData?.community.id || "",
    user?.id || "",
  );

  const canPost =
    user?.id === communityData?.community.ownerId ||
    (hasEnoughTokens && isUserBannedFromCommunity === false);

  useEffect(() => {
    if (communityData?.userTokenBalance) {
      setHasEnoughTokens(
        BigInt(communityData.userTokenBalance) >=
          BigInt(communityData.community.postingThreshold),
      );
    }
  }, [communityData]);

  return {
    hasEnoughTokens,
    setHasEnoughTokens,
    canPost,
    isUserBannedFromCommunity,
  };
};
