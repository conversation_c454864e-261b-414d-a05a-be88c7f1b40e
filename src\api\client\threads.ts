import { axios } from "@/lib/axios";
import { ThreadsResponse } from "@/queries/types";
import { ThreadsGIFResponse } from "@/queries/types/threads";
import {
  CommunityModerateActionResponse,
  CommunityModerateDeletePayload,
  CommunityModeratePinPayload,
  PostQuoteData,
  PostThreadAnswerData,
  PostThreadData,
  PostThreadResponse,
  QuoteResponse,
  ThreadActionData,
  ThreadPinData,
  ThreadResponse,
} from "@/types";

export const getThreads = async ({
  page,
  pageSize,
}: {
  page: number;
  pageSize: number;
}) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/feed/my?${searchParams.toString()}`,
  );
  return response.data;
};

export const getStagesThreads = async ({
  page,
  pageSize,
}: {
  page: number;
  pageSize: number;
}) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/get-stages?${searchParams.toString()}`,
  );
  return response.data;
};

export const getLivestreamsThreads = async ({
  page,
  pageSize,
}: {
  page: number;
  pageSize: number;
}) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/get-livestreams?${searchParams.toString()}`,
  );
  return response.data;
};

export const getTrendingThreads = async ({
  page,
  pageSize,
}: {
  page: number;
  pageSize: number;
}) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/feed/trendingPosts?${searchParams.toString()}`,
  );
  return response.data;
};

export const getTrenchesFeedThreads = async ({
  page,
  pageSize,
}: {
  page: number;
  pageSize: number;
}) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/feed/trenchesFeedPosts?${searchParams.toString()}`,
  );
  return response.data;
};

export const getUploadedSize = async () => {
  const response = await axios.get<number>(`/threads/totalUploaded`);

  return response.data;
};

export const getBookmarks = async ({
  page,
  pageSize,
}: {
  page: number;
  pageSize: number;
}) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/bookmarks?${searchParams.toString()}`,
  );
  return response.data;
};

export const getThreadsByUser = async ({
  userId,
  page,
  pageSize,
}: {
  userId: string;
  page: number;
  pageSize: number;
}) => {
  const searchParams = new URLSearchParams({
    userId,
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/feed/user?${searchParams.toString()}`,
  );
  return response.data;
};

export const getThreadsByCommunity = async ({
  communityId,
  page,
  pageSize,
}: {
  communityId: string;
  page: number;
  pageSize: number;
}) => {
  const searchParams = new URLSearchParams({
    communityId,
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/feed/community?${searchParams.toString()}`,
  );
  return response.data;
};

export const getThreadById = async (threadId: string | null) => {
  if (!threadId) return;
  const searchParams = new URLSearchParams({
    threadId,
  });

  const response = await axios.get<ThreadResponse>(
    `/threads?${searchParams.toString()}`,
  );
  return response.data;
};

export const getAnswersByThread = async ({
  threadId,
  page,
  pageSize,
}: {
  threadId: string;
  page: number;
  pageSize: number;
}) => {
  const searchParams = new URLSearchParams({
    threadId,
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/answers?${searchParams.toString()}`,
  );
  return response.data;
};

export const getNestedAnswersByThread = async ({
  threadId,
  page,
  pageSize,
}: {
  threadId: string;
  page: number;
  pageSize: number;
}) => {
  const searchParams = new URLSearchParams({
    threadId,
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/nested?${searchParams.toString()}`,
  );
  return response.data;
};

export const getThreadsGIFs = async (searchString?: string) => {
  const searchParams = searchString
    ? new URLSearchParams({
        searchString,
      })
    : null;

  const response = await axios.get<ThreadsGIFResponse>(
    `/threads/gif${searchParams ? "?" + searchParams.toString() : ""}`,
  );
  return response.data;
};

export const postThread = async (data: PostThreadData) => {
  const response = await axios.post<PostThreadResponse>("/threads", data);
  return response.data;
};

export const postQuote = async (data: PostQuoteData) => {
  const response = await axios.post<QuoteResponse>("/threads/quote", data);
  return response.data;
};

export const postRepost = async (data: { threadId: string }) => {
  const response = await axios.post<ThreadResponse>("/threads/repost", data);
  return response.data;
};

export const deleteRepost = async ({ threadId }: { threadId: string }) => {
  const searchParams = new URLSearchParams({
    threadId,
  });

  const response = await axios.delete<ThreadResponse>(
    `/threads/repost?${searchParams.toString()}`,
  );
  return response.data;
};

export const postThreadAnswer = async (data: PostThreadAnswerData) => {
  const response = await axios.post<ThreadResponse>("/threads/answer", data);
  return response.data;
};

export const postLikeThread = async ({ threadId }: ThreadActionData) => {
  const response = await axios.post<ThreadResponse>("/threads/like", {
    threadId,
  });
  return response.data;
};

export const postUnlikeThread = async ({ threadId }: ThreadActionData) => {
  const response = await axios.post<ThreadResponse>("/threads/unlike", {
    threadId,
  });
  return response.data;
};

export const postBookmarkThread = async ({ threadId }: ThreadActionData) => {
  const response = await axios.post<ThreadResponse>("/threads/bookmark", {
    threadId,
  });
  return response.data;
};

export const postUnbookmarkThread = async ({ threadId }: ThreadActionData) => {
  const response = await axios.post<ThreadResponse>("/threads/unbookmark", {
    threadId,
  });
  return response.data;
};

export const postTipThread = async (data: {
  threadId?: string;
  tipAmount: string;
  userId: string;
  currency: string;
}) => {
  const response = await axios.post<ThreadResponse>("/threads/tip", data);
  return response.data;
};

export const postTipNotify = async (data: {
  threadId?: string;
  isToken?: boolean;
  tokenContractAddress?: string;
  tipAmount: string;
  userId: string;
  currency: string;
  txHash: string;
  txData: string;
}) => {
  const response = await axios.post<ThreadResponse>("/threads/tipNotify", data);
  return response.data;
};

export const postPinThread = async ({ threadId, isPinned }: ThreadPinData) => {
  const response = await axios.post<ThreadResponse>("/threads/pin", {
    threadId,
    isPinned,
  });
  return response.data;
};

export const deleteThread = async ({ threadId }: ThreadActionData) => {
  const searchParams = new URLSearchParams({
    threadId,
  });

  const response = await axios.delete<ThreadResponse>(
    `/threads?${searchParams.toString()}`,
  );
  return response.data;
};

export const moderateDeleteCommunityThread = async (
  data: CommunityModerateDeletePayload,
): Promise<CommunityModerateActionResponse> => {
  const response = await axios.delete<CommunityModerateActionResponse>(
    "/threads/moderate-community-feed/delete",
    {
      data,
    },
  );
  return response.data;
};

export const moderatePinCommunityThread = async (
  data: CommunityModeratePinPayload,
) => {
  const response = await axios.post<CommunityModerateActionResponse>(
    "/threads/moderate-community-feed/pin",
    data,
  );
  return response.data;
};
