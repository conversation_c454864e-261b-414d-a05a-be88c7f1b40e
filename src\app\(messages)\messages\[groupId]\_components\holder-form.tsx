import { Dispatch, FC, SetStateAction, useState } from "react";
import { useParams } from "next/navigation";

import { TradeTicketsModal } from "@/app/(main)/[userHandle]/_components/trade-tickets-modal";
import { ProgressBarLink } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";

interface HolderFormProps {
  setIsTicketPurchased: Dispatch<SetStateAction<boolean>>;
  subjectName: string;
}

export const HolderForm: FC<HolderFormProps> = ({
  setIsTicketPurchased,
  subjectName,
}) => {
  const hint = `You must own this user's ticket to be able to send Direct Messages.`;

  return (
    <>
      <div className="flex w-full flex-col gap-4 border-r-2 border-t-2 border-dark-gray bg-gray-bg p-6">
        <span className="text-sm">{hint}</span>
        <div className="flex w-full gap-2.5">
          <div className="flex w-1/2">
            <TradeTicketsModal
              userHandle={subjectName}
              setIsTicketPurchased={setIsTicketPurchased}
            >
              <Button className="w-full">{`Buy Ticket`}</Button>
            </TradeTicketsModal>
          </div>
          <ProgressBarLink href={"/messages"} className="flex w-1/2">
            <Button variant="outline" className="w-full">
              No, Thanks
            </Button>
          </ProgressBarLink>
        </div>
      </div>
    </>
  );
};
