FROM node:20.9.0-alpine AS base

FROM base AS deps
WORKDIR /app
COPY package*.json ./
RUN npm config set "//registry.tiptap.dev/:_authToken" luH2cGxV+GzGCMEcLEb79O4mG+h98yHe5fAI502oUglM9RfEzxj07Hs6w8+3PfQg
RUN npm ci

FROM base AS builder
ENV NEXT_PUBLIC_MAINNET_RPC_URL "https://api.avax.network/ext/bc/C/rpc"
ENV NEXT_PRIVATE_STANDALONE true

# production
#ENV NEXT_PUBLIC_API_URL="https://api.starsarena.com"
#ENV NEXT_PUBLIC_SOCKET_URL="https://api.starsarena.com"
#ENV NEXT_PUBLIC_APP_DOMAIN="https://v2.starsarena.com"
# stage
ENV NEXT_PUBLIC_API_URL="https://api.satest-dev.com"
ENV NEXT_PUBLIC_SOCKET_URL="https://api.satest-dev.com"
ENV NEXT_PUBLIC_APP_DOMAIN="https://satest-dev.com"
ENV NEXT_PUBLIC_DYNAMIC_ENVIRONMENT_ID="0f187ac0-6ac1-49ec-85b1-dc811006cde0"
ENV NEXT_PUBLIC_LIVEKIT_URL="wss://the-arena-staging-24i3jrx1.livekit.cloud"
ENV NEXT_PUBLIC_POSTHOG_KEY="phc_9OcBtIl44kiqT5vouTHbCL637gYSTyL2HIFZVXhbZ8O"
ENV NEXT_PUBLIC_POSTHOG_HOST="https://us.i.posthog.com"
ENV NEXT_PUBLIC_HTTP_BASIC_AUTH_ENABLED="true"
ENV NEXT_PUBLIC_HTTP_BASIC_AUTH="stagingViewer:BSlociDzV!9Eql%u"

ARG NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
ENV NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=$NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY

WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

FROM base AS runner
WORKDIR /app
COPY --from=builder /app/public ./public
RUN mkdir .next
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
EXPOSE 8080
ENV PORT 8080
ENV HOSTNAME "0.0.0.0"
CMD ["node", "server.js"]
