"use client";

import { useEffect, useRef, useState } from "react";

import QRCodeStyling from "qr-code-styling";

import { CopyIcon, ShieldIcon } from "@/components/icons-v2/account-security";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

interface IAddMfaModalProps {
  mfaRegisterData: MfaRegisterData;
  onContinue: () => void;
  onClose: () => void;
}

interface MfaRegisterData {
  uri: string;
  secret: string;
}

export const AddMfaModal = (props: IAddMfaModalProps) => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [qrCodeMode, setQrCodeMode] = useState(true);
  const ref = useRef(null);

  const copyAll = () => {
    const textToCopy = props.mfaRegisterData.secret;
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        toast.green("Setup key copied!");
      })
      .catch((err) => {
        toast.red("Failed to copy Setup key!");
      });
  };

  const qrCode = new QRCodeStyling({
    width: 270,
    height: 270,
    type: "svg",
    data: props.mfaRegisterData.uri,
    dotsOptions: {
      color: "#000",
      type: "dots",
    },
    backgroundOptions: {
      color: "#fff",
    },
    image: "/images/arena_qr.png",
    imageOptions: {
      crossOrigin: "anonymous",
      margin: 4,
      imageSize: 0.3,
    },
  });

  useEffect(() => {
    if (ref.current) {
      qrCode.append(ref.current);
    }
  }, []);

  return (
    <div
      className={
        isTablet
          ? "fixed inset-0 z-40 flex content-center items-center justify-center"
          : ""
      }
    >
      <div
        className="bg-black fixed inset-0 z-30 bg-[#020202CC]"
        onClick={props.onClose}
      />
      <div
        className={`fixed z-50 border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] px-[24px] pb-[48px] pt-[24px] shadow-[0px_4px_16px_0px_rgba(0,0,0,0.25)]  ${isTablet ? "rounded-[20px]" : "bottom-[0px] left-0 right-0 rounded-b-none rounded-t-[20px]"}`}
      >
        <div
          className={`inline-flex w-full flex-col items-start justify-center gap-[32px] ${isTablet ? "mx-auto max-w-[402px]" : ""}`}
        >
          <div className="font-inter flex flex-col items-center gap-[8px] self-stretch text-[16px] font-semibold leading-[22px] text-[#F4F4F4]">
            <ShieldIcon />
            <p>Add a new authenticator app</p>
          </div>
          <div className="flex w-full justify-center">
            {qrCodeMode && (
              <div
                className={`flex bg-[var(--LIGHT-BACKGROUND,#1A1A1A)] ${isTablet ? "w-[314px]" : "w-full"} border-[var(--GRAY-TEXT, #808080D9)] flex-col items-center justify-start gap-[12px] self-stretch rounded-[9.179px] border-[0.918px] p-[22px_22px_11px_22px]`}
              >
                {<div ref={ref} className={"w-[270px], h-[270px]"} />}
                <div
                  className="font-inter cursor-pointer text-center text-[11.015px] font-normal leading-[16.523px] text-light-gray-text [leading-trim:both] [text-edge:cap]"
                  onClick={() => setQrCodeMode(false)}
                >
                  {"Can’t scan QR code?"}
                </div>
              </div>
            )}
            {!qrCodeMode && (
              <div
                className={`flex ${isTablet ? "w-[314px]" : "w-full"} flex-col items-center justify-start gap-[12px] self-stretch`}
              >
                <div className="font-inter self-stretch text-[12.851px] font-normal leading-[19.277px] text-light-gray-text">
                  Enter the following code in your authenticator app to set up
                  authentication. Please note that it must be a time-based
                  authenticator.
                </div>
                <div className="border-[var(--GRAY-TEXT, #808080)] flex h-[44px] items-center justify-between self-stretch rounded-[10px] border-[1px] bg-[var(--LIGHT-BACKGROUND,#1A1A1A)] p-[10px_16px]">
                  <div className="font-inter text-center text-[14px] font-normal lowercase leading-[16.794px] text-light-gray-text">
                    {props.mfaRegisterData.secret}
                  </div>
                  <div className="h-[17px] w-[17px]" onClick={copyAll}>
                    <CopyIcon />
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="flex w-full items-center gap-[8px]">
            <Button
              className="flex-grow basis-[66%] sm:w-[234px]"
              onClick={props.onContinue}
            >
              Continue
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
