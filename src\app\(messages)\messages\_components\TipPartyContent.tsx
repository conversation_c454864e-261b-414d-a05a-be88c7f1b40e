import React from "react";

import * as RadioGroup from "@radix-ui/react-radio-group";

import { Button } from "@/components/ui/button";

export interface TipPartyOption {
  value: string;
  label: React.ReactNode;
  emoji?: React.ReactNode;
}

interface TipPartyContentProps {
  options: TipPartyOption[];
  selected: string;
  setSelected: (value: string) => void;
  infoText: React.ReactNode;
  onContinue: () => void;
  continueLabel?: string;
  disabled?: boolean;
}

export const TipPartyContent: React.FC<TipPartyContentProps> = ({
  options,
  selected,
  setSelected,
  infoText,
  onContinue,
  continueLabel = "Continue",
  disabled = false,
}) => {
  return (
    <div className="flex flex-1 flex-col gap-5 ">
      <div className="flex flex-1 flex-col justify-center gap-5">
        <RadioGroup.Root
          onValueChange={setSelected}
          value={selected}
          className="flex justify-between gap-4"
        >
          {options.map((opt) => (
            <RadioGroup.Item
              key={opt.value}
              value={opt.value}
              className="rounded-[10px] border border-[#646464] aria-checked:border-[#EB540A]"
              asChild
            >
              <button className="relative flex flex-1 items-center gap-4 p-4 text-left">
                <p className="whitespace-pre-line text-sm font-semibold text-off-white">
                  {opt.label}
                </p>
                {opt.emoji && (
                  <span className="pointer-events-none absolute bottom-1 right-0 text-4xl">
                    {opt.emoji}
                  </span>
                )}
              </button>
            </RadioGroup.Item>
          ))}
        </RadioGroup.Root>
        <div className="rounded-lg bg-[#1A1A1A] p-4 text-center sm:bg-[#2A2A2A]">
          {infoText}
        </div>
      </div>
      <Button
        className="mt-8 w-full sm:static sm:mt-8"
        onClick={onContinue}
        disabled={disabled}
      >
        {continueLabel}
      </Button>
    </div>
  );
};
