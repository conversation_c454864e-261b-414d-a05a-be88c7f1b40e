import { useMemo } from "react";

import { User } from "@/queries/types";
import { abbreviateNumber, cn, divideBigInt, formatAvax } from "@/utils";

import { ArenaLogo } from "./icons";
import { TriangleDownOutlineIcon } from "./icons/triangle-down-outline";
import { TriangleUpOutlineIcon } from "./icons/triangle-up-outline";
import { ProgressBarLink } from "./progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Button } from "./ui/button";

interface UserCardUIProps {
  user: User;
  handleFollow: () => void;
}

export const UserCardUI = ({ user, handleFollow }: UserCardUIProps) => {
  const ticketPrice = useMemo(() => {
    if (user.stats?.keyPrice && !user.keyPrice) {
      return "0";
    }

    const formattedEther = formatAvax(user.stats?.keyPrice || user.keyPrice);

    if (parseFloat(formattedEther) >= 1) {
      return parseFloat(formattedEther).toFixed(2);
    }

    return formattedEther;
  }, [user.stats?.keyPrice, user.keyPrice]);

  const [isNegative, percentageIncrease] = useMemo(() => {
    const keyPrice = BigInt(user.stats?.keyPrice || 0);
    const lastKeyPrice = BigInt(user?.lastKeyPrice || 0);

    const percentage = lastKeyPrice
      ? 100 * (divideBigInt(keyPrice, lastKeyPrice) - 1)
      : keyPrice
        ? 100
        : 0;

    return [percentage < 0, abbreviateNumber(percentage, 2, false)];
  }, [user.stats?.keyPrice, user?.lastKeyPrice]);

  return (
    <div
      className="relative isolate flex w-[150px] flex-col items-center justify-start gap-[10px] rounded-xl bg-[#2B2B2B] p-4"
      style={{
        background: user.bannerUrl
          ? `url(${user.bannerUrl}) no-repeat center/cover`
          : undefined,
      }}
    >
      <div className="absolute inset-0 -z-10 overflow-hidden rounded-xl">
        <div className="absolute inset-0 bg-[#141414]/80 backdrop-blur-sm" />
        {!user.bannerUrl && (
          <ArenaLogo className="absolute -left-6 -top-10 h-[425px] w-[270px] opacity-5" />
        )}
      </div>
      <ProgressBarLink href={`/${user.twitterHandle}`}>
        <Avatar className="size-12">
          <AvatarImage src={user.twitterPicture} />
          <AvatarFallback />
        </Avatar>
      </ProgressBarLink>
      <div className="flex min-w-0 flex-col items-center justify-start gap-[6px] overflow-hidden text-center">
        <ProgressBarLink
          href={`/${user.twitterHandle}`}
          className="max-w-[118px] truncate text-xs font-medium text-off-white"
        >
          {user.twitterName}
        </ProgressBarLink>
        <ProgressBarLink
          href={`/${user.twitterHandle}`}
          className="max-w-[118px] truncate text-[11px] leading-4 text-gray-text"
        >
          @{user.twitterHandle}
        </ProgressBarLink>
        <div className="flex items-center gap-[6px]">
          <div className="w-max">
            <img
              src="/images/avax.png"
              className="size-[14px] rounded-full"
              alt="AVAX logo"
            />
          </div>
          <span className="text-xs font-medium leading-5 text-off-white">
            {ticketPrice}
          </span>
          <span
            className={cn(
              "flex items-center gap-[4px] text-sm",
              isNegative ? "text-danger" : "text-[#40B877]",
            )}
          >
            {percentageIncrease !== "0" && (
              <>
                {isNegative ? (
                  <TriangleDownOutlineIcon className="h-4 w-4" />
                ) : (
                  <TriangleUpOutlineIcon className="h-4 w-4" />
                )}
              </>
            )}
            <span>{percentageIncrease}%</span>
          </span>
        </div>
      </div>
      <Button
        variant="outline"
        className={cn(
          "w-full min-w-[118px] py-2",
          user.following ? "border-gray-text" : "border-brand-orange",
        )}
        onClick={handleFollow}
      >
        {user.following ? "Unfollow" : "Follow"}
      </Button>
    </div>
  );
};
