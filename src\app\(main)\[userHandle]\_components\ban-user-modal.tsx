"use client";

import { useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useBanUserMutation } from "@/queries/user-mutations";
import { User } from "@/types";

interface BanUserModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: User;
}

export const BanUserModal = ({ open, setOpen, user }: BanUserModalProps) => {
  const queryClient = useQueryClient();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const { mutateAsync: banUser, isPending } = useBanUserMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["user", "isBanned", user.id],
      });

      const previousIsBanned = queryClient.getQueryData([
        "user",
        "isBanned",
        user.id,
      ]);

      queryClient.setQueryData(["user", "isBanned", user.id], true);

      return { previousIsBanned };
    },
    onError: (err, variables, context) => {
      toast.red(`Failed to ban ${user.twitterName}.`);
      queryClient.setQueryData(
        ["user", "isBanned", user.id],
        context?.previousIsBanned,
      );
    },
    onSuccess: () => {
      toast.red(`${user.twitterName} is now banned!`);

      queryClient.invalidateQueries({
        queryKey: ["threads", "user", user.id],
      });
    },
  });

  const handleBan = async () => {
    setOpen(false);
    await banUser({ userId: user.id });
  };

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-sm">
          <BanUserModalContent
            user={user}
            setOpen={setOpen}
            isPending={isPending}
            handleBan={handleBan}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="justify-start gap-6 text-left">
        <BanUserModalContent
          user={user}
          setOpen={setOpen}
          isPending={isPending}
          handleBan={handleBan}
        />
      </DrawerContent>
    </Drawer>
  );
};

interface BanUserModalContentProps {
  user: User;
  setOpen: (open: boolean) => void;
  isPending: boolean;
  handleBan: () => void;
}

const BanUserModalContent = ({
  user,
  setOpen,
  isPending,
  handleBan,
}: BanUserModalContentProps) => {
  return (
    <>
      <DialogHeader className="space-y-4">
        <DialogTitle>Ban @{user.twitterHandle}?</DialogTitle>
        <DialogDescription className="text-gray-text">
          When you ban a user @{user.twitterHandle}, all the chat messages and
          associated threads from @{user.twitterHandle} are removed permanently.
          <br />
          Are you sure you want to proceed?
        </DialogDescription>
      </DialogHeader>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => {
            setOpen(false);
          }}
        >
          Cancel
        </Button>
        <Button
          variant="destructive"
          className="flex-1"
          onClick={handleBan}
          disabled={isPending}
        >
          Ban
        </Button>
      </div>
    </>
  );
};
