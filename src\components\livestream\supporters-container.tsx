"use client";

import { useEffect, useMemo, useRef, useState } from "react";

import {
  useConnectionState,
  useLocalParticipant,
  useParticipantAttributes,
  useRoomInfo,
} from "@livekit/components-react";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { ConnectionState } from "livekit-client";
import Skeleton from "react-loading-skeleton";

import { userQueries } from "@/queries";
import { livestreamQueries } from "@/queries/livestream-queries";
import { TippingInfo } from "@/queries/types/livestream";
import { useLivestreamStore } from "@/stores/livestream";
import { cn } from "@/utils";

import { AnimatedNumber } from "../animated-number";
import { AddCurrencyDollarOutlineIcon } from "../icons";
import { ProgressBarLink } from "../progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { CustomTipModal } from "./livestream-custom-tip-modal";

export function SupportersContainer() {
  const supportersType = useLivestreamStore((state) => state.supportersType);
  const connectionState = useConnectionState();

  if (connectionState !== ConnectionState.Connected) return null;

  if (supportersType === "LIVESTREAM") {
    return <TopStreamSupporters />;
  }

  return <TopCreatorSupporters />;
}

const TopStreamSupporters = () => {
  const localParticipant = useLocalParticipant();
  const { attributes } = useParticipantAttributes({
    participant: localParticipant.localParticipant,
  });
  const livestreamId = useLivestreamStore((state) => state.id!);
  const [customTipOpen, setCustomTipOpen] = useState(false);
  const { data: livestreamData } = useQuery(
    livestreamQueries.livestreamSimpleInfo(livestreamId),
  );

  const { data: userData } = useQuery({
    ...userQueries.byHandle(livestreamData?.host.user.twitterHandle ?? ""),
    enabled: !!livestreamData?.host.user.twitterHandle,
  });

  const roomInfo = useRoomInfo();
  const metadata = roomInfo.metadata
    ? JSON.parse(roomInfo.metadata)
    : { tippingInfo: {} };

  const topStreamTippers = useMemo(() => {
    const tippingInfo = metadata.tippingInfo as TippingInfo;
    return tippingInfo?.topStreamTippers ?? [];
  }, [metadata]);

  // const [tippers, setTippers] = useState<TopTipper[]>([
  //   {
  //     user: {
  //       id: "2602b4ca-0609-497f-ad68-7d45db2b6d73",
  //       twitterHandle: "bobthearenaqa",
  //       twitterName: "bobthearenaqa",
  //       twitterPicture:
  //         "https://static.starsarena.com/uploads/078c7fc3-dea8-4f95-3dd5-dbc481b656be1731696942294.png",
  //     },
  //     totalTipped: 323.56,
  //     lastTip: {
  //       id: "8958ad17-b5fa-448d-974a-76d4d518685b",
  //       amount: 132.86,
  //       createdOn: "2025-03-13T11:45:35.485Z",
  //     },
  //   },
  //   {
  //     user: {
  //       id: "2602b4ca-0609-497f-ad68-rewqrretq",
  //       twitterHandle: "Some other user",
  //       twitterName: "Some other user",
  //       twitterPicture:
  //         "https://static.starsarena.com/uploads/078c7fc3-dea8-4f95-3dd5-dbc481b656be1731696942294.png",
  //     },
  //     totalTipped: 200,
  //     lastTip: {
  //       id: "8958ad17-b5fa-448d-974a-43215",
  //       amount: 87,
  //       createdOn: "2025-03-13T11:45:35.485Z",
  //     },
  //   },
  //   {
  //     user: {
  //       id: "1d46065b-c4df-4b7e-ac53-73505695f07d",
  //       twitterHandle: "i2rrUANURfmV8w5",
  //       twitterName: "Zhapar Manas uulu",
  //       twitterPicture:
  //         "https://static.starsarena.com/uploads/4d22e45b-b83d-238e-90f0-066ed11d6e9c1731696935281.png",
  //     },
  //     totalTipped: 113.88,
  //     lastTip: {
  //       id: "653d82ba-02cb-4423-9ecc-d4cf0c6e44ae",
  //       amount: 113.88,
  //       createdOn: "2025-03-13T11:45:02.577Z",
  //     },
  //   },
  // ]);

  if (!topStreamTippers || topStreamTippers.length === 0) {
    return (
      <>
        <div className="hidden h-full flex-col overflow-y-auto sm:flex ">
          <div className="mt-2 flex-shrink-0 px-4 text-sm text-gray-text">
            Highest tippers for this stream
          </div>
          <div className="relative isolate mt-4 flex h-0 flex-grow flex-col gap-1">
            <UserListItem
              user={{
                id: attributes?.id ?? "",
                twitterHandle: attributes?.username ?? "",
                twitterName: attributes?.name ?? "",
                twitterPicture: attributes?.avatar ?? "",
              }}
              totalTipped={0}
              place={1}
            />
            {Array.from({ length: 9 }).map((_, index) => (
              <UserListItemLoading key={index} place={index + 2} />
            ))}
            <div className="absolute inset-0 z-10 bg-dark-bk/50" />
            <div className="absolute inset-x-6 top-1/2 z-20 flex -translate-y-1/2 flex-col gap-8 rounded-[10px] border border-lighter-background bg-light-background px-4 py-6">
              <div className="flex flex-col gap-6">
                <div className="flex flex-col items-center gap-2">
                  <AddCurrencyDollarOutlineIcon className="size-6 text-off-white" />
                  <h4 className="text-base font-semibold text-off-white">
                    No tips on this stream yet!
                  </h4>
                </div>
                <p className="text-center text-sm text-light-gray-text">
                  Tip this creator more than $1 to compete with other users and
                  be a top supporter!
                </p>
              </div>
              <Button className="w-full" onClick={() => setCustomTipOpen(true)}>
                Tip Creator
              </Button>
            </div>
          </div>
          {userData && userData.user && (
            <CustomTipModal
              userToSend={userData.user}
              open={customTipOpen}
              setOpen={setCustomTipOpen}
            />
          )}
        </div>
        <div className="mt-4 flex flex-col gap-2 px-4 text-center sm:hidden">
          <h4 className="text-base font-semibold text-off-white">
            No tips on this stream yet!
          </h4>
          <p className="text-sm text-light-gray-text">
            Tip this creator more than $1 to compete with other users and be a
            top supporter!
          </p>
        </div>
      </>
    );
  }

  return (
    <div className="flex h-full flex-col overflow-y-auto">
      <div className="mt-2 flex-shrink-0 px-4 text-sm text-gray-text">
        Highest tippers for this stream
        {/* <div className="flex gap-2">
          <button
            className="rounded-md bg-light-background px-2 py-1 text-sm text-gray-text"
            onClick={() =>
              setTippers((prev) => {
                // Create a shuffled copy of the previous state
                const shuffled = [...prev];

                // Fisher-Yates shuffle algorithm
                for (let i = shuffled.length - 1; i > 0; i--) {
                  const j = Math.floor(Math.random() * (i + 1));
                  [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
                }

                return shuffled;
              })
            }
          >
            Randomize
          </button>
          <button
            className="rounded-md bg-light-background px-2 py-1 text-sm text-gray-text"
            onClick={() =>
              setTippers((prev) => {
                return prev.map((tipper, index) => {
                  if (index === 0) {
                    const newTip = (Math.random() * 1000).toFixed(2);
                    return {
                      ...tipper,
                      totalTipped: tipper.totalTipped + Number(newTip),
                      lastTip: {
                        ...tipper.lastTip,
                        id: v4(),
                        amount: newTip,
                      },
                    };
                  }
                  return tipper;
                });
              })
            }
          >
            update first tip
          </button>
        </div> */}
      </div>
      <div className="mt-4 flex h-0 flex-grow flex-col gap-1">
        {topStreamTippers.map((tipper, index) => (
          <motion.div
            key={tipper.user.id}
            layoutId={tipper.user.id + "-top-stream-tipper"}
          >
            <UserListItem
              user={tipper.user}
              totalTipped={tipper.totalTipped}
              lastTip={tipper.lastTip}
              place={index + 1}
            />
          </motion.div>
        ))}
      </div>
    </div>
  );
};

const TopCreatorSupporters = () => {
  const [customTipOpen, setCustomTipOpen] = useState(false);
  const livestreamId = useLivestreamStore((state) => state.id!);
  const { data: livestreamData } = useQuery(
    livestreamQueries.livestreamSimpleInfo(livestreamId),
  );

  const localParticipant = useLocalParticipant();
  const { attributes } = useParticipantAttributes({
    participant: localParticipant.localParticipant,
  });

  const { data: userData } = useQuery({
    ...userQueries.byHandle(livestreamData?.host.user.twitterHandle ?? ""),
    enabled: !!livestreamData?.host.user.twitterHandle,
  });

  const roomInfo = useRoomInfo();
  const metadata = roomInfo.metadata
    ? JSON.parse(roomInfo.metadata)
    : { tippingInfo: {} };

  const topCreatorTippers = useMemo(() => {
    const tippingInfo = metadata.tippingInfo as TippingInfo;
    return (tippingInfo?.topCreatorTippers ?? []).filter(
      (tipper) => tipper != null,
    );
  }, [metadata]);

  if (!topCreatorTippers || topCreatorTippers.length === 0) {
    return (
      <>
        <div className="hidden h-full flex-col overflow-y-auto sm:flex">
          <div className="mt-2 flex-shrink-0 px-4 text-sm text-gray-text">
            Highest tippers for this creator
          </div>
          <div className="relative isolate mt-4 flex h-0 flex-grow flex-col gap-1">
            <UserListItem
              user={{
                id: attributes?.id ?? "",
                twitterHandle: attributes?.username ?? "",
                twitterName: attributes?.name ?? "",
                twitterPicture: attributes?.avatar ?? "",
              }}
              totalTipped={0}
              place={1}
            />
            {Array.from({ length: 9 }).map((_, index) => (
              <UserListItemLoading key={index} place={index + 2} />
            ))}
            <div className="absolute inset-0 z-10 bg-dark-bk/50" />
            <div className="absolute inset-x-6 top-1/2 z-20 flex -translate-y-1/2 flex-col gap-8 rounded-[10px] border border-lighter-background bg-light-background px-4 py-6">
              <div className="flex flex-col gap-6">
                <div className="flex flex-col items-center gap-2">
                  <AddCurrencyDollarOutlineIcon className="size-6 text-off-white" />
                  <h4 className="text-base font-semibold text-off-white">
                    No tips on this creator yet!
                  </h4>
                </div>
                <p className="text-center text-sm text-light-gray-text">
                  Tip this creator more than $1 to compete with other users and
                  be a top supporter!
                </p>
              </div>
              <Button className="w-full" onClick={() => setCustomTipOpen(true)}>
                Tip Creator
              </Button>
            </div>
          </div>
          {userData && userData.user && (
            <CustomTipModal
              userToSend={userData.user}
              open={customTipOpen}
              setOpen={setCustomTipOpen}
            />
          )}
        </div>
        <div className="mt-4 flex flex-col gap-2 px-4 text-center sm:hidden">
          <h4 className="text-base font-semibold text-off-white">
            No tips on this creator yet!
          </h4>
          <p className="text-sm text-light-gray-text">
            Tip this creator more than $1 to compete with other users and be a
            top supporter!
          </p>
        </div>
      </>
    );
  }

  return (
    <div className="flex h-full flex-col overflow-y-auto">
      <div className="mt-2 flex-shrink-0 px-4 text-sm text-gray-text">
        Highest tippers for this creator
      </div>
      <div className="mt-4 flex h-0 flex-grow flex-col gap-1">
        {topCreatorTippers.map((tipper, index) => (
          <motion.div
            key={tipper.user.id}
            layoutId={tipper.user.id + "-top-creator-tipper"}
          >
            <UserListItem
              user={tipper.user}
              totalTipped={tipper.totalTipped}
              lastTip={tipper.lastTip}
              place={index + 1}
            />
          </motion.div>
        ))}
      </div>
    </div>
  );
};

const UserListItem = ({
  user,
  totalTipped,
  lastTip,
  place,
}: {
  user: {
    id: string;
    twitterHandle: string;
    twitterName: string;
    twitterPicture: string;
  };
  totalTipped?: number;
  lastTip?: {
    id: string;
    amount: number;
    createdOn: string;
  };
  place: number;
}) => {
  const [shouldAnimate, setShouldAnimate] = useState(false);
  const prevTipIdRef = useRef(lastTip?.id);

  useEffect(() => {
    if (prevTipIdRef.current !== lastTip?.id) {
      setShouldAnimate(true);
      prevTipIdRef.current = lastTip?.id;
    }
  }, [lastTip?.id]);

  return (
    <ProgressBarLink
      href={`/${user.twitterHandle}`}
      className={cn(
        "flex w-full justify-between gap-4 overflow-hidden px-6 py-2",
      )}
    >
      <div className="flex flex-grow items-center gap-[10px]">
        <div className="relative">
          <Avatar className="relative size-[42px]">
            <AvatarImage src={user.twitterPicture} />
            <AvatarFallback />
          </Avatar>
          <div className="absolute -bottom-1 -right-1 flex size-5 items-center justify-center rounded-full bg-light-background text-xs font-bold leading-none text-off-white shadow-[-1px_-1px_2px_0px_rgba(27,27,27,0.50)]">
            {place}
          </div>
        </div>
        <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
          <div className="flex gap-1.5">
            <h4 className="truncate text-[#F4F4F4]">{user.twitterName}</h4>
          </div>
          <div className="truncate text-[#808080]">@{user.twitterHandle}</div>
        </div>
      </div>
      <div className="flex flex-shrink-0 flex-col items-end justify-center gap-[2px]">
        <div className="flex items-center gap-[6px]">
          <span className="text-sm font-medium text-[#F4F4F4]">
            ${totalTipped ? <AnimatedNumber value={totalTipped} /> : null}
          </span>
        </div>
        <motion.span
          className={cn("flex items-center gap-[4px] text-sm text-gray-text")}
          initial={{ color: "rgb(128, 128, 128)" }}
          animate={
            shouldAnimate
              ? {
                  scale: [1, 1.15, 1, 1, 1],
                  color: [
                    "rgb(128, 128, 128)",
                    "rgb(34, 197, 94)",
                    "rgb(34, 197, 94)",
                    "rgb(34, 197, 94)",
                    "rgb(128, 128, 128)",
                  ],
                }
              : { color: "rgb(128, 128, 128)", scale: 1 }
          }
          transition={{
            duration: 1.5,
            times: [0, 0.05, 0.4, 1.5, 1.5],
            ease: "easeInOut",
          }}
          onAnimationComplete={() => setShouldAnimate(false)}
        >
          +{lastTip?.amount ? numberFormatter.format(lastTip?.amount) : "$"}
        </motion.span>
      </div>
    </ProgressBarLink>
  );
};

const UserListItemLoading = ({ place }: { place: number }) => {
  return (
    <div className="flex w-full justify-between gap-4 overflow-hidden px-6 py-2">
      <div className="flex flex-grow items-center gap-[10px]">
        <div className="relative">
          <Skeleton circle className="size-[42px]" />
          <div className="absolute -bottom-1 -right-1 flex size-5 items-center justify-center rounded-full bg-light-background text-xs font-bold leading-none text-off-white shadow-[-1px_-1px_2px_0px_rgba(27,27,27,0.50)]">
            {place}
          </div>
        </div>
        <div className="flex w-full min-w-0  flex-col gap-1 leading-4">
          <div className="flex gap-1.5">
            <Skeleton className="mt-1 h-[14px] w-28" />
          </div>
          <div className="">
            <Skeleton className="h-[12px] w-20" />
          </div>
        </div>
      </div>
      <div className="flex flex-shrink-0 flex-col items-end justify-center gap-1 leading-4">
        <Skeleton className="h-[14px] w-6" />

        <Skeleton className="h-[14px] w-10" />
      </div>
    </div>
  );
};

const numberFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 2,
});
