"use client";

import { forwardRef, useMemo } from "react";
import { useRouter } from "next/navigation";

import { useQuery } from "@tanstack/react-query";
import { cva, VariantProps } from "class-variance-authority";
import { useInView } from "react-intersection-observer";

import { useSharesStatsQuery } from "@/queries";
import { stageQueries } from "@/queries/stage-queries";
import { LiveStageMinimal, StageUserSimple } from "@/queries/types";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";
import { ThreadPrivacyTypeEnum } from "@/types";

import { BarChartFilledIcon } from "../icons";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { StageInfoModal } from "./stage-info-modal";

interface LiveStageInfoPillProps extends VariantProps<typeof containerStyles> {
  stage: LiveStageMinimal;
}

export const LiveStageInfoPill = ({
  stage,
  variant,
}: LiveStageInfoPillProps) => {
  const { inView, ref } = useInView({
    threshold: 0.9,
  });
  const { user } = useUser();
  const router = useRouter();
  const { data, isLoading } = useQuery({
    ...stageQueries.stageInfoSimple(stage.id),
    refetchInterval: (query) => {
      if (
        inView &&
        (query.state.data?.stage.isActive ||
          (query.state.data?.stage.isRecorded &&
            !query.state.data?.stage.isRecordingComplete))
      ) {
        return 5 * 1000;
      }

      return false;
    },
  });
  const activeStage = data?.stage;
  const { data: statsData, isLoading: isStatsDataLoading } =
    useSharesStatsQuery({
      userId: user?.id,
    });
  const stageId = useStageStore((state) => state.id);
  const actions = useStageStore((state) => state.actions);

  const [live, restListenersCount] = useMemo(() => {
    if (!data) return [[], 0];

    return [data.live.slice(0, 3), data.listenersCount - 3];
  }, [data]);

  const liveForModal = useMemo(() => {
    if (!data) return [];
    return data.live.slice(0, 4);
  }, [data]);

  const isHoldingUserTicket = useMemo(() => {
    if (!activeStage) return false;
    if (isStatsDataLoading) return false;

    if (!activeStage.hostId) return false;

    if (user?.id === activeStage.hostId) return true;

    if (!statsData?.holdingsByUser) return false;

    return parseFloat((statsData?.holdingsByUser).toString()) > 0;
  }, [activeStage, isStatsDataLoading, statsData]);

  if (isLoading) {
    return (
      <Pill
        live={[]}
        stage={{
          name: "",
        }}
        variant={variant}
        ref={ref}
        listenersCount={0}
      />
    );
  }
  if (!activeStage || (!activeStage.isActive && activeStage.endedOn)) {
    return null;
  }

  if (stageId && stageId === activeStage.id) {
    return (
      <Pill
        live={live}
        stage={activeStage}
        onClick={() => {
          actions.setFullScreen(true);
        }}
        variant={variant}
        ref={ref}
        listenersCount={restListenersCount}
      />
    );
  }

  if (
    activeStage.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS &&
    !isHoldingUserTicket
  ) {
    return (
      <Pill
        live={live}
        stage={activeStage}
        onClick={() => {
          router.push(
            `/${data?.host.user.twitterHandle}/status/${activeStage.threadId}`,
          );
        }}
        variant={variant}
        ref={ref}
        listenersCount={restListenersCount}
      />
    );
  }

  return (
    <StageInfoModal
      live={liveForModal ?? []}
      stage={data?.stage ?? stage}
      isLoading={isLoading}
      listenersCount={data?.listenersCount ?? 0}
    >
      <Pill
        live={live}
        stage={activeStage}
        variant={variant}
        ref={ref}
        listenersCount={restListenersCount}
      />
    </StageInfoModal>
  );
};

interface PillProps extends VariantProps<typeof containerStyles> {
  live: StageUserSimple[];
  stage: {
    name: string;
  };
  listenersCount: number;
  onClick?: () => void;
}

const Pill = forwardRef<HTMLDivElement, PillProps>(
  ({ stage, live, onClick, listenersCount, variant }, ref) => {
    const avatarStyle = useMemo(() => {
      return live.map((_, index) => ({
        zIndex: live.length - index,
      }));
    }, [live]);

    return (
      <div
        role="button"
        className={containerStyles({ variant })}
        onClick={onClick}
        ref={ref}
      >
        <div className="flex items-center gap-1">
          <div className="flex -space-x-5">
            {live.map((user, index) => (
              <Avatar
                key={user.id}
                className={avatarStyles({ variant })}
                style={avatarStyle[index]}
              >
                <AvatarImage src={user.user.twitterPicture} />
                <AvatarFallback />
              </Avatar>
            ))}
          </div>
        </div>
        <div className="truncate text-sm font-semibold text-off-white">
          {stage.name}
        </div>
        <div className="ml-auto mr-1 flex items-center justify-center gap-2">
          {listenersCount > 0 ? (
            <span className="text-sm font-semibold text-white">
              +{abbreviateNumber(listenersCount)}
            </span>
          ) : null}
          <div className="flex size-6 items-center justify-center">
            <BarChartFilledIcon className="w-4 text-brand-orange" />
          </div>
        </div>
      </div>
    );
  },
);

Pill.displayName = "LiveStageInfoPill";

const containerStyles = cva(
  "flex items-center gap-[10px]  bg-purple-gradient",
  {
    variants: {
      variant: {
        mobile:
          "h-[58px] w-[85vw] px-3 py-2 rounded-full snap-center snap-always",
        desktop: "h-[52px] w-full px-3 py-2 rounded-[10px]",
      },
    },
    defaultVariants: {
      variant: "mobile",
    },
  },
);

const avatarStyles = cva("border border-light-gray-text", {
  variants: {
    variant: {
      mobile: "size-[42px]",
      desktop: "size-[36px]",
    },
  },
  defaultVariants: {
    variant: "mobile",
  },
});

function abbreviateNumber(number: number) {
  const formatter = new Intl.NumberFormat("en-US", {
    notation: "compact",
    compactDisplay: "short",
  });
  return formatter.format(number);
}
