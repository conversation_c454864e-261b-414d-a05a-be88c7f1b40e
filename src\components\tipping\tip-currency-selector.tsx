import React, { useState } from "react";

import { SystemCurrency } from "@/api/client/currency";
import { ChevronDownFilled } from "@/components/icons/chevron-down-filled";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { formatPrice } from "@/utils/format-token-price";
import { formatNumericValue } from "@/utils/number";

import { CloseOutlineIcon, SearchFilledIcon } from "../icons";
import { Dialog, DialogContent, DialogTrigger } from "../ui/dialog";
import { Drawer, DrawerContent, DrawerTrigger } from "../ui/drawer";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { TippingCurrencyItemV2 } from "./tip-currency-item-v2";

interface TipCurrencySelectorProps {
  token: SystemCurrency;
  sortedCurrencies: SystemCurrency[];
  onValueChange: (value: string) => void;
}

export const TipCurrencySelector: React.FC<TipCurrencySelectorProps> = ({
  token,
  sortedCurrencies,
  onValueChange,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const filteredCurrencies = sortedCurrencies.filter(
    (currency) =>
      currency.symbol.toLowerCase().includes(searchValue.toLowerCase()) ||
      currency.name.toLowerCase().includes(searchValue.toLowerCase()),
  );

  const Trigger = () => (
    <div
      className="flex w-full cursor-pointer items-center justify-between rounded-lg border border-gray-text p-3"
      id={`form-item-${token.symbol}`}
    >
      <div className="text-base leading-5">
        {formatNumericValue(
          token.isToken
            ? parseFloat(
                formatPrice(token.balance).toFixed(
                  formatPrice(token.balance) > 1 ? 2 : 4,
                ),
              ).toString()
            : Number(token.balance).toString(),
        )}
      </div>
      <div className="flex items-center gap-2">
        <img
          src={token.photoURL}
          className="size-4 rounded-full"
          alt={`${token.symbol} logo`}
        />
        <span className="text-base leading-5">
          {token.isToken ? `$${token.symbol}` : token.symbol}
        </span>
        <ChevronDownFilled className="text-gray-500 h-4 w-4" />
      </div>
    </div>
  );

  const Content = () => (
    <>
      <div className=" z-10  px-4 pt-3  ">
        <h3 className="mb-3 text-lg font-semibold text-off-white ">
          Select a Token
        </h3>

        <div className="relative">
          <Input
            value={searchValue}
            onChange={handleSearch}
            placeholder="Search name or symbol"
            className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-10 ring-0 placeholder:text-gray-text"
          />
          <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
          {searchValue && (
            <button
              className="absolute right-[10px] top-1/2 -translate-y-1/2 rounded-full border border-dark-gray bg-[rgba(20,20,20,0.88)] p-1"
              onClick={() => setSearchValue("")}
            >
              <CloseOutlineIcon className="pointer-events-auto size-[14px] select-none text-off-white" />
            </button>
          )}
        </div>
      </div>

      <div className="hide-scrollbar min-h-0 flex-1 overflow-y-auto px-8 py-4">
        {filteredCurrencies.map((currency) => (
          <div
            key={currency.contractAddress}
            className="hover:bg-gray-100 cursor-pointer py-2"
            onClick={() => {
              onValueChange(currency.symbol);
              setIsOpen(false);
            }}
          >
            <TippingCurrencyItemV2 currency={currency} />
          </div>
        ))}
      </div>
    </>
  );

  return (
    <div className="flex flex-col gap-2">
      <Label htmlFor={`form-item-${token.symbol}`}>
        <div className="flex flex-row">
          <div className="mr-1 font-normal text-light-gray-text">Balance:</div>
          <div className="text-off-white">${token.balanceUsd}</div>
        </div>
      </Label>

      {isTablet ? (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>{Trigger()}</DialogTrigger>
          <DialogContent className="flex h-full max-h-[395px] w-[390px] flex-col rounded-[20px] !bg-[#1A1A1A] p-0">
            {Content()}
          </DialogContent>
        </Dialog>
      ) : (
        <Drawer
          open={isOpen}
          onOpenChange={(open) => {
            setIsOpen(open);
            if (!open) {
              setSearchValue("");
            }
          }}
        >
          <DrawerTrigger asChild>{Trigger()}</DrawerTrigger>

          <DrawerContent className="flex max-h-[80vh] flex-col p-0">
            {Content()}
          </DrawerContent>
        </Drawer>
      )}
    </div>
  );
};
