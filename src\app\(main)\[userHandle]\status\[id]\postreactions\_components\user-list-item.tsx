"use client";

import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useFollowMutation, useUnfollowMutation } from "@/queries";
import {
  PostReactionsLikesResponse,
  PostReactionsRepostsResponse,
  User,
} from "@/queries/types/postreactions";
import { useUser } from "@/stores";

export const UserListItem = ({
  user,
  threadId,
}: {
  user: User;
  threadId: string;
}) => {
  const { user: me } = useUser();
  const queryClient = useQueryClient();
  const { mutateAsync: follow } = useFollowMutation({
    onMutate: async () => {
      toast.green(`You're now following ${user.twitterName}!`);
      const previousPostReactionsLikes = queryClient.getQueriesData({
        queryKey: ["postreactions", "likes", threadId],
      });
      const previousPostReactionsReposts = queryClient.getQueriesData({
        queryKey: ["postreactions", "reposts", threadId],
      });

      queryClient.setQueryData<InfiniteData<PostReactionsLikesResponse>>(
        ["postreactions", "likes", threadId],
        (old) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                likedUsers: page.likedUsers.map((f) => {
                  if (f.id === user.id) {
                    return {
                      ...f,
                      isFollowing: true,
                    };
                  }
                  return f;
                }),
              };
            }),
          };
        },
      );

      queryClient.setQueryData<InfiniteData<PostReactionsRepostsResponse>>(
        ["postreactions", "reposts", threadId],
        (old) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                repostedUsers: page.repostedUsers.map((f) => {
                  if (f.id === user.id) {
                    return {
                      ...f,
                      isFollowing: true,
                    };
                  }
                  return f;
                }),
              };
            }),
          };
        },
      );

      return { previousPostReactionsLikes, previousPostReactionsReposts };
    },
    onError(err, variables, context) {
      toast.danger(`Failed to follow ${user.twitterName}.`);
      queryClient.setQueryData(
        ["postreactions", "likes", threadId],
        context?.previousPostReactionsLikes,
      );
      queryClient.setQueryData(
        ["postreactions", "reposts", threadId],
        context?.previousPostReactionsReposts,
      );
    },
  });

  const { mutateAsync: unfollow } = useUnfollowMutation({
    onMutate: async () => {
      const previousPostReactionsLikes = queryClient.getQueriesData({
        queryKey: ["postreactions", "likes", threadId],
      });
      const previousPostReactionsReposts = queryClient.getQueriesData({
        queryKey: ["postreactions", "reposts", threadId],
      });

      queryClient.setQueryData<InfiniteData<PostReactionsLikesResponse>>(
        ["postreactions", "likes", threadId],
        (old) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                likedUsers: page.likedUsers.map((f) => {
                  if (f.id === user.id) {
                    return {
                      ...f,
                      isFollowing: false,
                    };
                  }
                  return f;
                }),
              };
            }),
          };
        },
      );

      queryClient.setQueryData<InfiniteData<PostReactionsRepostsResponse>>(
        ["postreactions", "reposts", threadId],
        (old) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                repostedUsers: page.repostedUsers.map((f) => {
                  if (f.id === user.id) {
                    return {
                      ...f,
                      isFollowing: false,
                    };
                  }
                  return f;
                }),
              };
            }),
          };
        },
      );

      return { previousPostReactionsLikes, previousPostReactionsReposts };
    },
    onError(err, variables, context) {
      toast.danger(`Failed to unfollow ${user.twitterName}.`);
      queryClient.setQueryData(
        ["postreactions", "likes", threadId],
        context?.previousPostReactionsLikes,
      );
      queryClient.setQueryData(
        ["postreactions", "reposts", threadId],
        context?.previousPostReactionsReposts,
      );
    },
  });

  function handleFollow() {
    if (user.isFollowing) {
      unfollow({ userId: user.id });
    } else {
      follow({ userId: user.id });
    }
  }

  return (
    <div className="flex w-full justify-between gap-4 px-6 py-4">
      <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
        <ProgressBarLink href={`/${user.twitterHandle}`}>
          <Avatar className="size-[42px]">
            <AvatarImage src={user.twitterPicture} />
            <AvatarFallback />
          </Avatar>
        </ProgressBarLink>
        <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
          <div className="flex gap-1.5">
            <ProgressBarLink
              href={`/${user.twitterHandle}`}
              className="truncate text-[#F4F4F4]"
            >
              {user.twitterName}
            </ProgressBarLink>
          </div>
          <ProgressBarLink
            href={`/${user.twitterHandle}`}
            className="truncate text-[#808080]"
          >
            @{user.twitterHandle}
          </ProgressBarLink>
        </div>
      </div>
      {me?.id !== user.id && (
        <div className="flex flex-shrink-0 flex-col items-end justify-center gap-[2px]">
          <Button
            variant={user.isFollowing ? "outline" : "secondary"}
            onClick={handleFollow}
            className="h-[34px] w-24"
          >
            {user.isFollowing ? "Unfollow" : "Follow"}
          </Button>
        </div>
      )}
    </div>
  );
};
