"use client";

import { useCallback, useState } from "react";

import { useQuery } from "@tanstack/react-query";
import { Emoji } from "emoji-picker-react";

import { stageQueries } from "@/queries/stage-queries";

import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { useDataChannelsContext } from "./stores/data-channels-context";

// [ "❤️", "🔥", "💯", "👏", "🤯"], "✋", "270b",
// ["⚔️", "🤣", "🫡", "👍",  "🤡"],
const defaultEmotes = [
  ["2764-fe0f", "1f525", "1f4af", "1f44f", "1f92f"],
  ["2694-fe0f", "1f923", "1fae1", "1f44d", "1f921"],
];

export const EmoteModal = ({ children }: { children: React.ReactNode }) => {
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild>{children}</PopoverTrigger>
      <PopoverContent
        className="z-50 w-auto max-w-80 px-4"
        collisionPadding={16}
      >
        <EmoteModalContent setOpen={setOpen} />
      </PopoverContent>
    </Popover>
  );
};

const EmoteModalContent = ({
  setOpen,
}: {
  setOpen: (open: boolean) => void;
}) => {
  const { chat } = useDataChannelsContext();

  const { data } = useQuery(stageQueries.emotes());

  const emotes =
    data?.emotes && data?.emotes.length > 0 ? data?.emotes : defaultEmotes;

  const handleEmote = useCallback((emote: string) => {
    setOpen(false);
    const message = {
      type: "emote",
      emote,
    };
    chat.send(JSON.stringify(message));
  }, []);

  return (
    <div className="flex items-center justify-center gap-3">
      <div className="flex flex-col items-center justify-center gap-3">
        {emotes.map((row, i) => {
          return (
            <div className="flex items-center gap-3" key={i + "emote"}>
              {row.map((emote, j) => (
                <button
                  key={i + j}
                  className="flex size-8 items-center justify-center"
                  onClick={() => {
                    handleEmote(emote);
                  }}
                >
                  <Emoji unified={emote} size={32} />
                </button>
              ))}
            </div>
          );
        })}
      </div>
    </div>
  );
};
