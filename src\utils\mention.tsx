export function removeMentionSpans(htmlString: string): string {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlString, "text/html");

  const mentionSpans = doc.querySelectorAll('span[data-type="mention"]');
  mentionSpans.forEach((span) => {
    const textContent = span.textContent ?? "";
    span.replaceWith(textContent);
  });

  return doc.body.innerHTML;
}

export function convertMentionSpanToATag(htmlString: string): string {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlString, "text/html");

  const mentionSpans = doc.querySelectorAll('span[data-type="mention"]');
  mentionSpans.forEach((span) => {
    const a = doc.createElement("a");
    a.href = `/${span.getAttribute("data-id")}`;
    a.textContent = span.textContent;
    a.className = span.className;

    // Copy all data attributes
    Array.from(span.attributes).forEach((attr) => {
      if (attr.name.startsWith("data-")) {
        a.setAttribute(attr.name, attr.value);
      }
    });

    span.parentNode?.replaceChild(a, span);
  });

  return doc.body.innerHTML;
}
