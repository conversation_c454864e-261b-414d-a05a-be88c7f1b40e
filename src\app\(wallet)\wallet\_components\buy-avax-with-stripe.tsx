"use client";

import { useEffect, useState } from "react";

import { postCreateOnrampSession } from "@/api/client/stripe";
import { StripeCrypto } from "@/app/(wallet)/wallet/_components/stripe-crypto-elements";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Drawer, DrawerTrigger } from "@/components/ui/drawer";
import { Drawer as DrawerPrimitive } from "@/components/ui/vaul";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/utils";

export const BuyAvaxWithStripeModal = () => {
  const [open, setOpen] = useState(false);
  const [clientSecret, setClientSecret] = useState("");
  const [isPending, setIsPending] = useState<boolean>(false);

  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  useEffect(() => {
    const createOnRampSession = async () => {
      if (isPending) return;
      setIsPending(true);
      try {
        const data = await postCreateOnrampSession();
        setClientSecret(data.clientSecret);
      } catch (e) {
        console.log(e);
      }

      setIsPending(false);
    };

    void createOnRampSession();
  }, []);

  if (isTablet) {
    return (
      <Dialog
        open={open}
        onOpenChange={(open) => {
          setOpen(open);
        }}
      >
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="h-[44px] w-full border-brand-orange"
          >
            Buy AVAX with
            <img
              src="/images/stripe.png"
              className="h-[22px] w-auto"
              alt="Stripe logo"
            />
          </Button>
        </DialogTrigger>
        <DialogContent>
          <div className="flex max-h-screen items-center gap-3">
            <DialogClose className="hidden p-1 sm:inline-block">
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </DialogClose>
            <h3 className="text-base font-semibold leading-[22px] text-off-white">
              Buy AVAX
            </h3>
          </div>
          <div>
            <div className="m-auto max-w-[400px] items-center align-middle">
              <StripeCrypto clientSecret={clientSecret} />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
      }}
    >
      <DrawerTrigger asChild>
        <Button
          variant="outline"
          className="h-[44px] w-full border-brand-orange"
        >
          Buy AVAX with
          <img
            src="/images/stripe.png"
            className="h-[22px] w-auto"
            alt="Stripe logo"
          />
        </Button>
      </DrawerTrigger>
      <DrawerPrimitive.Portal>
        <DrawerPrimitive.Content
          className={cn(
            "fixed inset-x-0 bottom-0  z-50 mt-24 flex h-auto max-h-screen flex-col rounded-t-[20px] border border-[#3B3B3B]/30 bg-[#0F0F0F]/90 p-6 pb-12 shadow-[0px_4px_16px_0px_rgba(0,0,0,0.25)] backdrop-blur-sm focus-visible:outline-none",
            "gap-4",
          )}
        >
          <h2 className="text-base font-semibold leading-[22px] text-off-white">
            Buy AVAX
          </h2>
          <div>
            <div className="m-auto max-w-[400px] items-center align-middle">
              <StripeCrypto clientSecret={clientSecret} />
            </div>
          </div>
        </DrawerPrimitive.Content>
      </DrawerPrimitive.Portal>
    </Drawer>
  );
};
