import { <PERSON><PERSON><PERSON>, <PERSON>, SetStateAction } from "react";

import { ProgressBarLink } from "@/components/progress-bar";
import { But<PERSON> } from "@/components/ui/button";

interface BlockedUserFormProps {
  subjectName: string;
}

export const BlockedForm: FC<BlockedUserFormProps> = ({ subjectName }) => {
  const hint = `You blocked this user, to continue this conversation please unblock them through their profile page.`;
  return (
    <div className="flex w-full flex-col gap-4 border-r-2 border-t-2 border-dark-gray bg-gray-bg p-6">
      <span className="text-sm">{hint}</span>
      <div className="flex w-full gap-2.5">
        <ProgressBarLink className="flex w-1/2" href={`/${subjectName}`}>
          <Button className="w-full">{`Go to Profile`}</Button>
        </ProgressBarLink>
        <ProgressBarLink className="flex w-1/2" href={"/messages"}>
          <Button variant="outline" className="w-full">
            Back
          </Button>
        </ProgressBarLink>
      </div>
    </div>
  );
};
