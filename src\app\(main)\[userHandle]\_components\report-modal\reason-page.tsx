import { Label } from "@radix-ui/react-label";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

export const reportTypes = [
  {
    reason: "Bots & Spam",
    description:
      "The user is a bot, it's spamming or presents inorganic activity.",
  },
  {
    reason: "Scammers",
    description:
      "The user is impersonating someone else, stealing content, lying or sharing malicious links.",
  },
  {
    reason: "Abhorrent Content",
    description:
      "The user is posting content meant to disturb, engaging in animal abuse or child exploitation.",
  },
  {
    reason: "Hate & Harassment",
    description:
      "The user is threatening another user, inciting violence against a person or group, harassing someone or sharing sensitive personal information.",
  },
  {
    reason: "Other",
    description: "Please provide additional details for your report.",
  },
];

export const ReasonPage = ({
  setReason,
  isContentReport,
  selectedReason,
}: {
  setReason: (reason: string) => void;
  isContentReport: boolean;
  selectedReason: string | null;
}) => (
  <div className="px-6">
    <h4 className="text-xl font-semibold leading-none text-off-white">
      {isContentReport
        ? "Why are you reporting this content?"
        : "Why are you reporting this user?"}
    </h4>
    <RadioGroup
      onValueChange={setReason}
      className="mt-4"
      value={selectedReason || ""}
    >
      {reportTypes.map((type) => (
        <Label
          className="flex items-start justify-between gap-4 py-2"
          key={type.reason}
        >
          <div className="flex flex-col gap-2">
            <h5 className="text-sm font-semibold text-white">{type.reason}</h5>
            <p className="text-sm text-gray-text">{type.description}</p>
          </div>
          <RadioGroupItem value={type.reason} />
        </Label>
      ))}
    </RadioGroup>
  </div>
);
