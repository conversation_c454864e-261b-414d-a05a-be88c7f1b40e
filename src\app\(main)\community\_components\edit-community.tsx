"use client";

import { FC, useEffect, useRef, useState } from "react";
import Image from "next/image";
import { notFound, useRouter } from "next/navigation";

import { zodResolver } from "@hookform/resolvers/zod";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { useQueryClient } from "@tanstack/react-query";
import { AnimatePresence, motion, useMotionTemplate } from "framer-motion";
import <PERSON><PERSON><PERSON>, { Area } from "react-easy-crop";
import { useForm } from "react-hook-form";
import { v4 as uuid } from "uuid";

import { PageHeader } from "@/app/_components/page-header";
import {
  EditCommunityFormInput,
  EditCommunityFormInputType,
  EditCommunityFormState,
} from "@/app/(create-community)/create-community/_components/create-community-form-input";
import { ArrowBackOutlineIcon, PencilOutlineIcon } from "@/components/icons";
import { GroupIcon } from "@/components/icons-v2/group-logo";
import { toast } from "@/components/toast";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogOverlay,
  DialogPortal,
} from "@/components/ui/dialog";
import { TextInput } from "@/components/ui/text-input";
import { Textarea } from "@/components/ui/textarea";
import { useCommunityByStrQuery } from "@/queries";
import {
  useCommunityNameTakenCheckQuery,
  useEditGroupMutation,
} from "@/queries/groups-queries";
import { useUser } from "@/stores";
import { cn, upload } from "@/utils";
import getCroppedImg from "@/utils/crop-image";

import {
  HANDLE_PHASE,
  MAX_DESCRIPTION_LENGTH,
  MAX_NAME_LENGTH,
} from "./consts";
import { EditCommunitySkeleton } from "./edit-community-skeleton";

interface EditCommunityProps {
  param: string;
}

export const EditCommunity: FC<EditCommunityProps> = ({ param }) => {
  const router = useRouter();
  const { user } = useUser();
  const queryClient = useQueryClient();

  if (!user) {
    throw new Error("User not specified");
  }

  const { data, isLoading } = useCommunityByStrQuery(param);

  const { data: nameCheckData } = useCommunityNameTakenCheckQuery(
    data?.community.name,
    data?.community.id,
  );

  const { mutateAsync: editGroup, isPending } = useEditGroupMutation({
    onMutate: async (formState) => {
      await queryClient.cancelQueries({
        queryKey: ["community", "param", param],
      });

      const previousData = queryClient.getQueryData([
        "community",
        "param",
        param,
      ]);

      queryClient.setQueryData(["community", "param", param], (old: any) => {
        return {
          ...old,
          name: formState.name || old.name,
          bannerURL: formState.bannerUrl || old.bannerUrl,
          description: formState.description || old.description,
        };
      });

      return { previousData };
    },
    onError: (err, variables, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(
          ["community", "param", param],
          context.previousData,
        );
      }
    },
    onSuccess: () => {
      toast.green("Saved Successfully!");
      queryClient.invalidateQueries({
        queryKey: ["community", "param", param],
      });
      if (data && data.community && data.community.tokenPhase < HANDLE_PHASE) {
        router.push(`/community/${data?.community?.contractAddress}`);
      } else {
        router.push(`/community/${data?.community?.name}`);
      }
    },
  });

  const form = useForm<EditCommunityFormInputType>({
    resolver: zodResolver(EditCommunityFormInput(data?.community.name || "")),
    mode: "onChange",
    reValidateMode: "onChange",
    shouldFocusError: true,
  });

  useEffect(() => {
    if (data) {
      form.setValue("name", data.community.name || "");
      form.setValue("bannerUrl", data.community.bannerURL || "");
      form.setValue("description", data.community.description || "");
      setBannerUrl(data.community.bannerURL || "");
    }

    if (data) {
      if (data.community.tokenPhase >= HANDLE_PHASE) {
        const newHandler = data.community.name;
        const newUrl = `/community/${newHandler}/edit`;
        window.history.replaceState(null, "", newUrl);
      }
    }
  }, [data, form]);

  const [bannerUrl, setBannerUrl] = useState<string>("");
  const [isUploading, setIsUploading] = useState(false);
  const [open, setOpen] = useState(false);
  const [previewURL, setPreviewURL] = useState<string | null>(null);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [progress, setProgress] = useState(0);
  const width = useMotionTemplate`${progress}%`;
  const inputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.danger("Uploaded image file cannot exceed 5 MB");
        return;
      }

      setOpen(true);
      const previewURL = URL.createObjectURL(file);
      setPreviewURL(previewURL);
    }
  };

  const resetUploading = () => {
    setIsUploading(false);
    setProgress(0);
    setPreviewURL(null);
    if (inputRef.current) inputRef.current.value = "";
  };

  const handleApply = async () => {
    if (!croppedAreaPixels || !previewURL) return;

    const blob = await getCroppedImg(previewURL, croppedAreaPixels);

    if (!blob) return;

    // @ts-expect-error
    blob.name = "image.jpeg";
    // @ts-expect-error
    blob.lastModified = new Date();

    const file = new File([blob], `${uuid()}.jpg`, { type: "image/jpeg" });

    setIsUploading(true);
    try {
      const res = await upload({
        file,
        onProgressChange: (progressValue) => {
          setProgress(progressValue);
        },
      });
      setBannerUrl(res.url);
      form.setValue("bannerUrl", res.url);
      resetUploading();
      setOpen(false);
    } catch {
      toast.danger("File upload failed");
      resetUploading();
      setOpen(false);
    }
  };

  const handleSubmit = async (formData: EditCommunityFormInputType) => {
    if (
      formData.name === data?.community.name &&
      formData.bannerUrl === data?.community.bannerURL &&
      (formData.description === data?.community.description ||
        (formData.description === "" && data?.community.description === null))
    ) {
      return toast.danger("No changes to save");
    }
    const formState: EditCommunityFormState = {
      communityId: data?.community.id || "",
      name: formData.name === data?.community.name ? "" : formData.name,
      bannerUrl:
        formData.bannerUrl === data?.community.bannerURL
          ? ""
          : formData.bannerUrl,
      description:
        formData.description === data?.community.description
          ? ""
          : formData.description,
    };
    await editGroup(formState);
  };

  if (!isLoading && !data) return notFound();

  if (isLoading)
    return (
      <div className="flex flex-1 flex-col">
        <PageHeader hint="Edit Profile Page" isBorder isSticky />
        <EditCommunitySkeleton />
      </div>
    );

  return (
    <div className="flex flex-1 flex-col">
      <PageHeader hint="Edit Profile Page" isBorder isSticky />
      <div className="relative mb-6 flex flex-1 flex-col gap-3">
        <div className="relative">
          <div
            className="aspect-[4/1] w-full overflow-hidden bg-[#0e0e0e]"
            style={{
              backgroundImage: `url(${bannerUrl || "/images/default-banner.png"})`,
              backgroundSize: "cover",
            }}
          />
          <div className="absolute right-8 top-full -translate-y-1/2">
            <Button
              className="size-[44px] items-center justify-center p-0"
              onClick={() => {
                !isUploading && inputRef.current && inputRef.current.click();
              }}
              disabled={isUploading || isLoading}
            >
              <PencilOutlineIcon className="size-6 text-off-white" />
            </Button>
            <input
              ref={inputRef}
              style={{ display: "none" }}
              type="file"
              accept="image/*"
              onChange={handleFileChange}
            />
          </div>
        </div>
        <div className="mb-4 flex flex-col gap-4 px-6">
          <div className="flex items-center">
            {isLoading && (
              <div className="mr-6 h-[92px] w-[92px] rounded-[8px] border-[1px] border-[#EB540A] bg-gray-text"></div>
            )}
            {!isLoading && (
              <div className="relative">
                <Image
                  width={92}
                  height={92}
                  src={data?.community.photoURL || ""}
                  alt="Group Logo"
                  className="mr-6 rounded-[8px] border-[1px] border-[#EB540A]"
                />
              </div>
            )}
          </div>
          <div className="flex flex-col">
            <div className="flex gap-1">
              <GroupIcon />
              <span className="font-semibold text-[#f3f3f3]">{`${data?.community.tokenName} ($${data?.community.ticker})`}</span>
            </div>
            <span className="text-sm font-normal text-[#808080]">
              {`@${data?.community.name}`}
            </span>
          </div>
        </div>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="relative flex flex-1 flex-col gap-2 px-6"
        >
          <div className="flex flex-col gap-2">
            <label className="text-xs font-semibold text-[#f3f3f3]">
              DESCRIPTION
            </label>
            <div className="relative">
              <Textarea
                className="min-h-[55px] py-4 pr-16 text-[#808080]"
                placeholder="What is your token about?"
                {...form.register("description")}
                maxLength={MAX_DESCRIPTION_LENGTH}
              />
              <div className="absolute bottom-4 right-8 text-[#808080]">
                {MAX_DESCRIPTION_LENGTH -
                  (form.watch("description") ?? "").length}
              </div>
            </div>
            <span className="ml-1 text-xs text-danger">
              {form.formState.errors.description?.message}
            </span>
          </div>
          <div className="relative flex flex-col gap-2">
            <label
              className={cn(
                "text-xs font-semibold",
                nameCheckData?.isAvailable
                  ? "text-[#808080]"
                  : "text-[#f3f3f3]",
              )}
            >
              PROFILE PAGE HANDLE
            </label>
            <div className="relative">
              <TextInput
                className={cn(
                  nameCheckData?.isAvailable ? "" : "text-[#808080]",
                )}
                placeholder="new_handle"
                {...form.register("name")}
                maxLength={MAX_NAME_LENGTH}
                disabled={nameCheckData?.isAvailable}
                errorMessage={form.formState.errors.name?.message}
              />
              <div className="absolute right-8 top-1/2 -translate-y-1/2 text-[#808080]">
                {MAX_NAME_LENGTH - (form.watch("name") ?? "").length}
              </div>
            </div>
          </div>
          <Button
            className="mt-auto w-full"
            disabled={isPending || isLoading}
            type="submit"
          >
            Save
          </Button>
        </form>
      </div>
      <Dialog
        open={open}
        onOpenChange={(open) => {
          setOpen(open);
          !open && resetUploading();
        }}
      >
        <DialogPortal>
          <DialogOverlay className="z-[200]" />
          <DialogPrimitive.Content className="pt-pwa fixed left-[50%] top-[50%] z-[200] flex h-full w-full max-w-[524px] translate-x-[-50%] translate-y-[-50%] flex-col overflow-hidden bg-dark-bk px-0 pb-0 shadow-lg backdrop-blur-sm duration-200 sm:rounded-[20px] sm:border sm:border-[rgba(59,59,59,0.30)] sm:bg-[rgba(15,15,15,0.90)] sm:backdrop-blur-sm lg:h-[500px]">
            <div className="top-pwa relative inset-x-0 flex justify-between bg-dark-bk bg-opacity-60 py-1 pl-6 pr-3">
              <DialogClose
                onClick={() => {
                  if (inputRef.current) inputRef.current.value = "";
                  setOpen(false);
                }}
              >
                <ArrowBackOutlineIcon className="size-5 text-off-white" />
              </DialogClose>
              <Button
                variant="ghost"
                onClick={handleApply}
                disabled={isUploading}
              >
                Apply
              </Button>
            </div>
            <div className="relative flex flex-1 items-center justify-center overflow-auto">
              {previewURL && (
                <Cropper
                  showGrid={false}
                  maxZoom={2}
                  image={previewURL}
                  crop={crop}
                  zoom={zoom}
                  aspect={3 / 1}
                  onCropChange={setCrop}
                  onZoomChange={setZoom}
                  onCropComplete={(_, croppedAreaPixels) => {
                    setCroppedAreaPixels(croppedAreaPixels);
                  }}
                />
              )}
            </div>
            <AnimatePresence>
              {isUploading && (
                <motion.div
                  style={{ width }}
                  exit={{ opacity: 0 }}
                  className="absolute bottom-0 left-0 h-1 min-w-4 bg-brand-orange"
                />
              )}
            </AnimatePresence>
          </DialogPrimitive.Content>
        </DialogPortal>
      </Dialog>
    </div>
  );
};
