import React, { createContext, useContext } from "react";

const DynamicContext = createContext<any>({
  user: {
    address: "0xMockUser",
    dynamicAddress: "0xMockUser",
  },
  primaryWallet: {
    getPublicClient: async () => ({
      readContract: async () => 2, // feePercentage
    }),
  },
});

export const useDynamicContext = () => useContext(DynamicContext);

export const DynamicProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => (
  <DynamicContext.Provider
    value={{
      user: {
        address: "0xMockUser",
        dynamicAddress: "0xMockUser",
      },
      primaryWallet: {
        getPublicClient: async () => ({
          readContract: async () => 2,
        }),
      },
    }}
  >
    {children}
  </DynamicContext.Provider>
);
