import * as React from "react";
import Image from "next/image";

import { useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAgreeRestakeMutation } from "@/queries/token-portal-mutations";

interface RestakeSubmitModalParams {
  open: boolean;
  onOpenChange: (value: ((prevState: boolean) => boolean) | boolean) => void;
  handleRestakeRewards: () => Promise<void>;
}

export const RestakeSubmitModal = ({
  handleRestakeRewards,
  onOpenChange,
  open,
}: RestakeSubmitModalParams) => {
  const queryClient = useQueryClient();

  const { mutateAsync: agreeRestake } = useAgreeRestakeMutation({
    onError: (err, variables, context) => {
      toast.red(`Failed to agree to restake`);
    },
    onSuccess: () => {
      queryClient.setQueryData(["token-portal", "isRestakeAgreed"], {
        isRestakeAgreed: true,
      });
    },
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="top-[70%] max-w-[87%] gap-6 rounded-[10px] border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] px-4 shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)] backdrop-blur-sm sm:top-[50%] sm:max-w-[524px]">
        <DialogHeader className="flex-col items-center space-y-4">
          <Image
            src="/assets/arena-coin.png"
            className="size-6 rounded-full"
            alt={`AVAX logo`}
            width={24}
            height={24}
          />
          <DialogTitle className="text-center leading-[22px]">
            Restake your tokens
          </DialogTitle>
        </DialogHeader>
        <div className="flex-col">
          <p className="text-center text-sm text-light-gray-text">
            By clicking Agree & Restake, you agree to:
          </p>
          <ul className="mt-2 list-inside list-disc pl-2 text-center text-sm leading-[21px] text-[#B5B5B5]">
            <li>Convert any AVAX earnings into ARENA.</li>
            <li>
              Restake all your accumulated ARENA
              <br /> rewards into your staking pool.
            </li>
          </ul>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={() => onOpenChange(false)}
            variant="outline"
            className="flex flex-auto px-4 py-2 text-sm text-off-white"
          >
            Cancel
          </Button>
          <Button
            onClick={async () => {
              await agreeRestake();
              onOpenChange(false);
              await handleRestakeRewards();
            }}
            className="flex-auto px-4 py-2 text-sm text-off-white"
          >
            Agree & Restake
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
