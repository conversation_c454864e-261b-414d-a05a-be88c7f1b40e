"use client";

import { useState } from "react";
import { useRouter, useSelectedLayoutSegments } from "next/navigation";

import { useDynamicContext } from "@dynamic-labs/sdk-react-core";

import {
  AddOutlineIcon,
  EllipsisHorizontalFilledIcon,
  LiveOutlineIcon,
  LogoIcon,
} from "@/components/icons";
import {
  ChatbubbleOutlineIcon,
  HomeOutlineIcon,
  NotificationsOutlineIcon,
  SearchOutlineIcon,
  WalletOutlineIcon,
} from "@/components/icons-v2";
import { useLivestreamEditor } from "@/components/livestream/hooks/use-livestream-editor";
import { ProgressBarLink } from "@/components/progress-bar";
import { useStageEditor } from "@/components/stages/hooks/use-stage-editor";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useUnseenNotificationsQuery } from "@/queries";
import { useHomeStore, useHomeTabStore, usePostStore, useUser } from "@/stores";
import { useFeatureFlagsStore } from "@/stores/flags";
import { UserFlaggedEnum } from "@/types";
import { cn } from "@/utils";

export const SideNav = () => {
  const { user } = useUser();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const segments = useSelectedLayoutSegments();
  const tab = useHomeTabStore((state) => state.tab);
  const followingTimelineRef = useHomeStore(
    (state) => state.followingTimelineRef,
  );
  const trendingTimelineRef = useHomeStore(
    (state) => state.trendingTimelineRef,
  );
  const reset = usePostStore((state) => state.reset);
  const showStages = useFeatureFlagsStore((state) => state.stages);

  const isProfileActive = segments[0] === user?.twitterHandle;
  const isBanned = user?.flag === UserFlaggedEnum.SUSPENDED;

  const { data, isLoading } = useUnseenNotificationsQuery();
  const [_, setStageEditor] = useStageEditor();
  const [__, setLivestreamEditor] = useLivestreamEditor();

  const links = [
    {
      name: "Home",
      href: `/home`,
      isActive: segments.length === 1 && segments[0] === "home",
      icon: HomeOutlineIcon,
      disabled: false,
    },
    {
      name: "Explore",
      href: `/explore`,
      isActive: segments.length === 1 && segments[0] === "explore",
      icon: SearchOutlineIcon,
      disabled: isBanned,
    },
    {
      name: "Notifications",
      href: `/notifications`,
      isActive: segments.length === 1 && segments[0] === "notifications",
      icon: NotificationsOutlineIcon,
      disabled: isBanned,
    },
    ...(showStages
      ? [
          {
            name: "Live",
            href: `/live`,
            isActive: segments.length === 1 && segments[0] === "live",
            icon: LiveOutlineIcon,
            disabled: isBanned,
          },
        ]
      : []),
    {
      name: "Messages",
      href: `/messages`,
      isActive: segments[0] === "messages",
      icon: ChatbubbleOutlineIcon,
      disabled: isBanned,
    },
    {
      name: "Wallet",
      href: `/wallet`,
      isActive: segments.length === 1 && segments[0] === "wallet",
      icon: WalletOutlineIcon,
      disabled: isBanned,
    },
  ];

  const { handleLogOut } = useDynamicContext();

  return (
    <nav className="flex w-[44px] select-none flex-col items-end ">
      <div className="fixed top-0 h-full w-[44px]">
        <div className="flex h-full flex-col gap-6 py-8 ">
          <div className="flex size-12 items-center justify-center">
            <LogoIcon className="h-[36px] text-brand-orange" />
          </div>
          <div className="flex">
            <ProgressBarLink
              href="/champions"
              className="flex h-[52px] cursor-pointer items-center justify-between rounded-[10px] border border-[#eb540a] bg-center sm:h-12 sm:w-12 "
              style={{
                backgroundImage: "url('/icons/champion-banner-desktop.svg')",
              }}
            >
              <img
                src="/icons/arena-champion-user.svg"
                className="mx-auto h-7 w-7 rounded-full drop-shadow-badge" // xl:mx-0
                alt="Champions logo"
              />
            </ProgressBarLink>
          </div>
          <div className="flex flex-col">
            {links.map(({ href, icon: Icon, isActive, name }, index) => {
              if (isActive) {
                return (
                  <button
                    key={name + "-side-nav-link"}
                    className={cn(
                      "group py-1.5",
                      isActive ? "text-off-white" : "text-gray-text ",
                      index === 0 && "pt-0",
                    )}
                    onClick={() => {
                      if (segments.length === 1 && segments[0] === "home") {
                        if (tab === "following") {
                          followingTimelineRef.current?.scrollToIndex({
                            index: 0,
                            behavior: "smooth",
                            offset: -300,
                          });
                        } else if (tab === "trending") {
                          trendingTimelineRef.current?.scrollToIndex({
                            index: 0,
                            behavior: "smooth",
                            offset: -300,
                          });
                        }
                      } else {
                        window.scrollTo({
                          top: 0,
                          behavior: "smooth",
                        });
                      }
                    }}
                  >
                    <div className="flex items-center justify-center gap-3 rounded-lg p-[10px] transition-colors group-hover:bg-gray-bg ">
                      <div className="relative">
                        <Icon className="size-6 flex-shrink-0" />
                        {name === "Notifications" &&
                          !isLoading &&
                          data &&
                          data?.count > 0 && (
                            <div className="absolute -right-2 -top-2 flex min-w-5 items-center justify-center rounded bg-dark-gray px-1 py-0.5 text-xs text-off-white">
                              {data.count}
                            </div>
                          )}
                      </div>
                    </div>
                  </button>
                );
              }

              return (
                <ProgressBarLink
                  href={href}
                  key={name + "-side-nav-link"}
                  className={cn(
                    "group py-1.5",
                    isActive ? "text-off-white" : "text-gray-text ",
                    index === 0 && "pt-0",
                  )}
                >
                  <div className="flex items-center justify-center gap-3 rounded-lg p-[10px] transition-colors group-hover:bg-gray-bg">
                    <div className="relative">
                      <Icon className="size-6 flex-shrink-0" />
                      {name === "Notifications" &&
                        !isLoading &&
                        data &&
                        data?.count > 0 && (
                          <div className="absolute -right-2 -top-2 flex min-w-5 items-center justify-center rounded bg-dark-gray px-1 py-0.5 text-xs text-off-white">
                            {data.count}
                          </div>
                        )}
                    </div>
                  </div>
                </ProgressBarLink>
              );
            })}
            <DropdownMenu>
              <DropdownMenuTrigger className="group py-1.5 outline-none">
                <div className="flex items-center justify-center gap-3 rounded-lg p-[10px] text-gray-text transition-colors group-hover:bg-gray-bg ">
                  <EllipsisHorizontalFilledIcon
                    className={cn("size-6 flex-shrink-0 fill-gray-text")}
                  />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="start"
                side="top"
                className="hide-scrollbar hidden max-h-[350px] w-[220px] overflow-y-auto sm:block"
                collisionPadding={{
                  bottom: 24,
                }}
              >
                <DropdownMenuItem asChild>
                  <div
                    className={cn(
                      "pointer-events-none",
                      isBanned && "opacity-20",
                    )}
                    tabIndex={-1}
                  >
                    {`Arena Launch (Coming Soon)`}
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <ProgressBarLink
                    href="/referral"
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    Refer & Earn
                  </ProgressBarLink>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <ProgressBarLink
                    href="/bookmarks"
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    Bookmarks
                  </ProgressBarLink>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <ProgressBarLink
                    href="/app-store"
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    Arena App Store
                  </ProgressBarLink>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <ProgressBarLink
                    href="/tokenomics"
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    {`$ARENA Tokenomics`}
                  </ProgressBarLink>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <ProgressBarLink
                    href="/settings"
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    Settings & Support
                  </ProgressBarLink>
                </DropdownMenuItem>

                <DropdownMenuItem asChild>
                  <a
                    href="https://arena.trade/"
                    target="_blank"
                    rel="noreferrer"
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    onClick={(e) => {
                      if (isBanned) {
                        e.preventDefault();
                      }
                    }}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    ArenaBook
                  </a>
                </DropdownMenuItem>
                {user?.isMod && (
                  <DropdownMenuItem asChild>
                    <ProgressBarLink href="/reports">
                      Moderation
                    </ProgressBarLink>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  onSelect={async () => {
                    await handleLogOut();
                  }}
                >
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <ProgressBarLink
              href={`/` + user?.twitterHandle}
              className={cn(
                "group pt-1.5",
                isProfileActive ? "text-off-white" : "text-gray-text",
              )}
            >
              {/* xl:justify-start */}
              <div className="flex items-center justify-center gap-3 rounded-lg p-[10px] transition-colors group-hover:bg-gray-bg ">
                <Avatar className="size-6 border border-white xl:border-none">
                  <AvatarImage src={user?.twitterPicture} />
                  <AvatarFallback />
                </Avatar>
                {/* <span className="hidden text-base font-semibold leading-5 xl:inline">
                  Profile
                </span> */}
              </div>
            </ProgressBarLink>
            {segments.length === 1 && segments[0] === "live" && showStages ? (
              <>
                <Button
                  // xl:w-full
                  className="mx-auto mt-8 size-10 p-0 "
                  onClick={() => {
                    setStageEditor({ composeStage: true });
                  }}
                >
                  {/* xl:hidden */}
                  <AddOutlineIcon className="size-10 flex-shrink-0 p-[10px] text-white " />
                  {/* <span className="hidden xl:inline">Create Your Stage</span> */}
                </Button>
                <Button
                  // xl:w-full
                  className="mx-auto mt-8 size-10 p-0 "
                  onClick={() => {
                    setLivestreamEditor({ composeLivestream: true });
                  }}
                >
                  {/* xl:hidden */}
                  <AddOutlineIcon className="size-10 flex-shrink-0 p-[10px] text-white " />
                  {/* <span className="hidden xl:inline">
                    Create Your Livestream
                  </span> */}
                </Button>
              </>
            ) : (
              // xl:w-full
              <Button className="mx-auto mt-8 size-10 p-0 " asChild>
                <ProgressBarLink
                  href="/compose/post"
                  onClick={() => {
                    reset();
                  }}
                >
                  {/* xl:hidden */}
                  <AddOutlineIcon className="size-10 flex-shrink-0 p-[10px] text-white " />
                  {/* <span className="hidden xl:inline">Post</span> */}
                </ProgressBarLink>
              </Button>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};
