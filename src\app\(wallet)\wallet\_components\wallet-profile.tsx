"use client";

import { ComponentProps, useEffect, useMemo, useState } from "react";
import Link from "next/link";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import Skeleton from "react-loading-skeleton";
import { Address, formatEther as formatEtherViem, Hex } from "viem";

import { getUserToSigner } from "@/api/balance";
import { LocatedNumber } from "@/components/located-number";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  airdropVestingAddress,
  ARENA_AIRDROP_VESTING_ABI,
  stakingContractABI,
  stakingContractAddress,
  tokenContractABI,
} from "@/environments/stakingABI";
import { ARENA, AVAX } from "@/environments/tokens";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import { useMeQuery, useSharesStatsQuery } from "@/queries";
import {
  useAvaxPriceQuery,
  useSupportedCurrenciesQuery,
} from "@/queries/currency-queries";
import { useTokenPortalClaimableBalanceQuery } from "@/queries/token-portal-queries";
import { useUser } from "@/stores";
import { useTutorialStore } from "@/stores/tutorial";
import { cn, formatEther, numberFormatter } from "@/utils";
import { formatPrice } from "@/utils/format-token-price";

import { BuyAvaxWithStripeModal } from "./buy-avax-with-stripe";
import { DepositModal } from "./deposit-modal";
import { DynamicMigrationExecutedExternallyModal } from "./dynamic-migration-executed-externally-modal";
import { DynamicMigrationModal } from "./dynamic-migration-modal";
import { MoreOptionsModal } from "./more-options-modal";
import { WithdrawModal } from "./withdraw-modal";

export const WalletProfile = () => {
  const [token] = useState<{
    name: string;
    icon: string;
    native?: boolean;
  }>(AVAX);
  const { user } = useUser();
  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useSupportedCurrenciesQuery();
  const { data: meUserData } = useMeQuery();
  const meUser = meUserData?.user;

  const isTutorialOpen = useTutorialStore((state) => state.isTutorialOpen);

  const { data: stats, isLoading: isStatsLoading } = useSharesStatsQuery({
    userId: user?.id,
  });
  const { data: avaxPrice, isLoading: isAvaxPriceLoading } =
    useAvaxPriceQuery();

  const { balance, isBalancesLoading } = useWalletCurrencies({
    user,
    currenciesData,
    isCurrenciesLoading,
  });

  const { primaryWallet, handleLogOut } = useDynamicContext();

  const { data } = useTokenPortalClaimableBalanceQuery();

  const [totalBalance, setTotalBalance] = useState(0);

  const [userTokenBalance, setUserTokenBalance] = useState<number | null>(null);
  const [airdropClaimed, setAirdropClaimed] = useState<number | null>(null);
  const [stake, setStake] = useState<number | null>(null);
  const [usdArena, setUsdArena] = useState(0);

  const [earnings, portfolioValue] = useMemo(() => {
    let earnings = "0.00";
    let portfolioValue = 0;

    if (stats?.stats?.feesEarned) {
      earnings = formatEther(stats.stats.feesEarned);
    }

    const ticketsValue =
      stats?.portfolioValue && avaxPrice?.avax
        ? Number(
            formatEther(stats.portfolioValue.toString()).replace(/,/g, ""),
          ) * avaxPrice.avax
        : 0;

    const calculateCurrenciesValue = () => {
      if (isCurrenciesLoading || !currenciesData || !avaxPrice?.avax) return 0;

      return currenciesData.currencies.reduce((total, currency) => {
        const balanceValue = Number(
          currency.isToken
            ? formatPrice(currency.balance || "0")
            : (balance[currency.symbol as keyof typeof balance] || "0").replace(
                /,/g,
                "",
              ),
        );
        const rate = Number(
          currency.isToken
            ? formatPrice(currency.systemRate || "0")
            : currency.systemRate,
        );

        return (
          total + balanceValue * rate * (currency.isToken ? avaxPrice.avax : 1)
        );
      }, 0);
    };

    const calculateStakeValue = () => {
      if (!stake) return 0;
      return stake * usdArena;
    };

    portfolioValue =
      ticketsValue + calculateCurrenciesValue() + calculateStakeValue();

    return [earnings, portfolioValue];
  }, [
    stats,
    avaxPrice,
    isCurrenciesLoading,
    currenciesData,
    balance,
    stake,
    usdArena,
  ]);

  useEffect(() => {
    if (!currenciesData || isCurrenciesLoading) return;

    const arenaCurrency = currenciesData.currencies.find(
      (currency) => !currency.isToken && currency.symbol === "ARENA",
    );

    if (arenaCurrency?.systemRate) {
      setUsdArena(Number(arenaCurrency.systemRate));
    }
  }, [currenciesData, isCurrenciesLoading]);

  useEffect(() => {
    const fetchData = async () => {
      if (!primaryWallet) {
        console.error("primaryWallet is not initialized");
        return;
      }
      if (!isEthereumWallet(primaryWallet)) {
        throw new Error("This wallet is not an Ethereum wallet");
      }

      const publicClient = await primaryWallet.getPublicClient();

      const userTokenBalanceRes = await publicClient.readContract({
        address: ARENA.address as Address,
        abi: tokenContractABI,
        functionName: "balanceOf",
        args: [primaryWallet.address as Hex],
      });
      setUserTokenBalance(Number(formatEtherViem(userTokenBalanceRes)));

      const airdropClaimedRes = await publicClient.readContract({
        address: airdropVestingAddress as Address,
        abi: ARENA_AIRDROP_VESTING_ABI,
        functionName: "airdropClaimed",
        args: [primaryWallet.address as Hex],
      });
      setAirdropClaimed(Number(formatEtherViem(airdropClaimedRes)));

      const [stakeRes] = await publicClient.readContract({
        address: stakingContractAddress as Address,
        abi: stakingContractABI,
        functionName: "getUserInfo",
        args: [primaryWallet.address as Hex, ARENA.address as Address],
      });
      setStake(Number(formatEtherViem(stakeRes)));
    };

    void fetchData();
  }, [primaryWallet]);

  useEffect(() => {
    if (
      userTokenBalance !== null &&
      airdropClaimed !== null &&
      stake !== null &&
      data
    ) {
      setTotalBalance(
        data.remainingArenaTokens +
          userTokenBalance +
          stake -
          airdropClaimed -
          data.forfeited,
      );
    }
  }, [userTokenBalance, airdropClaimed, stake, data]);

  const [showDynamicMigrationModal, setShowDynamicMigrationModal] =
    useState(false);
  const [
    showDynamicMigrationExecutedExternallyModal,
    setShowDynamicMigrationExecutedExternallyModal,
  ] = useState(false);

  useEffect(() => {
    const fetchUserToSigner = async () => {
      if (meUser) {
        let userToSignerResponse = await getUserToSigner(meUser.address);
        userToSignerResponse = userToSignerResponse.toLowerCase();

        if (!meUser.dynamicAddress) {
          await handleLogOut();
          return;
        }

        const address = meUser?.address?.toLowerCase();
        const dynamicAddress = meUser?.dynamicAddress?.toLowerCase();

        if (
          address === dynamicAddress &&
          meUser?.addressBeforeDynamicMigration
        ) {
          // migrated user
          //
          // we can check that userToSignerResponse === "0x000..."
          //
          // also we can ensure that getUserToSigner(addressBeforeDynamicMigration) === address
          // otherwise, the user could execute migrateSigner externally after executing the migration on the Arena
          // so we can check:
          // let userToSignerResponse2 = await getUserToSigner(
          //   meUser.addressBeforeDynamicMigration,
          // );
          // userToSignerResponse2 = userToSignerResponse2.toLowerCase();
          // if (
          //   address !== userToSignerResponse2 &&
          //   userToSignerResponse2 !== "0x000..."
          // ) {
          //   setShowDynamicMigrationExecutedExternallyModal(true);
          // } else if (
          //   address !== userToSignerResponse2 &&
          //   userToSignerResponse2 === "0x000..."
          // ) {
          //   setShowDynamicMigrationExecutedExternallyModal(true);
          //   setShowDynamicMigrationModal(true);
          // }
        } else if (
          address === dynamicAddress &&
          !meUser?.addressBeforeDynamicMigration
        ) {
          // "new" user
          // should be: userToSignerResponse === "0x000..."
          // otherwise something went wrong
        } else if (
          address !== dynamicAddress &&
          userToSignerResponse === dynamicAddress
        ) {
          // attempt to synchronize the migration on the backend
          setShowDynamicMigrationModal(true);
        } else if (
          address !== dynamicAddress &&
          userToSignerResponse === "******************************************"
        ) {
          setShowDynamicMigrationModal(true);
        } else if (
          address !== dynamicAddress &&
          userToSignerResponse !== dynamicAddress
        ) {
          setShowDynamicMigrationExecutedExternallyModal(true);
        }
      }
    };
    fetchUserToSigner();
  }, [meUser]);

  return (
    <div className="relative z-20 overflow-hidden">
      <div
        className={cn(
          "absolute bottom-[40px] left-1/2 block size-[700px] -translate-x-1/2 rounded-full bg-[#E74141] bg-opacity-20",
          token.name === "AVAX" && "bg-[#E74141]",
          token.name === "COQ" && "bg-[#F7D881]",
          token.name === "GURS" && "bg-white",
          token.name === "NOCHILL" && "bg-[#80BCF8]",
        )}
      />
      <div className="z-10 px-6 pb-6 pt-[calc(14px+env(safe-area-inset-top))] shadow-[0px_-5px_14px_20px_rgba(0,0,0,0.25)] backdrop-blur-[120px]">
        {isTutorialOpen && (
          <div className="absolute inset-0 z-40 bg-dark-bk/65" />
        )}
        <div className="flex items-center justify-between">
          <ProgressBarLink
            href={`/${user?.twitterHandle}`}
            className="flex items-center gap-2"
          >
            <Avatar className="h-9 w-9">
              <AvatarImage src={user?.twitterPicture} />
              <AvatarFallback />
            </Avatar>
            <div>
              <h2 className="text-sm font-semibold leading-4 text-white">
                {user?.twitterName}
              </h2>
              <p className="text-xs text-gray-text">@{user?.twitterHandle}</p>
            </div>
          </ProgressBarLink>
          <MoreOptionsModal />
        </div>

        <div className="mt-2 flex gap-2">
          <div
            className={cn(
              "flex flex-1 items-center justify-between rounded-[10px] border border-dark-gray px-4 py-2",
              isTutorialOpen &&
                "relative z-50 shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)]",
            )}
          >
            <div>
              <h3 className="text-[14px] font-semibold text-gray-text">
                Portfolio Value
              </h3>
              <div className="mt-1 flex items-center gap-[6px] font-bold">
                <span className="text-[15px] font-semibold leading-6 text-off-white">
                  {isStatsLoading ||
                  isAvaxPriceLoading ||
                  isCurrenciesLoading ||
                  isBalancesLoading ? (
                    <Skeleton className="h-[15px] w-10" />
                  ) : (
                    `$${numberFormatter.format(Number(portfolioValue.toFixed(2).replace(/\.?0+$/, "")))}`
                  )}
                </span>
              </div>
            </div>
          </div>
          <div className="flex flex-1 items-center justify-between rounded-[10px] border border-dark-gray px-4 py-2">
            <div>
              <h3 className="text-[14px] font-semibold text-gray-text">
                Fees Earned
              </h3>
              <div className="mt-1 flex items-center gap-[6px]">
                <span className="text-[15px] font-semibold leading-6 text-off-white">
                  {isStatsLoading ||
                  isAvaxPriceLoading ||
                  isCurrenciesLoading ||
                  isBalancesLoading ? (
                    <Skeleton className="h-[15px] w-10" />
                  ) : (
                    `$${numberFormatter.format(Number((Number(earnings.replace(/,/g, "")) * (avaxPrice?.avax || 0)).toFixed(2).replace(/\.?0+$/, "")))}`
                  )}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-4 flex gap-2">
          <div className="flex-1">
            <DepositModal />
          </div>
          {showDynamicMigrationModal ? <DynamicMigrationModal /> : null}
          {showDynamicMigrationExecutedExternallyModal &&
          user?.dynamicAddress ? (
            <DynamicMigrationExecutedExternallyModal
              dynamicAddress={user.dynamicAddress}
            />
          ) : null}
          <div className="flex-1">
            <WithdrawModal />
          </div>
        </div>
        <div className="mt-2">
          <BuyAvaxWithStripeModal />
        </div>
        <div className="mt-4">
          <div className="relative isolate flex items-center justify-between overflow-hidden rounded-[10px] bg-[linear-gradient(285deg,#6F15C8_6.36%,#30005F_119.12%)] p-4 shadow-[0px_0px_10px_2px_rgba(170,88,251,0.25)]">
            <div className="flex flex-col gap-1">
              <h4 className="text-xs font-semibold leading-5 text-off-white">
                Total Balance
              </h4>
              <div className="flex items-center gap-1.5 ">
                <img
                  src="/assets/arena-coin.png"
                  className="size-[22px] rounded-full"
                  alt={`Arena Coin logo`}
                />
                <span className="text-xl font-semibold leading-6 text-off-white ">
                  {LocatedNumber(totalBalance)}
                </span>
              </div>
            </div>
            <Button
              variant="outline"
              className="border border-off-white px-4 py-2"
              asChild
            >
              <Link href="wallet/token-portal">Arena Portal</Link>
            </Button>
            <ArenaLogo className="absolute right-0 top-0 -z-10 w-[179px]" />
            <div className="absolute inset-0 -z-10 rounded-[10px] border border-off-white opacity-10" />
          </div>
        </div>
      </div>
    </div>
  );
};

const ArenaLogo = (props: ComponentProps<"svg">) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 148 69"
      fill="none"
      {...props}
    >
      <g
        style={{ mixBlendMode: "luminosity", opacity: "0.1" }}
        clipPath="url(#clip0_14918_21803)"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M189 188V31.3576C189 -19.1457 149.055 -60 99.5503 -60C50.0461 -60 10 -19.1457 10 31.3576V188H13.9241V43.0596C13.9241 -5.18542 52.2597 -44.2947 99.4497 -44.2947C146.64 -44.2947 185.076 -5.08278 185.076 43.0596V188H189ZM173.409 173.23V40.2992V40.1965C173.409 -1.47896 140.305 -35.2505 99.4545 -35.2505C58.6034 -35.2505 25.5 -1.47896 25.5 40.1965V173.127H29.4241V50.3588C29.4241 10.9416 60.817 -20.9823 99.4545 -20.9823C138.092 -20.9823 169.485 11.0442 169.485 50.4614V173.23H173.409ZM157.903 49.2182V158.334H153.979V57.7381C153.979 27.046 129.529 2.10229 99.4436 2.10229C69.3587 2.10229 44.9085 27.046 44.9085 57.7381V158.334H40.9844V49.2182C40.9844 16.2679 67.1451 -10.4209 99.4436 -10.4209C131.742 -10.4209 157.802 16.2679 157.802 49.2182H157.903ZM142.305 143.553V58.1494C142.305 34.0269 123.087 14.4209 99.4415 14.4209C75.7962 14.4209 56.5781 34.0269 56.5781 58.1494V143.553H60.5022V65.1295C60.5022 43.2653 78.0098 25.4043 99.4415 25.4043C120.873 25.4043 138.381 43.1626 138.381 65.1295V143.553H142.305ZM126.814 67.0783V128.77H122.89V72.5186C122.89 59.3796 112.426 48.6014 99.4463 48.6014C86.4665 48.6014 76.0022 59.2769 76.0022 72.5186V128.77H72.0781V67.0783C72.0781 51.6809 84.3536 39.1577 99.4463 39.1577C114.539 39.1577 126.714 51.6809 126.714 67.0783H126.814ZM111.224 113.887V76.0099C111.224 69.4404 105.892 64 99.452 64C93.0125 64 87.6797 69.3378 87.6797 76.0099V113.887H91.6038V79.808C91.6038 75.394 95.1255 71.8013 99.452 71.8013C103.779 71.8013 107.3 75.394 107.3 79.808V113.887H111.224Z"
          fill="url(#paint0_linear_14918_21803)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_14918_21803"
          x1="134"
          y1="38.5"
          x2="84.7908"
          y2="32.8204"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#6F15C8" />
          <stop offset="0.73" stopColor="#AC58FF" />
          <stop offset="0.968627" stopColor="#AC58FF" />
        </linearGradient>
        <clipPath id="clip0_14918_21803">
          <rect
            width="179"
            height="248"
            fill="white"
            transform="translate(0 -60)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
