"use client";

import { useState } from "react";

import { MailIcon } from "@/components/icons-v2/account-security";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

interface IEmailInputModalProps {
  handleEmail: (email: string) => void;
  handleClose: () => void;
}

export const EmailInputModal = (props: IEmailInputModalProps) => {
  const [email, setEmail] = useState<string>("");
  const [isEmailValid, setIsEmailValid] = useState<boolean>(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const verifyAndSetEmail = (email: string) => {
    if (isValidEmail(email)) {
      setIsEmailValid(true);
      setEmail(email);
    } else {
      setIsEmailValid(false);
      setEmail("");
    }
  };

  const handleRegister = () => {
    if (isEmailValid) {
      props.handleEmail(email);
    }
  };

  return (
    <div
      className={
        isTablet
          ? "fixed inset-0 z-50 flex content-center items-center justify-center "
          : ""
      }
    >
      <div
        className="bg-black fixed inset-0 z-40 bg-[#020202CC]"
        onClick={props.handleClose}
      />
      <div
        className={`" fixed z-50 px-[24px] pb-[48px] pt-[24px] ${isTablet ? "rounded-[20px]" : "bottom-[0px] w-full rounded-t-[20px]"}`}
        style={{ backgroundColor: "rgba(15, 15, 15, 0.9)" }}
      >
        <div className="justify-left flex flex-col items-center gap-[32px]">
          <div className="font-inter flex flex-col items-start gap-[8px] self-stretch text-[16px] font-semibold leading-[22px] text-[#F4F4F4]">
            <p>Log-in using recovery email</p>
          </div>
          <div className="flex w-full flex-col items-start gap-[24px] self-stretch">
            <div className="font-inter text-[14px] font-normal leading-normal text-[var(--LIGHT-GRAY-TEXT,#B5B5B5)]">
              Please enter the email you set for recovery.
            </div>
            <input
              type="email"
              className="flex h-[44px] items-center gap-[8.397px] self-stretch rounded-[10px] border border-[var(--GRAY-TEXT,#808080)] bg-[var(--LIGHT-BACKGROUND,#1A1A1A)] px-[24px] py-[10px] opacity-85"
              placeholder="<EMAIL>"
              style={{
                color: "#F4F4F4",
                textAlign: "left",
                fontSize: "14px",
                fontStyle: "normal",
                fontWeight: "400",
                lineHeight: "16.794px",
              }}
              onChange={(e) => verifyAndSetEmail(e.target.value)}
            />
          </div>
          <div className="flex-space-around flex w-full items-center gap-[8px]">
            <button
              className={`flex h-[44px] w-full items-center justify-center rounded-[40px] ${isEmailValid ? "bg-[var(--ORANGE-GRADIENT,#D64C05)]" : "bg-[var(--ORANGE-GRADIENT,#D64C05)] opacity-50"} px-[60px] py-[8px]`}
              onClick={handleRegister}
              disabled={!isEmailValid}
            >
              <p className="font-inter text-center text-[14px] font-semibold leading-[20px] text-[var(--OFF-WHITE,#F4F4F4)]">
                Continue
              </p>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
