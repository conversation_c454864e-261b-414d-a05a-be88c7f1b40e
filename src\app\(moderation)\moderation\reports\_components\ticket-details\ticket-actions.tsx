"use client";

import { FC, useState } from "react";

import { Button } from "@/components/ui/button";
import {
  ReportTicket,
  TicketStatus,
  TicketType,
} from "@/queries/types/report-tickets";
import { UserFlaggedEnum } from "@/types/user";

import { SuspendAccountModal } from "./suspend-account-modal";

interface TicketActionsProps {
  ticket: ReportTicket;
  onDeletePost: () => void;
  onSuspendAccount: () => void;
  onVisitProfile: () => void;
  onCloseReopen: () => void;
  isPending: boolean;
}

export const TicketActions: FC<TicketActionsProps> = ({
  ticket,
  onDeletePost,
  onSuspendAccount,
  onVisitProfile,
  onCloseReopen,
  isPending,
}) => {
  const isLivestream =
    ticket.type === TicketType.POST_REPORT &&
    ticket.reportedThread.threadType === "livestream" &&
    ticket.reportedThread.livestream;
  const [isSuspendOpen, setIsSuspendOpen] = useState(false);

  return (
    <div className="flex flex-grow flex-col justify-between">
      <div className="flex flex-col gap-4 py-4">
        <h3 className="text-sm font-semibold">Actions</h3>
        {ticket.type === TicketType.POST_REPORT && (
          <>
            <Button
              variant="secondary"
              onClick={onDeletePost}
              disabled={
                ticket.status === TicketStatus.CLOSED ||
                ticket.reportedThread.isDeleted
              }
              loading={isPending}
            >
              {isLivestream ? "Suspend Livestream" : "Delete Post"}
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsSuspendOpen(true)}
              disabled={
                ticket.status === TicketStatus.CLOSED ||
                ticket.reportedUser.flag === UserFlaggedEnum.SUSPENDED
              }
              loading={isPending}
            >
              Suspend Account
            </Button>
          </>
        )}
        {ticket.type === TicketType.USER_REPORT && (
          <>
            <Button
              variant="secondary"
              onClick={onVisitProfile}
              disabled={ticket.status === TicketStatus.CLOSED}
            >
              Visit Profile
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsSuspendOpen(true)}
              disabled={
                ticket.status === TicketStatus.CLOSED ||
                ticket.reportedUser.flag === UserFlaggedEnum.SUSPENDED
              }
              loading={isPending}
            >
              Suspend Account
            </Button>
          </>
        )}
      </div>
      <Button
        className="mt-6"
        variant="default"
        onClick={onCloseReopen}
        disabled={isPending}
      >
        {ticket.status === TicketStatus.ACTIVE
          ? "Close Ticket"
          : "Re-open Ticket"}
      </Button>
      <SuspendAccountModal
        open={isSuspendOpen}
        setOpen={setIsSuspendOpen}
        user={ticket.reportedUser}
        onConfirm={onSuspendAccount}
      />
    </div>
  );
};
