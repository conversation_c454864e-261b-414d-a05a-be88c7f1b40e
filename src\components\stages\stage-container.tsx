"use client";

import { useEffect } from "react";
import { useSelectedLayoutSegments } from "next/navigation";

import {
  LiveKit<PERSON><PERSON>,
  RoomAudioRenderer,
  useIsSpeaking,
  useLocalParticipant,
} from "@livekit/components-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";

import { env } from "@/env";
import { stageQueries } from "@/queries/stage-queries";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";

import { useLogSpeakingDuration } from "./hooks/use-log-speaking-duration";
import { ResponsiveSwitcher } from "./responsive-switcher";
import { StageContinueListeningModal } from "./stage-continue-listening-modal";
import { DataChannelsProvider } from "./stores/data-channels-context";

export const StageContainer = () => {
  const queryClient = useQueryClient();
  const { user } = useUser();
  const segments = useSelectedLayoutSegments();
  const canShowCurrentlyListening = useStageStore(
    (state) => state.canShowCurrentlyListening,
  );
  const token = useStageStore((state) => state.token);
  const actions = useStageStore((state) => state.actions);
  const { data: currentlyListening, isLoading } = useQuery(
    stageQueries.currentlyListening(),
  );
  const logSpeakingDuration = useLogSpeakingDuration();

  useEffect(() => {
    if (segments.length === 0) {
      actions.reset();
    }
  }, [segments]);

  if (!user) return null;

  if (token) {
    return (
      <LiveKitRoom
        audio={false}
        token={token}
        serverUrl={env.NEXT_PUBLIC_LIVEKIT_URL}
        onDisconnected={() => {
          logSpeakingDuration();
          actions.reset();
          queryClient.invalidateQueries({
            queryKey: stageQueries.currentlyListeningKey(),
          });
        }}
      >
        <DataChannelsProvider>
          <ResponsiveSwitcher />
        </DataChannelsProvider>
        <RoomAudioRenderer />
        <IsUserSpeaking />
        <PreventScreenLock />
      </LiveKitRoom>
    );
  }

  if (
    currentlyListening?.stageUser &&
    !isLoading &&
    segments.length > 0 &&
    canShowCurrentlyListening
  ) {
    return (
      <StageContinueListeningModal stageUser={currentlyListening.stageUser} />
    );
  }

  return null;
};

const IsUserSpeaking = () => {
  const local = useLocalParticipant();
  const isSpeaking = useIsSpeaking(local.localParticipant);
  const startedTime = useStageStore((state) => state.startedTime);
  const actions = useStageStore((state) => state.actions);
  const logSpeakingDuration = useLogSpeakingDuration();

  useEffect(() => {
    if (isSpeaking) {
      actions.setStartedTime(Date.now());
    } else if (startedTime > 0 && !isSpeaking) {
      actions.addSpeakingDuration(Date.now() - startedTime);
      actions.setStartedTime(0);
    }
  }, [isSpeaking]);

  useEffect(() => {
    document.addEventListener("visibilitychange", function logData() {
      if (document.visibilityState === "hidden") {
        logSpeakingDuration();
      }
    });
  }, []);

  return null;
};

const PreventScreenLock = () => {
  useEffect(() => {
    let wakeLock: WakeLockSentinel | null = null;

    const requestWakeLock = async () => {
      try {
        if ("wakeLock" in navigator) {
          wakeLock = await navigator.wakeLock.request("screen");
        }
      } catch (err) {
        console.log("Wake Lock error:", err);
      }
    };

    requestWakeLock();

    return () => {
      if (wakeLock) {
        wakeLock
          .release()
          .catch((err) => console.log("Wake Lock release error:", err));
      }
    };
  }, []);

  return null;
};
