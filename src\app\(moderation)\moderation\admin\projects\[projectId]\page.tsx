"use client";

import { FC, useEffect } from "react";
import { useParams } from "next/navigation";

import { CurrencyCreate } from "@/app/(moderation)/moderation/admin/projects/[projectId]/_components/currency-create";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

import { useAdmin } from "../../context/admin-context";
import { CurrencyEdit } from "./_components/currency-edit";

const ProjectDetailPage: FC = () => {
  const params = useParams();
  const projectId = params.projectId as string;
  const isLargeTablet = useMediaQuery(BREAKPOINTS.lg);
  const { setSelectedProject, setSelectedReward } = useAdmin();

  useEffect(() => {
    setSelectedReward(null);

    if (projectId === "new") {
      setSelectedProject(null);
    }
  }, [projectId, setSelectedProject, setSelectedReward]);

  if (projectId === "new") {
    return <CurrencyCreate />;
  }

  return <CurrencyEdit projectId={projectId} />;
};

export default ProjectDetailPage;
