"use client";

import { useEffect, useMemo, useState } from "react";

import {
  useConnectionState,
  useLocalParticipant,
  useParticipantAttributes,
  useRoomInfo,
} from "@livekit/components-react";
import { AnimatePresence, motion } from "framer-motion";
import { ConnectionState } from "livekit-client";

import { TippingInfo } from "@/queries/types/livestream";
import { useLivestreamStore } from "@/stores/livestream";

import { AnimatedNumber } from "../animated-number";
import { ArrowBackOutlineIcon, CrownFilledIcon } from "../icons";
import { Button } from "../ui/button";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
  useCarousel,
} from "../ui/carousel";

export function TipCards() {
  const connectionState = useConnectionState();
  const localParticipant = useLocalParticipant();
  const { attributes } = useParticipantAttributes({
    participant: localParticipant.localParticipant,
  });
  const [api, setApi] = useState<CarouselApi>();
  const actions = useLivestreamStore((state) => state.actions);

  const roomInfo = useRoomInfo();
  const metadata = roomInfo.metadata
    ? JSON.parse(roomInfo.metadata)
    : { tippingInfo: {} };

  const tippingStats = useMemo(() => {
    const tippingInfo = metadata.tippingInfo as TippingInfo;
    return (
      tippingInfo.stats ?? {
        totalTips: 0,
        totalTipsEver: 0,
        biggestTip: null,
        biggestTipEver: null,
      }
    );
  }, [metadata]);

  useEffect(() => {
    if (!api) {
      return;
    }

    api.on("select", () => {
      if (api.selectedScrollSnap() === 0) {
        actions.setSupportersType("LIVESTREAM");
      } else {
        actions.setSupportersType("CREATOR");
      }
    });
  }, [api]);

  if (!tippingStats || connectionState !== ConnectionState.Connected)
    return null;

  return (
    <div className="py-2">
      <Carousel setApi={setApi}>
        <CarouselContent>
          <CarouselItem>
            <div className="relative grid grid-cols-2 gap-2 overflow-hidden py-4 pl-4 pr-6">
              <AnimatePresence mode="popLayout" initial={false}>
                {tippingStats?.biggestTip ? (
                  <motion.div
                    key={`${tippingStats?.biggestTip?.tipper.twitterName}-biggest-tip-card`}
                    className="relative isolate flex min-w-0 flex-col gap-1 rounded bg-light-background px-4 pb-4 pt-6"
                    initial={{ y: -100, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: 100, opacity: 0 }}
                    transition={{
                      type: "spring",
                      duration: 0.8,
                      bounce: 0.4,
                    }}
                  >
                    <div className="truncate text-sm font-medium leading-none text-off-white">
                      {tippingStats?.biggestTip?.tipper.twitterName}
                    </div>
                    <div className="w-full overflow-hidden text-xl font-semibold text-off-white">
                      $
                      <AnimatedNumber
                        value={tippingStats?.biggestTip?.amount || 0}
                      />
                    </div>
                    <div className="absolute -top-3 left-3 bg-purple-gradient px-2 py-1 text-[10px] font-extrabold text-off-white">
                      Biggest Tip
                    </div>
                    <div
                      className="clip-card-image mask-card-image absolute bottom-0 right-0 top-0 -z-10 h-full w-1/2  rounded-r bg-cover"
                      style={{
                        backgroundImage: `url(${tippingStats.biggestTip?.tipper.twitterPicture})`,
                      }}
                    />
                  </motion.div>
                ) : (
                  <motion.div
                    key={`${attributes?.username}-biggest-tip-card`}
                    className="relative isolate flex min-w-0 flex-1 flex-col gap-1 rounded bg-light-background px-4 pb-4 pt-6"
                    initial={{ y: -100, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: 100, opacity: 0 }}
                    transition={{
                      type: "spring",
                      duration: 0.8,
                      bounce: 0.4,
                    }}
                  >
                    <NameAnimation name={attributes?.name ?? ""} />
                    <div className="text-xl font-semibold text-off-white">
                      $
                    </div>
                    <div className="absolute -top-3 left-3 bg-purple-gradient px-2 py-1 text-[10px] font-extrabold text-off-white">
                      Biggest Tip
                    </div>
                    <div
                      className="clip-card-image mask-card-image absolute bottom-0 right-0 top-0 -z-10 h-full w-1/2  rounded-r bg-cover"
                      style={{
                        backgroundImage: `url(${attributes?.avatar})`,
                      }}
                    />
                  </motion.div>
                )}
              </AnimatePresence>

              <div className="relative isolate flex flex-col gap-1 rounded bg-light-background px-4 pb-4 pt-6">
                <div className="text-sm font-medium leading-none text-off-white">
                  On this Stream
                </div>
                <div className="w-full overflow-hidden text-xl font-semibold text-off-white">
                  $<AnimatedNumber value={tippingStats?.totalTips || 0} />
                </div>
                <div className="absolute -top-3 left-3 bg-purple-gradient px-2 py-1 text-[10px] font-extrabold text-off-white">
                  Total Tips
                </div>
              </div>
            </div>
          </CarouselItem>
          <CarouselItem>
            <div className="relative flex gap-2 overflow-hidden py-4 pl-6 pr-4">
              <AnimatePresence mode="popLayout" initial={false}>
                {tippingStats?.biggestTipEver ? (
                  <motion.div
                    key={`${tippingStats?.biggestTipEver?.tipper.twitterName}-biggest-tip-card`}
                    className="relative isolate flex min-w-0 flex-1 flex-col gap-1 rounded bg-light-background px-4 pb-4 pt-6"
                    initial={{ y: -100, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: 100, opacity: 0 }}
                    transition={{
                      type: "spring",
                      duration: 0.8,
                      bounce: 0.2,
                    }}
                  >
                    <div className="truncate text-sm font-medium leading-none text-off-white">
                      {tippingStats?.biggestTipEver?.tipper.twitterName}
                    </div>
                    <div className="text-xl font-semibold text-off-white">
                      $
                      <AnimatedNumber
                        value={tippingStats?.biggestTipEver?.amount || 0}
                      />
                    </div>
                    <div className="absolute -top-3 left-3 flex items-center gap-1 bg-purple-gradient px-2 py-1 text-[10px] font-extrabold text-off-white">
                      <CrownFilledIcon className="size-3 text-off-white" />
                      Biggest Tip Ever
                    </div>
                    <div
                      className="clip-card-image mask-card-image absolute bottom-0 right-0 top-0 -z-10 h-full w-1/2  rounded-r bg-cover"
                      style={{
                        backgroundImage: `url(${tippingStats?.biggestTipEver?.tipper.twitterPicture})`,
                      }}
                    />
                  </motion.div>
                ) : (
                  <motion.div
                    key={`${attributes?.username}-biggest-tip-card`}
                    className="relative isolate flex min-w-0 flex-1 flex-col gap-1 rounded bg-light-background px-4 pb-4 pt-6"
                    initial={{ y: -100, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: 100, opacity: 0 }}
                    transition={{
                      type: "spring",
                      duration: 0.8,
                      bounce: 0.2,
                    }}
                  >
                    <NameAnimation name={attributes?.name ?? ""} />
                    <div className="text-xl font-semibold text-off-white">
                      $
                    </div>
                    <div className="absolute -top-3 left-3 flex items-center gap-1 bg-purple-gradient px-2 py-1 text-[10px] font-extrabold text-off-white">
                      <CrownFilledIcon className="size-3 text-off-white" />
                      Biggest Tip Ever
                    </div>
                    <div
                      className="clip-card-image mask-card-image absolute bottom-0 right-0 top-0 -z-10 h-full w-1/2  rounded-r bg-cover"
                      style={{
                        backgroundImage: `url(${attributes?.avatar})`,
                      }}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
              <div className="relative isolate flex flex-1 flex-col gap-1 rounded bg-light-background px-4 pb-4 pt-6">
                <div className="text-sm font-medium leading-none text-off-white">
                  On this Creator
                </div>
                <div className="text-xl font-semibold text-off-white">
                  $<AnimatedNumber value={tippingStats?.totalTipsEver || 0} />
                </div>
                <div className="absolute -top-3 left-3 flex items-center gap-1 bg-purple-gradient px-2 py-1 text-[10px] font-extrabold text-off-white">
                  <CrownFilledIcon className="size-3 text-off-white" />
                  Total Tips Ever
                </div>
              </div>
            </div>
          </CarouselItem>
        </CarouselContent>
        <PreviousButton />
        <NextButton />
      </Carousel>
    </div>
  );
}

const NameAnimation = ({ name }: { name: string }) => {
  const [isFirstText, setIsFirstText] = useState(true);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsFirstText((prev) => !prev);
    }, 3000); // Switch every 3 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <AnimatePresence mode="popLayout" initial={false}>
      {isFirstText ? (
        <motion.div
          key="text1"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0, transition: { duration: 0.3 } }}
          transition={{ duration: 0.5 }}
          className="truncate text-sm font-medium leading-none text-gray-text"
        >
          {name}
        </motion.div>
      ) : (
        <motion.div
          key="text2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0, transition: { duration: 0.3 } }}
          transition={{ duration: 0.5 }}
          className="truncate text-sm font-medium leading-none text-off-white"
        >
          Take this spot!
        </motion.div>
      )}
    </AnimatePresence>
  );
};

const PreviousButton = () => {
  const { scrollPrev, canScrollPrev } = useCarousel();

  if (!canScrollPrev) return null;

  return (
    <Button
      variant="outline"
      className="absolute left-2.5 top-1/2 z-30 flex size-7 -translate-y-1/2 rounded-full border-none bg-purple-gradient p-1 lg:items-center lg:justify-center"
      onClick={scrollPrev}
    >
      <ArrowBackOutlineIcon className="size-4 text-white" />
    </Button>
  );
};

const NextButton = () => {
  const { scrollNext, canScrollNext } = useCarousel();

  if (!canScrollNext) return null;

  return (
    <Button
      variant="outline"
      className="absolute right-2.5 top-1/2 z-30 flex size-7 -translate-y-1/2 rotate-180 rounded-full border-none bg-purple-gradient p-1 lg:items-center lg:justify-center"
      onClick={scrollNext}
    >
      <ArrowBackOutlineIcon className="size-4 text-white" />
    </Button>
  );
};
