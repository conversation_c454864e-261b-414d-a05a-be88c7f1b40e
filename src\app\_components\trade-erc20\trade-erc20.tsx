"use client";

import {
  <PERSON><PERSON><PERSON>,
  FC,
  ReactNode,
  SetStateAction,
  useEffect,
  useState,
} from "react";

import { EthereumWallet } from "@dynamic-labs/ethereum-core";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { Tabs, TabsContent, TabsList } from "@radix-ui/react-tabs";
import { Account, Chain, PublicClient, Transport, WalletClient } from "viem";

import { ArrowBackHeader } from "@/app/(main)/_components/arrow-back-header";
import { TabsFlatTrigger } from "@/components/ui/tabs";
import { MinTokenData } from "@/environments/tokens";
import { ThreadUser } from "@/types";
import { CommunityExtended } from "@/types/community";
import { cn } from "@/utils";

import { PageHeader } from "../page-header";
import { BuyERC20Tab } from "./buy-erc20-tab";
import { SellERC20Tab } from "./sell-erc20-tab";
import { TradeERC20Skeleton } from "./trade-erc20-skeleton";

export type TradeERC20Context = {
  dynamicWallet: EthereumWallet;
  publicClient: PublicClient<Transport, Chain>;
  walletClient: WalletClient<Transport, Chain, Account>;
  token: MinTokenData;
  tokenPrice: number;
  userBalance: bigint;
  userTokenBalance: bigint;
  avaxPrice: number;
  conversionRate: bigint;
  community?: CommunityExtended;
  postAuthor?: ThreadUser | undefined;
};

interface TradeTabRenderProps {
  entryRequirement?: (ctx: TradeERC20Context) => ReactNode;
  terms?: () => ReactNode;
}

export interface TradeTabProps extends TradeTabRenderProps {
  ctx: TradeERC20Context;
  updateCtx: () => Promise<void>;
}

export type GetTokenAmountReturnType = {
  tokenAmount: string;
  tokenDisplayAmount?: string;
  commission?: bigint | null;
  avaxAmount?: string;
  avaxDisplayAmount?: string;
};

interface BuyTrade {
  swapAvaxToToken: (
    ctx: TradeERC20Context,
    avaxAmount: string,
    commission: bigint | null,
    slippage: number,
    tokenAmount?: string,
  ) => Promise<void>;
  getTokenAmount: (
    ctx: TradeERC20Context,
    amount: string,
    calculateCommission: (amount: string) => bigint,
  ) => Promise<GetTokenAmountReturnType>;
}

export type GetAvaxAmountReturnType = {
  avaxAmount: string;
  commission?: bigint | null;
};

interface SellTrade {
  swapTokenToAvax: (
    ctx: TradeERC20Context,
    tokenAmount: string,
    commission: bigint | null,
    slippage: number,
  ) => Promise<void>;
  getAvaxAmount: (
    ctx: TradeERC20Context,
    amount: bigint,
    calculateCommission: (destAmount: string) => bigint,
  ) => Promise<GetAvaxAmountReturnType>;
}

export interface BuyTradeTabProps extends TradeTabProps, BuyTrade {}

export interface SellTradeTabProps extends TradeTabProps, SellTrade {}

interface TradeERC20Props extends BuyTrade, SellTrade, TradeTabRenderProps {
  requestCtxData: (
    setCtx: Dispatch<SetStateAction<TradeERC20Context>>,
  ) => Promise<void>;
}

export const TradeERC20: FC<TradeERC20Props> = ({
  requestCtxData,
  swapAvaxToToken,
  getTokenAmount,
  swapTokenToAvax,
  getAvaxAmount,
  entryRequirement,
  terms,
}) => {
  const [currentTab, setCurrentTab] = useState<string>("buy");
  const { primaryWallet } = useDynamicContext();

  const [ctx, setCtx] = useState<TradeERC20Context | undefined>();

  const updateCtx = async () =>
    await requestCtxData(setCtx as Dispatch<SetStateAction<TradeERC20Context>>);

  useEffect(() => {
    if (primaryWallet) updateCtx();
  }, [primaryWallet]);

  const [hasScroll, setHasScroll] = useState(false);

  useEffect(() => {
    const checkScroll = () =>
      setHasScroll(document.body.scrollHeight > window.innerHeight);

    checkScroll();
    window.addEventListener("resize", checkScroll);

    return () => window.removeEventListener("resize", checkScroll);
  }, []);

  return (
    <>
      <PageHeader isSticky />
      <Tabs
        value={currentTab}
        onValueChange={setCurrentTab}
        className="mx-3 mt-6 flex flex-1 flex-col"
      >
        <TabsList className={cn("flex items-end gap-4 px-7 pt-2")}>
          <TabsFlatTrigger value="buy" className="h-[60px] flex-1">
            <div className="font-inter text-[26px] font-semibold leading-[30px]">
              Buy
            </div>
          </TabsFlatTrigger>
          <TabsFlatTrigger value="sell" className="h-[60px] flex-1 ">
            <div className="font-inter text-[26px] font-semibold leading-[30px]">
              Sell
            </div>
          </TabsFlatTrigger>
        </TabsList>

        <div className="relative top-[-1px] z-[-1] mx-[10px] h-px w-full bg-dark-gray" />

        <TabsContent
          value="buy"
          className={cn(
            "flex flex-1 flex-col",
            hasScroll && "mt-6",
            currentTab === "sell" && "hidden",
          )}
        >
          {ctx ? (
            <BuyERC20Tab
              ctx={ctx}
              updateCtx={updateCtx}
              swapAvaxToToken={swapAvaxToToken}
              getTokenAmount={getTokenAmount}
              entryRequirement={entryRequirement}
              terms={terms}
            />
          ) : (
            <TradeERC20Skeleton />
          )}
        </TabsContent>
        <TabsContent
          value="sell"
          className={cn(
            "flex flex-1 flex-col",
            hasScroll && "mt-6",
            currentTab === "buy" && "hidden",
          )}
        >
          {ctx ? (
            <SellERC20Tab
              ctx={ctx}
              updateCtx={updateCtx}
              swapTokenToAvax={swapTokenToAvax}
              getAvaxAmount={getAvaxAmount}
              entryRequirement={entryRequirement}
              terms={terms}
            />
          ) : (
            <TradeERC20Skeleton />
          )}
        </TabsContent>
      </Tabs>
    </>
  );
};
