"use client";

import * as React from "react";

import type { VariantProps } from "class-variance-authority";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ottieReact } from "@lottiefiles/dotlottie-react";
import { Slot } from "@radix-ui/react-slot";
import { cva } from "class-variance-authority";

import { cn } from "@/utils";

/*
ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2
*/
const buttonVariants = cva(
  "inline-flex relative overflow-hidden items-center justify-center whitespace-nowrap rounded-full text-sm font-semibold transition-colors  disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none",
  {
    variants: {
      variant: {
        default:
          "bg-[linear-gradient(98deg,#FF7817_-10.93%,#D05700_-10.93%,#DD3C09_57.47%)] text-off-white",
        destructive: "bg-red-600 text-off-white hover:bg-red-600/90",
        outline:
          "border border-gray-text text-off-white text-sm font-semibold hover:bg-gray-text/10",
        "outline-ghost":
          "border border-[#3B3B3B] text-off-white text-sm font-semibold hover:bg-gray-text/10",
        secondary: "bg-off-white text-dark-bk hover:bg-off-white/90",
        ghost: "hover:bg-gray-text/10",
        link: "text-slate-900 underline-offset-4 hover:underline dark:text-slate-50",
        active: "bg-green text-off-white",
      },
      size: {
        default: "px-4 py-3",
        sm: "rounded-md px-3",
        lg: "rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      loading,
      children,
      disabled,
      ...props
    },
    ref,
  ) => {
    const lottieRef = React.useRef<DotLottie | null>(null);

    React.useEffect(() => {
      if (loading) {
        lottieRef.current?.play();
      } else {
        lottieRef.current?.pause();
      }
    }, [loading]);

    const dotLottieRefCallback = React.useCallback((ref: DotLottie | null) => {
      lottieRef.current = ref;
    }, []);

    const buttonClassName = React.useMemo(
      () =>
        cn(
          buttonVariants({ variant, size }),
          loading && "disabled:opacity-100",
          className,
        ),
      [variant, size, loading, className],
    );

    const loadingClassName = React.useMemo(
      () =>
        cn(
          "absolute inset-0 flex items-center justify-center transition-[opacity,transform] duration-300",
          loading ? "translate-y-0" : "translate-y-[-150%]",
        ),
      [loading],
    );

    const childrenClassName = React.useMemo(
      () =>
        cn(
          "inline-flex items-center justify-center gap-1 transition-[opacity,transform] duration-300",
          loading ? "translate-y-[150%]" : "translate-y-[0%]",
        ),
      [loading],
    );

    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={buttonClassName}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {asChild ? (
          children
        ) : (
          <>
            <div className={loadingClassName}>
              <span>
                <DotLottieReact
                  src="/assets/animations/loader.lottie"
                  loop
                  speed={1.3}
                  className="size-6 flex-shrink-0"
                  dotLottieRefCallback={dotLottieRefCallback}
                />
              </span>
            </div>
            <div className={childrenClassName}>{children}</div>
          </>
        )}
      </Comp>
    );
  },
);

Button.displayName = "Button";

export { Button, buttonVariants };
