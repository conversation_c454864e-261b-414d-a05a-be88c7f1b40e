import Link from "next/link";

// import { assets } from "@/data/threads";
import { CloseOutlineIcon } from "@/components/icons";
import { ImagePreview } from "@/components/image-preview";
import { Asset } from "@/types";

interface PhotoPageProps {
  params: { id: string };
}

function PhotoPage({ params: { id } }: PhotoPageProps) {
  const assets: Asset[] = [];

  const asset = assets.find((asset) => asset.id === parseInt(id, 10));

  if (!asset) return <div>Asset not found</div>;

  return (
    <div className="relative flex h-screen flex-col">
      <Link
        href="/home"
        className="absolute left-2 top-2 z-10 flex h-12 w-12 items-center justify-center text-white"
      >
        <CloseOutlineIcon className="h-6 w-6" />
      </Link>
      <ImagePreview url={asset.url} />
    </div>
  );
}

export default PhotoPage;
