import { InvitedRole, Role } from "@/components/stages/constants";
import { Thread, ThreadUser } from "@/types";

export interface StageBadgeType {
  id: number;
  createdOn: string;
  stageId: string;
  badgeType: number;
}

export interface Stage {
  privacyType: number;
  hostId: string;
  isActive: boolean;
  name: string;
  threadId: string;
  isRecorded: boolean;
  endedOn: string | null;
  recordingUrl: string | null;
  id: string;
  createdOn: string;
  isRecordingComplete: boolean;
  badgeTypes?: StageBadgeType[];
  scheduledStartTime?: string;
  startedOn?: string;
}

export interface CreateStageParams {
  name: string;
  record: boolean;
  privacyType: number;
  badgeTypes?: number[];
  scheduledStartTime?: string;
}

export interface EditStageParams {
  stageId: string;
  name: string;
  record: boolean;
  privacyType: number;
  badgeTypes?: number[];
  scheduledStartTime?: string;
}

export interface CreateStageResponse {
  stage: Stage;
  token?: string;
}

export interface EditStageResponse {
  stage: Stage;
}

export interface JoinStageParams {
  stageId: string;
}

export interface JoinStageResponse {
  token: string;
}

export interface UpdateRoleParams {
  stageId: string;
  userId: string;
  role: Role;
}

export interface UpdateRoleResponse {
  success: boolean;
}

export interface RequestToSpeakParams {
  stageId: string;
}

export interface RequestToSpeakResponse {
  success: boolean;
}

export interface EndStageParams {
  stageId: string;
}

export interface EndStageResponse {
  success: boolean;
}

export interface LiveStage extends Stage {
  thread: Thread;
}

export interface LiveStageMinimal {
  id: string;
  privacyType: number;
  count: number;
  type: "stage" | "livestream";
}

export interface LiveStagesResponse {
  live: LiveStageMinimal[];
}

export interface StageUser {
  id: string;
  createdOn: string;
  stageId: string;
  userId: string;
  role: Role;
  invitedRole: InvitedRole;
  hasRequestedToSpeak: boolean;
  isMuted: boolean;
  isPresent: boolean;
  isBlocked: boolean;
  user: ThreadUser;
  stage: Stage;
}

export interface GetStageInfoResponse {
  stage: Stage;
  live: StageUser[];
  host: StageUser;
  cohosts: StageUser[];
  invitedCohosts: StageUser[];
  speakers: StageUser[];
  invitedSpeakers: StageUser[];
  requestedToSpeak: StageUser[];
  listenersCount: number;
  tunedInCount: number;
  canJoin: boolean;
}

export interface StageUserSimple {
  id: string;
  role: Role;
  user: {
    twitterPicture: string;
    twitterHandle: string;
    twitterName: string;
  };
}

export interface GetStageInfoSimpleResponse {
  stage: Stage;
  live: StageUserSimple[];
  host: StageUserSimple;
  listenersCount: number;
  tunedInCount: number;
  canJoin: boolean;
  hasSetReminder: boolean;
}

export interface GetStageSpeakersResponse {
  speakers: StageUser[];
}

export interface GetMostRecentTipResponse {
  currency: string;
  amount: string;
}

export interface PostTipParams {
  stageId: string;
  toUserId: string;
  currency: string;
  amount: number;
}

export interface PostTipResponse {
  success: boolean;
}

export interface GetCurrentlyListeningResponse {
  stageUser: StageUser;
}

export interface PostInviteParams {
  stageId: string;
  invitedUserId: string;
  roleType: Role;
}

export interface PostInviteResponse {
  success: boolean;
}

export interface GetIsUserInvitedParams {
  stageId: string;
  userId: string;
}

export interface GetIsUserInvitedResponse {
  user?: StageUser;
}

export interface PostAcceptInvitationParams {
  stageId: string;
  userId: string;
}

export interface PostAcceptInvitationResponse {
  success: boolean;
}

export interface PostCancelInvitationParams {
  stageId: string;
  invitedUserId: string;
}

export interface PostCancelInvitationResponse {
  success: boolean;
}

export interface PostLeaveStageParams {
  stageId: string;
}

export interface PostLeaveStageResponse {
  success: boolean;
}

export interface PostBlockUserParams {
  stageId: string;
  userId: string;
}

export interface PostBlockUserResponse {
  success: boolean;
}

export type StageParticipant = {
  id: string;
  name: string;
  avatar: string;
  username: string;
  role: Role;
};

export interface PostUpdateSpeakingDurationParams {
  stageId: string;
  speakingDuration: number;
}

export interface PostUpdateSpeakingDurationResponse {
  success: boolean;
}

export interface PostToggleMuteParams {
  stageId: string;
}

export interface PostToggleMuteResponse {
  success: boolean;
}

export interface PostPinPostParams {
  stageId: string;
  postId: string;
}

export interface PostPinPostResponse {
  success: boolean;
}

export interface DeletePinPostParams {
  stageId: string;
  postId: string;
}

export interface DeletePinPostResponse {
  success: boolean;
}

export interface PostStartStageParams {
  stageId: string;
}

export interface PostStartStageResponse {
  token: string;
}

export interface PostRemindStageParams {
  stageId: string;
}

export interface PostRemindStageResponse {
  success: boolean;
}

export interface DeleteStageParams {
  stageId: string;
}

export interface DeleteStageResponse {
  success: boolean;
}

export interface PostToggleRaisedHandParams {
  stageId: string;
}

export interface PostToggleRaisedHandResponse {
  success: boolean;
}

export interface PostDropDownToListenerParams {
  stageId: string;
}

export interface PostDropDownToListenerResponse {
  success: boolean;
}
export interface PostUpdateAttributesParams {
  stageId: string;
}

export interface PostDropDownToListenerParams {
  stageId: string;
}

export interface PostDropDownToListenerResponse {
  success: boolean;
}

export interface GetEmotesResponse {
  emotes: string[][];
}

export interface PutUpdateEmotesParams {
  emotes: string[][];
}

export interface PutUpdateEmotesResponse {
  emotes: string[][];
}
