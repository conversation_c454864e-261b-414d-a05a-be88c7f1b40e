"use client";

import { startTransition, useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";

import {
  useLocalParticipant,
  useParticipantAttribute,
  useParticipantAttributes,
} from "@livekit/components-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { ErrorBoundary } from "react-error-boundary";

import {
  postJoinLivestream,
  postUpdateAttributes,
} from "@/api/client/livestream";
import { ReportModal } from "@/app/(main)/[userHandle]/_components/report-modal";
import {
  AnnotationOpenOutlineIcon,
  ChevronDownFilled,
  CurrencyDollarOutlineIcon,
  ExclamationCircleOutlineIcon,
  LogoIcon,
  PartyOutlineIcon,
  TipOutlineIcon,
} from "@/components/icons";
import { ChatbubbleOutlineIcon } from "@/components/icons-v2";
import { UsersOutlineIcon } from "@/components/icons/users-outline";
import { Role, ROLES } from "@/components/livestream/constants";
import { HostInfo } from "@/components/livestream/host-info";
import { TimeCounter, ViewCounter } from "@/components/livestream/live-info";
import { LivestreamChatContainer } from "@/components/livestream/livestream-chat";
import { CustomTipModal } from "@/components/livestream/livestream-custom-tip-modal";
import { LivestreamTippingPartyModal } from "@/components/livestream/livestream-tipping-party-modal";
import { RightPanel } from "@/components/livestream/right-panel";
import { SupportersContainer } from "@/components/livestream/supporters-container";
import { TipCards } from "@/components/livestream/tip-cards";
import { VideoPlayer } from "@/components/livestream/video-player-new";
import { ViewersModeration } from "@/components/livestream/viewers-moderation";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { livestreamQueries, userQueries } from "@/queries";
import { useUser } from "@/stores";
import { useLivestreamStore } from "@/stores/livestream";

import { SideNav } from "./_components/side-nav";

const ControlsBar = dynamic(
  () => import("@/components/livestream/controls-bar"),
  {
    ssr: false,
  },
);

export default function LivestreamPage({
  params: { twitterHandle },
}: {
  params: { twitterHandle: string };
}) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { user } = useUser();
  // const segments = useSelectedLayoutSegments();
  // const canShowCurrentlyListening = useStageStore(
  //   (state) => state.canShowCurrentlyListening,
  // );
  const savedTwitterHandle = useLivestreamStore((state) => state.twitterHandle);
  const token = useLivestreamStore((state) => state.token);
  const actions = useLivestreamStore((state) => state.actions);
  const isNewStream = useLivestreamStore((state) => state.isNewStream);
  // const { data: currentlyListening, isLoading } = useQuery(
  //   stageQueries.currentlyListening(),
  // );
  // const logSpeakingDuration = useLogSpeakingDuration();

  useQuery({
    queryKey: [
      "livestream",
      { twitterHandle, token, isNewStream, savedTwitterHandle },
    ],
    queryFn: async () => {
      if (
        twitterHandle &&
        (!token || isNewStream || savedTwitterHandle !== twitterHandle)
      ) {
        try {
          actions.reset();

          const data = await postJoinLivestream({
            twitterHandle,
          });

          actions.setId(data.id);
          actions.setTwitterHandle(twitterHandle);
          actions.setToken(data.token);
          actions.setIsNewStream(false);
          queryClient.prefetchQuery({
            queryKey: livestreamQueries.livestreamSimpleInfoKey(data.id),
          });

          return data;
        } catch (error) {
          if (
            error instanceof AxiosError &&
            error.response?.data.message !== "Livestream has ended."
          ) {
            console.error(error.response?.data);
            toast.danger(
              error.response?.data.message ||
                "An unexpected error occurred. Please try again later.",
            );
          }
          router.push("/live?streamTab=livestreams");
        }
      }

      return null;
    },
  });

  useEffect(() => {
    if (!user) {
      startTransition(() => {
        router.push("/live?streamTab=livestreams");
        actions.reset();
      });
    }
  }, [user]);

  if (!user) return null;

  if (token && !isNewStream) {
    return <LivestreamContainer />;
  }

  // if (
  //   currentlyListening?.stageUser &&
  //   !isLoading &&
  //   segments.length > 0 &&
  //   canShowCurrentlyListening
  // ) {
  //   return (
  //     <StageContinueListeningModal stageUser={currentlyListening.stageUser} />
  //   );
  // }

  return null;
}

function LivestreamContainer() {
  const isLaptop = useMediaQuery(BREAKPOINTS.lg);
  const id = useLivestreamStore((state) => state.id!);
  const actions = useLivestreamStore((state) => state.actions);
  const local = useLocalParticipant();
  const myRole = useParticipantAttribute("role", {
    participant: local.localParticipant,
  }) as Role;
  const { attributes } = useParticipantAttributes({
    participant: local.localParticipant,
  });

  useQuery({
    queryKey: ["livestream-participant-attributes", id, attributes],
    queryFn: async () => {
      if (
        !attributes ||
        !attributes?.id ||
        !attributes?.name ||
        !attributes?.avatar ||
        !attributes?.username ||
        !attributes?.role
      ) {
        await postUpdateAttributes({
          livestreamId: id,
        });
      }
      return true;
    },
  });

  useEffect(() => {
    actions.setMyRole(myRole);
  }, [myRole]);

  if (isLaptop) {
    return <LivetreamDesktop />;
  }

  return <LivestreamMobile />;
}

function LivetreamDesktop() {
  const { user } = useUser();
  const id = useLivestreamStore((state) => state.id!);
  const actions = useLivestreamStore((state) => state.actions);
  const [customTipOpen, setCustomTipOpen] = useState(false);
  const [isReportOpen, setIsReportOpen] = useState(false);
  const { data } = useQuery(livestreamQueries.livestreamSimpleInfo(id));
  const local = useLocalParticipant();
  const myRole = useParticipantAttribute("role", {
    participant: local.localParticipant,
  }) as Role;

  const { data: userData, isLoading } = useQuery({
    ...userQueries.byHandle(data?.host.user.twitterHandle ?? ""),
    enabled: !!data?.host.user.twitterHandle,
  });

  if (!userData && isLoading) {
    return null;
  }

  return (
    <div className="isolate mx-auto flex min-h-[100svh] max-w-[1350px] gap-6 px-4">
      <header className="relative z-[3] hidden sm:flex sm:flex-shrink-0 sm:flex-col sm:items-end">
        <SideNav />
      </header>
      <div className="absolute mx-auto min-h-[100svh] max-w-[1350px]">
        <div className="absolute left-48 top-16 -z-20 size-[800px] rounded-full bg-[#876CA2] blur-[200px]" />
      </div>
      <div className="absolute inset-0 -z-10 bg-[rgba(28,22,20,0.80)] backdrop-blur" />
      <div className="flex flex-grow-[2] flex-nowrap justify-start gap-4 pt-8 xl:gap-7">
        <div className="relative flex w-full flex-grow flex-col">
          <VideoPlayer />
          {myRole === ROLES.HOST && <ControlsBar />}
          <div className="mt-6 flex items-start justify-between gap-6 px-3">
            <div className="flex flex-col">
              <h1 className="text-2xl font-semibold">
                {data?.livestream.name}
              </h1>
              <div className="mt-2 flex items-center gap-4">
                <TimeCounter />
                <ViewCounter />
              </div>
            </div>
            <div className="flex items-center gap-2">
              {myRole !== ROLES.HOST && (
                <>
                  <Button
                    variant="outline"
                    className="size-10"
                    onClick={() => setIsReportOpen(true)}
                  >
                    <ExclamationCircleOutlineIcon className="size-6 text-off-white" />
                  </Button>
                  <Button
                    variant="outline"
                    className="size-10"
                    onClick={() => setCustomTipOpen(true)}
                  >
                    <TipOutlineIcon className="size-6 text-off-white" />
                  </Button>
                </>
              )}
              <ErrorBoundary fallback={null}>
                <LivestreamTippingPartyModal>
                  <Button variant="outline" className="size-10">
                    <PartyOutlineIcon className="size-6 text-off-white" />
                  </Button>
                </LivestreamTippingPartyModal>
              </ErrorBoundary>
              {myRole === ROLES.HOST && (
                <Button
                  variant="outline"
                  className="size-10"
                  onClick={() => {
                    actions.setChatOpenInNewWindow(true);
                  }}
                >
                  <AnnotationOpenOutlineIcon className="size-6 text-off-white" />
                </Button>
              )}
              {(myRole === ROLES.HOST || user?.isMod) && (
                <ViewersModeration>
                  <Button variant="outline" className="size-10">
                    <UsersOutlineIcon className="size-6 text-off-white" />
                  </Button>
                </ViewersModeration>
              )}
            </div>
          </div>
          {myRole !== ROLES.HOST && (
            <div className="mt-6 flex items-start justify-between gap-6">
              <HostInfo />
            </div>
          )}
        </div>
        <div className="hidden flex-shrink-0 pb-10 lg:flex lg:w-[290px] lg:flex-col xl:w-[380px]">
          <div className="flex h-full max-h-[calc(100svh-64px)] w-full flex-col overflow-hidden rounded-xl border border-dark-gray bg-dark-bk">
            <RightPanel />
          </div>
        </div>
      </div>
      {userData && userData.user && (
        <>
          <CustomTipModal
            userToSend={userData.user}
            open={customTipOpen}
            setOpen={setCustomTipOpen}
          />
          <ReportModal
            open={isReportOpen}
            setOpen={setIsReportOpen}
            user={userData.user}
            threadId={data?.livestream.threadId}
          />
        </>
      )}
    </div>
  );
}

function LivestreamMobile() {
  const router = useRouter();
  const [customTipOpen, setCustomTipOpen] = useState(false);
  const [isReportOpen, setIsReportOpen] = useState(false);
  const id = useLivestreamStore((state) => state.id!);
  const { data } = useQuery(livestreamQueries.livestreamSimpleInfo(id));
  const isChatOpen = useLivestreamStore((state) => state.chat.isOpen);
  const actions = useLivestreamStore((state) => state.actions);

  const { data: userData } = useQuery({
    ...userQueries.byHandle(data?.host.user.twitterHandle ?? ""),
    enabled: !!data?.host.user.twitterHandle,
  });

  return (
    <div className="pt-pwa fixed inset-0 flex flex-col">
      <div className="relative flex w-full flex-shrink-0 flex-col overflow-hidden bg-light-background pb-4 pl-2 pr-4 pt-2">
        <LogoIcon className="pointer-events-none absolute -right-6 top-2 w-[120px] select-none text-light-gray-text opacity-5" />
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            className="bg-none p-2 hover:bg-none"
            onClick={() => {
              if (window.history.length > 1) {
                router.back();
              } else {
                router.push("/home");
              }
            }}
          >
            <ChevronDownFilled className="size-6 text-off-white" />
          </Button>
          <h3 className="inline text-lg font-semibold leading-[22px] text-off-white">
            {data?.livestream.name}
          </h3>
          <div className="flex items-center gap-2">
            {/* {data?.host?.userId === user?.id ? (
              <EndStageConfirmationModal onConfirm={handleLeave}>
                <Button
                  variant="ghost"
                  className="-mr-3 -mt-1 bg-none px-3 py-1 text-base text-danger hover:bg-none"
                >
                  End
                </Button>
              </EndStageConfirmationModal>
            ) : (
              <Button
                variant="ghost"
                className="-mr-3 -mt-1 bg-none px-3 py-1 text-base text-danger hover:bg-none"
                onClick={handleLeave}
              >
                Leave
              </Button>
            )}
            <ToggleChat /> */}
          </div>
        </div>
      </div>
      <VideoPlayer />
      <div className="flex items-center justify-between border-b border-gray-border bg-light-background px-4 py-3">
        <div className="flex items-center gap-2">
          <div className="rounded-md bg-danger px-2 py-1.5 text-sm font-semibold leading-none text-off-white">
            LIVE
          </div>
          <h4 className="text-sm font-semibold text-off-white">
            {data?.host.user.twitterName}
          </h4>
        </div>
        <ViewCounter />
      </div>
      <div className="relative flex h-full w-full flex-grow flex-col">
        {isChatOpen ? (
          <LivestreamChatContainer />
        ) : (
          <div className="flex flex-grow flex-col">
            <TipCards />
            <SupportersContainer />
          </div>
        )}
      </div>
      <div className="pb-pwa flex flex-shrink-0 flex-col border-t border-gray-border">
        <div className="flex justify-between px-6 py-4">
          <div />
          <div className="flex items-center pb-2">
            <button className="p-1.5" onClick={() => actions.toggleChatOpen()}>
              <ChatbubbleOutlineIcon className="size-6 text-off-white" />
            </button>
            <button className="p-1.5" onClick={() => setIsReportOpen(true)}>
              <ExclamationCircleOutlineIcon
                strokeWidth={1.8}
                className="size-6 text-off-white"
              />
            </button>
            <button className="p-1.5" onClick={() => setCustomTipOpen(true)}>
              <CurrencyDollarOutlineIcon
                strokeWidth={1.8}
                className="size-6 text-off-white"
              />
            </button>
            <ErrorBoundary fallback={null}>
              <LivestreamTippingPartyModal>
                <button className="p-1.5">
                  <PartyOutlineIcon
                    strokeWidth={1.8}
                    className="size-6 text-off-white"
                  />
                </button>
              </LivestreamTippingPartyModal>
            </ErrorBoundary>
          </div>
        </div>
      </div>
      {userData && userData.user && (
        <>
          <CustomTipModal
            userToSend={userData.user}
            open={customTipOpen}
            setOpen={setCustomTipOpen}
          />
          <ReportModal
            open={isReportOpen}
            setOpen={setIsReportOpen}
            user={userData.user}
            threadId={data?.livestream.threadId}
          />
        </>
      )}
    </div>
  );
}
