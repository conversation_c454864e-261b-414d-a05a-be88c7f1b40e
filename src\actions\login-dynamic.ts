"use server";

import { cookies } from "next/headers";

import { env } from "@/env";
import { axios } from "@/lib/axios";
import { Me } from "@/types";

interface LoginDynamicProps {
  token: string;
  ref: string | null;
}

export async function loginDynamic({ token, ref }: LoginDynamicProps) {
  try {
    const { data } = await axios.post(
      `${env.NEXT_PUBLIC_API_URL}/auth/dynamic-jwt-exchange`,
      { token, ref },
    );

    if (data.errorCode) {
      throw data;
    }

    await axios.post(`/term-of-use/approve`, undefined, {
      headers: {
        Authorization: `Bearer ${data.token}`,
      },
    });

    // 30 days
    const expires = new Date(Date.now() + 30 * 1000 * 60 * 60 * 24);

    cookies().set("token", data.token, { httpOnly: true, expires });
    cookies().set(
      "user",
      JSON.stringify({
        ...data.user,
        loggedInAt: new Date(),
      }),
      {
        httpOnly: true,
        expires,
      },
    );
    cookies().set("twitterUser", JSON.stringify(data.twitterUser), {
      httpOnly: true,
      expires,
    });

    return { data };
  } catch (error: any) {
    return { error: error.message };
  }
}

interface UpdateCookiesProps {
  user: Me;
}

export async function updateCookies({ user }: UpdateCookiesProps) {
  const originalExpires = user?.loggedInAt
    ? new Date(new Date(user?.loggedInAt).getTime() + 30 * 24 * 60 * 60 * 1000)
    : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

  cookies().set("user", JSON.stringify(user), {
    httpOnly: true,
    expires: originalExpires,
  });
}
