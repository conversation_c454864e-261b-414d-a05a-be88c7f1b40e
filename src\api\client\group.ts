import {
  CreateCommunityFormState,
  EditCommunityFormState,
} from "@/app/(create-community)/create-community/_components/create-community-form-input";
import { axios } from "@/lib/axios";
import {
  CommunityNameCheckResponse,
  CreateCommunityResponse,
} from "@/queries/types";
import {
  CommunityExtended,
  GetFeedEligibleGroups,
  GetTokenHoldersData,
  GroupStatsResponse,
  GroupTokenHoldersResponse,
} from "@/types/community";

export const postCreateCommunity = async (data: CreateCommunityFormState) => {
  const response = await axios.post<CreateCommunityResponse>(
    "/communities/create-community",
    data,
  );
  return response.data;
};

export const updateCommunity = async (data: EditCommunityFormState) => {
  const response = await axios.patch("/communities/update-community", data);
  return response.data;
};

export const getCommunityById = async (params: { communityId: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<{
    community: CommunityExtended;
    userTokenBalance?: string;
  }>(`/communities/get-community-profile?${searchParams.toString()}`);
  return response.data;
};

export const getCommunityByStr = async (params: { param: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<{
    community: CommunityExtended;
    userTokenBalance?: string;
  }>(`/communities/get-community-profile-candidate?${searchParams.toString()}`);
  return response.data;
};

export const getCommunityNameCheck = async (params: {
  name: string;
  communityId?: string;
}) => {
  const response = await axios.get<CommunityNameCheckResponse>(
    "/communities/check-name",
    {
      params: {
        name: params.name,
        communityId: params.communityId,
      },
    },
  );
  return response.data;
};

interface CommunitiesResponse {
  communities: CommunityExtended[];
  numberOfPages: number;
  numberOfResults: number;
  pageSize: number;
}

export const getFeedEligibleCommunities = async ({
  page,
  pageSize,
  searchString,
}: GetFeedEligibleGroups) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
    searchString,
  });

  const response = await axios.get<CommunitiesResponse>(
    `/communities/eligible-for-feed?${searchParams.toString()}`,
  );

  return response.data;
};

export const getCommunityStats = async (contractAddress: string) => {
  const searchParams = new URLSearchParams({
    contractAddress,
  });

  const response = await axios.get<GroupStatsResponse>(
    `/communities/group-stats?${searchParams.toString()}`,
  );
  return response.data;
};

export const getGroupTokenHolders = async ({
  page,
  pageSize,
  contractAddress,
}: GetTokenHoldersData) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
    contractAddress,
  });

  const response = await axios.get<GroupTokenHoldersResponse>(
    `/communities/holders?${searchParams.toString()}`,
  );
  return response.data;
};

interface GroupsSearchData {
  searchString: string;
}

export const getGroupsSearch = async ({ searchString }: GroupsSearchData) => {
  const searchParams = new URLSearchParams({
    searchString,
  });

  const response = await axios.get<CommunitiesResponse>(
    `/communities/search?${searchParams.toString()}`,
  );
  return response.data;
};

export const getTrendingGroups = async () => {
  const response = await axios.get("/communities/trending");
  return response.data;
};

export const banCommunity = async (communityId: string) => {
  const response = await axios.post(`/communities/${communityId}/ban`);
  return response.data;
};

export const getIsUserBannedFromCommunity = async ({
  communityId,
  userId,
}: {
  communityId: string;
  userId: string;
}): Promise<boolean> => {
  const searchParams = new URLSearchParams({
    userId,
  });
  const response = await axios.get<boolean>(
    `/communities/${communityId}/isUserBanned?${searchParams.toString()}`,
  );
  return response.data;
};
