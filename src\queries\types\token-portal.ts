export interface TokenPortalClaimableBalanceResponse {
  claimableArenaUsd: number;
  totalClaimed: number;
  remainingArenaTokens: number;
  remainingArenaUsd: number;
  monthlyProgress: number;
  forfeited: number;
  currentSeason: number;
  endsAt: string;
  seasonAllocation: number;
  seasonMaxAllocation: number;
  memberAllocationAddress: string;
  totalAllocation: string;
  allocation: string;
  forfeitedStr: string;
}

export interface TokenPortalRankResponse {
  userId: number;
  rank: number;
  points: number;
}

export interface TopStakerLeaderboardResponse {
  twitterHandle: string;
  twitterPicture: string;
  twitterName: string;
  ewmaScore: string;
  lastEwmaScore: string;
}

export interface StakersLeaderboardRank {
  rank: number;
  twitterHandle: string;
  twitterPicture: string;
  twitterName: string;
  ewmaScore: string;
  lastEwmaScore: string;
}

export interface StakersLeaderboardRankResponse {
  staker: StakersLeaderboardRank;
}

export interface TopStakersLeaderboardResponse {
  stakers: TopStakerLeaderboardResponse[];
}

export interface TokenPortalGetClaimTxParamsResponse {
  proof: string[];
  allocation: string;
  contractAddress: string;
}

export interface APYResponse {
  apy: number | null;
}

export interface RestakeAgreedResponse {
  isRestakeAgreed: boolean;
}

export interface IRewardCoin {
  image?: string;
  symbol: string;
  name: string;
  amount: string;
  systemRate: string;
}

export interface RewardsResponse {
  upcomingRewards: IRewardCoin[];
  previousRewards: {
    airdrops: IRewardCoin[];
    stakingRewards: IRewardCoin[];
  };
}
