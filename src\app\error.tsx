"use client";

import { startTransition, useEffect } from "react";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const router = useRouter();

  const refreshAndReset = () => {
    startTransition(() => {
      router.refresh();
      reset();
    });
  };

  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center gap-3">
      <h2 className="text-xl font-semibold text-off-white">
        Something went wrong!
      </h2>
      <Button variant="outline" onClick={refreshAndReset}>
        Try again
      </Button>
    </div>
  );
}
