"use client";

import { memo, useEffect, useMemo } from "react";

import {
  useLocalParticipant,
  useParticipantAttribute,
  useParticipants,
} from "@livekit/components-react";
import { useQuery } from "@tanstack/react-query";
import { RoomEvent } from "livekit-client";

import { useToggleMuteMutation } from "@/queries";
import { stageQueries } from "@/queries/stage-queries";
import { useStageStore } from "@/stores/stage";
import { cn } from "@/utils";

import { ArrowBackOutlineIcon, UserAddOutlineIcon } from "../icons";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { CohostUserListItem } from "./cohost-user-list-item";
import { MAX_COHOSTS, MAX_SPEAKERS, Role, ROLES } from "./constants";
import { useIsRoomMuted } from "./hooks/use-is-room-muted";
import { InviteCohostModal } from "./invite-cohost-modal";
import { InviteSpeakerModal } from "./invite-speaker-modal";
import { InvitedCohostUserListItem } from "./invited-cohost-user-list-item";
import { InvitedSpeakerUserListItem } from "./invited-speaker-user-list-item";
import { ListenerUserListItem } from "./listener-user-list-item";
import { RequestedToSpeakUserListItem } from "./requested-to-speak-user-list-item";
import { SpeakerUserListItem } from "./speaker-user-list-item";

const MAX_LISTENERS = 50;

export const GuestsContent = memo(() => {
  const local = useLocalParticipant();
  const role = useParticipantAttribute("role", {
    participant: local.localParticipant,
  }) as Role;
  const actions = useStageStore((state) => state.actions);
  const id = useStageStore((state) => state.id!);
  const { data, isLoading, refetch } = useQuery({
    ...stageQueries.stageInfo(id),
  });
  const participants = useParticipants({
    updateOnlyOn: [
      RoomEvent.ParticipantConnected,
      RoomEvent.ParticipantDisconnected,
      RoomEvent.ParticipantAttributesChanged,
    ],
  });

  const participantsIds = useMemo(() => {
    return participants.map((participant) => participant.identity);
  }, [participants]);

  const [listeners, listenersCount] = useMemo(() => {
    const listeners = participants.filter((participant) => {
      return participant.attributes.role === "LISTENER";
    });
    const slicedListeners = listeners.slice(0, MAX_LISTENERS);

    return [slicedListeners, slicedListeners.length];
  }, [participants]);

  const invitedSpeakersIds = useMemo(() => {
    return data?.invitedSpeakers.map((user) => user.userId) ?? [];
  }, [data?.invitedSpeakers]);

  const invitedCohostsIds = useMemo(() => {
    return data?.invitedCohosts.map((user) => user.userId) ?? [];
  }, [data?.invitedCohosts]);

  const requestedToSpeak = useMemo(() => {
    return data?.requestedToSpeak.filter((user) =>
      participantsIds.includes(user.userId),
    );
  }, [data?.requestedToSpeak, participantsIds]);

  useEffect(() => {
    refetch();
  }, [participants]);

  if (isLoading || !data) return null;

  return (
    <div className="pb-pwa flex flex-col overflow-y-auto">
      <div className="sticky top-0 z-20 bg-dark-bk px-6">
        <div className="flex items-center gap-2 py-4">
          <div className="flex-1">
            <button
              className="flex flex-shrink-0"
              onClick={() => {
                actions.setIsGuestsModalOpen(false);
              }}
            >
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </button>
          </div>
          <h2 className="text-base font-semibold leading-[22px] text-off-white">
            Guests
          </h2>
          <div className="flex-1" />
        </div>
      </div>
      <div className="flex flex-col px-6 py-5">
        <h4 className="text-base font-semibold text-off-white">Host</h4>
        <div className="flex w-full justify-between gap-4 py-4">
          <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
            <Avatar className="size-[42px]">
              <AvatarImage src={data.host?.user?.twitterPicture} />
              <AvatarFallback />
            </Avatar>
            <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
              <div className="flex gap-1.5">
                <span className="truncate text-off-white">
                  {data.host?.user?.twitterName}
                </span>
              </div>
              <span className="truncate text-gray-text">
                @{data.host?.user?.twitterHandle}
              </span>
            </div>
            {role === ROLES.HOST && (
              <div className="ml-auto">
                <MuteAllButton />
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="flex flex-col border-t border-off-white/10 px-6 py-5">
        <h4 className="text-base font-semibold text-off-white">Co-hosts</h4>
        <p className="mt-1 text-sm text-gray-text">
          {data.cohosts.length === 0 && data.invitedCohosts.length === 0
            ? `${MAX_COHOSTS} Open Spots`
            : `${data.cohosts.length + data.invitedCohosts.length} Co-Hosts - ${MAX_COHOSTS - data.cohosts.length - data.invitedCohosts.length} Open Spots`}
        </p>
        {(data.cohosts && data.cohosts.length > 0) ||
        (data.invitedCohosts && data.invitedCohosts.length > 0) ? (
          <div className="my-4 flex flex-col gap-4 ">
            {data.cohosts && data.cohosts.length > 0 ? (
              <>
                {data.cohosts?.map((user) => (
                  <CohostUserListItem key={user.id} user={user} />
                ))}
              </>
            ) : null}
            {data.invitedCohosts && data.invitedCohosts.length > 0 ? (
              <>
                {data.invitedCohosts?.map((user) => (
                  <InvitedCohostUserListItem key={user.id} user={user} />
                ))}
              </>
            ) : null}
          </div>
        ) : null}
        {data.cohosts.length + data.invitedCohosts.length < MAX_COHOSTS &&
          role === ROLES.HOST && (
            <InviteCohostModal>
              <button
                className={cn(
                  "-mx-3 flex items-center gap-2 rounded-lg px-3 py-2 outline-none hover:bg-gray-text/10",
                  {
                    "mt-2":
                      data.cohosts &&
                      data.cohosts.length === 0 &&
                      data.invitedCohosts &&
                      data.invitedCohosts.length === 0,
                  },
                )}
              >
                <UserAddOutlineIcon className="size-5 text-brand-orange" />
                <span className="text-sm font-medium leading-5 text-off-white">
                  Invite a Co-Hosts
                </span>
              </button>
            </InviteCohostModal>
          )}
      </div>
      <div className="flex flex-col border-t border-off-white/10 px-6 py-5">
        <h4 className="text-base font-semibold text-off-white">Speakers</h4>
        <p className="mt-1 text-sm text-gray-text">
          {data.speakers.length === 0 && data.invitedSpeakers.length === 0
            ? `${MAX_SPEAKERS} Open Spots`
            : `${data.speakers.length + data.invitedSpeakers.length} Speakers - ${MAX_SPEAKERS - data.speakers.length - data.invitedSpeakers.length} Open Spots`}
        </p>
        {(data.speakers && data.speakers.length > 0) ||
        (data.invitedSpeakers && data.invitedSpeakers.length > 0) ? (
          <div className="my-4 flex flex-col gap-4 ">
            {data.speakers && data.speakers.length > 0 ? (
              <>
                {data.speakers?.map((user) => (
                  <SpeakerUserListItem key={user.id} user={user} />
                ))}
              </>
            ) : null}
            {data.invitedSpeakers && data.invitedSpeakers.length > 0 ? (
              <>
                {data.invitedSpeakers?.map((user) => (
                  <InvitedSpeakerUserListItem key={user.id} user={user} />
                ))}
              </>
            ) : null}
          </div>
        ) : null}
        {data.speakers.length + data.invitedSpeakers.length < MAX_SPEAKERS && (
          <InviteSpeakerModal>
            <button
              className={cn(
                "-mx-3 flex items-center gap-2 rounded-lg px-3 py-2 outline-none hover:bg-gray-text/10",
                {
                  "mt-2":
                    data.speakers &&
                    data.speakers.length === 0 &&
                    data.invitedSpeakers &&
                    data.invitedSpeakers.length === 0,
                },
              )}
            >
              <UserAddOutlineIcon className="size-5 text-brand-orange" />
              <span className="text-sm font-medium leading-5 text-off-white">
                Invite a Speaker
              </span>
            </button>
          </InviteSpeakerModal>
        )}
      </div>
      <div className="flex flex-col border-t border-off-white/10 px-6 py-5">
        <h4 className="text-base font-semibold text-off-white">
          Requests to speak
        </h4>
        {requestedToSpeak && requestedToSpeak.length > 0 ? (
          <div className="mt-4 flex flex-col gap-4">
            {requestedToSpeak?.map((user) => (
              <RequestedToSpeakUserListItem key={user.id} user={user} />
            ))}
          </div>
        ) : (
          <p className="mt-1 text-sm text-gray-text">None</p>
        )}
      </div>
      <div className="flex flex-col border-t border-off-white/10 px-6 py-5">
        <h4 className="text-base font-semibold text-off-white">Listeners</h4>
        <p className="mt-1 text-sm text-gray-text">
          {listenersCount} people are listening
        </p>
        {listeners.length > 0 ? (
          <div className="mt-4 flex flex-col gap-4">
            {listeners?.map((participant) => (
              <ListenerUserListItem
                key={participant.identity + "-listener"}
                participant={participant}
                invited={
                  invitedCohostsIds.includes(participant.identity)
                    ? "COHOST"
                    : invitedSpeakersIds.includes(participant.identity)
                      ? "SPEAKER"
                      : undefined
                }
              />
            ))}
          </div>
        ) : null}
      </div>
    </div>
  );
});

GuestsContent.displayName = "GuestsContent";

const MuteAllButton = memo(() => {
  const id = useStageStore((state) => state.id!);
  const isRoomMuted = useIsRoomMuted();

  const { mutate: toggleMute, isPending } = useToggleMuteMutation();

  return (
    <Button
      variant="outline"
      className={cn(
        "h-[34px] w-32",
        isRoomMuted ? "w-36 border-brand-orange" : "w-28",
      )}
      onClick={() => {
        toggleMute({ stageId: id });
      }}
      disabled={isPending}
    >
      {isRoomMuted ? "Allow unmuting" : "Mute all"}
    </Button>
  );
});

MuteAllButton.displayName = "MuteAllButton";
