"use client";

import { useEffect, useRef, useState } from "react";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { parseISO } from "date-fns";
import { motion, useMotionValue } from "framer-motion";
import { Virtuoso } from "react-virtuoso";
import { Address, formatEther as formatEtherViem, Hex } from "viem";

import { HANDLE_PHASE } from "@/app/(main)/community/_components/consts";
import { useHideTokensWithZeroBalance } from "@/app/(main)/wallet/hooks/use-hide-tokens-with-zero-balance";
import { GroupIcon, OfficialGroupIcon } from "@/components/icons-v2/group-logo";
import { ProgressBarLink } from "@/components/progress-bar";
import { ShareHolderItemLoadingSkeleton } from "@/components/share-holder-item-loading-skeleton";
import { ShareHoldingItem } from "@/components/share-holding-item";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  stakingContractABI,
  stakingContractAddress,
} from "@/environments/stakingABI";
import { ARENA } from "@/environments/tokens";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import { useSharesHoldingsInfiniteQuery } from "@/queries";
import {
  useAvaxPriceQuery,
  useSupportedCurrenciesQuery,
} from "@/queries/currency-queries";
import { useTradesQuery } from "@/queries/trade-queries";
import { useUser } from "@/stores";
import { useTutorialStore } from "@/stores/tutorial";
import { cn, formatTimeDistance, numberFormatter } from "@/utils";
import { formatMarketCap } from "@/utils/format-market-cap";
import { formatPrice } from "@/utils/format-token-price";

export const WalletTabs = () => {
  const tabRef = useRef<HTMLDivElement>(null);
  const backgroundColor = useMotionValue("rgb(2 2 2 / 0.9)");

  const { primaryWallet } = useDynamicContext();

  const {
    data: holdingsData,
    isLoading: isHoldingsLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useSharesHoldingsInfiniteQuery();
  const [hideZeroBalance] = useHideTokensWithZeroBalance();
  const { data: avaxPriceData, isLoading: isAvaxPriceLoading } =
    useAvaxPriceQuery();
  const { data: tradesData } = useTradesQuery();

  const { user } = useUser();
  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useSupportedCurrenciesQuery();
  const [stake, setStake] = useState<number>();

  useEffect(() => {
    const fetchData = async () => {
      if (!primaryWallet) return;
      if (!isEthereumWallet(primaryWallet)) {
        throw new Error("This wallet is not an Ethereum wallet");
      }

      const publicClient = await primaryWallet.getPublicClient();

      const [stakeRes] = await publicClient.readContract({
        address: stakingContractAddress as Address,
        abi: stakingContractABI,
        functionName: "getUserInfo",
        args: [primaryWallet.address as Hex, ARENA.address as Address],
      });
      setStake(Number(formatEtherViem(stakeRes)));
    };

    void fetchData();
  }, [primaryWallet]);

  const { sortedCurrencies, isBalancesLoading } = useWalletCurrencies({
    user,
    currenciesData,
    isCurrenciesLoading,
    stake,
    hideZeroBalance,
  });

  const isTutorialOpen = useTutorialStore((state) => state.isTutorialOpen);

  useEffect(() => {
    const handleScroll = () => {
      if (tabRef.current && tabRef.current.getBoundingClientRect().top <= 0) {
        backgroundColor.set("rgb(15 15 15 / 0.9)");
      } else {
        backgroundColor.set("rgb(2 2 2 / 0.9)");
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [backgroundColor]);

  return (
    <>
      {isTutorialOpen && (
        <div className="absolute inset-0 z-10 bg-dark-bk/65" />
      )}
      <Tabs
        defaultValue="your-currencies"
        onValueChange={() => {
          scrollTo({
            top: 0,
          });
        }}
        ref={tabRef}
        className="flex-grow"
      >
        <motion.div
          className={cn(
            "sticky top-0 z-10 mt-[calc(-0px-env(safe-area-inset-top))] w-full pt-[calc(13px+env(safe-area-inset-top))] shadow-[0px_-5px_14px_20px_rgba(0,0,0,0.25)] backdrop-blur-[9px]",
          )}
          style={{ backgroundColor }}
        >
          <TabsList className="flex w-full justify-evenly">
            <TabsTrigger value="your-currencies">Tokens</TabsTrigger>
            <TabsTrigger value="ticket-holders">Tickets</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
          </TabsList>
        </motion.div>
        <TabsContent
          value="your-currencies"
          className="h-[calc(100%-53px)] py-[8px]"
        >
          {(isCurrenciesLoading || isBalancesLoading) &&
            Array.from({ length: 10 }).map((_, i) => (
              <ShareHolderItemLoadingSkeleton key={i} />
            ))}
          {currenciesData &&
            !isCurrenciesLoading &&
            !isAvaxPriceLoading &&
            sortedCurrencies.map((currency) => (
              <div key={currency.contractAddress}>
                {(
                  hideZeroBalance
                    ? Number(
                        currency.isToken
                          ? formatPrice(currency.balance)
                          : currency.balance,
                      ) &&
                      Number(
                        currency.isToken
                          ? formatPrice(currency.balance)
                          : currency.balance,
                      ) > 0
                    : true
                ) ? (
                  currency.isToken ? (
                    <TokenItem
                      name={currency?.name}
                      symbol={currency?.symbol}
                      systemRate={currency?.systemRate || "0"}
                      balance={currency?.balance || "0"}
                      photoURL={currency?.photoURL || ""}
                      contractAddress={currency?.contractAddress || ""}
                      tokenPhase={currency?.tokenPhase || 0}
                      avaxPrice={avaxPriceData?.avax || 0}
                      tokenName={currency.tokenName || ""}
                    />
                  ) : currency.official ? (
                    <ProgressBarLink
                      href={`/community/${`${currency.tokenName}`}`}
                    >
                      <CurrencyItem
                        symbol={currency?.symbol}
                        systemRate={currency?.systemRate || "0"}
                        balance={currency?.balance || "0"}
                        name={currency?.name}
                        isStakedArena={currency?.isStakedArena}
                        photoURL={currency?.photoURL}
                        official={currency?.official}
                      />
                    </ProgressBarLink>
                  ) : (
                    <CurrencyItem
                      symbol={currency?.symbol}
                      systemRate={currency?.systemRate || "0"}
                      balance={currency?.balance || "0"}
                      name={currency?.name}
                      isStakedArena={currency?.isStakedArena}
                      photoURL={currency?.photoURL}
                      official={currency?.official}
                    />
                  )
                ) : null}
              </div>
            ))}
        </TabsContent>
        <TabsContent
          value="ticket-holders"
          className="h-[calc(100%-53px)] py-[8px]"
        >
          {holdingsData?.pages[0] &&
            !isHoldingsLoading &&
            holdingsData.pages[0].holdings.length === 0 && (
              <div className="flex items-center justify-center">
                <div className="mt-20 max-w-64 text-center">
                  <h4 className="text-sm font-semibold text-[#EDEDED]">
                    {`You don't hold any ticket yet!`}
                  </h4>
                </div>
              </div>
            )}
          <Virtuoso
            useWindowScroll
            data={
              holdingsData?.pages.flatMap((page) => page?.holdings || []) || []
            }
            endReached={() => {
              if (hasNextPage) {
                fetchNextPage();
              }
            }}
            overscan={200}
            itemContent={(index, holding) => {
              return <ShareHoldingItem holder={holding} />;
            }}
            components={{
              Footer: () => {
                if (isHoldingsLoading || isFetchingNextPage) {
                  return (
                    <>
                      {Array.from({ length: 5 }).map((_, i) => (
                        <ShareHolderItemLoadingSkeleton key={i} />
                      ))}
                    </>
                  );
                }
                return null;
              },
            }}
          />
        </TabsContent>
        <TabsContent value="activity" className="h-[calc(100%-53px)] py-[8px]">
          {tradesData && tradesData.trades.length === 0 && (
            <div className="flex items-center justify-center">
              <div className="mt-20 max-w-64 text-center">
                <h4 className="text-sm font-semibold text-[#EDEDED]">
                  No activity to show!
                </h4>
              </div>
            </div>
          )}
          {tradesData &&
            tradesData.trades.map((trade) => (
              <TradeItem
                key={trade.id}
                traderHandle={trade.traderTwitterHandle}
                traderPicture={trade.traderTwitterPicture}
                traderName={trade.traderTwitterName}
                subjectHandle={trade.subjectTwitterHandle}
                subjectPicture={trade.subjectTwitterPicture}
                subjectName={trade.subjectTwitterName}
                createdOn={trade.createdOn}
                shareAmount={trade.shareAmount}
                amount={trade.amount}
                isBuy={trade.isBuy}
                isToken={trade.isToken}
              />
            ))}
        </TabsContent>
      </Tabs>
    </>
  );
};

interface CurrencyItemProps {
  symbol: string;
  systemRate: string;
  balance: string;
  name: string;
  isStakedArena?: boolean;
  photoURL?: string;
  official?: boolean;
}

const CurrencyItem = ({
  symbol,
  systemRate,
  balance,
  name,
  isStakedArena,
  photoURL,
  official,
}: CurrencyItemProps) => {
  const usdBalanceEquivalent = Number(
    (Number(balance) * Number(systemRate)).toFixed(2),
  );

  return (
    <div className="flex w-full flex-col px-6 py-4">
      <div className="flex items-center gap-[10px]">
        <img
          src={photoURL}
          className={cn(
            "size-[42px] rounded-full",
            isStakedArena && "grayscale",
          )}
          alt={`${symbol} logo`}
        />
        <div className="flex w-full flex-col">
          <div className="flex w-full items-center justify-between text-sm leading-4">
            <div className="flex items-center gap-2">
              {official ? (
                <div className="flex items-center gap-1">
                  <OfficialGroupIcon />
                  <h4 className="text-[#F4F4F4]">{symbol}</h4>
                </div>
              ) : (
                <h4 className="text-[#F4F4F4]">{symbol}</h4>
              )}
              {isStakedArena && (
                <div className="items-center justify-start rounded-[4px] bg-gradient-to-r from-[#d64c05] to-[#ff5526] px-1">
                  <div className="text-xs font-semibold text-white">Staked</div>
                </div>
              )}
            </div>
            <h4 className="text-[#F4F4F4]">
              {numberFormatter.format(Number(balance))}
            </h4>
          </div>
          <div className="mt-1 flex w-full items-center justify-between text-sm leading-4">
            <p className="text-[#808080]">{name}</p>
            <p className="text-[#808080]">
              {Number(balance) === 0
                ? "$0"
                : usdBalanceEquivalent < 0.01
                  ? "< $0.01"
                  : `$${numberFormatter.format(usdBalanceEquivalent)}`}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

interface TokenItemProps {
  symbol: string;
  name: string;
  systemRate: string;
  balance: string;
  photoURL: string;
  contractAddress: string;
  tokenPhase: number;
  avaxPrice: number;
  tokenName: string;
}

const TokenItem = ({
  symbol,
  name,
  systemRate,
  balance,
  photoURL,
  contractAddress,
  tokenPhase,
  avaxPrice,
  tokenName,
}: TokenItemProps) => {
  const systemRateOfToken = formatPrice(systemRate);
  const tokenBalance = formatPrice(balance);
  const usdBalanceEquivalent = Number(
    (tokenBalance * systemRateOfToken * (avaxPrice || 0)).toFixed(2),
  );

  return (
    <ProgressBarLink
      href={`/community/${tokenPhase >= HANDLE_PHASE ? name : contractAddress}`}
      className="flex w-full flex-col px-6 py-4"
    >
      <div className="flex items-center gap-[10px]">
        <Avatar className="size-[42px]">
          <AvatarImage src={photoURL} />
          <AvatarFallback />
        </Avatar>
        <div className="flex w-full flex-col">
          <div className="flex w-full items-center justify-between text-sm leading-4">
            <div className="flex items-center gap-1">
              <GroupIcon />
              <h4 className="text-[#F4F4F4]">${symbol}</h4>
            </div>
            <h4 className="text-[#F4F4F4]">{formatMarketCap(tokenBalance)}</h4>
          </div>
          <div className="mt-1 flex w-full items-center justify-between text-sm leading-4">
            <p className="text-[#808080]">{tokenName}</p>
            <p className="text-[#808080]">
              {Number(balance) === 0
                ? "$0"
                : usdBalanceEquivalent < 0.01
                  ? "< $0.01"
                  : `$${numberFormatter.format(usdBalanceEquivalent)}`}
            </p>
          </div>
        </div>
      </div>
    </ProgressBarLink>
  );
};

interface TradeItemProps {
  traderHandle: string;
  traderPicture: string;
  traderName: string;
  subjectHandle: string;
  subjectPicture: string;
  subjectName: string;
  createdOn: string;
  shareAmount: string;
  amount: number;
  isBuy: boolean;
  isToken: boolean;
}

const TradeItem = ({
  traderHandle,
  traderPicture,
  traderName,
  subjectHandle,
  subjectPicture,
  subjectName,
  createdOn,
  shareAmount,
  amount,
  isBuy,
  isToken,
}: TradeItemProps) => {
  const createdOnDate = parseISO(createdOn);

  const formattedAmount = formatPrice(amount.toString());

  return (
    <div className="flex w-full flex-row items-center justify-between px-6 py-4">
      <div className="mr-[12px] flex">
        <div className="flex -space-x-2">
          <Avatar className="z-[1] size-[29px]" asChild>
            <ProgressBarLink href={`/${traderHandle}`}>
              <AvatarImage src={traderPicture} />
              <AvatarFallback />
            </ProgressBarLink>
          </Avatar>
          <Avatar className="size-[29px]" asChild>
            <ProgressBarLink
              href={
                isToken ? `/community/${subjectHandle}` : `/${subjectHandle}`
              }
            >
              <AvatarImage src={subjectPicture} />
              <AvatarFallback />
            </ProgressBarLink>
          </Avatar>
        </div>
      </div>
      <div className="w-full flex-1 items-center text-sm text-white">
        <ProgressBarLink href={`/${traderHandle}`} className="font-semibold">
          {traderName}
        </ProgressBarLink>{" "}
        {isBuy ? "bought" : "sold"}{" "}
        {isToken ? formatMarketCap(formatPrice(shareAmount)) : shareAmount}{" "}
        {!isToken && "tickets of"}{" "}
        <ProgressBarLink
          href={isToken ? `/community/${subjectHandle}` : `/${subjectHandle}`}
          className="font-semibold"
        >
          {isToken ? `$${subjectName}` : subjectName}
        </ProgressBarLink>
        {isToken &&
          ` for ${numberFormatter.format(Number(formattedAmount.toFixed(formattedAmount < 1 ? 4 : 2)))} AVAX`}
      </div>
      <div className={cn("flex-shrink-0 text-xs leading-5 text-gray-text")}>
        {formatTimeDistance(createdOnDate)}
      </div>
    </div>
  );
};
