import { axios } from "@/lib/axios";
import {
  PostReactionsLikesResponse,
  PostReactionsQuotesResponse,
  PostReactionsRepostsResponse,
} from "@/queries/types/postreactions";

export const getPostReactionsLikes = async ({
  threadId,
  likePage,
  likePerPage,
}: {
  threadId: string;
  likePage: number;
  likePerPage: number;
}) => {
  const searchParams = new URLSearchParams({
    threadId,
    likePage: likePage.toString(),
    likePerPage: likePerPage.toString(),
  });

  const response = await axios.get<PostReactionsLikesResponse>(
    `/post-reactions/likes?${searchParams.toString()}`,
  );
  return response.data;
};

export const getPostReactionsReposts = async ({
  threadId,
  repostPage,
  repostPerPage,
}: {
  threadId: string;
  repostPage: number;
  repostPerPage: number;
}) => {
  const searchParams = new URLSearchParams({
    threadId,
    repostPage: repostPage.toString(),
    repostPerPage: repostPerPage.toString(),
  });

  const response = await axios.get<PostReactionsRepostsResponse>(
    `/post-reactions/reposts?${searchParams.toString()}`,
  );
  return response.data;
};

export const getPostReactionsQuotes = async ({
  threadId,
  quotePage,
  quotePerPage,
}: {
  threadId: string;
  quotePage: number;
  quotePerPage: number;
}) => {
  const searchParams = new URLSearchParams({
    threadId,
    quotePage: quotePage.toString(),
    quotePerPage: quotePerPage.toString(),
  });

  const response = await axios.get<PostReactionsQuotesResponse>(
    `/post-reactions/quotes?${searchParams.toString()}`,
  );
  return response.data;
};
