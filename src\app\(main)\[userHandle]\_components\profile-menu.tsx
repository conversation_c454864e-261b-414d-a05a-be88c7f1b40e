"use client";

import { useState } from "react";

import CopyToClipboard from "react-copy-to-clipboard";

import { BanUserModal } from "@/app/(main)/[userHandle]/_components/ban-user-modal";
import { UnbanUserModal } from "@/app/(main)/[userHandle]/_components/unban-user-modal";
import {
  BanOutlineIcon,
  EllipsisHorizontalFilledIcon,
  FlagOutlineIcon,
  LinkOutlineIcon,
  UnbanOutlineIcon,
} from "@/components/icons";
import { HideRepostsIcon } from "@/components/icons/hide-reposts-icon";
import { ShowRepostsIcon } from "@/components/icons/show-reposts-icon";
import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useIsBlockedByUserQuery, useIsUserBlockedQuery } from "@/queries";
import { useUser } from "@/stores";
import { User, UserFlaggedEnum } from "@/types";

import { BlockUserModal } from "./block-user-modal";
import { ReportModal } from "./report-modal";
import { UnblockUserModal } from "./unblock-user-modal";

export const ProfileMenu = ({
  user,
  isMe,
  showReposts,
  setRepostsVisibility,
}: {
  user?: User;
  isMe: boolean;
  showReposts: boolean;
  setRepostsVisibility: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const { user: me } = useUser();
  const [open, setOpen] = useState(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const { data: isUserBlocked } = useIsUserBlockedQuery(user?.id || "");
  const { data: isBlockedByUser } = useIsBlockedByUserQuery(user?.id || "");
  const [isReportOpen, setIsReportOpen] = useState(false);
  const [isBlockOpen, setIsBlockOpen] = useState(false);
  const [isUnblockOpen, setIsUnblockOpen] = useState(false);
  const [isBanOpen, setIsBanOpen] = useState(false);
  const [isUnbanOpen, setIsUnbanOpen] = useState(false);
  const isSuspended = user?.flag === UserFlaggedEnum.SUSPENDED;

  if (!user) return null;

  return (
    <>
      {isTablet && (
        <DropdownMenu open={open} onOpenChange={setOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="ml-1 flex size-[26px] flex-shrink-0 items-center justify-center border-none p-0 outline-none"
              disabled={
                ((isBlockedByUser || isSuspended) && !me?.isMod) || false
              }
            >
              <EllipsisHorizontalFilledIcon className="size-5 fill-off-white" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[220px]">
            {isMe && (
              <DropdownMenuItem className="w-full gap-4" asChild>
                <ProgressBarLink href={`/${user.twitterHandle}/fee-settings`}>
                  <LinkOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Creator Fee Settings</span>
                </ProgressBarLink>
              </DropdownMenuItem>
            )}
            {!isUserBlocked &&
              !isSuspended &&
              (showReposts ? (
                <DropdownMenuItem
                  className="gap-4"
                  onSelect={() => {
                    setRepostsVisibility(false);
                  }}
                >
                  <HideRepostsIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Hide Reposts</span>
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem
                  className="gap-4"
                  onSelect={() => {
                    setRepostsVisibility(true);
                  }}
                >
                  <ShowRepostsIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Show Reposts</span>
                </DropdownMenuItem>
              ))}
            {!isSuspended &&
              (isMe ? (
                <DropdownMenuItem className="w-full gap-4" asChild>
                  <CopyToClipboard
                    text={
                      typeof window !== "undefined" ? window.location.href : ""
                    }
                    onCopy={() => {
                      toast.green("Copied the profile link to the clipboard");
                      setOpen(false);
                    }}
                  >
                    <button className="gap-4">
                      <LinkOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Share profile</span>
                    </button>
                  </CopyToClipboard>
                </DropdownMenuItem>
              ) : (
                <>
                  <DropdownMenuItem
                    className="gap-4"
                    onSelect={() => {
                      setIsReportOpen(true);
                    }}
                  >
                    <FlagOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Report</span>
                  </DropdownMenuItem>
                  {isUserBlocked ? (
                    <DropdownMenuItem
                      className="gap-4"
                      onSelect={() => {
                        setIsUnblockOpen(true);
                      }}
                    >
                      <UnbanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Unblock</span>
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem
                      className="gap-4"
                      onSelect={() => {
                        setIsBlockOpen(true);
                      }}
                    >
                      <BanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Block</span>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem className="w-full gap-4" asChild>
                    <CopyToClipboard
                      text={
                        typeof window !== "undefined"
                          ? window.location.href
                          : ""
                      }
                      onCopy={() => {
                        toast.green("Copied the profile link to the clipboard");
                        setOpen(false);
                      }}
                    >
                      <button>
                        <LinkOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                        <span>Copy link to profile</span>
                      </button>
                    </CopyToClipboard>
                  </DropdownMenuItem>
                </>
              ))}
            {me?.isMod && (
              <>
                {isSuspended ? (
                  <DropdownMenuItem
                    className="gap-4"
                    onClick={() => {
                      setOpen(false);
                      setIsUnbanOpen(true);
                    }}
                  >
                    <UnbanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Unban</span>
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem
                    className="gap-4"
                    onClick={() => {
                      setOpen(false);
                      setIsBanOpen(true);
                    }}
                  >
                    <BanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Ban</span>
                  </DropdownMenuItem>
                )}
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      {!isTablet && (
        <Drawer open={open} onOpenChange={setOpen}>
          <DrawerTrigger
            disabled={isBlockedByUser || (isSuspended && !me?.isMod) || false}
          >
            <EllipsisHorizontalFilledIcon className="size-5 fill-off-white" />
          </DrawerTrigger>
          <DrawerContent className="px-4 pt-4">
            {isMe && (
              <ProgressBarLink
                href={`/${user.twitterHandle}/fee-settings`}
                className="flex items-center gap-2 p-2 text-base leading-5"
              >
                <LinkOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Creator Fee Settings</span>
              </ProgressBarLink>
            )}
            {!isUserBlocked &&
              !isSuspended &&
              (showReposts ? (
                <button
                  className="flex items-center gap-2 p-2 text-base leading-5"
                  onClick={() => {
                    setOpen(false);
                    setRepostsVisibility(false);
                  }}
                >
                  <HideRepostsIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Hide Reposts</span>
                </button>
              ) : (
                <button
                  className="flex items-center gap-2 p-2 text-base leading-5"
                  onClick={() => {
                    setOpen(false);
                    setRepostsVisibility(true);
                  }}
                >
                  <ShowRepostsIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Show Reposts</span>
                </button>
              ))}
            {!isSuspended &&
              (isMe ? (
                <CopyToClipboard
                  text={
                    typeof window !== "undefined" ? window.location.href : ""
                  }
                  onCopy={() => {
                    toast.green("Copied the profile link to the clipboard");
                    setOpen(false);
                  }}
                >
                  <button className="flex items-center gap-2 p-2 text-base leading-5">
                    <LinkOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Share profile</span>
                  </button>
                </CopyToClipboard>
              ) : (
                <>
                  <button
                    className="flex items-center gap-2 p-2 text-base leading-5"
                    onClick={() => {
                      setOpen(false);
                      setIsReportOpen(true);
                    }}
                  >
                    <FlagOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Report</span>
                  </button>
                  {isUserBlocked ? (
                    <button
                      className="flex items-center gap-2 p-2 text-base leading-5"
                      onClick={() => {
                        setOpen(false);
                        setIsUnblockOpen(true);
                      }}
                    >
                      <UnbanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Unblock</span>
                    </button>
                  ) : (
                    <button
                      className="flex items-center gap-2 p-2 text-base leading-5"
                      onClick={() => {
                        setOpen(false);
                        setIsBlockOpen(true);
                      }}
                    >
                      <BanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Block</span>
                    </button>
                  )}
                  <CopyToClipboard
                    text={
                      typeof window !== "undefined" ? window.location.href : ""
                    }
                    onCopy={() => {
                      toast.green("Copied the profile link to the clipboard");
                      setOpen(false);
                    }}
                  >
                    <button className="flex items-center gap-2 p-2 text-base leading-5">
                      <LinkOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Copy link to profile</span>
                    </button>
                  </CopyToClipboard>
                </>
              ))}
            {me?.isMod && (
              <>
                {isSuspended ? (
                  <button
                    className="flex items-center gap-2 p-2 text-base leading-5"
                    onClick={() => {
                      setOpen(false);
                      setIsUnbanOpen(true);
                    }}
                  >
                    <UnbanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Unban</span>
                  </button>
                ) : (
                  <button
                    className="flex items-center gap-2 p-2 text-base leading-5"
                    onClick={() => {
                      setOpen(false);
                      setIsBanOpen(true);
                    }}
                  >
                    <BanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Ban</span>
                  </button>
                )}
              </>
            )}
          </DrawerContent>
        </Drawer>
      )}
      <BlockUserModal open={isBlockOpen} setOpen={setIsBlockOpen} user={user} />
      <UnblockUserModal
        open={isUnblockOpen}
        setOpen={setIsUnblockOpen}
        user={user}
      />
      <ReportModal open={isReportOpen} setOpen={setIsReportOpen} user={user} />
      <BanUserModal open={isBanOpen} setOpen={setIsBanOpen} user={user} />
      <UnbanUserModal open={isUnbanOpen} setOpen={setIsUnbanOpen} user={user} />
    </>
  );
};
