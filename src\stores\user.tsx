"use client";

import { createContext, useContext, useEffect, useMemo, useState } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

import {
  getAuthToken,
  useDynamicContext,
  useIsLoggedIn,
} from "@dynamic-labs/sdk-react-core";

import { loginDynamic, updateCookies } from "@/actions/login-dynamic";
import { logout } from "@/actions/logout";
import { useUserByIdQuery } from "@/queries";
import { DEFAULT_LOGIN_REDIRECT, DEFAULT_SUSPENDED_REDIRECT } from "@/routes";
import { Me, TwitterUser, UserFlaggedEnum } from "@/types";

const UserContext = createContext<{
  user: Me | null;
  twitterUser: TwitterUser | null;
  token: string | null;
  updateUser: (updatedUser: Partial<Me>) => void;
}>({
  user: null,
  twitterUser: null,
  token: null,
  updateUser: () => {},
});

export function useUser() {
  return useContext(UserContext);
}

interface UserProviderProps {
  user: Me | null;
  twitterUser: TwitterUser | null;
  token: string | null;
  children: React.ReactNode;
}

const handleLoginDynamic = async (
  token: string,
  router: any,
  ref: string | null,
  callbackUrl: string | null,
) => {
  try {
    const result = await loginDynamic({ token, ref });
    if (result.data) {
      if (result.data.user.flag === UserFlaggedEnum.SUSPENDED) {
        window.location.href = DEFAULT_LOGIN_REDIRECT;
      } else {
        if (callbackUrl) {
          window.location.href = callbackUrl;
        } else {
          window.location.href = DEFAULT_LOGIN_REDIRECT;
        }
      }
    } else if (result.error) {
      console.error("error", result.error);
      router.push("/?error=Something went wrong!");
    }
  } catch (error) {
    console.error("error", error);
    router.push("/?error=Something went wrong!");
  }
};

export function UserProvider({
  children,
  user,
  twitterUser,
  token,
}: UserProviderProps) {
  const [currentUser, setCurrentUser] = useState<Me | null>(user);
  const { handleLogOut } = useDynamicContext();
  const isDynamicLoggedIn = useIsLoggedIn();
  const router = useRouter();
  const pathname = usePathname();
  const { data } = useUserByIdQuery(user?.id || "");

  useEffect(() => {
    const dynamicAuthExpiresAtStr = localStorage.getItem(
      "dynamic_auth_expires_at",
    );

    if (dynamicAuthExpiresAtStr) {
      const dynamicAuthExpiresAt = Number(dynamicAuthExpiresAtStr) * 1000;
      const isDynamicAuthExpired = Date.now() >= dynamicAuthExpiresAt;

      if (isDynamicAuthExpired) {
        handleLogOut();
      }
    }
  }, [handleLogOut]);

  const searchParams = useSearchParams();
  const ref = searchParams.get("ref");
  const callbackUrl = searchParams.get("callbackUrl");

  const value = useMemo(
    () => ({
      user,
      twitterUser,
      token,
    }),
    [user, twitterUser, token],
  );

  useEffect(() => {
    const checkSuspension = async () => {
      // Only check backend if the flag isn't already suspended
      if (value.user?.id && value.user?.flag !== UserFlaggedEnum.SUSPENDED) {
        if (
          data?.user.flag === UserFlaggedEnum.SUSPENDED &&
          pathname !== DEFAULT_SUSPENDED_REDIRECT
        ) {
          router.push(DEFAULT_SUSPENDED_REDIRECT);
          await logout();
        }
      }
    };

    checkSuspension();
  }, [value, handleLogOut, router]);

  useEffect(() => {
    const checkTokenAndLoginDynamic = async () => {
      if (!token && isDynamicLoggedIn) {
        const authToken = getAuthToken();
        if (authToken) {
          await handleLoginDynamic(authToken, router, ref, callbackUrl);
        }
      }
    };
    checkTokenAndLoginDynamic();
  }, [isDynamicLoggedIn, ref, router, token]);

  const updateUser = async (updatedUser: Partial<Me>) => {
    const newUser = { ...currentUser!, ...updatedUser };
    setCurrentUser(newUser);

    if (updatedUser) {
      await updateCookies({ user: newUser });
    }
  };

  return (
    <UserContext.Provider
      value={{ user: currentUser, twitterUser, token, updateUser }}
    >
      {children}
    </UserContext.Provider>
  );
}
