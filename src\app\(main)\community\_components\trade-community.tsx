"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, ReactNode } from "react";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { EthereumWallet } from "@dynamic-labs/ethereum-core";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import {
  Address,
  Chain,
  formatEther,
  parseEther,
  PublicClient,
  Transport,
} from "viem";

import { fetchTokenBalance } from "@/api/balance";
import { getAvaxPrice } from "@/api/client/currency";
import { getCommunityByStr } from "@/api/client/group";
import { ERC20_CONTRACT_ABI } from "@/environments/BACKEND_FRIENDS_CONTRACT";
import { MinTokenData } from "@/environments/tokens";
import { useLaunchToken } from "@/hooks/use-launch-token";
import { useTradeERC20 } from "@/hooks/use-trade-erc20";
import { useUser } from "@/stores";
import { CommunityExtended } from "@/types/community";

import { EntryRequirement } from "../../../_components/trade-erc20/entry-requirment";
import {
  GetAvaxAmountReturnType,
  GetTokenAmountReturnType,
  TradeERC20,
  TradeERC20Context,
} from "../../../_components/trade-erc20/trade-erc20";
import { TradeERC20TnCs } from "../../../_components/trade-erc20/trade-erc20-modals";

export type CommunityTradeContext = {
  community: CommunityExtended;
  userBalance: bigint;
  userTokenBalance: bigint;
  totalSupply: bigint;
  avaxPrice: number;
  conversionRate: bigint;
};

interface TradeCommunityProps {
  param: string;
}

export const TradeCommunity: FC<TradeCommunityProps> = ({ param }) => {
  const { user } = useUser();
  const { primaryWallet } = useDynamicContext();

  const {
    swapAvaxToToken: swapAvaxToTokenLP,
    getTokenAmount: getTokenAmountLP,
    swapTokenToAvax: swapTokenToAvaxLP,
    getAvaxAmount: getAvaxAmountLP,
    getConversionRate: getConversionRateLP,
  } = useTradeERC20();

  const {
    buy,
    sell,
    calculatePurchaseAmountAndPrice,
    calculateRewardWithFees,
  } = useLaunchToken();

  const getConversionRate = async (
    primaryWallet: EthereumWallet,
    publicClient: PublicClient<Transport, Chain>,
    bcGroupId: string,
  ): Promise<bigint> => {
    const { tokens } = await calculatePurchaseAmountAndPrice(
      primaryWallet,
      publicClient,
      BigInt(bcGroupId),
      parseEther("1"),
    );
    return tokens;
  };

  const requestCtxData = async (setCtx: Dispatch<TradeERC20Context>) => {
    if (!user) {
      throw new Error("User not specified");
    }

    if (!primaryWallet) {
      throw new Error("Dynamic wallet is not initialized");
    }

    if (!isEthereumWallet(primaryWallet)) {
      throw new Error("This wallet is not a Ethereum wallet");
    }

    const communityQuery = await getCommunityByStr({
      param,
    });

    if (!communityQuery?.community) {
      throw new Error("Community not specified");
    }

    const token: MinTokenData = {
      decimals: 18,
      address: communityQuery.community.contractAddress,
      icon: communityQuery.community.photoURL,
      symbol: communityQuery.community.ticker,
    };

    const isLP = communityQuery.community.isLP;

    const publicClient = await primaryWallet.getPublicClient();
    const walletClient = await primaryWallet.getWalletClient();

    const bundle = await Promise.allSettled([
      fetchTokenBalance(primaryWallet.address, { symbol: "AVAX" }),
      publicClient.readContract({
        address: token.address as Address,
        abi: ERC20_CONTRACT_ABI,
        functionName: "balanceOf",
        args: [primaryWallet.address as Address],
      }),
      getAvaxPrice(),
      isLP
        ? getConversionRateLP(token)
        : getConversionRate(
            primaryWallet,
            publicClient,
            communityQuery.community.bcGroupId,
          ),
    ]);

    const tokenPrice = Number(communityQuery.community.stats?.price || "0");

    setCtx({
      dynamicWallet: primaryWallet,
      publicClient,
      walletClient,
      token,
      tokenPrice,
      userBalance:
        bundle[0].status === "fulfilled" ? bundle[0].value.balance : 0n,
      userTokenBalance:
        bundle[1].status === "fulfilled"
          ? bundle[1].value > 4n
            ? bundle[1].value
            : 0n
          : 0n,
      avaxPrice: bundle[2].status === "fulfilled" ? bundle[2].value.avax : 0,
      conversionRate: bundle[3].status === "fulfilled" ? bundle[3].value : 0n,
      community: communityQuery.community,
    });
  };

  const swapAvaxToToken = async (
    ctx: TradeERC20Context,
    avaxAmount: string,
    commission: bigint | null,
    slippage: number,
    tokenAmount?: string,
  ) => {
    if (!user) {
      throw new Error("User not specified");
    }

    if (!ctx.community || !ctx.community.bcGroupId) {
      throw new Error("Community is not defined");
    }

    if (!tokenAmount) {
      throw new Error("Token amount is not defined");
    }

    if (ctx.community.isLP)
      await swapAvaxToTokenLP(ctx, avaxAmount, commission, slippage);
    else
      await buy(
        ctx.dynamicWallet,
        ctx.publicClient,
        ctx.walletClient,
        user,
        BigInt(ctx.community.bcGroupId),
        parseEther(tokenAmount),
        parseEther(avaxAmount),
      );
  };

  const getTokenAmountLaunch = async (
    ctx: TradeERC20Context,
    community: CommunityExtended,
    amount: string,
  ): Promise<GetTokenAmountReturnType> => {
    if (amount === "0") {
      return { tokenAmount: "0" };
    }

    const { tokens, price } = await calculatePurchaseAmountAndPrice(
      ctx.dynamicWallet,
      ctx.publicClient,
      BigInt(community.bcGroupId),
      parseEther(amount),
    );

    const tokenAmounts = {
      tokenAmount: formatEther(tokens),
      tokenDisplayAmount: formatEther(tokens),
    };

    if (Math.abs(Number(formatEther(price)) - Number(amount)) > 0.001) {
      const avaxAmount = formatEther(price);
      const avaxDisplayAmount = parseFloat(formatEther(price))
        .toFixed(4)
        .replace(/(\.\d*?[1-9])0+$|\.0+$/g, "$1");
      return {
        ...tokenAmounts,
        avaxAmount,
        avaxDisplayAmount,
      };
    } else return tokenAmounts;
  };

  const getTokenAmount = async (
    ctx: TradeERC20Context,
    amount: string,
    calculateCommission: (amount: string) => bigint,
  ): Promise<GetTokenAmountReturnType> => {
    if (!ctx.community || !ctx.community.bcGroupId) {
      throw new Error("Community is not defined");
    }

    if (ctx.community.isLP)
      return await getTokenAmountLP(ctx, amount, calculateCommission);
    else return await getTokenAmountLaunch(ctx, ctx.community, amount);
  };

  const swapTokenToAvax = async (
    ctx: TradeERC20Context,
    tokenAmount: string,
    commission: bigint | null,
    slippage: number,
  ) => {
    if (!user) {
      throw new Error("User not specified");
    }

    if (!ctx.community || !ctx.community.bcGroupId) {
      throw new Error("Community is not defined");
    }

    if (ctx.community.isLP)
      await swapTokenToAvaxLP(ctx, tokenAmount, commission, slippage);
    else
      await sell(
        ctx.dynamicWallet,
        ctx.publicClient,
        ctx.walletClient,
        user,
        BigInt(ctx.community.bcGroupId),
        parseEther(tokenAmount),
      );
  };

  const getAvaxAmountLaunch = async (
    ctx: TradeERC20Context,
    community: CommunityExtended,
    amount: bigint,
  ): Promise<GetAvaxAmountReturnType> => {
    if (amount <= 0n) return { avaxAmount: "0" };

    const formatedAmount = amount / 10n ** 18n;

    const price = await calculateRewardWithFees(
      ctx.dynamicWallet,
      ctx.publicClient,
      BigInt(community.bcGroupId),
      BigInt(formatedAmount),
    );

    return { avaxAmount: formatEther(price) };
  };

  const getAvaxAmount = async (
    ctx: TradeERC20Context,
    amount: bigint,
    calculateCommission: (destAmount: string) => bigint,
  ): Promise<GetAvaxAmountReturnType> => {
    if (!ctx.community || !ctx.community.bcGroupId) {
      throw new Error("Community is not defined");
    }

    if (ctx.community.isLP)
      return await getAvaxAmountLP(ctx, amount, calculateCommission);
    else return await getAvaxAmountLaunch(ctx, ctx.community, amount);
  };

  const EntryRequirementProp = (ctx: TradeERC20Context): ReactNode => {
    const ticker = ctx.community?.ticker
      ? ctx.community.ticker
      : ctx.community?.name || ctx.token.symbol;
    return <EntryRequirement ticker={ticker} />;
  };

  return (
    <TradeERC20
      requestCtxData={requestCtxData}
      swapAvaxToToken={swapAvaxToToken}
      getTokenAmount={getTokenAmount}
      swapTokenToAvax={swapTokenToAvax}
      getAvaxAmount={getAvaxAmount}
      entryRequirement={EntryRequirementProp}
      terms={() => <TradeERC20TnCs />}
    />
  );
};
