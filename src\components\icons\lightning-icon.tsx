import { ComponentProps, SVGProps } from "react";

export const LightningIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 20 20"
    fill="none"
    {...props}
  >
    <path
      d="M8.80954 11.418H9.6236L9.06188 18L9.7197 16.85L14.2401 8.93594L14.4425 8.58204H11.1363L11.698 2L11.0402 3.15001L6.51972 11.0641L6.31738 11.418H8.80954Z"
      fill="currentColor"
    />
  </svg>
);

export const LightningWithShadowIcon = (props: ComponentProps<"svg">) => (
  <svg
    width="30"
    height="40"
    viewBox="0 0 30 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g filter="url(#filter0_d_12766_19758)">
      <path
        d="M13.2158 21.611H14.1407L13.5025 29.0892L14.2499 27.7826L19.3858 18.791L19.6157 18.389H15.8593L16.4975 10.9108L15.7501 12.2173L10.6142 21.2089L10.3843 21.611H13.2158Z"
        fill="#DC4D0A"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_12766_19758"
        x="0.297425"
        y="0.823853"
        width="29.4051"
        height="38.3522"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset />
        <feGaussianBlur stdDeviation="5.04346" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 1 0 0 0 0 0.328889 0 0 0 0 0 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_12766_19758"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_12766_19758"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);

export const LightningWithShadowIconOff = (props: ComponentProps<"svg">) => (
  <svg
    width="27"
    height="34"
    viewBox="0 0 27 34"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g filter="url(#filter0_d_12766_20290)">
      <mask id="path-1-inside-1_12766_20290" fill="white">
        <path d="M12.242 18.418H13.056L12.4943 25L13.1521 23.85L17.6726 15.9359L17.8749 15.582H14.5687L15.1304 9L14.4726 10.15L9.95216 18.0641L9.74982 18.418H12.242Z" />
      </mask>
      <path
        d="M12.242 18.418H13.056L12.4943 25L13.1521 23.85L17.6726 15.9359L17.8749 15.582H14.5687L15.1304 9L14.4726 10.15L9.95216 18.0641L9.74982 18.418H12.242Z"
        stroke="#B5B5B5"
        strokeWidth="2"
        shapeRendering="crispEdges"
        mask="url(#path-1-inside-1_12766_20290)"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_12766_20290"
        x="0.871669"
        y="0.121852"
        width="25.8814"
        height="33.7563"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset />
        <feGaussianBlur stdDeviation="4.43907" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.854902 0 0 0 0 0.854902 0 0 0 0 0.854902 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_12766_20290"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_12766_20290"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);

export const LightningIconOrange = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 84 85"
    fill="none"
    {...props}
  >
    <g filter="url(#filter0_d_14158_17643)">
      <path
        d="M37.3381 47.5994H39.8572L38.119 67.9674L40.1546 64.4087L54.143 39.9189L54.7691 38.8238H44.5381L46.2763 18.4558L44.2407 22.0145L30.2524 46.5043L29.6262 47.5994H37.3381Z"
        fill="#DC4D0A"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_14158_17643"
        x="0.909515"
        y="-10.2609"
        width="82.5763"
        height="106.945"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset />
        <feGaussianBlur stdDeviation="14.3584" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 1 0 0 0 0 0.328889 0 0 0 0 0 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_14158_17643"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_14158_17643"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
