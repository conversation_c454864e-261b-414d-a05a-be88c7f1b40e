import {
  queryOptions,
  UndefinedInitialDataOptions,
  useInfiniteQuery,
  useQuery,
} from "@tanstack/react-query";

import { getRequestUsersSearch } from "@/api/client/chat";
import { getCommunityById, getCommunityByStr } from "@/api/client/group";
import {
  getIsBlockedByUser,
  getIsUserAcceptedAppStore,
  getIsUserBanned,
  getIsUserBlocked,
  getMe,
  getNewCommunities,
  getNewUsers,
  getNotifiedClick,
  getOfficialTopCommunities,
  getReferralStats,
  getReferrers,
  getTopCommunities,
  getTopUsers,
  getUprisingAnyUser,
  getUprisingCurrentUser,
  getUprisingQuestCards,
  getUprisingTopBadges,
  getUprisingTopUsers,
  getUserByHandle,
  getUserById,
  getUserPreferences,
  getUsersSearch,
  getUsersWithBadgesByType,
} from "@/api/client/user";
import { UprisingUserDetailed } from "@/queries/types/uprising-user";
import {
  UprisingQuestCardsResponse,
  UprisingTopBadgesResponse,
} from "@/queries/types/uprising-user-leaderboard-response";

import {
  NewCommunitiesResponse,
  NewUsersResponse,
  TopCommunitiesResponse,
} from "./types";
import { TopUsersResponse } from "./types/top-users-response";

export const userQueries = {
  byHandleKey: (handle: string) => ["user", "handle", handle],
  byHandle: (handle: string) =>
    queryOptions({
      queryKey: userQueries.byHandleKey(handle),
      queryFn: () => getUserByHandle({ handle }),
    }),
  byIdKey: (userId: string) => ["user", "id", userId],
  byId: (userId: string) =>
    queryOptions({
      queryKey: userQueries.byIdKey(userId),
      queryFn: () => getUserById({ userId }),
    }),
};

export const useUserByHandleQuery = (handle: string) => {
  return useQuery({
    queryKey: ["user", "handle", handle],
    queryFn: () => {
      return getUserByHandle({ handle });
    },
  });
};

export const useCommunityByIdQuery = (id: string) => {
  return useQuery({
    queryKey: ["community", "id", id],
    queryFn: () => {
      return getCommunityById({ communityId: id });
    },
    enabled: !!id,
  });
};

export const useCommunityByStrQuery = (param: string) => {
  return useQuery({
    queryKey: ["community", "param", param],
    queryFn: () => {
      if (param === "") return;
      return getCommunityByStr({ param });
    },
    enabled: typeof param === "string",
  });
};

export const useUserByIdQuery = (userId: string) => {
  return useQuery({
    queryKey: ["user", "id", userId],
    queryFn: () => {
      return getUserById({ userId });
    },
  });
};

export const useUsersSearchQuery = (searchString: string) => {
  return useQuery({
    queryKey: ["user", "search", searchString],
    queryFn: () => {
      if (!searchString) return null;

      return getUsersSearch({ searchString });
    },
    enabled: !!searchString,
  });
};

type TopUsersQueryOptions = Omit<
  UndefinedInitialDataOptions<TopUsersResponse>,
  "queryKey"
>;

export const useTopUsersQuery = (options?: TopUsersQueryOptions) => {
  return useQuery<TopUsersResponse>({
    queryKey: ["user", "top"],
    queryFn: getTopUsers,
    // cache for 5 minutes to prevent unnecessary requests
    staleTime: 1000 * 60 * 5,
    ...options,
  });
};

export const useBadgeLeaderboardQuery = () => {
  return useQuery<UprisingTopBadgesResponse>({
    queryKey: ["home", "uprising-badge-leaderboard"],
    queryFn: getUprisingTopBadges,
  });
};

export const useUprisingCurrentUserQuery = () => {
  return useQuery<UprisingUserDetailed>({
    queryKey: ["home", "uprising-current-user"],
    queryFn: getUprisingCurrentUser,
  });
};

export const useUprisingQuestCardsQuery = () => {
  return useQuery<UprisingQuestCardsResponse>({
    queryKey: ["home", "uprising-quest-cards"],
    queryFn: getUprisingQuestCards,
  });
};

export const useUprisingAnyUserQuery = (userUuid: string) => {
  return useQuery<UprisingUserDetailed>({
    queryKey: ["home", "any-uprising-user", userUuid],
    queryFn: () => {
      return getUprisingAnyUser(userUuid);
    },
  });
};

export const useUprisingUserLeaderboardInfiniteQuery = () => {
  return useInfiniteQuery({
    queryKey: ["home", "uprising-user-leaderboard"],
    queryFn: ({ pageParam }) => {
      return getUprisingTopUsers(pageParam);
    },
    initialPageParam: {
      page: 1,
      pageSize: 20,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const total = lastPage.count;
      const currentLength = pages.reduce((prev, curr) => {
        return prev + curr.results.length;
      }, 0);

      if (
        total !== currentLength &&
        (total > currentLength + lastPageParam.pageSize ||
          (total < currentLength + lastPageParam.pageSize &&
            total - currentLength < lastPageParam.pageSize))
      ) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
    refetchOnMount: "always",
  });
};

type NewUsersQueryOptions = Omit<
  UndefinedInitialDataOptions<NewUsersResponse>,
  "queryKey"
>;

export const useNewUsersQuery = (options?: NewUsersQueryOptions) => {
  return useQuery<NewUsersResponse>({
    queryKey: ["user", "page"],
    queryFn: getNewUsers,
    // cache for 5 minutes to prevent unnecessary requests
    staleTime: 1000 * 60 * 5,
    ...options,
  });
};

export const useUsersWithBadgesByTypeQuery = (type: number) => {
  return useInfiniteQuery({
    queryKey: ["user", "badges", type.toString()],
    queryFn: ({ pageParam }) => getUsersWithBadgesByType(type, pageParam),
    initialPageParam: {
      page: 1,
      pageSize: 25,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (lastPage.numberOfPages > lastPageParam.page) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
  });
};

type TopCommunitiesQueryOptions = Omit<
  UndefinedInitialDataOptions<TopCommunitiesResponse>,
  "queryKey"
>;

export const useTopCommunitiesQuery = (
  options?: TopCommunitiesQueryOptions,
) => {
  return useQuery<TopCommunitiesResponse>({
    queryKey: ["communities", "top"],
    queryFn: getTopCommunities,
    // cache for 5 minutes to prevent unnecessary requests
    staleTime: 1000 * 60 * 5,
    ...options,
  });
};

export const useOfficialTopCommunitiesQuery = (
  options?: TopCommunitiesQueryOptions,
) => {
  return useQuery<TopCommunitiesResponse>({
    queryKey: ["communities", "top-official"],
    queryFn: getOfficialTopCommunities,
    // cache for 5 minutes to prevent unnecessary requests
    staleTime: 1000 * 60 * 5,
    ...options,
  });
};

type NewCommunitiesQueryOptions = Omit<
  UndefinedInitialDataOptions<NewCommunitiesResponse>,
  "queryKey"
>;

export const useNewCommunitiesQuery = (
  options?: NewCommunitiesQueryOptions,
) => {
  return useQuery<NewCommunitiesResponse>({
    queryKey: ["communities", "page"],
    queryFn: getNewCommunities,
    // cache for 5 minutes to prevent unnecessary requests
    staleTime: 1000 * 60 * 5,
    ...options,
  });
};

export const useIsUserBlockedQuery = (userId?: string) => {
  return useQuery({
    queryKey: ["user", "isBlocked", userId],
    queryFn: () => {
      if (!userId) return false;
      return getIsUserBlocked({ userId });
    },
    enabled: !!userId,
  });
};

export const useIsBlockedByUserQuery = (userId?: string) => {
  return useQuery({
    queryKey: ["user", "isBlockedByUser", userId],
    queryFn: () => {
      if (!userId) return false;
      return getIsBlockedByUser({ userId });
    },
    enabled: Boolean(userId),
  });
};

export const useIsUserBannedQuery = (userId: string) => {
  return useQuery({
    queryKey: ["user", "isBanned", userId],
    queryFn: () => {
      if (!userId) return false;
      return getIsUserBanned({ userId });
    },
    enabled: !!userId,
  });
};

export const useIsUserAcceptedAppStore = (userId: string) => {
  return useQuery({
    queryKey: ["user", "isAcceptedAppStore", userId],
    queryFn: () => {
      if (!userId) return false;
      return getIsUserAcceptedAppStore({ userId });
    },
    enabled: !!userId,
  });
};

export const useReferrersQuery = () => {
  return useQuery({
    queryKey: ["user", "referrers"],
    queryFn: getReferrers,
  });
};

export const useMeQuery = () => {
  return useQuery({
    queryKey: ["user", "me"],
    queryFn: getMe,
  });
};

export const useNotifiedQuery = () => {
  return useQuery({
    queryKey: ["user", "Notified"],
    queryFn: getNotifiedClick,
  });
};

export const useReferralStatsQuery = () => {
  return useQuery({
    queryKey: ["user", "stats", "referral"],
    queryFn: getReferralStats,
  });
};

export const useRequestUsersSearchQuery = (searchString: string) => {
  return useQuery<TopUsersResponse | null>({
    queryKey: ["request", "search", searchString],
    queryFn: () => {
      if (!searchString) return null;

      return getRequestUsersSearch({ searchString });
    },
    enabled: !!searchString,
  });
};

export const useUserPreferences = () => {
  return useQuery({
    queryKey: ["user", "preferences"],
    queryFn: getUserPreferences,
  });
};
