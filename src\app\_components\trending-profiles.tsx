import React, { useEffect, useRef, useState } from "react";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { TrendingUserCard } from "@/components/trending-user-card";
import { UserCardLoadingSkeleton } from "@/components/user-card-loading-skeleton";

interface TrendingProfilesProps {
  users: any[];
  isLoading: boolean;
}

export const TrendingProfiles: React.FC<TrendingProfilesProps> = ({
  users,
  isLoading,
}) => {
  const trendingRef = useRef<HTMLDivElement>(null);
  const scrollAmount = 200;
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  const scrollLeft = () => {
    if (trendingRef.current) {
      trendingRef.current.scrollBy({ left: -scrollAmount, behavior: "smooth" });
    }
  };

  const scrollRight = () => {
    if (trendingRef.current) {
      trendingRef.current.scrollBy({ left: scrollAmount, behavior: "smooth" });
    }
  };

  const checkScrollButtons = () => {
    if (trendingRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = trendingRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth);
    }
  };

  useEffect(() => {
    const element = trendingRef.current;
    const handleResize = () => checkScrollButtons();

    element?.addEventListener("scroll", checkScrollButtons, { passive: true });
    window.addEventListener("resize", handleResize);

    checkScrollButtons();

    return () => {
      element?.removeEventListener("scroll", checkScrollButtons);
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <div className="relative z-20 rounded-[20px] border-dark-gray p-0 py-4 lg:mt-4 lg:border">
      <div className="px-6">
        <h2 className="text-base font-semibold leading-5 text-off-white">
          Trending
        </h2>
        <p className="mt-1 text-sm text-gray-text">
          Most popular users in the Arena right now
        </p>
      </div>
      <div className="flex items-center">
        {canScrollLeft && (
          <button
            onClick={scrollLeft}
            className="absolute left-2 z-30 hidden h-8 w-8 rounded-full border border-brand-orange bg-brand-orange p-1 lg:block"
          >
            <ArrowBackOutlineIcon className="text-white" />
          </button>
        )}
        <div
          ref={trendingRef}
          className="hide-scrollbar relative mt-4 flex flex-grow items-start justify-start gap-4 overflow-x-auto px-4"
        >
          {isLoading
            ? Array.from({ length: 5 }).map((_, index) => (
                <UserCardLoadingSkeleton key={index} />
              ))
            : users.map((user) => (
                <TrendingUserCard key={user.id} user={user} />
              ))}
        </div>
        {canScrollRight && (
          <button
            onClick={scrollRight}
            className="absolute right-2 z-30 hidden h-8 w-8 rotate-180 rounded-full border border-brand-orange bg-brand-orange p-1 lg:block"
          >
            <ArrowBackOutlineIcon className="text-white" />
          </button>
        )}
      </div>
    </div>
  );
};
