"use client";

import { useState } from "react";

import { CopyToClipboard } from "react-copy-to-clipboard";
import QRCode from "react-qr-code";

import { ArrowBackOutlineIcon, CopyOutlineIcon } from "@/components/icons";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import { Label } from "@/components/ui/label";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useUser } from "@/stores";

export const DepositModal = () => {
  const { user } = useUser();
  const [open, setOpen] = useState(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const address = user?.address ?? "";

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="h-[44px] w-full">
            Deposit
          </Button>
        </DialogTrigger>
        <DialogContent className="gap-6">
          <DepositModalContent address={address} />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button variant="outline" className="h-[44px] w-full">
          Deposit
        </Button>
      </DrawerTrigger>
      <DrawerContent className="gap-4">
        <DepositModalContent address={address} />
      </DrawerContent>
    </Drawer>
  );
};

export const DepositModalContent = ({ address }: { address: string }) => {
  return (
    <>
      <div className="flex items-center gap-3">
        <DialogClose className="hidden p-1 sm:inline-block">
          <ArrowBackOutlineIcon className="size-5 text-off-white" />
        </DialogClose>
        <h3 className="text-base font-semibold leading-[22px] text-off-white">
          Deposit
        </h3>
      </div>

      <p className="text-sm text-[#B5B5B5]">
        Transfer AVAX to the address below to fund your wallet for ticket buys
        and paying gas fees.
      </p>
      <div className="mx-auto size-[219px] rounded-[20px] bg-white p-4">
        <QRCode
          size={256}
          value={address}
          viewBox={`0 0 256 256`}
          className="h-full w-full"
        />
      </div>
      <div className="mt-2 flex flex-col gap-2">
        <Label>Wallet address</Label>
        <div className="flex items-center justify-between gap-4 rounded-lg bg-[#313131] px-4 py-3 text-sm text-[#656565]">
          <span className="flex-grow truncate">{address}</span>
          <CopyToClipboard
            text={address}
            onCopy={() => {
              toast.green("Copied to clipboard");
            }}
          >
            <button className="flex-shrink-0">
              <CopyOutlineIcon className="size-5 text-[#9E9E9E]" />
            </button>
          </CopyToClipboard>
        </div>
      </div>
    </>
  );
};
