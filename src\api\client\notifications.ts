import { axios } from "@/lib/axios";
import {
  NotificationsResponse,
  UnseenNotificationsResponse,
} from "@/queries/types/notifications";

interface GetNotificationsParams {
  page: number;
  pageSize: number;
}

export const getNotificaitons = async ({
  page,
  pageSize,
}: GetNotificationsParams) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<NotificationsResponse>(
    `/notifications?${searchParams.toString()}`,
  );
  return response.data;
};

export const getUnseenNotifications = async () => {
  const response = await axios.get<UnseenNotificationsResponse>(
    "/notifications/unseen",
  );
  return response.data;
};

export const setAllNotificationsSeen = async () => {
  const response = await axios.get("/notifications/seen/all");
  return response.data;
};

export const setNotificationSeen = async (notificationId: string) => {
  const searchParams = new URLSearchParams({
    notificationId,
  });

  const response = await axios.get(
    `/notifications/seen?${searchParams.toString()}`,
  );
  return response.data;
};
