import { WarningOutlineIcon } from "@/components/icons/warning-outline";
import { ProgressBarLink } from "@/components/progress-bar";
import { But<PERSON> } from "@/components/ui/button";

function SuspendedUsersPage() {
  return (
    <div className="mx-auto flex min-h-screen max-w-screen-md select-none items-center justify-center px-6 py-14 text-light-gray-text">
      <div className="flex-col items-center justify-center gap-4 text-center">
        <div className="text-gray-400 flex w-full flex-col items-center justify-center rounded-[10px] bg-[#0e0e0e] p-4 text-center text-base">
          <WarningOutlineIcon color="#EB540A" />
          <span className="mt-2 font-bold text-white">
            Your account has been suspended!
          </span>
          <div className="mt-4 font-medium text-[#808080]">
            Your account has been suspended for violating The Arena&apos;s{" "}
            <ProgressBarLink
              href="/terms-of-use"
              className="font-semibold text-white"
            >
              terms of use
            </ProgressBarLink>
            . Please contact us to appeal the decision.
          </div>
        </div>
        <Button variant="outline" className="mt-8 w-full">
          <a
            href={"https://discord.gg/a5Fw3TFP5n"}
            target="_blank"
            rel="noreferrer"
          >
            Contact Us
          </a>
        </Button>
      </div>
    </div>
  );
}

export default SuspendedUsersPage;
