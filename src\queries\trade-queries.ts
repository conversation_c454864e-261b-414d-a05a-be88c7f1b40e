import { UndefinedInitialDataOptions, useQuery } from "@tanstack/react-query";

import {
  getRecentTrades,
  getTrades,
  getTrendingUsers,
  searchTicketTipping,
} from "@/api/client/trade";
import { getCommunityActivities } from "@/api/client/user";
import { CommunityTradesRecentResponse } from "@/types/community";

import {
  TradeRecentResponse,
  TradesUsersTrendingResponse,
  TradeTradesResponse,
} from "./types";

export const useTradesQuery = () => {
  return useQuery<TradeTradesResponse>({
    queryKey: ["trade", "trades"],
    queryFn: getTrades,
  });
};

export const useSearchTicketTippingQuery = (search: string) => {
  return useQuery({
    queryKey: ["trade", "ticket-search", search],
    queryFn: () => searchTicketTipping(search),
  });
};

type RecentTradesQueryOptions = Omit<
  UndefinedInitialDataOptions<TradeRecentResponse>,
  "queryKey"
>;

export const useRecentTradesQuery = (options?: RecentTradesQueryOptions) => {
  return useQuery<TradeRecentResponse>({
    queryKey: ["trade", "recent"],
    queryFn: getRecentTrades,
    // cache for 1 minute to prevent unnecessary requests
    staleTime: 1000 * 60,
    ...options,
  });
};

type RecentCommunityTradesQueryOptions = Omit<
  UndefinedInitialDataOptions<CommunityTradesRecentResponse>,
  "queryKey"
>;

export const useRecentCommunityTradesQuery = (
  options?: RecentCommunityTradesQueryOptions,
) => {
  return useQuery<CommunityTradesRecentResponse>({
    queryKey: ["trade", "recent", "communities"],
    queryFn: getCommunityActivities,
    // cache for 1 minute to prevent unnecessary requests
    staleTime: 1000 * 60,
    ...options,
  });
};

type TrendingUsersQueryOptions = Omit<
  UndefinedInitialDataOptions<TradesUsersTrendingResponse>,
  "queryKey"
>;

export const useTrendingUsersQuery = (options?: TrendingUsersQueryOptions) => {
  return useQuery<TradesUsersTrendingResponse>({
    queryKey: ["trade", "users", "trending"],
    queryFn: getTrendingUsers,
    // cache for 1 minute to prevent unnecessary requests
    staleTime: 1000 * 60,
    ...options,
  });
};
