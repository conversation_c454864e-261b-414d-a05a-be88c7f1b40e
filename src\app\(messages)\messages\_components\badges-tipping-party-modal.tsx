import { useEffect, useMemo, useState } from "react";

import { postTippingPartyNotify } from "@/api/client/chat";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { TippingPartySettingsIcon } from "@/components/icons/tipping-party-settings";
import { TippingInfoModal } from "@/components/tipping-info";
import { TipFormContent } from "@/components/tipping/tip-form-content";
import { ArenaDialogHeader } from "@/components/ui/arena-dialog-header";
import { Button } from "@/components/ui/button";
import { Dialog, DialogClose, DialogContent } from "@/components/ui/dialog";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import { useGroupMembersQuery } from "@/queries/chat-queries";
import { useTippableCurrenciesQuery } from "@/queries/currency-queries";
import { useUser } from "@/stores";
import { cn } from "@/utils/cn";

import {
  BadgesChatTipReceiver,
  EditBadgesTipReceiversModal,
} from "./edit-badges-tip-receivers-modal";

interface BadgeChatsTippingPartyModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  groupId: string;
}

export const BadgeChatsTippingPartyModal = ({
  open,
  setOpen,
  groupId,
}: BadgeChatsTippingPartyModalProps) => {
  const [isEditTipReceiversOpen, setIsEditTipReceiversOpen] = useState(false);
  const [tipReceivers, setTipReceivers] = useState<BadgesChatTipReceiver[]>([]);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const { user } = useUser();
  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useTippableCurrenciesQuery();
  const { sortedCurrencies } = useWalletCurrencies({
    user,
    currenciesData,
    isCurrenciesLoading,
  });
  const {
    data: badgeHoldersData,
    isLoading: isLoadingBadgeHolders,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useGroupMembersQuery(groupId);
  const holders = useMemo(() => {
    if (!badgeHoldersData) return [];
    return badgeHoldersData.pages.flatMap((page) => page.members);
  }, [badgeHoldersData]);
  useEffect(() => {
    if (!isFetchingNextPage && hasNextPage) {
      fetchNextPage();
    }
  }, [isFetchingNextPage, hasNextPage, badgeHoldersData]);
  useEffect(() => {
    if (!holders) return;
    setTipReceivers(
      holders
        .filter((holder) => holder.id !== user?.id)
        .map((holder) => {
          return {
            isChecked: true,
            user: holder,
            amount: "1",
          };
        }),
    );
  }, [holders, user?.id]);
  useEffect(() => {
    const handleResize = () => {
      const viewportHeight =
        window.visualViewport?.height || window.innerHeight;
      const windowHeight = window.innerHeight;
      if (viewportHeight < windowHeight) {
        setIsKeyboardVisible(true);
      } else {
        setIsKeyboardVisible(false);
      }
    };
    if (typeof visualViewport != "undefined") {
      window.visualViewport?.addEventListener("resize", handleResize);
    }
    return () => {
      if (typeof visualViewport != "undefined") {
        window.visualViewport?.removeEventListener("resize", handleResize);
      }
    };
  }, []);
  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent
          className={cn(
            "flex h-full w-full flex-grow flex-col bg-dark-bk  px-6 pt-0 backdrop-blur-sm sm:h-fit sm:w-[420px] sm:gap-0 sm:bg-[#1A1A1A] sm:p-6",
            isKeyboardVisible
              ? "flex-grow-0 justify-end pb-[calc(142px+env(safe-area-inset-bottom))]"
              : "flex-grow justify-between",
          )}
        >
          <ArenaDialogHeader
            title="Tipping Party"
            showBack={true}
            onBack={() => setOpen(false)}
            rightButton={
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setIsEditTipReceiversOpen(true);
                }}
                disabled={isLoadingBadgeHolders && !badgeHoldersData}
              >
                <TippingPartySettingsIcon className="size-4 text-off-white" />
              </Button>
            }
          />
          <div className="mb-2 mt-4 px-6 text-center">
            <h4 className="text-sm font-semibold text-off-white">
              Everyone in this chatroom will receive the same tip.
            </h4>
            <p className="text-sm leading-[18px] text-gray-text">
              (You will be excluded)
            </p>
          </div>
          <TipFormContent
            recepients={tipReceivers
              .filter((r) => r.isChecked)
              .map((r) => r.user)}
            setOpen={setOpen}
            sortedCurrencies={sortedCurrencies}
            distributionMode="equal"
            buttonLabel="Send tips"
            // customRecipientsSelector={

            // }
            onPartyNotify={async ({ currency, txHash, txData, recipient }) => {
              await postTippingPartyNotify({
                currency,
                txHash,
                txData,
              });
            }}
          />
          <TippingInfoModal />
        </DialogContent>
      </Dialog>
      <EditBadgesTipReceiversModal
        open={isEditTipReceiversOpen}
        setOpen={setIsEditTipReceiversOpen}
        tipReceivers={tipReceivers}
        setTipReceivers={setTipReceivers}
        showTips={true}
      />
    </>
  );
};
