import Skeleton from "react-loading-skeleton";

import { Button } from "./ui/button";

export const UserCardLoadingSkeleton = () => {
  return (
    <div className="relative isolate flex w-[150px] flex-shrink-0 flex-col items-center justify-start gap-[10px] rounded-xl bg-[#2B2B2B] p-4">
      <div className="absolute inset-0 -z-10 overflow-hidden rounded-xl">
        <div className="absolute inset-0 bg-[#141414]/80 backdrop-blur-sm" />
      </div>
      <Skeleton circle className="size-12" />
      <div className="flex w-full flex-col items-center gap-[6px] overflow-hidden leading-none">
        <Skeleton className="h-[14px] w-[100px]" />
        <Skeleton className="h-3 w-[80px]" />
        <Skeleton className="h-3 w-[110px]" />
      </div>
      <Button
        variant="outline"
        className="w-full min-w-[118px] border-gray-text py-2"
        disabled
      >
        Follow
      </Button>
    </div>
  );
};
