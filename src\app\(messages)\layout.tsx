import { SideNav } from "../(main)/_components/side-nav";
import { MessagesBottomNav } from "./messages/_components/messages-bottom-nav";
import { SettingsProvider } from "./messages/context/settings-context";

interface MessagesLayoutProps {
  children: React.ReactNode;
}

function MessagesLayout({ children }: MessagesLayoutProps) {
  return (
    <div className="mx-auto flex min-h-[100dvh] max-w-[1350px] flex-col flex-nowrap items-stretch justify-center sm:flex-row">
      <header className="relative z-[3] hidden sm:flex sm:flex-shrink-0 sm:flex-grow sm:flex-col sm:items-end">
        <SideNav />
      </header>
      <SettingsProvider>{children}</SettingsProvider>
      <MessagesBottomNav />
    </div>
  );
}

export default MessagesLayout;
