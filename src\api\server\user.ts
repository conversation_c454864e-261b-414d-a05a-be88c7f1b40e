import { getAxios } from "@/lib/axios-server";
import { ReferralStatsResponse } from "@/queries/types/user";
import { User } from "@/types";

export const getUserByHandle = async (params: { handle: string }) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams(params);

  const response = await axios.get<{ user: User }>(
    `/user/handle?${searchParams.toString()}`,
  );
  return response.data;
};

interface UserSearchData {
  searchString: string;
}

export const getUsersSearch = async ({ searchString }: UserSearchData) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    searchString,
  });

  const response = await axios.get(`/user/search?${searchParams.toString()}`);
  return response.data;
};

export const getTopUsers = async () => {
  const axios = getAxios();

  const response = await axios.get("/user/top");
  return response.data;
};

export const getNewUsers = async () => {
  const axios = getAxios();

  const response = await axios.get("/user/page");
  return response.data;
};

export const getIsUserBlocked = async (params: { userId: string }) => {
  const axios = getAxios();
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<boolean>(
    `/user/isUserBlocked?${searchParams.toString()}`,
  );
  return response.data;
};

export const getIsBlockedByUser = async (params: { userId: string }) => {
  const axios = getAxios();
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<boolean>(
    `/user/isBlockedByUser?${searchParams.toString()}`,
  );
  return response.data;
};

export const blockUser = async (params: { userId: string }) => {
  const axios = getAxios();
  const searchParams = new URLSearchParams(params);

  const response = await axios.get(`/user/block?${searchParams.toString()}`);
  return response.data;
};

export const unblockUser = async (params: { userId: string }) => {
  const axios = getAxios();
  const searchParams = new URLSearchParams(params);

  const response = await axios.get(`/user/unblock?${searchParams.toString()}`);
  return response.data;
};

export const reportUser = async (data: {
  userId: string;
  reason: string;
  details: string;
}) => {
  const axios = getAxios();

  const response = await axios.post(`/user/report`, data);
  return response.data;
};
export const getReferrers = async () => {
  const axios = getAxios();

  const response = await axios.get("/user/referrers");
  return response.data;
};

export const getReferralStats = async () => {
  const axios = getAxios();

  const response = await axios.get<ReferralStatsResponse>(
    "/user/stats/referral",
  );
  return response.data;
};
