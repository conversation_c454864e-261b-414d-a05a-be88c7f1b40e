import { useQueryClient } from "@tanstack/react-query";

import { HANDLE_PHASE } from "@/app/(main)/community/_components/consts";
import { toast } from "@/components/toast";
import {
  useFollowCommunityMutation,
  useUnfollowCommunityMutation,
} from "@/queries";
import { TradesGroupsTrendingResponse } from "@/queries/types/new-users-response";
import { CommunityExtended } from "@/types/community";

import { CommunityCardUI } from "./community-card";

interface TrendingGroupCardProps {
  community: CommunityExtended;
}

export function TrendingGroupCard({ community }: TrendingGroupCardProps) {
  const queryClient = useQueryClient();

  const param =
    community.tokenPhase >= HANDLE_PHASE
      ? community.name
      : community.contractAddress;

  const { mutateAsync: follow } = useFollowCommunityMutation({
    onMutate: async () => {
      toast.green(`Joined $${community.ticker}!`);
      await queryClient.cancelQueries({
        queryKey: ["community", "param", param],
      });
      await queryClient.cancelQueries({
        queryKey: ["trade", "groups", "trending"],
      });

      const previousCommunity = queryClient.getQueryData([
        "community",
        "param",
        param,
      ]);
      const previousTrendingGroups = queryClient.getQueryData([
        "trade",
        "groups",
        "trending",
      ]);

      queryClient.setQueryData(
        ["community", "param", param],
        (
          old:
            | {
                community: CommunityExtended;
              }
            | undefined,
        ) => {
          if (!old) {
            return old;
          }

          return {
            ...old,
            community: {
              ...old.community,
              following: true,
            },
          };
        },
      );
      queryClient.setQueryData(
        ["trade", "groups", "trending"],
        (old: TradesGroupsTrendingResponse) => {
          if (!old) return old;

          return {
            ...old,
            communities: old.communities.map((c) => {
              if (c.id === community.id) {
                return {
                  ...c,
                  following: true,
                };
              }
              return c;
            }),
          };
        },
      );

      return { previousCommunity, previousTrendingGroups };
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to join $${community.ticker}.`);
      queryClient.setQueryData(
        ["community", "param", param],
        context.previousCommunity,
      );
      queryClient.setQueryData(
        ["trade", "groups", "trending"],
        context?.previousTrendingGroups,
      );
    },
  });
  const { mutateAsync: unfollow } = useUnfollowCommunityMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["community", "param", param],
      });
      await queryClient.cancelQueries({
        queryKey: ["trade", "groups", "trending"],
      });

      const previousCommunity = queryClient.getQueryData([
        "community",
        "param",
        param,
      ]);
      const previousTrendingGroups = queryClient.getQueryData([
        "trade",
        "groups",
        "trending",
      ]);

      queryClient.setQueryData(
        ["community", "param", param],
        (
          old:
            | {
                community: CommunityExtended;
              }
            | undefined,
        ) => {
          if (!old) {
            return old;
          }

          return {
            ...old,
            community: {
              ...old.community,
              following: false,
            },
          };
        },
      );
      queryClient.setQueryData(
        ["trade", "groups", "trending"],
        (old: TradesGroupsTrendingResponse) => {
          if (!old) return old;

          return {
            ...old,
            communities: old.communities.map((c) => {
              if (c.id === community.id) {
                return {
                  ...c,
                  following: false,
                };
              }
              return c;
            }),
          };
        },
      );

      return { previousCommunity, previousTrendingGroups };
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to leave ${community.ticker}.`);
      queryClient.setQueryData(
        ["community", "param", param],
        context.previousCommunity,
      );
      queryClient.setQueryData(
        ["trade", "groups", "trending"],
        context?.previousTrendingGroups,
      );
    },
  });

  function handleFollow() {
    if (community.following) {
      unfollow({ communityId: community.id });
    } else {
      follow({ communityId: community.id });
    }
  }

  return <CommunityCardUI community={community} handleFollow={handleFollow} />;
}
