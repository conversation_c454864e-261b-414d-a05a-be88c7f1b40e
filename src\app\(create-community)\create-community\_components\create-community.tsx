import { ChangeEvent, useCallback, useEffect, useRef, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { ethers } from "ethers";
import { debounce } from "lodash";
import { Address, encodeFunctionData, formatEther, parseEther } from "viem";

import { getCommunityById } from "@/api/client/group";
import { ArenaLaunchTnCs } from "@/app/(main)/community/_components/community-phase-modals";
import {
  A,
  B,
  CREATOR_FEE,
  CURVE_SCALER,
  MAX_GROUP_SUPPLY_BIGINT,
  TOKEN_SPLIT,
} from "@/app/(main)/community/_components/consts";
import { ArenaLogo } from "@/app/(main)/tokenomics/_components/arena-logo";
import { GroupIcon } from "@/components/icons-v2/group-logo";
import { LocatedNumber } from "@/components/located-number";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import {
  COMMUNITIES_CONTRACT,
  COMMUNITY_ABI,
  COMMUNITY_PRICES_ABY,
  COMMUNITY_PRICES_CONTRACT,
} from "@/environments/COMMUNITY_CONTRACT";
import { AVAX } from "@/environments/tokens";
import { useTokenBalancesQuery } from "@/queries";
import { useAvaxPriceQuery } from "@/queries/currency-queries";
import { useCreateGroupMutation } from "@/queries/groups-queries";
import { useUser } from "@/stores";
import { abbreviateNumber, cn } from "@/utils";
import { formatInputNumber } from "@/utils/format-token-price";
import { isNumber } from "@/utils/is-number";

import { CreateCommunityFormState } from "./create-community-form-input";

interface Props {
  formState: CreateCommunityFormState;
}

export const CreateCommunity = ({ formState }: Props) => {
  const router = useRouter();
  const [isCreateGroupPendingDynamic, setIsCreateGroupPendingDynamic] =
    useState(false);
  const [avaxAmount, setAvaxAmount] = useState<string>("0");
  const [avaxDisplayAmount, setAvaxDisplayAmount] = useState<string>("0");
  const [tokenAmount, setTokenAmount] = useState<string>("0");
  const [isDebounce, setIsDebounce] = useState<boolean>(false);
  const spanRef = useRef<HTMLSpanElement | null>(null);
  const [inputWidth, setInputWidth] = useState("20px");
  const { user } = useUser();
  const { balances } = useTokenBalancesQuery({
    address: user?.address,
    currencies: [{ symbol: "AVAX" }],
  });

  const userBalance = balances?.AVAX?.balance ?? BigInt(0);
  const { primaryWallet } = useDynamicContext();

  if (!user) {
    throw new Error("User not specified");
  }

  const { data: avaxPrice } = useAvaxPriceQuery();

  const { mutateAsync: createGroup, isPending: isCreateGroupPending } =
    useCreateGroupMutation();

  const setTokenAmountCallback = useCallback(
    debounce(async (amount: string) => {
      if (!primaryWallet) {
        throw new Error("Dynamic wallet is not initialized");
      }
      if (!isEthereumWallet(primaryWallet)) {
        throw new Error("This wallet is not a Ethereum wallet");
      }
      if (amount === "0") {
        setTokenAmount("0");
        setIsDebounce(false);
        return;
      }

      const publicClient = await primaryWallet.getPublicClient();

      const [tokens, price] = await publicClient.readContract({
        address: COMMUNITY_PRICES_CONTRACT.addressMainnet as Address,
        abi: COMMUNITY_PRICES_ABY,
        functionName: "calculatePurchaseAmountAndPriceParametric",
        args: [
          parseEther(amount),
          A,
          B,
          BigInt(CURVE_SCALER),
          BigInt(CREATOR_FEE),
          BigInt(TOKEN_SPLIT),
        ],
        account: primaryWallet.address as Address,
      });

      const newTokenAmount = formatEther(tokens);
      setTokenAmount(newTokenAmount);

      const MAX_SPENDING_PERCENTAGE = 70;
      if (getTotalSupplyPercentage(newTokenAmount) >= MAX_SPENDING_PERCENTAGE) {
        setAvaxAmount(formatEther(price));
        setAvaxDisplayAmount(
          formatInputNumber(formatEther(price)).replace(
            /(\.\d*?[1-9])0+$|\.0+$/g,
            "$1",
          ),
        );
      }

      setIsDebounce(false);
    }, 1000),
    [],
  );

  const setTokenAmountByString = (amount: string) => {
    setIsDebounce(true);
    setTokenAmountCallback(amount);
  };

  const handleInput = (e: ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    value = value.replace(/[^0-9.]/g, "");
    if (!isNumber(value)) {
      value = "0";
    }
    const maxAllowed = Math.min(
      10,
      Math.max(parseFloat(formatEther(userBalance)) - 0.005, 0),
    );
    if (value.startsWith(".")) value = "0" + value;
    if (parseFloat(value) > maxAllowed) {
      value = String(maxAllowed);
    }
    if (value.startsWith("0") && value.length > 1 && value[1] !== ".") {
      value = value.substring(1);
    }
    if (value.endsWith(".")) {
      setAvaxDisplayAmount(formatInputNumber(value));
      setAvaxAmount(value.slice(0, -1));
      setTokenAmountByString(value.slice(0, -1));
      return;
    }
    setAvaxDisplayAmount(formatInputNumber(value));
    value = formatInputNumber(value).replace(/,/g, "");
    setAvaxAmount(value);
    setTokenAmountByString(value);
  };

  const handleMax = async () => {
    const userBalanceEther = Number(formatEther(userBalance));
    const maxWithoutGas = Math.max(userBalanceEther - 0.005, 0);
    const max = Math.min(maxWithoutGas, 10).toString();
    handleInput({
      target: { value: max },
    } as ChangeEvent<HTMLInputElement>);
  };

  const handleCreate = async () => {
    if (user.address !== user.dynamicAddress?.toLowerCase()) {
      toast.danger("Migrate to Dynamic Wallet first!");
      return;
    }

    if (!primaryWallet) {
      throw new Error("Dynamic wallet is not initialized");
    }

    if (!isEthereumWallet(primaryWallet)) {
      throw new Error("This wallet is not an Ethereum wallet");
    }

    setIsCreateGroupPendingDynamic(true);

    let id!: string;
    try {
      const temporaryCommunity = await createGroup(formState);
      if (temporaryCommunity?.community?.id) {
        id = temporaryCommunity.community.id;
      } else {
        toast.danger(
          "Profile page handle taken, try choosing another one at step 1",
        );
        setIsCreateGroupPendingDynamic(false);
        return;
      }
    } catch {
      toast.red("An error occurred");
      setIsCreateGroupPendingDynamic(false);
      return;
    }

    const walletClient = await primaryWallet.getWalletClient();

    let tx!: Address;

    try {
      const functionName = "createToken";
      const name = formState.tokenName;
      const symbol = formState.ticker;
      const amount = parseEther(tokenAmount);
      const args: [
        number,
        number,
        bigint,
        number,
        Address,
        bigint,
        string,
        string,
        bigint,
      ] = [
        A,
        B,
        BigInt(CURVE_SCALER),
        CREATOR_FEE,
        primaryWallet.address as Address,
        BigInt(TOKEN_SPLIT),
        name,
        symbol,
        amount,
      ];

      const encodedData = encodeFunctionData({
        abi: COMMUNITY_ABI,
        functionName,
        args,
      });

      const idHex = ethers.hexlify(ethers.toUtf8Bytes(id)).slice(2);
      const dataWithId = `${encodedData}${idHex}` as `0x${string}`;

      const sendTransactionParams: any = {
        account: primaryWallet.address as Address,
        chain: walletClient.chain,
        to: COMMUNITIES_CONTRACT.addressMainnet as Address,
        data: dataWithId,
      };

      if (amount > 0n) {
        sendTransactionParams.value = parseEther(avaxAmount);
      }

      tx = await walletClient.sendTransaction(sendTransactionParams);
    } catch (e: unknown) {
      if (e instanceof Error) {
        if (e.name === "TransactionExecutionError") {
          toast.red(
            e.message || "Transaction execution failed. Please try again.",
          );
        } else {
          toast.red("An error occurred");
        }
      } else {
        toast.red("An unknown error occurred");
      }
      setIsCreateGroupPendingDynamic(false);
      return;
    }

    try {
      if (tx && id) {
        const contractAddress = await getContractAddress(id);
        router.push(`/community/${contractAddress}`);
      }
    } catch {
      localStorage.setItem("currentExploreTab", "tokens");
      router.push(`/explore`);
    } finally {
      setIsCreateGroupPendingDynamic(false);
    }
  };

  const getContractAddress = async (communityId: string) => {
    while (true) {
      let data: Awaited<ReturnType<typeof getCommunityById>> | null = null;
      try {
        data = await getCommunityById({ communityId });
      } catch {}
      if (data && data.community.contractAddress)
        return data.community.contractAddress;
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  };

  const getTotalSupplyPercentage = (tokens: string) =>
    Number(
      ((Number(tokens) / MAX_GROUP_SUPPLY_BIGINT) * 100).toFixed(
        (Number(tokens) / MAX_GROUP_SUPPLY_BIGINT) * 100 < 1 ? 4 : 2,
      ),
    );

  useEffect(() => {
    if (spanRef.current) {
      setInputWidth(`${spanRef.current.offsetWidth + 12}px`);
    }
  }, [avaxDisplayAmount]);

  return (
    <>
      <div>
        <div className="mb-4 inline-flex rounded-[20px] bg-brand-orange">
          <p className="px-2">Step 2/2</p>
        </div>
        <p className="mb-3 text-xl font-semibold text-off-white">
          Create &amp; Buy Tokens
        </p>
        <p className="mb-7 text-sm font-normal text-off-white">
          You can buy the first tokens right now if you wish. The gas fee will
          be added to the total.
        </p>
        <p className="mb-1 text-[11px] text-off-white">YOU ARE CREATING:</p>
      </div>
      <div className="relative mb-8 flex overflow-hidden rounded-[10px] bg-[#0e0e0e]">
        <div className="flex px-4 py-8">
          <Image
            width={92}
            height={92}
            src={formState.photoURL}
            alt="Group Logo"
            className="mr-6 rounded-[8px] border-[1px] border-[#EB540A]"
          />
          <div className="flex-col">
            <div className="flex gap-2">
              <GroupIcon />
              <p className="text-sm font-semibold text-[#f3f3f3]">
                {`${formState.tokenName} ($${formState.ticker})`}
              </p>
            </div>
            <span className="text-sm font-normal text-[#f3f3f3]">
              {`@${formState.name}`}
            </span>
          </div>
          <div className="absolute right-0 top-0 translate-x-1/2 overflow-hidden opacity-5">
            <ArenaLogo className="h-auto w-[150px] overflow-hidden text-[#F4F4F4]" />
          </div>
        </div>
      </div>
      <div className="mb-4 flex flex-col">
        <div className="mb-2 text-xs">
          <span className="mr-1 text-light-gray-text">BALANCE:</span>
          <span className="text-white">
            {LocatedNumber(formatEther(userBalance))}
          </span>
        </div>
        <div className="relative">
          <div className="flex h-[52px] items-center rounded-lg border border-[#3B3B3B] transition-colors focus-within:border-off-white">
            <div className="flex items-center pl-4">
              <img
                src="/assets/coins/avax.png"
                alt="avax"
                className="h-[16px] w-[16px] flex-shrink-0"
              />
            </div>
            <span
              ref={spanRef}
              className="invisible absolute whitespace-pre text-[16px] font-semibold leading-[20px]"
            >
              {avaxDisplayAmount || " "}
            </span>
            <div className="relative flex h-full flex-1 items-center">
              <input
                placeholder="Enter amount"
                type="string"
                value={avaxDisplayAmount}
                onChange={handleInput}
                className="h-full w-full border-0 bg-transparent pl-2 text-[16px] font-semibold leading-[20px] focus:outline-none"
              />
              <span
                className="absolute left-0 top-[19px] z-20 text-xs text-[#808080]"
                style={{
                  marginLeft: inputWidth,
                }}
              >
                {`($${formatInputNumber((Number(avaxAmount) * (avaxPrice?.avax || 0)).toString(), 2)})`}
              </span>
            </div>
            <div
              className="absolute right-4 top-1/2 -translate-y-1/2 cursor-pointer"
              onClick={handleMax}
            >
              <span className="text-xs underline">Max</span>
            </div>
          </div>
        </div>
        <div className="relative mt-8 flex w-full flex-col items-center justify-center rounded-[10px] bg-[#0e0e0e] p-4">
          <div className="flex items-center justify-center">
            <p className="mr-2 text-sm font-normal">You are buying</p>
            <Image
              src={formState.photoURL}
              width={40}
              height={40}
              className="pointer-events-none mr-1 size-4 select-none rounded-full"
              alt={`${formState.ticker} logo`}
            />
            <p
              className={cn(
                isDebounce
                  ? "text-sm font-normal text-light-gray-text"
                  : "text-[16px] font-semibold",
              )}
            >
              {isDebounce
                ? "calculating.."
                : abbreviateNumber(Number(tokenAmount), 1, true, [
                    "",
                    "K",
                    "M",
                    "B",
                    "T",
                    "Q",
                  ])}
            </p>
          </div>
          {Number(avaxAmount) > 0 && (
            <p className="text-sm font-normal text-[#808080]">
              {`${isDebounce ? "0.00" : getTotalSupplyPercentage(tokenAmount)}% of
              max supply`}
            </p>
          )}
        </div>
      </div>
      <div className="mt-auto flex flex-col gap-4 pb-6">
        <Button
          className="w-full"
          type="submit"
          onClick={handleCreate}
          loading={isCreateGroupPendingDynamic || isCreateGroupPending}
        >
          <div className="flex items-center justify-center gap-1">
            <p className="text-sm font-normal text-off-white">{`${Number(avaxAmount) > 0 ? `Create & Buy $${formState.ticker}` : `Create $${formState.ticker}`}`}</p>
            <div>
              <Image
                src={AVAX.icon}
                width={16}
                height={16}
                className="pointer-events-none ml-1 select-none rounded-full brightness-110 grayscale"
                alt={`${AVAX.name} logo`}
              />
            </div>
            <p className="text-sm font-normal text-off-white">
              {Number(avaxAmount) > 0
                ? `${abbreviateNumber(Number(avaxAmount), 3)}`
                : `0.002`}
            </p>
          </div>
        </Button>
        <ArenaLaunchTnCs />
      </div>
    </>
  );
};
