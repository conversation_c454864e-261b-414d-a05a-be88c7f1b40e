"use client";

import { ChangeEvent, useCallback, useMemo, useState } from "react";

import { useParticipants } from "@livekit/components-react";
import { useQuery } from "@tanstack/react-query";
import { RoomEvent } from "livekit-client";

import useThrottle from "@/hooks/use-throttle";
import { stageQueries } from "@/queries/stage-queries";
import { useStageStore } from "@/stores/stage";

import {
  ArrowBackOutlineIcon,
  CloseOutlineIcon,
  SearchFilledIcon,
} from "../icons";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "../ui/dialog";
import { Input } from "../ui/input";
import { ROLES } from "./constants";
import { InviteUserListItem } from "./invite-user-list-item";

export const InviteSpeakerModal = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const throttledSearchValue = useThrottle(searchValue);
  const id = useStageStore((state) => state.id!);
  const { data } = useQuery({
    ...stageQueries.stageInfo(id),
  });

  const speakersIds = useMemo(() => {
    return data?.speakers.map((user) => user.userId) ?? [];
  }, [data?.speakers]);

  const invitedSpeakersIds = useMemo(() => {
    return data?.invitedSpeakers.map((user) => user.userId) ?? [];
  }, [data?.invitedSpeakers]);

  const participants = useParticipants({
    updateOnlyOn: [
      RoomEvent.ParticipantConnected,
      RoomEvent.ParticipantDisconnected,
      RoomEvent.ParticipantAttributesChanged,
    ],
  });

  const formattedParticipants = useMemo(() => {
    return participants.map((participant) => ({
      id: participant.identity,
      name: participant.attributes?.name,
      avatar: participant.attributes?.avatar,
      username: participant.attributes?.username,
      role: participant.attributes?.role,
    }));
  }, [participants]);

  const filteredParticipants = useMemo(() => {
    if (!formattedParticipants) return [];

    return formattedParticipants.filter((participant) => {
      if (participant.role === ROLES.HOST || participant.role === ROLES.COHOST)
        return false;

      if (
        participant?.name
          ?.toLowerCase()
          ?.includes(throttledSearchValue?.toLowerCase()) ||
        participant?.username
          ?.toLowerCase()
          ?.includes(throttledSearchValue?.toLowerCase())
      ) {
        return true;
      }
      return false;
    });
  }, [throttledSearchValue, formattedParticipants]);

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const callbackRef = useCallback((inputElement: HTMLInputElement) => {
    if (inputElement) {
      setTimeout(() => {
        inputElement.focus();
      }, 200);
    }
  }, []);

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
      }}
    >
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="z-50 flex h-full w-full flex-col gap-0 overflow-y-auto bg-dark-bk px-0 pb-16 pt-0 sm:bg-dark-bk md:max-h-[700px]">
        <div className="sticky top-0 z-20 bg-dark-bk px-6 pb-4">
          <div className="flex items-center gap-2 pb-4 pt-[calc(1rem+env(safe-area-inset-top))]">
            <div className="flex-1">
              <DialogClose className="flex flex-shrink-0">
                <ArrowBackOutlineIcon className="size-5 text-off-white" />
              </DialogClose>
            </div>
            <h2 className="text-base font-semibold leading-[22px] text-off-white">
              Invite a Speaker
            </h2>
            <div className="flex-1" />
          </div>
          <div className="relative">
            <Input
              value={searchValue}
              onChange={handleSearch}
              placeholder="Search for people and groups"
              className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-10 ring-0  placeholder:text-gray-text"
              ref={callbackRef}
            />
            <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
            {searchValue && (
              <button className="absolute right-[10px] top-1/2 -translate-y-1/2 rounded-full border border-dark-gray bg-[rgba(20,20,20,0.88)] p-1">
                <CloseOutlineIcon
                  className="pointer-events-auto size-[14px] select-none text-off-white"
                  onClick={() => setSearchValue("")}
                />
              </button>
            )}
          </div>
        </div>
        {/* {throttledSearchValue && isSearchDataLoading && (
          <div className="flex flex-col gap-[32px] px-6 py-5">
            {Array(9)
              .fill(null)
              .map((_, index) => (
                <UserListItemLoadingSkeleton key={index} />
              ))}
          </div>
        )} */}
        {filteredParticipants && filteredParticipants.length > 0 && (
          <div className="flex flex-col gap-[32px] px-6 py-5">
            {filteredParticipants.map((user) => (
              <InviteUserListItem
                key={user.id}
                // @ts-ignore
                user={user}
                accepted={speakersIds.includes(user.id)}
                invited={invitedSpeakersIds.includes(user.id)}
                role="SPEAKER"
              />
            ))}
          </div>
        )}
        {throttledSearchValue &&
          filteredParticipants &&
          filteredParticipants.length === 0 && (
            <div className="mt-10 flex w-full items-center justify-center">
              <div className="max-w-64 text-center">
                <h4 className="text-sm font-semibold text-[#EDEDED]">
                  No users found!
                </h4>
              </div>
            </div>
          )}
        {/* {!throttledSearchValue && !isSearchDataLoading && (
          <div className="mt-10 flex w-full items-center justify-center">
            <div className="max-w-64 text-center">
              <h4 className="text-sm font-semibold text-[#EDEDED]">
                Search for a user to invite
              </h4>
            </div>
          </div>
        )} */}
      </DialogContent>
    </Dialog>
  );
};
