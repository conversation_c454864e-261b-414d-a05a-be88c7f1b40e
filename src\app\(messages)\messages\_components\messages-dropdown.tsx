import { FC, useState } from "react";

import { EllipsisHorizontalFilledIcon } from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export const MessageDropdown: FC = () => {
  const [open, setOpen] = useState(false);

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="-mr-2 flex size-10 flex-shrink-0 items-center justify-center border-none p-0 outline-none"
        >
          <EllipsisHorizontalFilledIcon className="size-5 fill-off-white" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[100px]">
        <ProgressBarLink href="/messages/settings" className="size-5">
          <DropdownMenuItem className="gap-4">
            <span>Settings</span>
          </DropdownMenuItem>
        </ProgressBarLink>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
