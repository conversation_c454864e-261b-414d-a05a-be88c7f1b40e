"use client";

import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { motion, useMotionValue } from "framer-motion";
import { Address, Hex } from "viem";

import { RewardsTab } from "@/app/(main)/champions/_components/rewards-tab";
import { ArrowBackOutlineIcon } from "@/components/icons";
import {
  stakingContractABI,
  stakingContractAddress,
} from "@/environments/stakingABI";
import { ARENA } from "@/environments/tokens";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import {
  useRewards,
  useStakersLeaderboardRankQuery,
} from "@/queries/token-portal-queries";
import { useUser } from "@/stores";
import { cn } from "@/utils";

import { ChampionUserItem } from "./champion-user-item";
import { ChampionsSocialBenefits } from "./champions-social-benefits";
import { ChampionsTabNavigation } from "./champions-tab-navigation";
import { ChampionsUserCard } from "./champions-user-card";
import { LearnMore } from "./learn-more";
import { StakingLeaderboard } from "./staking-leaderboard";

export const Champions = () => {
  const tabRef = useRef<HTMLDivElement>(null);
  const backgroundColor = useMotionValue("rgb(0 0 0 / 0)");

  const [currentTab, setCurrentTab] = useState<"leaderboard" | "rewards">(
    "leaderboard",
  );

  const { data: staker, isLoading: isStakerRankLoading } =
    useStakersLeaderboardRankQuery();
  const { user } = useUser();

  const [isScrolled, setIsScrolled] = useState(false);
  const [isAtTop, setIsAtTop] = useState(true);
  const [isChampions, setIsChampions] = useState(false);
  const userCardRef = useRef<HTMLDivElement | null>(null);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const router = useRouter();
  const [totalStaked, setTotalStaked] = useState("0");

  const { primaryWallet } = useDynamicContext();

  const { data: rewardsData, isLoading: isRewardsDataLoading } = useRewards();

  useEffect(() => {
    const handleScroll = () => {
      if (tabRef.current && tabRef.current.getBoundingClientRect().top < 0) {
        currentTab === "leaderboard"
          ? backgroundColor.set("rgb(43 22 54 / 1)")
          : backgroundColor.set("rgb(33 19 43 / 1)");
      } else {
        backgroundColor.set("rgb(0 0 0 / 0)");
      }

      const userCardHeight =
        userCardRef.current?.getBoundingClientRect().height || 0;
      setIsScrolled(window.scrollY > userCardHeight);
      setIsAtTop(window.scrollY === 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [backgroundColor]);

  const goBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push("/home");
    }
  };

  const getInternalArenaBalance = async () => {
    if (!primaryWallet) return;
    if (!isEthereumWallet(primaryWallet)) {
      throw new Error("This wallet is not a Ethereum wallet");
    }

    const publicClient = await primaryWallet.getPublicClient();

    const [stake] = await publicClient.readContract({
      address: stakingContractAddress as Address,
      abi: stakingContractABI,
      functionName: "getUserInfo",
      args: [primaryWallet.address as Hex, ARENA.address as Address],
    });

    setTotalStaked(stake.toString());
  };

  useEffect(() => {
    void getInternalArenaBalance();
  }, [primaryWallet]);

  useEffect(() => {
    if (isChampions) {
      document.body.classList.add("overflow-hidden");
    } else {
      document.body.classList.remove("overflow-hidden");
    }
  }, [isChampions]);

  return (
    <div
      className={cn(
        "select-none",
        currentTab === "rewards" ? "" : "bg-[#2B1636]",
      )}
      ref={tabRef}
    >
      {isChampions && (
        <ChampionsSocialBenefits
          setIsChampions={setIsChampions}
          isTablet={isTablet}
        />
      )}
      <motion.div
        className={cn(
          "sticky top-0 z-10 pt-[18px]",
          currentTab === "leaderboard" && "bg-[#2B1636]",
        )}
        style={{ backgroundColor }}
      >
        <div className="flex items-center justify-between px-6">
          <button
            className="hover:bg-blue-700 rounded-full p-2 transition-colors duration-200"
            onClick={goBack}
          >
            <ArrowBackOutlineIcon className="size-5 text-white" />
          </button>
          <h4 className="absolute left-1/2 -translate-x-1/2 transform text-lg font-semibold text-white">
            Arena Champion
          </h4>
        </div>
        <ChampionsTabNavigation
          currentTab={currentTab}
          setCurrentTab={setCurrentTab}
        />
      </motion.div>
      {currentTab === "leaderboard" && (
        <>
          {!isScrolled ? (
            <div
              className="mx-[24px] my-[16px] rounded-[12px] bg-[#000000] transition-opacity duration-300"
              ref={userCardRef}
            >
              <ChampionsUserCard
                rank={staker?.staker?.rank || 0}
                stakedBalance={totalStaked || ""}
                twitterPicture={
                  staker?.staker?.twitterPicture || user?.twitterPicture || ""
                }
                twitterName={
                  staker?.staker?.twitterName || user?.twitterName || ""
                }
              />
            </div>
          ) : (
            <div
              className="sticky top-[120px] z-10 bg-[#1A1A1A] transition-transform duration-300"
              ref={userCardRef}
            >
              <ChampionUserItem
                rank={staker?.staker?.rank || 0}
                twitterHandle={
                  staker?.staker?.twitterHandle || user?.twitterHandle || ""
                }
                twitterName={
                  staker?.staker?.twitterName || user?.twitterName || ""
                }
                twitterPicture={
                  staker?.staker?.twitterPicture || user?.twitterPicture || ""
                }
                ewmaScore={staker?.staker?.ewmaScore || ""}
                lastEwmaScore={staker?.staker?.lastEwmaScore || ""}
              />
            </div>
          )}
          <div className="h-[calc(100%-139px)] bg-[#000000] py-2">
            <StakingLeaderboard />
          </div>
          {isAtTop && <LearnMore isTablet={isTablet} />}
        </>
      )}
      {currentTab === "rewards" && (
        <RewardsTab
          onLearnMoreClick={() => setIsChampions(true)}
          isLoading={isRewardsDataLoading}
          rewards={rewardsData}
        />
      )}
    </div>
  );
};
