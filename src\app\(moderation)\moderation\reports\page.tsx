"use client";

import { useSelectedLayoutSegments } from "next/navigation";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

import { Reports } from "./_components/reports";

function ReportsPage() {
  const isLargeTablet = useMediaQuery(BREAKPOINTS.lg);
  const segments = useSelectedLayoutSegments();
  const hasTicketId = segments.length > 0;

  if (isLargeTablet && hasTicketId) {
    return null;
  }

  return <Reports />;
}

export default ReportsPage;
