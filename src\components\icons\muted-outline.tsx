import { ComponentProps } from "react";

export const MutedOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 17 16"
    fill="none"
    {...props}
  >
    <path
      d="M12.5784 8C12.5784 9.1047 12.1569 10.1642 11.4068 10.9453C10.6567 11.7264 9.63923 12.1653 8.57837 12.1653M8.57837 12.1653C7.5175 12.1653 6.50009 11.7264 5.74994 10.9453C4.9998 10.1642 4.57837 9.1047 4.57837 8M8.57837 12.1653V13.8788"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.16391 9.41421C7.53898 9.78929 8.04769 10 8.57812 10C9.10856 10 9.61727 9.78929 9.99234 9.41421C10.3674 9.03914 10.5781 8.53043 10.5781 8V4C10.5781 3.46957 10.3674 2.96086 9.99234 2.58579C9.61727 2.21071 9.10856 2 8.57812 2C8.04769 2 7.53898 2.21071 7.16391 2.58579C6.78884 2.96086 6.57812 3.46957 6.57812 4V8C6.57812 8.53043 6.78884 9.03914 7.16391 9.41421Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.91162 12.334L12.9116 3.33398"
      stroke="#020202"
      strokeLinecap="round"
    />
    <path
      d="M3.57812 11.3327L12.2448 2.66602"
      stroke="currentColor"
      strokeLinecap="round"
    />
  </svg>
);
