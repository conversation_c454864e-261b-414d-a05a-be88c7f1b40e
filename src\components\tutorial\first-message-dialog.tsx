"use client";

import { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";

import { setTutorialShown } from "@/api/client/user";
import { useGroupByUserIdQuery } from "@/queries";
import { useUser } from "@/stores";
import { useTutorialStore } from "@/stores/tutorial";

import { ChatAlt2OutlineIcon } from "../icons";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
} from "../ui/alert-dialog";
import { Button } from "../ui/button";

export const FirstMessageTutorialDialog = () => {
  const { user, updateUser } = useUser();
  const router = useRouter();
  const pathname = usePathname();
  const isFirstMessageTutorialOpen = useTutorialStore(
    (state) => state.isFirstMessageTutorialOpen,
  );
  const isOnHomePage = pathname === "/messages";
  const isOpen = isOnHomePage && isFirstMessageTutorialOpen;

  const actions = useTutorialStore((state) => state.actions);
  const [isClickedPending, setIsClickedPending] = useState(false);

  const { data: groupData, isLoading } = useGroupByUserIdQuery({
    userId: user?.id,
  });

  useEffect(() => {
    if (
      !user?.tutorial ||
      user?.tutorial?.messageTutorialShown ||
      (!isLoading &&
        (!groupData?.group?.lastUserId ||
          groupData?.group?.lastUserId === user?.id))
    ) {
      actions.setIsFirstMessageTutorialOpen(false);
    } else if (
      user?.tutorial &&
      !user?.tutorial?.messageTutorialShown &&
      !isLoading &&
      groupData?.group?.lastUserId !== user?.id
    ) {
      actions.setIsFirstMessageTutorialOpen(true);
    }
  }, [groupData, isLoading]);

  useEffect(() => {
    if (user?.tutorial?.messageTutorialShown) {
      actions.setIsFirstMessageTutorialOpen(false);
    }
  }, [user, actions]);

  const handleClick = async () => {
    setIsClickedPending(true);
    try {
      if (!groupData?.group) return;
      setTutorialShown("message");
      handleUpdate();
      router.push(`/messages/${groupData.group.id}`);
      actions.setIsFirstMessageTutorialOpen(false);
    } catch (error) {
      console.error(error);
    } finally {
      setIsClickedPending(false);
    }
  };

  const handleUpdate = () => {
    if (!user) return;
    updateUser({
      ...user,
      tutorial: {
        ...user.tutorial,
        messageTutorialShown: true,
      },
    });
  };

  return (
    <AlertDialog
      open={isOpen && !isLoading && !user?.tutorial?.messageTutorialShown}
      onOpenChange={actions.setIsFirstMessageTutorialOpen}
    >
      <AlertDialogContent
        overlayNoBG
        className="bottom-20 top-auto w-11/12 max-w-md translate-y-0 rounded-[10px] border border-off-white/20 p-6 text-off-white shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)] md:bottom-[35vh]"
      >
        <div className="flex flex-col items-center text-center">
          <ChatAlt2OutlineIcon className="size-7 text-off-white" />
          <h2 className="mt-2 text-base font-semibold text-off-white">
            You got your first message!
          </h2>
          <p className="mt-4 text-sm text-gray-text ">
            One of your ticket holders sent a message in your chatroom. Drop by
            and say hi!
          </p>
        </div>
        <AlertDialogFooter className="mt-2 flex-row items-baseline gap-2 pb-2">
          <Button
            onClick={() => {
              actions.setIsFirstMessageTutorialOpen(false);
              setTutorialShown("message");
              handleUpdate();
            }}
            variant="outline"
            className="h-9 w-1/3 text-off-white"
          >
            Close
          </Button>
          <Button
            onClick={handleClick}
            className="h-9 w-2/3 bg-orange-gradient"
            loading={isClickedPending}
          >
            Go to my chatroom
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
