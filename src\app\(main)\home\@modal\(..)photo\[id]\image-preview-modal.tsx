"use client";

import { useRouter } from "next/navigation";

import * as Dialog from "@radix-ui/react-dialog";

import { CloseOutlineIcon } from "@/components/icons";
import { ImagePreview } from "@/components/image-preview";

export function ImagePreviewModal({ url }: { url: string }) {
  const router = useRouter();

  function handleClose() {
    router.back();
  }

  return (
    <Dialog.Root open={true} onOpenChange={handleClose}>
      <Dialog.Portal>
        <Dialog.Overlay />
        <Dialog.Content className="bg-black/70 fixed inset-0 z-[999] flex flex-col">
          <ImagePreview url={url} />
          <Dialog.Close className="absolute left-2 top-2 flex h-12 w-12 items-center justify-center text-white focus-visible:outline-none">
            <CloseOutlineIcon className="h-6 w-6" />
          </Dialog.Close>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
