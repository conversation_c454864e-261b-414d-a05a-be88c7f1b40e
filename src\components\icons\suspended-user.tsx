import { ComponentProps } from "react";

export const SuspendedUserProfileOutlineIcon = (
  props: ComponentProps<"svg">,
) => (
  <svg
    width="92"
    height="92"
    viewBox="0 0 92 92"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="0.5" y="0.5" width="91" height="91" rx="45.5" fill="#3B3B3B" />
    <rect x="0.5" y="0.5" width="91" height="91" rx="45.5" stroke="#E8E8E8" />
  </svg>
);

export const SuspendedUserChatPreviewOutlineIcon = (
  props: ComponentProps<"svg">,
) => (
  <svg
    width="42"
    height="42"
    viewBox="0 0 42 42"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="0.5"
      y="0.5"
      width="41"
      height="41"
      rx="20.5"
      fill="#3B3B3B"
      stroke="#E8E8E8"
    />
  </svg>
);

export const SuspendedUserChatHeaderOutlineIcon = (
  props: ComponentProps<"svg">,
) => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="28" height="28" rx="14" fill="#EB540A" />
  </svg>
);
