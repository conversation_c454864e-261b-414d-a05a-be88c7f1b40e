"use client";

import { Di<PERSON>atch, SetStateAction } from "react";
import { useSearchParams } from "next/navigation";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { Hex } from "viem";

import { fetchTokenBalance } from "@/api/balance";
import { getAvaxPrice, getSystemCurrencies } from "@/api/client/currency";
import { getCommunityByStr } from "@/api/client/group";
import { getThreadById } from "@/api/client/threads";
import {
  TradeERC20,
  TradeERC20Context,
} from "@/app/_components/trade-erc20/trade-erc20";
import { TradeERC20TnCs } from "@/app/_components/trade-erc20/trade-erc20-modals";
import { ERC20_CONTRACT_ABI } from "@/environments/BACKEND_FRIENDS_CONTRACT";
import { swapTokens } from "@/environments/tokens";
import { useTradeERC20 } from "@/hooks/use-trade-erc20";
import { useUser } from "@/stores";

export const Exchange = () => {
  const { user } = useUser();
  const { primaryWallet } = useDynamicContext();

  const searchParams = useSearchParams();
  const ticker = searchParams.get("ticker")?.toUpperCase();
  const threadId = searchParams.get("threadid");

  const {
    swapAvaxToToken,
    getTokenAmount,
    swapTokenToAvax,
    getAvaxAmount,
    getConversionRate,
  } = useTradeERC20();

  const requestCtxData = async (
    setCtx: Dispatch<SetStateAction<TradeERC20Context>>,
  ) => {
    if (!user) {
      throw new Error("User not specified");
    }

    if (!primaryWallet) {
      throw new Error("Dynamic wallet is not initialized");
    }

    if (!isEthereumWallet(primaryWallet)) {
      throw new Error("This wallet is not a Ethereum wallet");
    }

    if (!ticker) {
      throw new Error("Token not found");
    }

    const token = swapTokens[ticker];

    if (!token) {
      throw new Error("Token not found");
    }

    const publicClient = await primaryWallet.getPublicClient();
    const walletClient = await primaryWallet.getWalletClient();

    const bundle = await Promise.allSettled([
      fetchTokenBalance(primaryWallet.address, { symbol: "AVAX" }),
      publicClient.readContract({
        address: token.address as Hex,
        abi: ERC20_CONTRACT_ABI,
        functionName: "balanceOf",
        args: [primaryWallet.address as Hex],
      }),
      getAvaxPrice(),
      getConversionRate(token),
      getCommunityByStr({ param: token.address }),
      getThreadById(threadId),
      getSystemCurrencies(),
    ]);

    const systemRate =
      bundle[6].status === "fulfilled"
        ? bundle[6].value.currencies.find((c) => c.symbol === token.symbol)
            ?.systemRate || "0"
        : "0";

    setCtx({
      dynamicWallet: primaryWallet,
      publicClient,
      walletClient,
      token,
      tokenPrice: Number(systemRate),
      userBalance:
        bundle[0].status === "fulfilled" ? bundle[0].value.balance : 0n,
      userTokenBalance:
        bundle[1].status === "fulfilled"
          ? bundle[1].value > 4n
            ? bundle[1].value
            : 0n
          : 0n,
      avaxPrice: bundle[2].status === "fulfilled" ? bundle[2].value.avax : 0,
      conversionRate: bundle[3].status === "fulfilled" ? bundle[3].value : 0n,
      community:
        bundle[4].status === "fulfilled"
          ? bundle[4].value.community
          : undefined,
      postAuthor:
        bundle[5].status === "fulfilled"
          ? bundle[5].value?.thread?.user
          : undefined,
    });
  };

  return (
    <TradeERC20
      requestCtxData={requestCtxData}
      swapAvaxToToken={swapAvaxToToken}
      getTokenAmount={getTokenAmount}
      swapTokenToAvax={swapTokenToAvax}
      getAvaxAmount={getAvaxAmount}
      terms={() => <TradeERC20TnCs />}
    />
  );
};
