"use client";

import {
  ChangeEvent,
  FC,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";

import { useQueryClient } from "@tanstack/react-query";
import { debounce } from "lodash";
import { formatEther, parseEther } from "viem";

import { LocatedBigNumber, LocatedNumber } from "@/components/located-number";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { abbreviateNumber, cn } from "@/utils";
import { formatInputNumber } from "@/utils/format-token-price";
import { isNumber } from "@/utils/is-number";

import { updatePricesPeriod } from "./consts";
import { SlippageModal } from "./slippage-modal";
import { SlippageSetting } from "./slippage-settings";
import { GetAvaxAmountReturnType, SellTradeTabProps } from "./trade-erc20";
import { TradeERC20TokenInfo } from "./trade-erc20-token-info";

const percentage = [10, 25, 50, 75];

export const SellERC20Tab: FC<SellTradeTabProps> = ({
  ctx,
  updateCtx,
  swapTokenToAvax,
  getAvaxAmount,
  entryRequirement,
  terms,
}) => {
  const [avaxAmount, setAvaxAmount] = useState<string>("0");
  const [tokenAmount, setTokenAmount] = useState<string>("0");
  const [tokenDisplayAmount, setTokenDisplayAmount] = useState<string>("0");
  const [commission, setCommission] = useState<bigint | null>(null);
  const [isDebounce, setIsDebounce] = useState<boolean>(false);
  const [isProcess, setIsProcess] = useState<boolean>(false);
  const spanRef = useRef<HTMLSpanElement | null>(null);
  const queryClient = useQueryClient();
  const [inputWidth, setInputWidth] = useState("20px");
  const [slippage, setSlippage] = useState<number>(0.5);
  const [isSlippageModapOpen, setIsSlippageModapOpen] =
    useState<boolean>(false);

  const calculateCommission = (destAmount: string): bigint =>
    BigInt(destAmount) / 200n;

  const updateTokens = ({
    avaxAmount,
    commission,
  }: GetAvaxAmountReturnType) => {
    setAvaxAmount(avaxAmount);
    if (commission) setCommission(commission);
  };

  const setAvaxByAmountCallback = useCallback(
    debounce(async (amount: bigint) => {
      const tokens = await getAvaxAmount(ctx, amount, calculateCommission);

      updateTokens(tokens);
      setIsDebounce(false);
    }, 1000),
    [],
  );

  const handleInput = (e: ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    value = value.replace(/[^0-9.]/g, "");

    if (Number(value) > Number(formatEther(ctx.userTokenBalance)))
      value = formatEther(ctx.userTokenBalance);

    if (!isNumber(value)) value = "0";
    if (value.startsWith(".")) value = "0" + value;
    if (value.startsWith("0") && value.length > 1 && value[1] !== ".")
      value = value.substring(1);
    if (value.endsWith(".")) {
      setTokenDisplayAmount(formatInputNumber(value));
      setTokenAmount(value.slice(0, -1));
      setIsDebounce(true);
      setAvaxByAmountCallback(parseEther(value.slice(0, -1)));
      return;
    }
    if (ctx.community && !ctx.community.isLP) value = value.split(".")[0];
    setTokenDisplayAmount(formatInputNumber(value));
    setTokenAmount(value);
    setIsDebounce(true);
    setAvaxByAmountCallback(parseEther(value));
  };

  const handleMax = async () => {
    handleInput({
      target: { value: formatEther(ctx.userTokenBalance) },
    } as ChangeEvent<HTMLInputElement>);
  };

  const handlePick = (percent: number) => {
    const amount = (ctx.userTokenBalance / 100n) * BigInt(percent);
    handleInput({
      target: { value: formatEther(amount) },
    } as ChangeEvent<HTMLInputElement>);
  };

  const resetInputs = () => {
    setAvaxAmount("0");
    setTokenAmount("0");
    setTokenDisplayAmount("0");
    setCommission(null);
    queryClient.invalidateQueries({
      queryKey: ["currency", "system"],
    });
  };

  const handleSell = async () => {
    setIsProcess(true);

    let isError = false;

    try {
      await swapTokenToAvax(ctx, tokenAmount, commission, slippage);
    } catch (e) {
      console.log(e);
      isError = true;
    } finally {
      await updateCtx();
      resetInputs();
      setIsProcess(false);
      if (isError) toast.red("Transaction failed!");
      else toast.green("Successfully sold!");
    }
  };

  const handleUpdate = () =>
    handleInput({
      target: { value: tokenAmount },
    } as ChangeEvent<HTMLInputElement>);

  const handleSlippageSetup = () => setIsSlippageModapOpen(true);

  useEffect(() => {
    if (spanRef.current) setInputWidth(`${spanRef.current.offsetWidth + 12}px`);
  }, [tokenDisplayAmount]);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const fetchValues = async () => {
      if (avaxAmount === "0") return;
      const tokens = await getAvaxAmount(
        ctx,
        parseEther(tokenAmount),
        calculateCommission,
      );
      if (tokens.avaxAmount !== avaxAmount) updateTokens(tokens);
    };

    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = setInterval(fetchValues, updatePricesPeriod);

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [avaxAmount]);

  const price =
    (Number(tokenAmount) *
      ctx.avaxPrice *
      Number(ctx.community?.stats?.price || 0)) /
    10 ** 18;

  return (
    <>
      <div className="mx-[10px] flex flex-1 flex-col justify-center">
        <div className="flex flex-1 flex-col justify-center">
          <div className="mb-2 flex gap-1">
            <label className="text-[11px] font-semibold text-light-gray-text">
              BALANCE:
            </label>
            <label className="text-[11px] font-semibold text-off-white">
              {abbreviateNumber(
                Number(formatEther(ctx.userTokenBalance)),
                1,
                true,
                ["", "K", "M", "B", "T", "Q"],
              )}
            </label>
          </div>
          <div className="relative">
            <div className="flex h-[52px] items-center rounded-lg border border-[#3B3B3B] transition-colors focus-within:border-off-white">
              <div className="flex items-center pl-4">
                {ctx.token.icon ? (
                  <img
                    src={ctx.token.icon}
                    alt={ctx.token.symbol}
                    className="h-[16px] w-[16px] flex-shrink-0 rounded-full"
                  />
                ) : (
                  <div className="h-[16px] w-[16px] flex-shrink-0" />
                )}
              </div>
              <span
                ref={spanRef}
                className="invisible absolute whitespace-pre text-[16px] font-semibold leading-[20px]"
              >
                {tokenDisplayAmount || " "}
              </span>
              <div className="relative flex h-full flex-1 items-center">
                <input
                  type="text"
                  className="h-full w-full border-0 bg-transparent pl-2 text-[16px] font-semibold leading-[20px] focus:outline-none"
                  value={tokenDisplayAmount}
                  onChange={handleInput}
                />
                <span
                  className="absolute left-0 top-[19px] z-20 text-xs text-[#808080]"
                  style={{
                    marginLeft: inputWidth,
                  }}
                >
                  {`($${formatInputNumber(price.toString(), 2)})`}
                </span>
              </div>
              <button
                className="ml-auto rounded-r-lg px-4 py-2 text-[12px] font-normal leading-[20px] text-white underline"
                onClick={handleMax}
              >
                Max
              </button>
            </div>
          </div>

          <div className="flex gap-2 pt-3">
            {percentage.map((percent) => (
              <Button
                key={percent}
                variant="outline-ghost"
                onClick={() => handlePick(percent)}
                className={cn(
                  "h-[34px] p-0 px-3 text-[14px] font-normal data-[state=active]:border-brand-orange",
                  {
                    "border-brand-orange font-semibold":
                      ctx.userTokenBalance > 0n &&
                      formatEther(
                        (ctx.userTokenBalance / 100n) * BigInt(percent),
                      ) === tokenAmount,
                  },
                )}
              >
                {percent + "%"}
              </Button>
            ))}
          </div>

          <SlippageSetting
            conversionRate={ctx.conversionRate}
            symbol={ctx.token.symbol}
            handleUpdate={handleUpdate}
            handleSlippageSetup={handleSlippageSetup}
          />

          <div className="mb-2 flex gap-1">
            <label className="text-[11px] font-semibold text-light-gray-text">
              BALANCE:
            </label>
            <label className="text-[11px] font-semibold text-off-white">
              {LocatedNumber(formatEther(ctx.userBalance))}
            </label>
          </div>
          <div className="relative pb-2">
            <div className="flex h-[52px] items-center rounded-lg border border-[#3B3B3B]">
              <div className="flex items-center pl-4">
                <img
                  src="/assets/coins/avax.png"
                  alt="avax"
                  className="h-[16px] w-[16px] flex-shrink-0"
                />
              </div>
              <div className="flex items-end gap-1 border-0 bg-transparent pl-2">
                <span
                  className={cn(
                    "flex items-center leading-none",
                    isDebounce
                      ? "text-sm font-normal text-light-gray-text"
                      : "text-[16px] font-semibold",
                  )}
                >
                  {isDebounce
                    ? "calculating.."
                    : LocatedBigNumber(avaxAmount, 4)}
                </span>
              </div>
            </div>
          </div>
          {entryRequirement ? (
            entryRequirement(ctx)
          ) : (
            <div className="w-[2px] pb-12" />
          )}

          <Button
            variant="default"
            onClick={handleSell}
            loading={isProcess}
            disabled={tokenAmount === "0"}
          >{`Sell $${ctx.token.symbol.toUpperCase()}`}</Button>

          <TradeERC20TokenInfo ctx={ctx} />
        </div>
        {terms && terms()}
      </div>
      <SlippageModal
        setIsOpen={setIsSlippageModapOpen}
        isOpen={isSlippageModapOpen}
        slippage={slippage}
        setSlippage={setSlippage}
      />
    </>
  );
};
