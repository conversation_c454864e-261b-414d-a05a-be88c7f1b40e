import { useEffect, useState } from "react";

export const usePullToRefresh = (
  ref: React.RefObject<HTMLDivElement>,
  onTrigger: () => void,
  enabled: boolean,
) => {
  const [state, setState] = useState("idle");

  useEffect(() => {
    const el = ref.current;
    if (!el || !enabled) return;

    // attach the event listener
    el.addEventListener("touchstart", handleTouchStart);

    function handleTouchStart(startEvent: TouchEvent) {
      const el = ref.current;
      if (!el || !enabled) return;

      // get the initial Y position
      const initialY = startEvent.touches[0].screenY;

      el.addEventListener("touchmove", handleTouchMove);
      el.addEventListener("touchend", handleTouchEnd);

      function handleTouchMove(moveEvent: TouchEvent) {
        const el = ref.current;
        if (!el || !enabled) return;

        // get the current Y position
        const currentY = moveEvent.touches[0].clientY;

        // get the difference
        const dy = currentY - initialY;

        if (dy < 0) return;

        // now we are using the `appr` function
        el.style.transform = `translateY(${appr(dy)}px)`;

        if (dy > 150) {
          setState((old) => (old === "release" ? old : "release"));
        } else if (dy > 80) {
          setState((old) => (old === "pull" ? old : "pull"));
        } else {
          setState((old) => (old === "idle" ? old : "idle"));
        }
      }
      function handleTouchEnd(endEvent: TouchEvent) {
        const el = ref.current;
        if (!el || !enabled) return;

        // return the element to its initial position
        el.style.transform = "translateY(0)";
        setState((old) => (old === "idle" ? old : "idle"));

        // add transition
        el.style.transition = "transform 0.2s";

        // run the callback
        const y = endEvent.changedTouches[0].clientY;
        const dy = y - initialY;

        if (dy > 150) {
          onTrigger();
        }

        // listen for transition end event
        el.addEventListener("transitionend", onTransitionEnd);

        // cleanup
        el.removeEventListener("touchmove", handleTouchMove);
        el.removeEventListener("touchend", handleTouchEnd);
      }

      function onTransitionEnd() {
        const el = ref.current;
        if (!el || !enabled) return;

        // remove transition
        el.style.transition = "";

        // cleanup
        el.removeEventListener("transitionend", onTransitionEnd);
      }
    }

    return () => {
      // let's not forget to cleanup
      el.removeEventListener("touchstart", handleTouchStart);
    };
  }, [ref, enabled, onTrigger]);

  return state;
};

const MAX = 128;
const k = 0.4;
function appr(x: number) {
  return MAX * (1 - Math.exp((-k * x) / MAX));
}
