"use client";

import { useParams, useRouter } from "next/navigation";

import { ArrowBackOutlineIcon } from "@/components/icons";

export const Header = () => {
  const params = useParams() as { userHandle: string };
  const router = useRouter();

  const handleBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.replace("/" + params.userHandle);
    }
  };

  return (
    <div className="flex items-center px-6 py-4">
      <div className="flex-1">
        <button className="flex flex-shrink-0" onClick={handleBack}>
          <ArrowBackOutlineIcon className="size-5 text-off-white" />
        </button>
      </div>
      <h4 className="text-base font-semibold leading-5 text-white">
        Edit Profile
      </h4>
      <div className="flex-1" />
    </div>
  );
};
