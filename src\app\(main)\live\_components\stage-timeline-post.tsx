import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { PostUI } from "@/components/post";
import { toast } from "@/components/toast";
import {
  useBookmarkThreadMutation,
  useDeleteRepostMutation,
  useDeleteThreadMutation,
  useLikeThreadMutation,
  useRepostThreadMutation,
  useUnbookmarkThreadMutation,
  useUnlikeThreadMutation,
} from "@/queries";
import { ThreadsResponse } from "@/queries/types";
import { Thread } from "@/types";

interface StageTimelinePostProps {
  thread: Thread;
}

export const StageTimelinePost = ({ thread }: StageTimelinePostProps) => {
  const isRepost = thread.threadType === "repost";
  const activePost = isRepost && thread.repost ? thread.repost : thread;
  const queryClient = useQueryClient();

  const { mutateAsync: repost } = useRepostThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "stages-feed"],
      });

      const previousStagesFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "stages-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.reposted) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount + 1,
                          reposted: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount + 1,
                      reposted: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousStagesFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        context?.previousStagesFeed,
      );
    },
  });
  const { mutateAsync: deleteRepost } = useDeleteRepostMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "stages-feed"],
      });
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "trending-feed"],
      });

      const previousStagesFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "stages-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.reposted) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount - 1,
                          reposted: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount - 1,
                      reposted: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousStagesFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        context?.previousStagesFeed,
      );
    },
  });
  const { mutateAsync: like } = useLikeThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "stages-feed"],
      });

      const previousStagesFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "stages-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.like) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount + 1,
                          like: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && !t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount + 1,
                      like: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousStagesFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        context?.previousStagesFeed,
      );
    },
  });
  const { mutateAsync: unlike } = useUnlikeThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "stages-feed"],
      });

      const previousStagesFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "stages-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.like) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount - 1,
                          like: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount - 1,
                      like: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousStagesFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        context?.previousMyFeed,
      );
    },
  });
  const { mutateAsync: bookmark } = useBookmarkThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "stages-feed"],
      });

      const previousStagesFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "stages-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.bookmark) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount + 1,
                          bookmark: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && !t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount + 1,
                      bookmark: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousStagesFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        context?.previousStagesFeed,
      );
    },
  });
  const { mutateAsync: unbookmark } = useUnbookmarkThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "stages-feed"],
      });

      const previousStagesFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "stages-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.bookmark) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount - 1,
                          bookmark: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount - 1,
                      bookmark: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousStagesFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        context?.previousStagesFeed,
      );
    },
  });
  const { mutateAsync: deleteThread } = useDeleteThreadMutation({
    onMutate: async ({ threadId }) => {
      toast.red("Post deleted");
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "stages-feed"],
      });

      const previousStagesFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "stages-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.filter((t) => t.id !== threadId),
              };
            }),
          };
        },
      );

      return { previousStagesFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "stages-feed"],
        context?.previousStagesFeed,
      );
    },
  });

  const handleLike = async ({ threadId }: { threadId: string }) => {
    if (activePost.like) {
      await unlike({ threadId });
    } else {
      await like({ threadId });
    }
  };

  const handleBookmark = async ({ threadId }: { threadId: string }) => {
    if (activePost.bookmark) {
      await unbookmark({ threadId });
    } else {
      await bookmark({ threadId });
    }
  };

  const handleRepost = async ({ threadId }: { threadId: string }) => {
    if (activePost.reposted) {
      await deleteRepost({ threadId });
    } else {
      await repost({ threadId });
    }
  };

  const handleDelete = async ({ threadId }: { threadId: string }) => {
    await deleteThread({ threadId });
  };

  return (
    <PostUI
      thread={thread}
      handleLike={handleLike}
      handleBookmark={handleBookmark}
      handleRepost={handleRepost}
      handleDelete={handleDelete}
    />
  );
};
