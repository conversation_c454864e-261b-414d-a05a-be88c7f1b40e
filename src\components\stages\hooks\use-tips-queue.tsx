import { useCallback, useEffect, useRef, useState } from "react";

import { useStageStore } from "@/stores/stage";

interface Tip {
  currency: string;
  amount: number;
  from: {
    name: string;
    avatar: string;
    username: string;
  };
  id: string;
}

export function useTipQueue(userId: string, duration = 5000) {
  const [currentTip, setCurrentTip] = useState<Tip | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const queue = useStageStore((state) => state.users[userId]?.tips ?? []);
  const setQueue = useStageStore((state) => state.actions.setTips);

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const processNextTip = useCallback(() => {
    setQueue(userId, (prevQueue) => {
      if (prevQueue.length === 0) {
        setCurrentTip(null);
        setIsProcessing(false);
        return prevQueue;
      }

      const [nextTip, ...rest] = prevQueue;
      setCurrentTip(nextTip);
      setIsProcessing(true);

      return rest;
    });
  }, [userId, setQueue]);

  const addTip = useCallback(
    (tip: Tip) => {
      setQueue(userId, (prevQueue) => [...prevQueue, tip]);
      if (!isProcessing) {
        processNextTip();
      }
    },
    [isProcessing, processNextTip, userId, setQueue],
  );

  useEffect(() => {
    if (isProcessing) {
      timeoutRef.current = setTimeout(() => {
        processNextTip();
      }, duration);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isProcessing, currentTip, duration, processNextTip]);

  useEffect(() => {
    if (queue.length > 0 && !isProcessing) {
      processNextTip();
    }
  }, [isProcessing, processNextTip, queue.length]);

  return {
    queue,
    currentTip,
    addTip,
    isProcessing,
  };
}
