import Link from "next/link";

import { CogOutlineIcon } from "@/components/icons";
import { But<PERSON> } from "@/components/ui/button";

import { NotificationsList } from "./_components/notifications-list";

function NotificationsPage() {
  return (
    <div className="pt-pwa pb-pwa flex h-full flex-col">
      <div className="mt-[18px] flex items-center justify-between px-6 pb-5 lg:mt-10">
        <h1 className="text-[26px] font-semibold leading-[30px] text-white">
          Notifications
        </h1>
        <Button variant="ghost" size="icon">
          <Link href="/settings/notifications">
            <CogOutlineIcon className="size-6 text-off-white" />
          </Link>
        </Button>
      </div>
      <div className="mt-4 flex-grow">
        <NotificationsList />
      </div>
    </div>
  );
}

export default NotificationsPage;
