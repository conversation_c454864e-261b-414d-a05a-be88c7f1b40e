"use client";

import { useRouter } from "next/navigation";

import { motion, useTransform } from "framer-motion";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { useBoundedScroll } from "@/hooks";

export const Header = () => {
  const router = useRouter();

  const { scrollYBoundedProgress } = useBoundedScroll(300);
  const scrollYBoundedProgressDelayed = useTransform(
    scrollYBoundedProgress,
    [0, 0.3, 1],
    [0, 1, 1],
  );

  return (
    <motion.div
      className="sticky top-0 z-50 flex items-center justify-between border-b border-dark-gray bg-dark-bk/65 px-6 pb-4 pt-[calc(1rem+env(safe-area-inset-top))] backdrop-blur-md"
      style={{
        y: useTransform(scrollYBoundedProgressDelayed, [0, 1], ["0%", "-100%"]),
      }}
    >
      <div className="flex flex-1 items-center">
        <button
          onClick={() => {
            if (window.history.length > 1) {
              router.back();
            } else {
              router.push("/home");
            }
          }}
        >
          <ArrowBackOutlineIcon className="size-5" />
        </button>
      </div>
      <div className="text-base font-semibold leading-5">Post</div>
      <div className="flex-1" />
    </motion.div>
  );
};
