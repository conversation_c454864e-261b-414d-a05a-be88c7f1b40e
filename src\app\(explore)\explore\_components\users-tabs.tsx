"use client";

import { ChangeEvent, FC, useEffect, useRef, useState } from "react";

import { motion, useMotionValue } from "framer-motion";
import { Virtuoso } from "react-virtuoso";

import { TrendingProfiles } from "@/app/_components/trending-profiles";
import { CloseOutlineIcon, SearchFilledIcon } from "@/components/icons";
import { Input } from "@/components/ui/input";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import useThrottle from "@/hooks/use-throttle";
import { useUsersSearchQuery } from "@/queries";
import { TradesUsersTrendingResponse } from "@/queries/types";
import { cn } from "@/utils";

import { ActivityTab } from "./activity-tab";
import { BadgesTab } from "./badges-tab";
import { NewUsersTab } from "./new-users-tab";
import { TopUsersTab } from "./top-users-tab";
import { UserItem } from "./user-item";
import { UserItemSkeleton } from "./user-item-skeleton";

type UsersTabsProps = {
  trendingUsersData?: TradesUsersTrendingResponse;
  isTrendingUsersLoading: boolean;
};

export const UsersTabs: FC<UsersTabsProps> = ({
  trendingUsersData,
  isTrendingUsersLoading,
}) => {
  const isLargeScreen = useMediaQuery(BREAKPOINTS.lg);
  const [mounted, setMounted] = useState(false);

  const tabRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLInputElement>(null);

  const [currentTab, setCurrentTab] = useState<string>("top");
  const [searchValue, setSearchValue] = useState<string>("");
  const throttledSearchValue = useThrottle(searchValue);

  const backgroundColor = useMotionValue("rgb(20 20 20 / 0)");
  const [isSticked, setIsSticked] = useState(false);
  const [noDuration, setNoDuration] = useState(false);

  const { data: searchData, isLoading: isSearchDataLoading } =
    useUsersSearchQuery(throttledSearchValue);

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleTabChange = (value: string) => {
    setCurrentTab(value);
    setNoDuration(true);
    setTimeout(() => {
      setNoDuration(false);
    }, 300);
  };

  useEffect(() => {
    const handleScroll = () => {
      if (isLargeScreen) return;
      const topSafeArea = +getComputedStyle(document.documentElement)
        .getPropertyValue("--sat")
        .replace("px", "");

      if (
        tabRef.current &&
        tabRef.current.getBoundingClientRect().top <= 68 + topSafeArea
      ) {
        backgroundColor.set("rgb(20 20 20 / 0.88)");
        setIsSticked(true);
      } else {
        backgroundColor.set("rgb(20 20 20 / 0)");
        setIsSticked(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [backgroundColor]);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  if (isLargeScreen) {
    return (
      <Tabs
        value={currentTab}
        onValueChange={handleTabChange}
        ref={tabRef}
        className="flex-grow"
      >
        <div className="sticky top-[calc(env(safe-area-inset-top,0px))] z-10 w-full bg-[rgba(20,20,20,0.88)] pt-6 backdrop-blur-[9px]">
          <div className={cn("px-6", throttledSearchValue && "pb-5")}>
            <div className="relative">
              <Input
                value={searchValue}
                onChange={handleSearch}
                placeholder="Search the Arena"
                className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-10 ring-0  placeholder:text-gray-text"
              />
              <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
              {searchValue && (
                <button className="absolute right-[10px] top-1/2 -translate-y-1/2 rounded-full border border-dark-gray bg-[rgba(20,20,20,0.88)] p-1">
                  <CloseOutlineIcon
                    className="pointer-events-auto size-[14px] select-none text-off-white"
                    onClick={() => setSearchValue("")}
                  />
                </button>
              )}
            </div>
          </div>
          {!throttledSearchValue && (
            <TabsList className="mt-3 w-full justify-evenly">
              <TabsTrigger value="top">Top</TabsTrigger>
              <TabsTrigger value="new">New</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
              <TabsTrigger value="badges">Badges</TabsTrigger>
            </TabsList>
          )}
        </div>
        {!throttledSearchValue && (
          <>
            <TabsContent value="top" className="h-[calc(100%-139px)] py-6">
              <TopUsersTab />
            </TabsContent>
            <TabsContent value="new" className="h-[calc(100%-139px)] py-2">
              <NewUsersTab />
            </TabsContent>
            <TabsContent value="activity" className="h-[calc(100%-139px)] py-2">
              <ActivityTab />
            </TabsContent>
            <TabsContent value="badges" className="h-[calc(100%-139px)] py-3">
              <BadgesTab />
            </TabsContent>
          </>
        )}
        {throttledSearchValue && isSearchDataLoading && (
          <div className="mt-2">
            {Array(9)
              .fill(null)
              .map((_, index) => (
                <UserItemSkeleton key={index} />
              ))}
          </div>
        )}
        {throttledSearchValue &&
          !isSearchDataLoading &&
          searchData &&
          searchData.users.length > 0 && (
            <div className="mt-2">
              <Virtuoso
                useWindowScroll
                data={searchData.users || []}
                overscan={200}
                itemContent={(index, user) => {
                  return <UserItem user={user} />;
                }}
              />
            </div>
          )}
        {throttledSearchValue &&
          !isSearchDataLoading &&
          searchData &&
          searchData.users.length === 0 && (
            <div className="mt-10 flex w-full items-center justify-center">
              <div className="max-w-64 text-center">
                <h4 className="text-sm font-semibold text-[#EDEDED]">
                  No users found!
                </h4>
              </div>
            </div>
          )}
      </Tabs>
    );
  }

  return (
    <>
      <div className="px-6 pt-[23px]">
        <div className="relative">
          <Input
            ref={searchRef}
            value={searchValue}
            onChange={handleSearch}
            placeholder="Search the Arena"
            className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-10 placeholder:text-gray-text"
          />
          <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
          {searchValue && (
            <button className="absolute right-[10px]  top-1/2 -translate-y-1/2 rounded-full border border-dark-gray bg-dark-bk p-1">
              <CloseOutlineIcon
                className="pointer-events-auto size-[14px] select-none text-off-white"
                onClick={() => setSearchValue("")}
              />
            </button>
          )}
        </div>
      </div>
      {throttledSearchValue && isSearchDataLoading && (
        <div className="mt-2">
          {Array(9)
            .fill(null)
            .map((_, index) => (
              <UserItemSkeleton key={index} />
            ))}
        </div>
      )}
      {throttledSearchValue &&
        !isSearchDataLoading &&
        searchData &&
        searchData.users.length > 0 && (
          <div className="mt-2">
            <Virtuoso
              useWindowScroll
              data={searchData.users || []}
              overscan={200}
              itemContent={(index, user) => {
                return <UserItem user={user} />;
              }}
            />
          </div>
        )}
      {throttledSearchValue &&
        !isSearchDataLoading &&
        searchData &&
        searchData.users.length === 0 && (
          <div className="mt-10 flex w-full items-center justify-center">
            <div className="max-w-64 text-center">
              <h4 className="text-sm font-semibold text-[#EDEDED]">
                No users found!
              </h4>
            </div>
          </div>
        )}
      {!throttledSearchValue && (
        <>
          <motion.div
            className={cn("relative z-20 mt-5", isSticked && "select-none")}
            animate={{
              y: isSticked ? -81 : 0,
              visibility: isSticked ? "hidden" : "visible",
            }}
            transition={{
              ease: "linear",
              duration: noDuration ? 0 : 0.2,
              visibility: {
                delay: noDuration ? 0 : isSticked ? 0.2 : 0,
              },
            }}
          >
            <TrendingProfiles
              users={trendingUsersData?.users || []}
              isLoading={isTrendingUsersLoading}
            />
          </motion.div>
          <Tabs
            value={currentTab}
            onValueChange={handleTabChange}
            ref={tabRef}
            className="flex-grow"
          >
            <motion.div
              className="sticky top-0 z-10 mt-[calc(-68px-env(safe-area-inset-top))] w-full pt-[calc(81px+env(safe-area-inset-top))] shadow-[0px_-5px_14px_20px_rgba(0,0,0,0.25)] backdrop-blur-[9px]"
              style={{ backgroundColor }}
              transition={{
                ease: "linear",
                duration: noDuration ? 0 : 0.1,
              }}
            >
              <motion.div
                className="absolute inset-x-0 top-[calc(23px+env(safe-area-inset-top))] overflow-hidden px-6 opacity-0"
                animate={{
                  opacity: isSticked ? 1 : 0,
                }}
                transition={{
                  ease: "easeInOut",
                  duration: noDuration ? 0 : 0.1,
                }}
              >
                <div className="relative">
                  <Input
                    onClick={() => {
                      setNoDuration(true);
                      scrollTo({
                        top: 0,
                      });
                      searchRef.current?.focus();
                      setTimeout(() => {
                        setNoDuration(false);
                      }, 300);
                    }}
                    value={searchValue}
                    onChange={handleSearch}
                    placeholder="Search the Arena"
                    className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-5 placeholder:text-gray-text focus-visible:ring-0"
                  />
                  <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
                </div>
              </motion.div>
              <TabsList className="w-full justify-evenly">
                <TabsTrigger value="top">Top</TabsTrigger>
                <TabsTrigger value="new">New</TabsTrigger>
                <TabsTrigger value="activity">Activity</TabsTrigger>
                <TabsTrigger value="badges">Badges</TabsTrigger>
              </TabsList>
            </motion.div>
            <TabsContent value="top" className="h-full py-6">
              <TopUsersTab />
            </TabsContent>
            <TabsContent value="new" className="h-full py-2">
              <NewUsersTab />
            </TabsContent>
            <TabsContent value="activity" className="h-full py-2">
              <ActivityTab />
            </TabsContent>
            <TabsContent value="badges" className="h-full py-3">
              <BadgesTab />
            </TabsContent>
          </Tabs>
        </>
      )}
    </>
  );
};
