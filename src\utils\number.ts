export function formatNumericValue(value: string, maxFractionDigits?: number) {
  if (!value) return "";

  // Remove any non-numeric characters except dots
  let cleanValue = value.replace(/[^\d.]/g, "");

  // If value starts with a dot, add 0 before it
  if (cleanValue.startsWith(".")) {
    cleanValue = "0" + cleanValue;
  }

  // Ensure only one dot exists
  const parts = cleanValue.split(".");
  if (parts.length > 1) {
    cleanValue = parts[0] + "." + parts.slice(1).join("");
  }

  if (cleanValue.includes(".")) {
    let [integerPart, decimalPart] = cleanValue.split(".");

    if (integerPart) {
      const numericValue = parseFloat(integerPart);
      if (!isNaN(numericValue)) {
        integerPart = new Intl.NumberFormat("en-US", {
          useGrouping: true,
          maximumFractionDigits: 0,
        }).format(numericValue);
      }
    }

    if (decimalPart) {
      const intValue = parseInt(integerPart.replace(/,/g, ""));
      if (isNaN(intValue)) {
        return "";
      }

      if (maxFractionDigits) {
        decimalPart = decimalPart.slice(0, maxFractionDigits);
      } else {
        decimalPart =
          intValue > 0
            ? decimalPart.slice(0, 2) // Larger numbers: 2 decimal places
            : decimalPart.slice(0, 4); // Smaller numbers: 4 decimal places
      }
    }

    return `${integerPart}.${decimalPart}`;
  }

  const numericValue = parseFloat(cleanValue);
  if (isNaN(numericValue)) return "";

  const formatted =
    numericValue > 0
      ? largeNumberFormatter.format(numericValue)
      : smallNumberFormatter.format(numericValue);

  return formatted;
}

export function parseFormattedValue(value?: string) {
  if (!value) return 0;

  const formatted = value.replace(/,/g, "");
  return parseFloat(formatted);
}

/**
 * Handles numeric input with cursor position management
 * @param input The input value
 * @param cursorPosition Current cursor position
 * @returns Object with formatted value and new cursor position
 */
export function handleNumericInput(
  input: string,
  cursorPosition: number,
  maxFractionDigits?: number,
): { value: string; cursorPosition: number } {
  let value = input;
  let newPosition = cursorPosition;
  const lastChar = value.charAt(cursorPosition - 1);

  // Handle comma conversion
  if (lastChar === ",") {
    if (!value.includes(".")) {
      // Replace the comma with a dot
      value =
        value.substring(0, cursorPosition - 1) +
        "." +
        value.substring(cursorPosition);
    } else {
      // If dot already exists, remove the comma
      value =
        value.substring(0, cursorPosition - 1) +
        value.substring(cursorPosition);
      // Adjust cursor position back by one since we removed a character
      newPosition = cursorPosition - 1;
    }
  }
  // Handle dot input
  if (lastChar === ".") {
    // Check if there's already a dot in the value (excluding the one we just added)
    const valueBeforeCursor = value.substring(0, cursorPosition - 1);
    const valueAfterCursor = value.substring(cursorPosition);

    if (valueBeforeCursor.includes(".") || valueAfterCursor.includes(".")) {
      // If another dot exists, remove the one we just added
      value =
        value.substring(0, cursorPosition - 1) +
        value.substring(cursorPosition);
      // Adjust cursor position back by one since we removed a character
      newPosition = cursorPosition - 1;
    }
  }

  value = value.replace(/,/g, "");

  // Apply full formatting
  const formattedValue = formatNumericValue(value, maxFractionDigits);

  // Adjust cursor position if formatting changed the string length
  const lengthDifference = formattedValue.length - value.length;
  newPosition += lengthDifference;

  // Ensure cursor position is within valid range
  newPosition = Math.max(0, Math.min(newPosition, formattedValue.length));

  return {
    value: formattedValue,
    cursorPosition: newPosition,
  };
}

const largeNumberFormatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 2,
});

const smallNumberFormatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 4,
});
