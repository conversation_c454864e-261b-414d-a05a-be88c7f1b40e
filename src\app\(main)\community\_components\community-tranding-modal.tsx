import { useState } from "react";

import { InformationCircleOutlineIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

export const CommunityTrandingModal = () => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [open, setOpen] = useState(false);

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <div className="mt-auto flex w-full justify-center">
          <DialogTrigger asChild>
            <Button
              variant="outline"
              className="mb-4 mt-auto flex items-center gap-[10px] border-none text-off-white"
            >
              <InformationCircleOutlineIcon className="size-6" />
              <span className="text-sm font-semibold underline">
                How do I claim my locked tokens?
              </span>
            </Button>
          </DialogTrigger>
        </div>
        <DialogContent className="gap-4">
          <CommunityTrandingModalContent />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button
          variant="outline"
          className="mb-4 mt-auto flex w-full items-center gap-[10px] border-none text-off-white"
        >
          <InformationCircleOutlineIcon className="size-6" />
          <span className="text-sm font-semibold underline">
            How do I claim my locked tokens?
          </span>
        </Button>
      </DrawerTrigger>
      <DrawerContent className="gap-4">
        <CommunityTrandingModalContent />
      </DrawerContent>
    </Drawer>
  );
};

const CommunityTrandingModalContent = () => (
  <>
    <h3 className="text-base font-semibold leading-[22px] text-off-white">
      How to Claim Your Locked Tokens
    </h3>
    <p className="text-sm text-[#B5B5B5]">
      To unlock 100% of your airdrop, you will need to be an active user in the
      Arena. Your eligibility will be based on various activities, including but
      not limited to:
    </p>
    <ul className="mt-2 list-inside list-disc pl-2 text-sm text-[#B5B5B5]">
      <li>Arena Token Staking and Other Usage</li>
      <li>Portfolio Value</li>
      <li>Trading Volume</li>
      <li>Content Contributions (Timeline & Chatroom)</li>
      <li>Referrals</li>
    </ul>
    <p className="mt-2 text-sm text-[#B5B5B5]">
      We compare some metrics across users to ensure fairness. As The Arena
      evolves, new features and activities will also contribute to your score.
    </p>
    <p className="mt-2 text-sm text-[#B5B5B5]">
      The airdrop is designed to reward users who actively add value to the
      Arena, with an adaptive algorithm that recognizes and supports those
      driving the platform&apos;s growth.
    </p>
  </>
);
