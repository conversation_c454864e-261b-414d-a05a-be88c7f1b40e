import { FC } from "react";

import { SystemCurrencyCategoryEnum } from "@/api/client/admin-currency";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

export interface CategoryCheckboxesProps {
  categories: Record<SystemCurrencyCategoryEnum, boolean>;
  onCategoryChange: (
    category: SystemCurrencyCategoryEnum,
    value: boolean,
  ) => void;
}

export const CategoryCheckboxes: FC<CategoryCheckboxesProps> = ({
  categories,
  onCategoryChange,
}) => (
  <div>
    <Label className="mb-2 block text-xs font-medium uppercase text-off-white">
      INCLUDE PROJECT IN
    </Label>
    <div className="space-y-3">
      <div className="flex items-center justify-between rounded p-1">
        <Label
          htmlFor="supported-coins"
          className="text-sm font-normal capitalize text-gray-text"
        >
          Supported coins
        </Label>
        <Checkbox
          id="supported-coins"
          checked={categories[SystemCurrencyCategoryEnum.SUPPORTED]}
          onCheckedChange={(checked) => {
            onCategoryChange(
              SystemCurrencyCategoryEnum.SUPPORTED,
              checked as boolean,
            );
          }}
          className="size-5 rounded-full border-gray-text data-[state=checked]:border-none"
        />
      </div>

      <div className="flex items-center justify-between rounded p-1">
        <Label
          htmlFor="tipping-coins"
          className="text-sm font-normal capitalize text-gray-text"
        >
          Tipping coins
        </Label>
        <Checkbox
          id="tipping-coins"
          checked={categories[SystemCurrencyCategoryEnum.TIPPING]}
          onCheckedChange={(checked) => {
            onCategoryChange(
              SystemCurrencyCategoryEnum.TIPPING,
              checked as boolean,
            );
          }}
          disabled={!categories[SystemCurrencyCategoryEnum.SUPPORTED]}
          className="size-5 rounded-full border-gray-text data-[state=checked]:border-none"
        />
      </div>

      <div className="flex items-center justify-between rounded p-1">
        <Label
          htmlFor="social-exchange"
          className="text-sm font-normal capitalize text-gray-text"
        >
          Social exchange
        </Label>
        <Checkbox
          id="social-exchange"
          checked={categories[SystemCurrencyCategoryEnum.EXCHANGE]}
          onCheckedChange={(checked) => {
            onCategoryChange(
              SystemCurrencyCategoryEnum.EXCHANGE,
              checked as boolean,
            );
          }}
          disabled={!categories[SystemCurrencyCategoryEnum.SUPPORTED]}
          className="size-5 rounded-full border-gray-text data-[state=checked]:border-none"
        />
      </div>
    </div>
  </div>
);
