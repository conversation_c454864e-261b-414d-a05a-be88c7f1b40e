import axios, { AxiosRequestConfig } from "axios";

import { env } from "@/env";

const https = typeof window === "undefined" ? require("https") : null;

const instance = axios.create({
  baseURL: env.NEXT_PUBLIC_API_URL,
});

const createKeepAliveAgent = () => {
  return https
    ? new https.Agent({
        keepAlive: true,
        maxSockets: 100,
        maxFreeSockets: 10,
        keepAliveMsecs: 30000,
      })
    : undefined;
};

export const withKeepAlive = (config: AxiosRequestConfig = {}) => {
  const httpsAgent = createKeepAliveAgent();

  return {
    ...config,
    ...(httpsAgent && { httpsAgent }),
    proxy: false,
  };
};

export { instance as axios };
