"use client";

import { useLivestreamStore } from "@/stores/livestream";

import { NewWindow } from "../new-window";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "../ui/tabs";
import { LivestreamChatContainer } from "./livestream-chat";
import { SupportersContainer } from "./supporters-container";
import { TipCards } from "./tip-cards";

export const PoppedOutRightPanel = () => {
  const isOpenNewWindow = useLivestreamStore(
    (state) => state.chat.isOpenInNewWindow,
  );
  const actions = useLivestreamStore((state) => state.actions);

  if (!isOpenNewWindow) {
    return null;
  }

  return (
    <NewWindow
      onUnload={() => {
        actions.setChatOpenInNewWindow(false);
      }}
      features={{
        width: "400px",
        height: "800px",
      }}
    >
      <div className="flex h-full flex-col">
        <TipCards />
        <Tabs defaultValue="chat" className="flex flex-grow flex-col">
          <TabsList className="w-full">
            <TabsTrigger
              value="chat"
              className="flex-grow data-[state=active]:after:bg-purple"
            >
              Chat
            </TabsTrigger>
            <TabsTrigger
              value="supporters"
              className="flex-grow data-[state=active]:after:bg-purple"
            >
              Supporters
            </TabsTrigger>
          </TabsList>
          <TabsContent value="chat" className="flex-grow">
            <LivestreamChatContainer />
          </TabsContent>
          <TabsContent value="supporters" className="flex-grow">
            <SupportersContainer />
          </TabsContent>
        </Tabs>
      </div>
    </NewWindow>
  );
};
