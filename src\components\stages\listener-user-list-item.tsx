"use client";

import { useState } from "react";

import {
  useLocalParticipant,
  useParticipantAttribute,
  useParticipantAttributes,
} from "@livekit/components-react";
import { LocalParticipant, RemoteParticipant } from "livekit-client";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import {
  useBlockStageUserMutation,
  useCancelInvitationMutation,
  useInviteStageMutation,
} from "@/queries";
import { useStageStore } from "@/stores/stage";

import { BanOutlineIcon, MicOutlineIcon } from "../icons";
import { UsersOutlineIcon } from "../icons/users-outline";
import { Button } from "../ui/button";
import { Drawer, DrawerContent, DrawerTrigger } from "../ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { BlockStageUserModal } from "./block-stage-user-modal";
import { InvitedRole, ROLES } from "./constants";
import { useDataChannelsContext } from "./stores/data-channels-context";
import { ParticipantListItem } from "./user-list-item";

export const ListenerUserListItem = ({
  participant,
  invited,
}: {
  participant: RemoteParticipant | LocalParticipant;
  invited?: InvitedRole;
}) => {
  const local = useLocalParticipant();
  const role = useParticipantAttribute("role", {
    participant: local.localParticipant,
  });
  const { attributes } = useParticipantAttributes({ participant });
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const currentUser = {
    id: attributes?.id ?? "",
    name: attributes?.name ?? "",
    avatar: attributes?.avatar ?? "",
    username: attributes?.username ?? "",
    role: attributes?.role ?? "",
  };

  const [open, setOpen] = useState(false);
  const [isBlockOpen, setIsBlockOpen] = useState(false);
  const id = useStageStore((state) => state.id!);

  const { sendInvalidateStageInfo } = useDataChannelsContext();

  const { mutateAsync: invite, isPending: isInvitePending } =
    useInviteStageMutation({
      onSuccess: () => {
        sendInvalidateStageInfo();
      },
    });

  const { mutateAsync: cancelInvitation, isPending: isCancelInvitePending } =
    useCancelInvitationMutation({
      onSuccess: () => {
        sendInvalidateStageInfo();
      },
    });

  const { mutateAsync: blockUser, isPending: isBlockUserPending } =
    useBlockStageUserMutation({
      onSuccess: () => {
        sendInvalidateStageInfo();
      },
    });

  const isPending =
    isInvitePending || isCancelInvitePending || isBlockUserPending;

  const handleUpgradeToCohost = async () => {
    setOpen(false);

    await invite({
      stageId: id,
      invitedUserId: currentUser.id,
      roleType: "COHOST",
    });
  };

  const handleUpgradeToSpeaker = async () => {
    setOpen(false);

    await invite({
      stageId: id,
      invitedUserId: currentUser.id,
      roleType: "SPEAKER",
    });
  };

  const handleCancelInvite = async () => {
    setOpen(false);

    await cancelInvitation({
      stageId: id,
      invitedUserId: currentUser.id,
    });
  };

  const handleBlockUser = async () => {
    setOpen(false);
    setIsBlockOpen(true);
  };

  return (
    <>
      <ParticipantListItem user={currentUser}>
        {isTablet && (
          <DropdownMenu open={open} onOpenChange={setOpen}>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="h-[34px] w-28">
                Actions
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[220px]">
              {role === ROLES.HOST && (
                <>
                  {invited === "COHOST" ? (
                    <DropdownMenuItem className="w-full gap-4" asChild>
                      <button onClick={handleCancelInvite} disabled={isPending}>
                        <UsersOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                        <span>Cancel invitation</span>
                      </button>
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem className="w-full gap-4" asChild>
                      <button
                        onClick={handleUpgradeToCohost}
                        disabled={isPending}
                      >
                        <UsersOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                        <span>Invite to Co-Host</span>
                      </button>
                    </DropdownMenuItem>
                  )}
                </>
              )}
              {invited === "SPEAKER" ? (
                <DropdownMenuItem className="w-full gap-4" asChild>
                  <button onClick={handleCancelInvite} disabled={isPending}>
                    <MicOutlineIcon
                      className="size-5 flex-shrink-0 text-gray-text"
                      strokeWidth={42}
                    />
                    <span>Cancel invitation</span>
                  </button>
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem className="w-full gap-4" asChild>
                  <button onClick={handleUpgradeToSpeaker} disabled={isPending}>
                    <MicOutlineIcon
                      className="size-5 flex-shrink-0 text-gray-text"
                      strokeWidth={42}
                    />
                    <span>Invite to Speak</span>
                  </button>
                </DropdownMenuItem>
              )}
              {role === ROLES.HOST || role === ROLES.COHOST ? (
                <DropdownMenuItem className="w-full gap-4" asChild>
                  <button onClick={handleBlockUser} disabled={isPending}>
                    <BanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Remove</span>
                  </button>
                </DropdownMenuItem>
              ) : null}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        {!isTablet && (
          <Drawer open={open} onOpenChange={setOpen}>
            <DrawerTrigger asChild>
              <Button variant="outline" className="h-[34px] w-28">
                Actions
              </Button>
            </DrawerTrigger>
            <DrawerContent className="px-4 pt-4">
              {role === ROLES.HOST && (
                <>
                  {invited === "COHOST" ? (
                    <button
                      className="flex items-center gap-2 p-2 text-base leading-5"
                      onClick={handleCancelInvite}
                      disabled={isPending}
                    >
                      <UsersOutlineIcon className="size-6 text-gray-text" />{" "}
                      Cancel invitation
                    </button>
                  ) : (
                    <button
                      className="flex items-center gap-2 p-2 text-base leading-5"
                      onClick={handleUpgradeToCohost}
                      disabled={isPending}
                    >
                      <UsersOutlineIcon
                        className="size-6 text-gray-text"
                        strokeWidth={1.5}
                      />{" "}
                      Invite to Co-Host
                    </button>
                  )}
                </>
              )}
              {invited === "SPEAKER" ? (
                <button
                  className="flex items-center gap-2 p-2 text-base leading-5"
                  onClick={handleCancelInvite}
                  disabled={isPending}
                >
                  <MicOutlineIcon className="size-6 text-gray-text" /> Cancel
                  invitation
                </button>
              ) : (
                <button
                  className="flex items-center gap-2 p-2 text-base leading-5"
                  onClick={handleUpgradeToSpeaker}
                  disabled={isPending}
                >
                  <MicOutlineIcon className="size-6 text-gray-text" /> Invite to
                  Speak
                </button>
              )}
              {role === ROLES.HOST || role === ROLES.COHOST ? (
                <button
                  className="flex items-center gap-2 p-2 text-base leading-5"
                  onClick={handleBlockUser}
                  disabled={isPending}
                >
                  <BanOutlineIcon className="size-6 text-gray-text" /> Remove
                </button>
              ) : null}
            </DrawerContent>
          </Drawer>
        )}
      </ParticipantListItem>
      <BlockStageUserModal
        open={isBlockOpen}
        setOpen={setIsBlockOpen}
        user={currentUser}
        stageId={id}
      />
    </>
  );
};
