"use client";

import { useState } from "react";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import CopyToClipboard from "react-copy-to-clipboard";

import {
  livestreamQueries,
  useEndLivestreamMutation,
  useGenerateLivestreamIngressMutation,
} from "@/queries";
import { useLivestreamStore } from "@/stores/livestream";

import { CogOutlineIcon, CopyOutlineIcon, XCircleOutlineIcon } from "../icons";
import { toast } from "../toast";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { Label } from "../ui/label";
import { TextInput } from "../ui/text-input";
import { CameraButton, MicButton, ScreenShareButton } from "./control-buttons";
import { EndLivestreamConfirmationModal } from "./end-livestream-confirmation-modal";

export default function ControlsBar() {
  const id = useLivestreamStore((state) => state.id!);

  const { data: livestream } = useQuery(
    livestreamQueries.livestreamSimpleInfo(id),
  );

  if (livestream?.livestream.type === "EASY") {
    return <EasyControlsBar />;
  }

  return <ProControlsBar />;
}

function EasyControlsBar() {
  const id = useLivestreamStore((state) => state.id!);
  const actions = useLivestreamStore((state) => state.actions);
  const { mutateAsync: endLivestream } = useEndLivestreamMutation({
    onSuccess: () => {
      actions.reset();
    },
  });

  const handleEndLivestream = async () => {
    await endLivestream({
      livestreamId: id,
    });
  };

  return (
    <div className="mt-6 flex items-start justify-between gap-4 rounded-xl border border-[rgba(59,59,59,0.75)] bg-chat-bubble p-4">
      <div className="flex items-center gap-4">
        <MicButton />
        <CameraButton />
        <ScreenShareButton />
      </div>
      <EndLivestreamConfirmationModal onConfirm={handleEndLivestream}>
        <button className="flex items-center gap-1 rounded border border-danger bg-[#2A2A2A] px-4 py-2 text-sm font-semibold text-off-white">
          <XCircleOutlineIcon className="size-5 text-off-white" />
          <span className="">End Stream</span>
        </button>
      </EndLivestreamConfirmationModal>
    </div>
  );
}

function ProControlsBar() {
  const id = useLivestreamStore((state) => state.id!);
  const actions = useLivestreamStore((state) => state.actions);
  const { mutateAsync: endLivestream } = useEndLivestreamMutation({
    onSuccess: () => {
      actions.reset();
    },
  });

  const handleEndLivestream = async () => {
    await endLivestream({
      livestreamId: id,
    });
  };

  return (
    <div className="mt-6 flex items-center justify-between gap-4 rounded-xl border border-[rgba(59,59,59,0.75)] bg-chat-bubble p-4">
      <div className="flex items-center gap-4">
        <p>
          <strong>Pro Live Stream:</strong> Control this stream through your
          streaming software.
        </p>
      </div>
      <div className="flex items-center gap-2">
        <GenerateIngressModal>
          <button className="flex items-center gap-1 rounded border border-gray-text p-2 text-sm font-semibold text-off-white hover:bg-[#2A2A2A]">
            <CogOutlineIcon className="size-5 text-off-white" />
          </button>
        </GenerateIngressModal>
        <EndLivestreamConfirmationModal onConfirm={handleEndLivestream}>
          <button className="flex items-center gap-1 rounded border border-danger bg-[#2A2A2A] px-4 py-2 text-sm font-semibold text-off-white">
            <XCircleOutlineIcon className="size-5 text-off-white" />
            <span className="">End Stream</span>
          </button>
        </EndLivestreamConfirmationModal>
      </div>
    </div>
  );
}

function GenerateIngressModal({ children }: { children: React.ReactNode }) {
  const [open, setOpen] = useState(false);
  const id = useLivestreamStore((state) => state.id!);

  const queryClient = useQueryClient();

  const { data: livestreamIngress } = useQuery(
    livestreamQueries.livestreamIngress(),
  );

  const { mutateAsync: generateIngress, isPending } =
    useGenerateLivestreamIngressMutation({
      onSuccess: () => {
        toast.green("Ingress generated");
        queryClient.invalidateQueries({
          queryKey: livestreamQueries.livestreamIngressKey(),
        });
      },
    });

  const address = livestreamIngress?.server ?? "";
  const streamKey = livestreamIngress?.streamKey ?? "";

  const handleGenerateIngress = async () => {
    await generateIngress({
      livestreamId: id,
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-sm">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">
            Streaming Key Settings
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-4">
          <div className="mt-2 flex flex-col gap-2">
            <Label className="text-off-white">SERVER</Label>
            <div className="relative">
              <TextInput value={address} onChange={() => {}} disabled />
              <CopyToClipboard
                text={address}
                onCopy={() => {
                  toast.green("Copied to clipboard");
                }}
              >
                <button className="absolute right-4 top-1/2 -translate-y-1/2">
                  <CopyOutlineIcon className="size-5 text-[#9E9E9E]" />
                </button>
              </CopyToClipboard>
            </div>
          </div>
          <div className="mt-2 flex flex-col gap-2">
            <Label className="text-off-white">STREAMING KEY</Label>
            <div className="relative">
              <TextInput
                value={streamKey}
                type="password"
                onChange={() => {}}
                disabled
              />
              <CopyToClipboard
                text={streamKey}
                onCopy={() => {
                  toast.green("Copied to clipboard");
                }}
              >
                <button className="absolute right-4 top-1/2 -translate-y-1/2">
                  <CopyOutlineIcon className="size-5 text-[#9E9E9E]" />
                </button>
              </CopyToClipboard>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            className="flex-[1]"
            onClick={() => {
              setOpen(false);
            }}
          >
            Close
          </Button>
          <Button
            className="flex-[2]"
            onClick={handleGenerateIngress}
            loading={isPending}
          >
            Reset Streaming Key
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
