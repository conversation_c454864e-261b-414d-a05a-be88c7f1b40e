import { Stage } from "@/queries/types";
import { Livestream } from "@/queries/types/livestream";

import { Asset } from "./asset";
import { Bookmark } from "./bookmark";
import { CommunityExtended } from "./community";

export interface Thread {
  displayStatus: number;
  id: string;
  content: null | string;
  contentUrl: string;
  threadType: string;
  stage?: Stage;
  livestream?: Livestream;
  userId: string;
  userName: string;
  userHandle: string;
  userPicture: string;
  createdDate: string;
  answerCount: number;
  likeCount: number;
  bookmarkCount: number;
  repostCount: number;
  repostId: null | string;
  answerId: null | string;
  answer?: Thread;
  isDeleted: boolean;
  privacyType: number;
  answerPrivacyType: number;
  language: string;
  isPinned: boolean;
  pinnedInCommunity: boolean;
  paywall: boolean;
  price: string;
  tipAmount: number;
  tipCount: number;
  currency: string;
  currencyAddress: null | string;
  currencyDecimals: number;
  like:
    | {
        id: string;
        createdOn: string;
        threadId: string;
        userId: string;
      }
    | boolean
    | null;
  bookmark: Bookmark | boolean | null;
  reposted: Thread | boolean | null;
  images: Asset[];
  videos: Asset[];
  repost?: Thread;
  user: ThreadUser;
  communityId?: null | string;
  community?: CommunityExtended;
}

export interface ThreadUser {
  threadCount: number;
  followerCount: number;
  followingsCount: number;
  twitterFollowers: number;
  id: string;
  createdOn: string;
  twitterId: string;
  twitterHandle: string;
  twitterName: string;
  twitterPicture: string;
  lastLoginTwitterPicture: string;
  bannerUrl: string;
  address: string;
  ethereumAddress: string;
  solanaAddress: string;
  prevAddress: null | string;
  addressConfirmed: boolean;
  twitterDescription: string;
  signedUp: boolean;
  subscriptionCurrency: string;
  subscriptionCurrencyAddress: null | string;
  subscriptionPrice: string;
  keyPrice: string;
  subscriptionsEnabled: boolean;
  userConfirmed: boolean;
  twitterConfirmed: boolean;
  flag: number;
  ixHandle: string;
  handle: string;
}

export enum ThreadPrivacyTypeEnum {
  PUBLIC = 0,
  BADGEHOLDERS = 1,
  FOLLOWERS = 2,
  SHAREHOLDERS = 3,
  SUBSCRIBERS = 4,
  TOBUY = 5,
}

export interface FileType {
  id: string;
  isLoading: boolean;
  previewUrl: string;
  url: string;
  fileType: string;
  size: number;
}

export interface PostTweetOnXData {
  isPostToX: boolean;
  accessToken: string;
  mediaIds: string[];
}

export interface PostThreadData {
  content: string;
  privacyType: ThreadPrivacyTypeEnum;
  files: FileType[];
  communityId?: string;
  xPostData?: PostTweetOnXData;
}

export interface PostQuoteData {
  threadId: string;
  content: string;
  files: FileType[];
  communityId?: string;
}

export interface PostThreadAnswerData {
  content: string;
  threadId: string;
  userId: string;
  files: FileType[];
}

export interface postTweetOnXResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export interface PostThreadResponse {
  thread: Thread;
  xTweetResult?: postTweetOnXResponse;
}

export interface ThreadResponse {
  thread: Thread;
}

export interface QuoteResponse {
  repostingThread: Thread;
  thread: Thread;
}

export interface ThreadActionData {
  threadId: string;
}

export interface ThreadPinData {
  threadId: string;
  isPinned: boolean;
}

export interface CommunityModerateDeletePayload {
  communityId: string;
  threadId: string;
  banUser: boolean;
}

export interface CommunityModerateActionResponse {
  success: boolean;
}

export interface CommunityModeratePinPayload {
  communityId: string;
  threadId: string;
  pinnedInCommunity: boolean;
}
