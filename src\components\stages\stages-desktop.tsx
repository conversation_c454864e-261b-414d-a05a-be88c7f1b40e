"use client";

import { useSelectedLayoutSegments } from "next/navigation";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { AnimatePresence, motion } from "framer-motion";

import { useEndStageMutation } from "@/queries";
import { stageQueries } from "@/queries/stage-queries";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";

import { BarChartFilledIcon, CloseOutlineIcon } from "../icons";
import { useLogSpeakingDuration } from "./hooks/use-log-speaking-duration";
import { StageContent } from "./stage-content";
import { EndStageConfirmationModal } from "./stage-end-confirmation-modal";

export const StageDesktop = () => {
  const segments = useSelectedLayoutSegments();

  const isMessagesPage = segments.length > 0 && segments[0] === "(messages)";

  if (isMessagesPage) {
    return (
      <div className="relative mx-auto flex max-w-[1350px] flex-nowrap items-stretch justify-end">
        <div className="relative z-[3] hidden sm:flex sm:flex-shrink-0 sm:flex-grow sm:flex-col sm:items-end">
          <div className="flex w-[88px] flex-col items-end xl:w-[247px]" />
        </div>
        <div className="flex flex-shrink flex-grow-[2] justify-start">
          <div className="relative w-full sm:w-[610px] lg:w-[380px]">
            <div className="fixed bottom-1 w-[380px] justify-end">
              <MiniStage />
              <FullStage />
            </div>
          </div>
          <div className="hidden lg:flex lg:w-[536px] lg:flex-col xl:w-[638px]" />
        </div>
      </div>
    );
  }

  return (
    <div className="relative mx-auto flex max-w-[1350px] flex-nowrap items-stretch justify-end">
      <div className="relative z-[3] hidden sm:flex sm:flex-shrink-0 sm:flex-grow sm:flex-col sm:items-end">
        <div className="flex w-[88px] flex-col items-end xl:w-[247px]" />
      </div>
      <div className="flex flex-shrink flex-grow-[2] justify-start gap-4 xl:gap-7">
        <div className="relative w-[500px] xl:w-[615px]" />
        <div className="flex w-[290px] flex-col xl:w-[380px]">
          <div className="fixed bottom-5 w-[374px] justify-end">
            <MiniStage />
            <FullStage />
          </div>
        </div>
      </div>
    </div>
  );
};

const FullStage = () => {
  const isFullScreen = useStageStore((state) => state.isFullScreen);

  return (
    <AnimatePresence>
      {isFullScreen && (
        <motion.div
          initial={{
            height: 73,
            opacity: 1,
          }}
          animate={{
            height: 780,
            opacity: 1,
            transition: {
              opacity: { duration: 0.1 },
            },
          }}
          exit={{
            height: 73,
            opacity: 0,
            overflow: "hidden",
            transition: {
              opacity: { duration: 0.2, delay: 0.2 },
            },
          }}
          transition={{ type: "spring", bounce: 0, duration: 0.4 }}
          className="absolute bottom-0 flex w-full flex-col overflow-hidden rounded-[20px] border border-[#4C4C4C] bg-dark-bk"
        >
          <StageContent />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

const MiniStage = () => {
  const queryClient = useQueryClient();
  const { user } = useUser();
  const isStageOn = useStageStore((state) => Boolean(state.token));
  const id = useStageStore((state) => state.id!);
  const { data, isLoading } = useQuery({
    ...stageQueries.stageInfo(id),
  });
  const actions = useStageStore((state) => state.actions);
  const logSpeakingDuration = useLogSpeakingDuration();
  const { mutateAsync: endStage } = useEndStageMutation({
    onSuccess: () => {
      actions.reset();
    },
  });

  const handleLeave = async () => {
    logSpeakingDuration();
    if (data?.host?.userId === user?.id) {
      await endStage({
        stageId: id,
      });
    } else {
      actions.reset();
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: stageQueries.currentlyListeningKey(),
        });
        actions.setCanShowCurrentlyListening(true);
      }, 3000);
    }
  };

  if (!isStageOn || !data || isLoading) return null;

  return (
    <div
      className="flex cursor-pointer select-none items-center justify-between gap-4 rounded-[20px] border-t-2 border-brand-orange bg-purple-gradient px-6 py-3"
      onClick={() => {
        actions.setFullScreen(true);
      }}
    >
      <div className="flex min-w-0 flex-col">
        <div className="flex items-center gap-1">
          <BarChartFilledIcon className="w-3.5 text-brand-orange" />
          <span className="mt-0.5 text-xs text-off-white">
            {data.host.user.twitterName} (Host)
          </span>
        </div>
        <div className="mt-0.5 truncate text-sm font-semibold text-off-white">
          {data.stage.name}
        </div>
      </div>
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        {data?.host?.userId === user?.id ? (
          <EndStageConfirmationModal onConfirm={handleLeave}>
            <button className="rounded-full bg-dark-gray p-2">
              <CloseOutlineIcon className="size-5 text-off-white" />
            </button>
          </EndStageConfirmationModal>
        ) : (
          <button
            className="rounded-full bg-dark-gray p-2"
            onClick={handleLeave}
          >
            <CloseOutlineIcon className="size-5 text-off-white" />
          </button>
        )}
      </div>
    </div>
  );
};
