import Skeleton from "react-loading-skeleton";

export const TradeERC20Skeleton = () => {
  return (
    <div className="mx-[10px] flex flex-1 flex-col justify-center">
      <div className="flex flex-1 flex-col justify-center">
        <div className="mb-2 flex gap-1 leading-none">
          <Skeleton width={60} height={16.5} />
          <Skeleton width={25} height={16.5} />
        </div>
        <div className="relative">
          <div className="flex h-[52px] items-center rounded-lg border border-[#3B3B3B] transition-colors focus-within:border-off-white">
            <div className="flex items-center pl-4">
              <Skeleton circle={true} height={16} width={16} />
            </div>
            <Skeleton height={16} width={50} className="ml-2" />
          </div>
        </div>

        <div className="flex gap-2 pt-3">
          {[...Array(4)].map((_, index) => (
            <Skeleton
              key={index}
              width={56}
              height={33}
              className="flex items-center justify-center"
              style={{ borderRadius: "16px" }}
            />
          ))}
        </div>
        <div className="inline-block pb-8 pt-8 leading-none">
          <Skeleton
            width={"100%"}
            height={54}
            style={{ borderRadius: "6px" }}
          />
        </div>

        <div className="mb-2 flex gap-1 leading-none">
          <Skeleton width={60} height={16.5} />
          <Skeleton width={25} height={16.5} />
        </div>
        <div className="h-[1px]" />

        <div className="relative pb-12">
          <div className="flex h-[52px] items-center rounded-lg border border-[#3B3B3B]">
            <div className="flex items-center pl-4">
              <Skeleton circle={true} height={16} width={16} />
            </div>
            <Skeleton height={16} width={50} className="ml-2" />
          </div>
        </div>
        <div className="h-[4px]" />

        <Skeleton width={"100%"} height={44} style={{ borderRadius: "22px" }} />

        <div className="mt-6 flex items-center gap-4 pb-2">
          <div className="flex h-[68px] flex-grow items-center justify-between rounded-xl border border-[#3a3a3a] p-4 text-sm text-[#656565]">
            <div className="flex flex-col items-start leading-none">
              <Skeleton height={14.5} width={150} />
              <div className="h-[4px]" />
              <Skeleton height={14.5} width={100} />
            </div>
            <Skeleton height={20} width={20} />
          </div>
          <div className="flex h-[68px] w-[100px] flex-col rounded-xl border border-[#3a3a3a] p-4 leading-none">
            <Skeleton height={14.5} width={60} />
            <div className="h-[4px]" />
            <Skeleton height={14.5} width={40} />
          </div>
        </div>
      </div>
      <div className="w-full pb-16 pt-2">
        <div className="invisible h-[1px]" />
      </div>
    </div>
  );
};
