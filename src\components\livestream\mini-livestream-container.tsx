"use client";

import { memo, useEffect } from "react";
import { useSelectedLayoutSegments } from "next/navigation";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useLivestreamStore } from "@/stores/livestream";

import { VideoPlayer } from "./video-player-new";

export const MiniLivestreamContainer = memo(() => {
  const token = useLivestreamStore((state) => state.token);
  const actions = useLivestreamStore((state) => state.actions);
  const isLaptop = useMediaQuery(BREAKPOINTS.lg);
  const segments = useSelectedLayoutSegments();

  const isLivestreamPage =
    segments.length > 0 && segments[0] === "(livestream)";

  const isMessagesPage = segments.length > 0 && segments[0] === "(messages)";

  useEffect(() => {
    if (segments.length === 0) {
      actions.reset();
    }
  }, [segments]);

  if (token && isLaptop && !isLivestreamPage) {
    if (isMessagesPage) {
      return (
        <div className="relative z-10 mx-auto flex max-w-[1350px] flex-nowrap items-stretch justify-end">
          <div className="relative z-[3] hidden sm:flex sm:flex-shrink-0 sm:flex-grow sm:flex-col sm:items-end">
            <div className="flex w-[88px] flex-col items-end xl:w-[154px]" />
          </div>
          <div className="flex flex-shrink flex-grow-[2] justify-start gap-4 xl:gap-7">
            <div className="flex w-[450px] flex-col">
              <div className="fixed bottom-5 w-[444px] justify-end">
                <VideoPlayer type="mini" device="desktop" />
              </div>
            </div>
            <div className="relative w-[430px] xl:w-[545px]" />
          </div>
        </div>
      );
    }

    return (
      <div className="relative mx-auto flex max-w-[1350px] flex-nowrap items-stretch justify-end">
        <div className="relative z-[3] hidden sm:flex sm:flex-shrink-0 sm:flex-grow sm:flex-col sm:items-end">
          <div className="flex w-[88px] flex-col items-end xl:w-[247px]" />
        </div>
        <div className="flex flex-shrink flex-grow-[2] justify-start gap-4 xl:gap-7">
          <div className="relative w-[430px] xl:w-[545px]" />
          <div className="flex w-[450px] flex-col">
            <div className="fixed bottom-5 w-[444px] justify-end">
              <VideoPlayer type="mini" device="desktop" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
});

MiniLivestreamContainer.displayName = "MiniLivestreamContainer";
