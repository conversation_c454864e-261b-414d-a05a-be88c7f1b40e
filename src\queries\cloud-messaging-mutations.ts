import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import { postSaveCloudMessagingSettings } from "@/api/client/cloud-messaging";

import { SaveCloudMessagingSettingsVariables } from "./types/cloud-messaging";

type SaveCloudMessagingSettingsMutationType = MutationOptions<
  unknown,
  DefaultError,
  SaveCloudMessagingSettingsVariables,
  any
>;

export const useSaveCloudMessagingSettingsMutation = (
  options?: SaveCloudMessagingSettingsMutationType,
) => {
  return useMutation({
    mutationFn: postSaveCloudMessagingSettings,
    ...options,
  });
};
