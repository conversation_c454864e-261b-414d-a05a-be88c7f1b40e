"use client";

import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import { depositSolana } from "@/api/balance";
import { tipSolana } from "@/api/client/chain";
import { postTipNotify, postTipThread } from "@/api/client/threads";
import { toast } from "@/components/toast";
import { minDelay } from "@/utils/min-delay";

interface TipMutationData {
  currency: string;
  tipAmount: string;
  userId: string;
  threadId?: string;
  wallet: string;
  isDynamic?: boolean;
  txHash: string;
  txData: string;
  isToken?: boolean;
  tokenContractAddress?: string;
}

type TipMutationType = MutationOptions<
  unknown,
  DefaultError,
  TipMutationData,
  any
>;

async function tip({
  currency,
  tipAmount,
  userId,
  threadId,
  wallet,
  isDynamic,
  txHash,
  txData,
  isToken,
  tokenContractAddress,
}: TipMutationData) {
  if (
    ["SOL", "Bonk", "$WIF", "USEDCAR", "Moutai", "HARAMBE"].includes(currency)
  ) {
    const provider =
      typeof window !== "undefined" && (window as any).phantom?.solana;

    if (wallet == "Phantom") {
      await depositSolana(
        provider,
        parseFloat(tipAmount),
        currency,
        "finalized",
      );
      const response = tipSolana({
        userIdTo: userId,
        amount: parseFloat(tipAmount),
        currency,
      });

      return response;
    } else if (wallet == "The Arena") {
      const response = tipSolana({
        userIdTo: userId,
        amount: parseFloat(tipAmount),
        currency,
      });

      return response;
    }
  } else {
    let response;
    if (isDynamic) {
      response = await postTipNotify({
        threadId,
        tipAmount,
        userId,
        currency,
        txHash,
        txData,
        isToken,
        tokenContractAddress,
      });
    } else {
      if (currency.toLowerCase() === "champ") {
        toast.danger("Please migrate your wallet to tip $CHAMP!");
        return;
      }
      if (currency.toLowerCase() === "erol") {
        toast.danger("Please migrate your wallet to tip $EROL!");
        return;
      }
      if (currency.toLowerCase() === "ket") {
        toast.danger("Please migrate your wallet to tip $KET!");
        return;
      }
      if (currency.toLowerCase() === "wink") {
        toast.danger("Please migrate your wallet to tip $WINK!");
        return;
      }
      if (currency.toLowerCase() === "abc") {
        toast.danger("Please migrate your wallet to tip $ABC!");
        return;
      }
      if (currency.toLowerCase() === "mu") {
        toast.danger("Please migrate your wallet to tip $MU!");
        return;
      }
      if (currency.toLowerCase() === "blub") {
        toast.danger("Please migrate your wallet to tip $BLUB!");
        return;
      }
      if (currency.toLowerCase() === "boi") {
        toast.danger("Please migrate your wallet to tip $BOI!");
        return;
      }
      if (isToken) {
        toast.danger(`Please migrate your wallet to tip $${currency}!`);
        return;
      }
      response = await postTipThread({
        threadId,
        tipAmount,
        userId,
        currency,
      });
    }

    return response;
  }
}

export const useTipMutation = (options?: TipMutationType) => {
  return useMutation({
    mutationFn: async (data: TipMutationData) => {
      return await minDelay(tip(data));
    },
    ...options,
  });
};
