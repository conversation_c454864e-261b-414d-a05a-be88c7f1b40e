"use client";

import { useQuery } from "@tanstack/react-query";

import { stageQueries } from "@/queries/stage-queries";
import { useStageRecordingPlayerStore } from "@/stores/stage-recording-player";

import { BarChartFilledIcon, PauseFilledIcon, PlayFilledIcon } from "../icons";
import { Dialog, DialogContent } from "../ui/dialog";
import { StagePlayerContent } from "./stage-player-content";

export const StagePlayerMobile = () => {
  const isFullScreen = useStageRecordingPlayerStore(
    (state) => state.isRecordingPlayerFullScreen,
  );
  const actions = useStageRecordingPlayerStore((state) => state.actions);

  return (
    <>
      <Dialog
        open={isFullScreen}
        onOpenChange={(open) => {
          actions.setIsRecordingPlayerFullScreen(open);
        }}
      >
        <DialogContent className="pt-pwa z-50 flex h-full w-full flex-col gap-0 bg-dark-bk px-0 pb-0 sm:h-auto sm:py-6">
          <StagePlayerContent />
        </DialogContent>
      </Dialog>
    </>
  );
};

export const MinifiedStagePlayer = () => {
  const isPlayerOn = useStageRecordingPlayerStore((state) =>
    Boolean(state.url),
  );
  const id = useStageRecordingPlayerStore((state) => state.id!);
  const { data, isLoading } = useQuery({
    ...stageQueries.stageInfo(id),
    enabled: isPlayerOn,
  });
  const isFullScreen = useStageRecordingPlayerStore(
    (state) => state.isRecordingPlayerFullScreen,
  );
  const isPlaying = useStageRecordingPlayerStore((state) => state.isPlaying);
  const audioRef = useStageRecordingPlayerStore((state) => state.audioRef);
  const actions = useStageRecordingPlayerStore((state) => state.actions);

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    actions.setIsPlaying(!isPlaying);
  };

  if (isPlayerOn && !isFullScreen && data && !isLoading) {
    return (
      <div
        className="flex items-center justify-between border-t-2 border-brand-orange bg-purple-gradient px-6 py-3 sm:hidden"
        onClick={() => {
          actions.setIsRecordingPlayerFullScreen(true);
        }}
      >
        <div className="flex min-w-0 flex-col">
          <div className="flex items-center gap-1">
            <BarChartFilledIcon className="w-3.5 text-brand-orange" />
            <span className="mt-0.5 text-xs text-off-white">
              {data.host.user.twitterName} (Host)
            </span>
          </div>
          <div className="mt-0.5 truncate text-sm font-semibold text-off-white">
            {data.stage.name}
          </div>
        </div>
        <button
          className="flex-shrink-0 rounded-full p-2"
          onClick={(e) => {
            e.stopPropagation();
            togglePlayPause();
          }}
        >
          {isPlaying ? (
            <PauseFilledIcon className="size-5 text-off-white" />
          ) : (
            <PlayFilledIcon className="size-5 text-off-white" />
          )}
        </button>
      </div>
    );
  }

  return null;
};
