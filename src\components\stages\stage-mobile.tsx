"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";

import { useEndStageMutation } from "@/queries";
import { stageQueries } from "@/queries/stage-queries";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";

import { BarChartFilledIcon, CloseOutlineIcon } from "../icons";
import { Dialog, DialogContent } from "../ui/dialog";
import { useLogSpeakingDuration } from "./hooks/use-log-speaking-duration";
import { StageContent } from "./stage-content";
import { EndStageConfirmationModal } from "./stage-end-confirmation-modal";
import { MinifiedStagePlayer } from "./stage-player-mobile";

export const StageMobile = () => {
  const isFullScreen = useStageStore((state) => state.isFullScreen);
  const actions = useStageStore((state) => state.actions);

  return (
    <>
      <Dialog
        open={isFullScreen}
        onOpenChange={(open) => {
          actions.setFullScreen(open);
        }}
      >
        <DialogContent className="pt-pwa z-50 flex h-full w-full flex-col gap-0 bg-dark-bk px-0 pb-0 sm:left-1/2 sm:max-w-none sm:py-6">
          <StageContent />
        </DialogContent>
      </Dialog>
    </>
  );
};

export const MinifiedStage = () => {
  const { user } = useUser();
  const queryClient = useQueryClient();
  const isStageOn = useStageStore((state) => Boolean(state.token));
  const id = useStageStore((state) => state.id!);
  const { data, isLoading } = useQuery({
    ...stageQueries.stageInfo(id),
    enabled: isStageOn,
  });
  const isFullScreen = useStageStore((state) => state.isFullScreen);
  const actions = useStageStore((state) => state.actions);
  const logSpeakingDuration = useLogSpeakingDuration();

  const { mutateAsync: endStage } = useEndStageMutation({
    onSuccess: () => {
      actions.reset();
    },
  });

  const handleLeave = async () => {
    logSpeakingDuration();
    if (data?.host?.userId === user?.id) {
      await endStage({
        stageId: id,
      });
    } else {
      actions.reset();
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: stageQueries.currentlyListeningKey(),
        });
        actions.setCanShowCurrentlyListening(true);
      }, 3000);
    }
  };

  if (isStageOn && !isFullScreen && data && !isLoading) {
    return (
      <div
        className="flex items-center justify-between gap-4 border-t-2 border-brand-orange bg-purple-gradient px-6 py-3"
        onClick={() => {
          actions.setFullScreen(true);
        }}
      >
        <div className="flex min-w-0 flex-col">
          <div className="flex items-center gap-1">
            <BarChartFilledIcon className="w-3.5 text-brand-orange" />
            <span className="mt-0.5 text-xs text-off-white">
              {data.host.user.twitterName} (Host)
            </span>
          </div>
          <div className="mt-0.5 truncate text-sm font-semibold text-off-white">
            {data.stage.name}
          </div>
        </div>
        <div
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {data?.host?.userId === user?.id ? (
            <EndStageConfirmationModal onConfirm={handleLeave}>
              <button className="rounded-full bg-dark-gray p-2">
                <CloseOutlineIcon className="size-5 text-off-white" />
              </button>
            </EndStageConfirmationModal>
          ) : (
            <button
              className="rounded-full bg-dark-gray p-2"
              onClick={handleLeave}
            >
              <CloseOutlineIcon className="size-5 text-off-white" />
            </button>
          )}
        </div>
      </div>
    );
  }

  return null;
};

export const MinifiedStageTablet = () => {
  return (
    <div className="sticky inset-x-0 bottom-0 hidden w-full sm:block lg:hidden">
      <MinifiedStage />
      <MinifiedStagePlayer />
    </div>
  );
};
