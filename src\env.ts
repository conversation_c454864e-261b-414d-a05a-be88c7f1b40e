export const env = {
  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL ?? "",
  NEXT_PUBLIC_MAINNET_RPC_URL: process.env.NEXT_PUBLIC_MAINNET_RPC_URL ?? "",
  NEXT_PUBLIC_SOCKET_URL: process.env.NEXT_PUBLIC_SOCKET_URL ?? "",
  NEXT_PUBLIC_APP_DOMAIN: process.env.NEXT_PUBLIC_APP_DOMAIN ?? "",
  NEXT_PUBLIC_HTTP_BASIC_AUTH: process.env.NEXT_PUBLIC_HTTP_BASIC_AUTH ?? "",
  NEXT_PUBLIC_FIREBASE_VAPID_KEY:
    process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY ?? "",
  NEXT_PUBLIC_AVAX_CHAINID: process.env.NEXT_PUBLIC_AVAX_CHAINID ?? 43114,
  NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID:
    process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID ??
    "e80b2f49716ba2901d09ba0745983ce4",
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:
    process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ??
    "pk_live_51PJSikJD7G42nY3U5Yk0q30TseH7V99GFKJIf72bdsJEPiEBtgUqrLU7ifTOqyUjnhzP2ivMEv7ReNz7zdQyjQaE00Dh4sTVBv",
  NEXT_PUBLIC_DYNAMIC_ENVIRONMENT_ID:
    process.env.NEXT_PUBLIC_DYNAMIC_ENVIRONMENT_ID ?? "",
  NEXT_COINGECKO_API:
    process.env.NEXT_COINGECKO_API ??
    "https://api.coingecko.com/api/v3/simple/price?" +
      "ids=avalanche-2,coq-inu,ket,gursonavax,avax-has-no-chill," +
      "sausagers-meat,kimbo,joe,tech,solana,bonk,dogwifcoin," +
      "a-gently-used-2001-honda,moutai,harambe-2&vs_currencies=usd",
  NEXT_PUBLIC_LIVEKIT_URL: process.env.NEXT_PUBLIC_LIVEKIT_URL ?? "",
  NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY ?? "",
  NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST ?? "",
  NEXT_PUBLIC_HTTP_BASIC_AUTH_ENABLED:
    process.env.NEXT_PUBLIC_HTTP_BASIC_AUTH_ENABLED === "true",
};
