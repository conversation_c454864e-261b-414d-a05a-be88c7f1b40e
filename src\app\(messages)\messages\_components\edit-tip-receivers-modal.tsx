import { FC, useEffect, useState } from "react";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogClose, DialogContent } from "@/components/ui/dialog";
import { SharesHolder } from "@/queries/types";

export interface TipReceiver extends SharesHolder {
  isChecked: boolean;
}

interface EditTipReceiversModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  tipReceivers?: TipReceiver[];
  setTipReceivers: (tipReceivers: TipReceiver[]) => void;
  showTips: boolean;
}

export const EditTipReceiversModal: FC<EditTipReceiversModalProps> = ({
  open,
  setOpen,
  tipReceivers,
  setTipReceivers,
  showTips = true,
}) => {
  const [receivers, setReceivers] = useState(tipReceivers);

  const handleCheckedChange = (
    changedReceiver: TipReceiver,
    checked: boolean,
  ) => {
    if (!receivers) return;
    setReceivers(
      receivers.map((receiver) => {
        receiver.isChecked =
          receiver.traderId === changedReceiver.traderId
            ? checked
            : receiver.isChecked;

        return receiver;
      }),
    );
  };

  useEffect(() => {
    setReceivers(tipReceivers);
  }, [tipReceivers]);

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
        if (!open && receivers) {
          setTipReceivers(receivers);
        }
      }}
    >
      <DialogContent className="flex h-full w-full flex-col gap-0 overflow-y-auto bg-dark-bk p-0 sm:max-h-[650px] sm:bg-[rgba(15,15,15)]">
        <div className="sticky top-0 z-10 flex gap-2 bg-dark-bk px-6 pb-4 pt-[calc(1rem+env(safe-area-inset-top))] sm:bg-[rgba(15,15,15)]">
          <div className="flex items-center">
            <DialogClose>
              <ArrowBackOutlineIcon className="size-5" />
            </DialogClose>
          </div>
          <div className="text-base font-semibold leading-5">
            Edit Tip Receivers
          </div>
          <div className="flex-1" />
        </div>
        <div>
          {receivers &&
            receivers.length > 0 &&
            receivers.map((receiver) => (
              <div
                className="flex items-center justify-between px-6 py-4"
                key={receiver.id}
              >
                <div className="flex items-center gap-[10px]">
                  <ProgressBarLink
                    href={`/${receiver.traderUser?.twitterHandle}`}
                  >
                    <Avatar className="size-[42px]">
                      <AvatarImage src={receiver.traderUser?.twitterPicture} />
                      <AvatarFallback />
                    </Avatar>
                  </ProgressBarLink>

                  <div className="flex flex-col gap-1 text-sm leading-4">
                    <p className="text-[#808080]">
                      @{receiver.traderUser?.twitterHandle}
                    </p>
                    {showTips && (
                      <div className="text-sm font-semibold text-off-white">
                        {receiver.amount} tips
                      </div>
                    )}
                  </div>
                </div>

                <div className="px-2.5">
                  <Checkbox
                    className="h-[20px] w-[20px]"
                    checked={receiver.isChecked}
                    onCheckedChange={(checked: boolean) => {
                      handleCheckedChange(receiver, checked);
                    }}
                  />
                </div>
              </div>
            ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};
