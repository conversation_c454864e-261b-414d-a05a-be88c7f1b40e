"use client";

import { useRouter } from "next/navigation";

import { ArrowBackOutlineIcon } from "@/components/icons";

export const ArrowBackHeader = () => {
  const router = useRouter();

  const handleBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push("/home");
    }
  };

  return (
    <div className="sticky top-0 z-[100] flex items-center bg-dark-bk px-6 pb-4 pt-[calc(1rem+env(safe-area-inset-top))]">
      <div className="flex-1">
        <button className="flex flex-shrink-0" onClick={handleBack}>
          <ArrowBackOutlineIcon className="size-5 text-off-white" />
        </button>
      </div>
      <div className="flex-1" />
    </div>
  );
};
