import { useEffect, useRef } from "react";

import { useMotionValue, useScroll, useTransform } from "framer-motion";

import { clamp } from "@/utils";

const lerp = (start: number, end: number, factor: number) => {
  return start + (end - start) * factor;
};

export const useBoundedScroll = (threshold: number) => {
  const { scrollY } = useScroll();
  const scrollYBounded = useMotionValue(0);
  const scrollYBoundedProgress = useTransform(
    scrollYBounded,
    [0, threshold],
    [0, 1],
  );
  const targetRef = useRef(0);
  const frameRef = useRef(0);
  const damping = 0.05;

  useEffect(() => {
    const animate = () => {
      const current = scrollYBounded.get();
      const newValue = lerp(current, targetRef.current, 0.2);

      scrollYBounded.set(clamp(newValue, 0, threshold));
      frameRef.current = requestAnimationFrame(animate);
    };

    frameRef.current = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(frameRef.current);
  }, [threshold, scrollYBounded]);

  useEffect(() => {
    return scrollY.on("change", (current) => {
      if (current < 0) return;

      const previous = scrollY.getPrevious();
      if (!previous) return;

      const diff = current - previous;
      targetRef.current = clamp(
        targetRef.current + diff * (1 - damping),
        0,
        threshold,
      );
    });
  }, [threshold, scrollY]);

  return { scrollYBounded, scrollYBoundedProgress };
};
