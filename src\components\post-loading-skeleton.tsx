import Skeleton from "react-loading-skeleton";

import {
  BookmarkOutlineIcon,
  ChatBubblesOutlineIcon,
  HeartOutlineIcon,
  RepostOutlineIcon,
  TipOutlineIcon,
} from "@/components/icons";

export const PostLoadingSkeleton = () => {
  return (
    <div className="flex w-full gap-3 border-b border-dark-gray p-6">
      <div className="flex-shrink-0 leading-none">
        <Skeleton circle className="size-[42px]" />
      </div>
      <div className="flex w-full flex-col gap-2">
        <div className="flex">
          <Skeleton className="h-4 w-24" />・
          <Skeleton className="h-4 w-16" />・
          <Skeleton className="h-4 w-16" />
        </div>
        <div className="flex flex-col text-sm">
          <Skeleton className="h-[14px] w-full" />
          <Skeleton className="h-[14px] w-full" />
          <Skeleton className="h-[14px] w-full" />
          <Skeleton className="h-[14px] w-full" />
        </div>
        <div className="mt-1 flex items-center gap-4">
          <div className="flex items-center gap-1 rounded">
            <ChatBubblesOutlineIcon className="h-5 w-5 text-gray-text" />
            <Skeleton className="h-3 w-6" />
          </div>
          <div className="flex items-center gap-1 rounded">
            <RepostOutlineIcon className="h-5 w-5 text-gray-text" />
            <Skeleton className="h-3 w-6" />
          </div>
          <div className="flex items-center gap-1 rounded">
            <HeartOutlineIcon className="h-5 w-5 text-gray-text" />
            <Skeleton className="h-3 w-6" />
          </div>
          <div className="flex items-center gap-1 rounded">
            <BookmarkOutlineIcon className="h-5 w-5 text-gray-text" />
            <Skeleton className="h-3 w-6" />
          </div>
          <div className="ml-auto flex items-center rounded">
            <TipOutlineIcon className="h-5 w-5 text-gray-text" />
          </div>
        </div>
      </div>
    </div>
  );
};

export const MainPostLoadingSkeleton = () => {
  return (
    <div className="border-b border-dark-gray p-6">
      <div className="flex w-full flex-col gap-3">
        <div className="flex gap-3">
          <div className="flex-shrink-0 leading-none">
            <Skeleton circle className="size-[42px]" />
          </div>
          <div className="flex w-full min-w-0 flex-col gap-2">
            <div className="flex text-sm leading-4 text-[#878787]">
              <Skeleton className="h-4 w-24" />・
              <Skeleton className="h-4 w-16" />・
              <Skeleton className="h-4 w-16" />
            </div>
          </div>
        </div>
        <div className="flex flex-col text-sm">
          <Skeleton className="h-[14px] w-full" />
          <Skeleton className="h-[14px] w-full" />
          <Skeleton className="h-[14px] w-full" />
          <Skeleton className="h-[14px] w-full" />
        </div>

        <div className="mt-1 flex items-center gap-4">
          <div className="flex items-center gap-1 rounded">
            <ChatBubblesOutlineIcon className="h-5 w-5 text-gray-text" />
            <Skeleton className="h-3 w-6" />
          </div>
          <div className="flex items-center gap-1 rounded">
            <RepostOutlineIcon className="h-5 w-5 text-gray-text" />
            <Skeleton className="h-3 w-6" />
          </div>
          <div className="flex items-center gap-1 rounded">
            <HeartOutlineIcon className="h-5 w-5 text-gray-text" />
            <Skeleton className="h-3 w-6" />
          </div>
          <div className="flex items-center gap-1 rounded">
            <BookmarkOutlineIcon className="h-5 w-5 text-gray-text" />
            <Skeleton className="h-3 w-6" />
          </div>
          <div className="ml-auto flex items-center rounded">
            <TipOutlineIcon className="h-5 w-5 text-gray-text" />
          </div>
        </div>
      </div>
    </div>
  );
};
