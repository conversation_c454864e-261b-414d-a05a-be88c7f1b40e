"use client";

import { FC, useEffect } from "react";
import { useParams, usePathname, useRouter } from "next/navigation";

import {
  RewardCurrencies,
  RewardCurrencyStatusEnum,
} from "@/api/client/admin-rewards";
import { PlusCircleIcon } from "@/components/icons-v2/account-security";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useRewardsQuery } from "@/queries/rewards-query";
import { cn } from "@/utils";

import { useAdmin } from "../context/admin-context";

interface ChampionsRewardsProps {
  rewards?: RewardCurrencies[];
  isLoading?: boolean;
}

export const ChampionsRewards: FC<ChampionsRewardsProps> = ({
  rewards: externalRewards,
  isLoading: externalLoading,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();
  const currentRewardId = params?.rewardId as string;
  const isLargeTablet = useMediaQuery(BREAKPOINTS.lg);
  const { selectedReward, setSelectedReward, setSelectedProject } = useAdmin();

  const {
    data: hookRewards,
    isLoading: hookLoading,
    isError,
  } = useRewardsQuery();

  const rewards = externalRewards || hookRewards;
  const isLoading =
    externalLoading !== undefined ? externalLoading : hookLoading;

  const isNewRewardSelected = pathname === "/moderation/admin/rewards/new";

  useEffect(() => {
    if (rewards && currentRewardId && currentRewardId !== "new") {
      const rewardFromUrl = rewards.find(
        (reward) => reward.id.toString() === currentRewardId,
      );

      if (rewardFromUrl) {
        setSelectedReward({
          id: rewardFromUrl.id,
          name: rewardFromUrl.systemCurrency.name,
          description: "",
          image: rewardFromUrl.systemCurrency.image,
          rewardAmount: rewardFromUrl.amount,
        });
      }
    } else if (currentRewardId === "new") {
      setSelectedReward(null);
    }
  }, [rewards, currentRewardId, setSelectedReward]);

  const handleRewardClick = (
    reward: RewardCurrencies,
    event: React.MouseEvent,
  ) => {
    if (isLargeTablet) {
      event.preventDefault();
      setSelectedReward({
        id: reward.id,
        name: reward.systemCurrency.name,
        description: "",
        image: reward.systemCurrency.image,
        rewardAmount: reward.amount,
      });
      setSelectedProject(null);
      router.replace(`/moderation/admin/rewards/${reward.id}`, {
        scroll: false,
      });
    }
  };

  if (isLoading) {
    return <div className="p-4">Loading...</div>;
  }

  if (isError || !rewards) {
    return <div className="p-4">Error loading rewards</div>;
  }

  const formatStatus = (status: RewardCurrencyStatusEnum): string => {
    return status.charAt(0).toUpperCase() + status.slice(1) + " Rewards";
  };

  return (
    <div className="mt-4 flex flex-col">
      <h2 className="font-semibold">Champions Rewards Management</h2>
      <p className="text-sm font-normal">
        Add / remove projects to the Arena Champions rewards pool.
      </p>

      <div className="mt-4 flex flex-col gap-3 overflow-y-auto">
        {rewards.map((reward) => {
          const isSelected = selectedReward?.id === reward.id;

          return (
            <ProgressBarLink
              key={reward.id}
              href={`/moderation/admin/rewards/${reward.id}`}
              onClick={(e) => handleRewardClick(reward, e)}
              className={cn(
                "relative flex items-center justify-between rounded-[10px] border border-[#2a2a2a] p-4",
                isSelected
                  ? "bg-[#2A2A2A]"
                  : "bg-chat-bubble hover:bg-dark-gray",
              )}
            >
              <div className="flex items-center gap-3">
                <Avatar className="size-[30px]">
                  <AvatarImage src={reward.systemCurrency?.image} />
                  <AvatarFallback />
                </Avatar>

                <div>
                  <h2 className="text-sm font-semibold">
                    {reward.systemCurrency?.symbol}
                  </h2>
                  <p className="text-xs text-gray-text">
                    {reward.systemCurrency?.name}
                  </p>
                </div>
              </div>
              <div className="text-right text-sm">
                <h2 className="text-sm font-semibold">Phase</h2>
                <p className="text-sm text-gray-text">
                  {formatStatus(reward.status)}
                </p>
              </div>
              {isSelected && (
                <div className="absolute inset-y-0 right-0 w-1.5 rounded-r-[10px] bg-brand-orange" />
              )}
            </ProgressBarLink>
          );
        })}
      </div>

      <ProgressBarLink
        href="/moderation/admin/rewards/new"
        className={cn(
          "relative mt-3 flex items-center justify-between rounded-[10px] border border-[#2a2a2a] p-4",
          isNewRewardSelected ? "bg-[#2A2A2A]" : "hover:bg-dark-gray",
        )}
        onClick={(e) => {
          if (isLargeTablet) {
            e.preventDefault();
            setSelectedReward(null);
            setSelectedProject(null);
            router.replace("/moderation/admin/rewards/new", {
              scroll: false,
            });
          }
        }}
      >
        <div className="flex items-center gap-3">
          <PlusCircleIcon height={32} width={32} strokeWidth={2} />
          <div>
            <h3 className="font-medium">Add New Reward</h3>
            <p className="text-sm text-gray-text">
              To the Champions Rewards pool
            </p>
          </div>
        </div>
        {isNewRewardSelected && (
          <div className="absolute inset-y-0 right-0 w-1.5 rounded-r-[10px] bg-brand-orange" />
        )}
      </ProgressBarLink>
    </div>
  );
};
