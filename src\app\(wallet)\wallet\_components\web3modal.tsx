"use client";

import { createWeb3Modal, defaultConfig } from "@web3modal/ethers/react";

import { env } from "@/env";

const mainnet = {
  chainId: +env.NEXT_PUBLIC_AVAX_CHAINID,
  name: "Avalanche",
  currency: "AVAX",
  explorerUrl: "https://snowtrace.io/",
  rpcUrl: env.NEXT_PUBLIC_MAINNET_RPC_URL,
};

const metadata = {
  name: "Starsare<PERSON>",
  url: env.NEXT_PUBLIC_APP_DOMAIN,
  description: "",
  icons: [],
};

const ethersConfig = defaultConfig({
  /*Required*/
  metadata,

  /*Optional*/
  enableEIP6963: true, // true by default
  enableInjected: true, // true by default
  enableCoinbase: false, // true by default
});

createWeb3Modal({
  ethersConfig,
  chains: [mainnet],
  projectId: env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID,
});

export function Web3Modal({ children }: { children: React.ReactNode }) {
  return children;
}
