"use client";

import { FC } from "react";
import { redirect } from "next/navigation";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useUnseenCountQuery } from "@/queries/report-tickets-query";
import { useUser } from "@/stores/user";

import { ReportTicketsList } from "./report-tickets-list";

export const Reports: FC = () => {
  const { user } = useUser();

  if (!user?.isMod) {
    redirect("/");
  }

  const { data: unseenCountData } = useUnseenCountQuery();

  return (
    <div className="container mx-auto px-0 py-4 sm:p-4">
      <Tabs defaultValue="active" className="flex flex-grow flex-col">
        <TabsList className="flex">
          <TabsTrigger value="active" className="flex-1">
            Active Tickets{" "}
            {unseenCountData && unseenCountData > 0 && (
              <span className="ml-2 rounded bg-dark-gray px-[3px] py-1 text-xs font-semibold leading-none text-off-white">
                {unseenCountData}
              </span>
            )}
          </TabsTrigger>
          <TabsTrigger value="closed" className="flex-1">
            Closed Tickets
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          <ReportTicketsList status="Active" />
        </TabsContent>

        <TabsContent value="closed">
          <ReportTicketsList status="Closed" />
        </TabsContent>
      </Tabs>
    </div>
  );
};
