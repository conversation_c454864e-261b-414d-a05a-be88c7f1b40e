"use client";

import { useMemo, useState } from "react";

import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQueryClient } from "@tanstack/react-query";
import { Controller, useForm } from "react-hook-form";
import { parseUnits } from "viem";
import { z } from "zod";

import { sendFundsDynamic } from "@/api/client/dynamic/send-funds-dynamic";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Drawer, DrawerTrigger } from "@/components/ui/drawer";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { TextInput } from "@/components/ui/text-input";
import { Drawer as DrawerPrimitive } from "@/components/ui/vaul";
import { fallbackAvax } from "@/environments/tokens";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import { useChainWithdrawMutation } from "@/queries";
import { useSupportedCurrenciesQuery } from "@/queries/currency-queries";
import { useUser } from "@/stores";
import { cn } from "@/utils";
import { formatPrice } from "@/utils/format-token-price";
import { handleNumericInput } from "@/utils/number";

export const WithdrawModal = () => {
  const [open, setOpen] = useState(false);
  const [selectOpen, setSelectOpen] = useState(false);
  const { user } = useUser();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const queryClient = useQueryClient();

  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useSupportedCurrenciesQuery();

  const { sortedCurrencies } = useWalletCurrencies({
    user,
    currenciesData,
    isCurrenciesLoading,
  });

  const [symbol, setSymbol] = useState("AVAX");
  const token = useMemo(
    () => sortedCurrencies.find((t) => t.symbol === symbol) ?? fallbackAvax,
    [sortedCurrencies, symbol],
  );

  const [isPendingDynamic, setIsPendingDynamic] = useState(false);

  const { mutateAsync: withdraw, isPending } = useChainWithdrawMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["shares"],
      });
      queryClient.invalidateQueries({
        queryKey: ["wallet", "balance", user?.address, token.symbol],
      });
      queryClient.invalidateQueries({
        queryKey: ["wallet", "price"],
      });
      queryClient.invalidateQueries({
        queryKey: ["wallet", "fee"],
      });
      toast.green("Withdrawal successful!");
      form.reset();
      setOpen(false);
    },
  });

  const isSolanaCurrency = [
    "SOL",
    "Bonk",
    "$WIF",
    "USEDCAR",
    "Moutai",
    "HARAMBE",
  ].includes(token.symbol);

  const WithdrawInput = z.object({
    currency: z.string(),
    withdrawAddress: z.string().min(1, {
      message: "Withdraw address is required",
    }),
    withdrawAmount: z
      .string()
      .min(1, {
        message: "Withdraw amount is required",
      })
      .refine((v) => !isNaN(parseFloat(v.replace(/,/g, ""))), {
        message: "Withdraw amount must be a number",
      })
      .refine(
        (v) =>
          isSolanaCurrency
            ? true
            : parseFloat(v.replace(/,/g, "")) <=
              parseFloat(
                token.isToken
                  ? formatPrice(token.balance || "0").toString()
                  : (token.balance || "0").toString().replace(/,/g, ""),
              ),
        {
          message: "Insufficient balance",
        },
      ),
  });
  type WithdrawInputType = z.infer<typeof WithdrawInput>;

  const form = useForm<WithdrawInputType>({
    defaultValues: {
      currency: "AVAX",
    },
    resolver: zodResolver(WithdrawInput),
    mode: "all",
  });

  const { primaryWallet } = useDynamicContext();

  const onSubmit = async (data: WithdrawInputType) => {
    if (!isSolanaCurrency) {
      if (user && user.address === user.dynamicAddress?.toLowerCase()) {
        const decimals = data.currency === "MEAT" ? 6 : 18;

        const withdrawAmount = parseUnits(
          data.withdrawAmount.replace(/,/g, ""),
          decimals,
        );

        setIsPendingDynamic(true);
        try {
          await sendFundsDynamic(
            primaryWallet,
            data.withdrawAddress,
            withdrawAmount,
            data.currency,
            token.contractAddress,
          );
        } catch (e) {
          console.log("Something went wrong with sendFundsDynamic:", e);
        } finally {
          setIsPendingDynamic(false);
        }

        queryClient.invalidateQueries({
          queryKey: ["shares"],
        });
        queryClient.invalidateQueries({
          queryKey: ["wallet", "balance", user?.address, token.symbol],
        });
        toast.green("Withdrawal successful!");
        form.reset();
        setOpen(false);
      } else {
        await withdraw({
          currency: data.currency,
          withdrawAddress: data.withdrawAddress,
          withdrawAmount: data.withdrawAmount.replace(/,/g, ""),
        });
      }
    } else {
      await withdraw({
        currency: data.currency,
        withdrawAddress: data.withdrawAddress,
        withdrawAmount: data.withdrawAmount.replace(/,/g, ""),
      });
    }
  };

  if (isTablet) {
    return (
      <Dialog
        open={open}
        onOpenChange={(open) => {
          setOpen(open);
          if (!open) {
            form.reset();
            setSymbol("AVAX");
          }
        }}
      >
        <DialogTrigger asChild>
          <Button variant="outline" className="h-[44px] w-full">
            Withdraw
          </Button>
        </DialogTrigger>
        <DialogContent className="gap-6">
          <div className="flex items-center gap-3">
            <DialogClose className="hidden p-1 sm:inline-block">
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </DialogClose>
            <h3 className="text-base font-semibold leading-[22px] text-off-white">
              Withdraw
            </h3>
          </div>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-4"
          >
            <Controller
              name="currency"
              control={form.control}
              render={({ field: { name, onChange, value } }) => {
                return (
                  <div className="flex flex-col gap-2">
                    <Label htmlFor={`form-item-${name}`}>
                      Balance: {token.balance}
                    </Label>
                    <Select
                      name={name}
                      value={value}
                      onValueChange={(value) => {
                        setSymbol(value);
                        onChange(value);
                      }}
                    >
                      <SelectTrigger
                        className="w-full"
                        id={`form-item-${name}`}
                      >
                        <div className="flex items-center gap-2">
                          <img
                            src={token.photoURL}
                            className="size-4 rounded-full"
                            alt={`${token.symbol} logo`}
                          />
                          <span className="text-base leading-5">
                            {token.symbol}
                          </span>
                        </div>
                      </SelectTrigger>
                      <SelectContent className="max-h-48">
                        {sortedCurrencies.map((token) => (
                          <SelectItem
                            key={token.contractAddress}
                            value={token.symbol}
                            className="group pr-4"
                          >
                            <div className="flex w-full items-center justify-between gap-4">
                              <div className="flex items-center gap-4">
                                <img
                                  src={token.photoURL}
                                  className="size-8 rounded-full"
                                  alt={`${token.symbol} logo`}
                                />
                                {token.symbol}
                              </div>
                              <div className="group-data-[state=checked]:hidden">
                                {token.isToken
                                  ? formatPrice(token.balance || "0")
                                  : token.balance}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                );
              }}
            />
            <div className="relative">
              <TextInput
                label="Wallet address"
                placeholder="Enter wallet address"
                {...form.register("withdrawAddress")}
                errorMessage={form.formState.errors.withdrawAddress?.message}
              />
            </div>
            <Controller
              name="withdrawAmount"
              control={form.control}
              render={({ field: { name, onChange, value, onBlur, ref } }) => {
                return (
                  <TextInput
                    label="Amount to withdraw"
                    placeholder="Enter amount"
                    name={name}
                    value={value}
                    onChange={(event) => {
                      const input = event.target;
                      const position = input.selectionStart ?? 0;
                      const rawValue = input.value;

                      const { value, cursorPosition } = handleNumericInput(
                        rawValue,
                        position,
                      );

                      onChange(value);

                      // Update cursor position after React updates the DOM
                      setTimeout(() => {
                        if (input) {
                          input.setSelectionRange(
                            cursorPosition,
                            cursorPosition,
                          );
                        }
                      }, 0);
                    }}
                    onBlur={onBlur}
                    ref={ref}
                    errorMessage={form.formState.errors.withdrawAmount?.message}
                  />
                );
              }}
            />
            <Button
              className="mt-4 w-full"
              type="submit"
              disabled={isPending || isPendingDynamic}
            >
              Withdraw
            </Button>
          </form>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer
      open={open}
      onOpenChange={(open) => {
        if (!open) form.reset();
        setOpen(open);
      }}
    >
      <DrawerTrigger asChild>
        <Button variant="outline" className="h-[44px] w-full">
          Withdraw
        </Button>
      </DrawerTrigger>
      <DrawerPrimitive.Portal>
        <DrawerPrimitive.Overlay onClick={selectOpen ? () => {} : undefined} />
        <DrawerPrimitive.Content
          className={cn(
            "fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[20px] border border-[#3B3B3B]/30 bg-[#0F0F0F]/90 p-6 pb-12 shadow-[0px_4px_16px_0px_rgba(0,0,0,0.25)] backdrop-blur-sm focus-visible:outline-none",
            "gap-4",
          )}
        >
          <h2 className="text-base font-semibold leading-[22px] text-off-white">
            Withdraw
          </h2>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-4"
          >
            <Controller
              name="currency"
              control={form.control}
              render={({ field: { name, onChange, value } }) => {
                return (
                  <div className="flex flex-col gap-2">
                    <Label htmlFor={`form-item-${name}`}>
                      Balance: {token.balance}
                    </Label>
                    <Select
                      name={name}
                      value={value}
                      onValueChange={(value) => {
                        setSymbol(value);
                        onChange(value);
                      }}
                      open={selectOpen}
                      onOpenChange={setSelectOpen}
                    >
                      <SelectTrigger
                        className="w-full"
                        id={`form-item-${name}`}
                      >
                        <div className="flex items-center gap-2">
                          <img
                            src={token.photoURL}
                            className="size-4 rounded-full"
                            alt={`${token.symbol} logo`}
                          />
                          <span className="text-base leading-5">
                            {token.symbol}
                          </span>
                        </div>
                      </SelectTrigger>
                      <SelectContent className="max-h-48">
                        {sortedCurrencies.map((token) => (
                          <SelectItem
                            key={token.contractAddress}
                            value={token.symbol}
                            className="group pr-4"
                          >
                            <div className="flex w-full items-center justify-between gap-4">
                              <div className="flex items-center gap-4">
                                <img
                                  src={token.photoURL}
                                  className="size-8 rounded-full"
                                  alt={`${token.symbol} logo`}
                                />
                                {token.symbol}
                              </div>
                              <div className="group-data-[state=checked]:hidden">
                                {token.balance}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                );
              }}
            />
            <div className="relative">
              <TextInput
                label="Wallet address"
                placeholder="Enter wallet address"
                {...form.register("withdrawAddress")}
                errorMessage={form.formState.errors.withdrawAddress?.message}
              />
            </div>
            <Controller
              name="withdrawAmount"
              control={form.control}
              render={({ field: { name, onChange, value, onBlur, ref } }) => {
                return (
                  <TextInput
                    label="Amount to withdraw"
                    placeholder="Enter amount"
                    name={name}
                    value={value}
                    onChange={(event) => {
                      const input = event.target;
                      const position = input.selectionStart ?? 0;
                      const rawValue = input.value;

                      const { value, cursorPosition } = handleNumericInput(
                        rawValue,
                        position,
                      );
                      onChange(value);

                      // Update cursor position after React updates the DOM
                      setTimeout(() => {
                        if (input) {
                          input.setSelectionRange(
                            cursorPosition,
                            cursorPosition,
                          );
                        }
                      }, 0);
                    }}
                    onBlur={onBlur}
                    ref={ref}
                    errorMessage={form.formState.errors.withdrawAmount?.message}
                  />
                );
              }}
            />
            <Button
              className="mt-4 w-full"
              type="submit"
              disabled={isPending || isPendingDynamic}
            >
              Withdraw
            </Button>
          </form>
        </DrawerPrimitive.Content>
      </DrawerPrimitive.Portal>
    </Drawer>
  );
};
