import { abbreviateNumber } from "./abbreviate-number";

export const formatAsSubscript = (num: string, showDollarSign = true) => {
  const currencySymbol = showDollarSign ? "$" : "";

  if (!num.includes(".") || Number(num) > 1)
    return `${currencySymbol}${abbreviateNumber(Number(num))}`;

  if (Number(num) === 0) {
    return <span>{currencySymbol}0</span>;
  }

  const [integerPart, decimalPart] = num.split(".");
  const leadingZerosCount = (decimalPart.match(/^0+/) || [""])[0].length;
  const trimmedDecimal = decimalPart.replace(/^0+/, "").slice(0, 4);

  return (
    <span className="flex">
      {currencySymbol}
      {integerPart}.
      {leadingZerosCount > 1 ? (
        <span>
          0<sub>{leadingZerosCount}</sub>
          {trimmedDecimal}
        </span>
      ) : (
        trimmedDecimal
      )}
    </span>
  );
};
