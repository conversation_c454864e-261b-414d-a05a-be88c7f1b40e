import { <PERSON><PERSON><PERSON>, FC, SetStateAction } from "react";

import { But<PERSON> } from "@/components/ui/button";

interface ChatSearchRequestProps {
  setCurrentPage: Dispatch<
    SetStateAction<"messages" | "request" | "receivedRequest">
  >;
}

export const ChatSearchRequest: FC<ChatSearchRequestProps> = ({
  setCurrentPage,
}) => {
  const onClick = () => setCurrentPage("request");

  return (
    <Button
      onClick={onClick}
      className="flex h-11 w-11 items-center justify-center rounded-full bg-brand-orange p-0"
    >
      <img
        src="/icons/chatbubble.svg"
        alt="chatbubble"
        className="h-[24px] w-[24px] flex-shrink-0"
      />
    </Button>
  );
};
