"use client";

import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import { processTicket } from "@/api/client/reports";
import { toast } from "@/components/toast";

export enum ModeratorAction {
  DELETE_POST = "Delete Post",
  SUSPEND_ACCOUNT = "Suspend Account",
  CLOSE_REPORT = "Close Report",
  REOPEN_TICKET = "Re-open Ticket",
}

type ProcessTicketMutationType = MutationOptions<
  void,
  DefaultError,
  { ticketId: number; action: ModeratorAction },
  any
>;

export const useProcessTicketMutation = (
  options?: ProcessTicketMutationType,
) => {
  return useMutation({
    mutationFn: processTicket,
    onSuccess: () => {
      toast.green("Action completed successfully!");
    },
    onError: () => {
      toast.red("Failed to process the ticket. Please try again.");
    },
    ...options,
  });
};
