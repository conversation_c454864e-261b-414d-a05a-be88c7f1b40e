import { ComponentProps } from "react";

export const GroupIcon = (props: ComponentProps<"svg">) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M14.1667 17.1673H18.3334V15.5007C18.3334 14.9811 18.1715 14.4745 17.8702 14.0512C17.5689 13.6279 17.1433 13.309 16.6524 13.1388C16.1615 12.9686 15.6298 12.9556 15.1311 13.1015C14.6325 13.2474 14.1917 13.5451 13.8701 13.9532M14.1667 17.1673H5.83341M14.1667 17.1673V15.5007C14.1667 14.954 14.0617 14.4315 13.8701 13.9532M13.8701 13.9532C13.5606 13.1798 13.0265 12.5169 12.3367 12.0499C11.6469 11.583 10.8331 11.3334 10.0001 11.3334C9.1671 11.3334 8.35322 11.583 7.66342 12.0499C6.97362 12.5169 6.43954 13.1798 6.13008 13.9532M5.83341 17.1673H1.66675V15.5007C1.66679 14.9811 1.82869 14.4745 2.12997 14.0512C2.43124 13.6279 2.8569 13.309 3.34779 13.1388C3.83868 12.9686 4.3704 12.9556 4.86903 13.1015C5.36766 13.2474 5.80844 13.5451 6.13008 13.9532M5.83341 17.1673V15.5007C5.83341 14.954 5.93841 14.4315 6.13008 13.9532M12.5001 6.33398C12.5001 6.99703 12.2367 7.63291 11.7678 8.10175C11.299 8.57059 10.6631 8.83398 10.0001 8.83398C9.33704 8.83398 8.70116 8.57059 8.23231 8.10175C7.76347 7.63291 7.50008 6.99703 7.50008 6.33398C7.50008 5.67094 7.76347 5.03506 8.23231 4.56622C8.70116 4.09738 9.33704 3.83398 10.0001 3.83398C10.6631 3.83398 11.299 4.09738 11.7678 4.56622C12.2367 5.03506 12.5001 5.67094 12.5001 6.33398ZM17.5001 8.83398C17.5001 9.27601 17.3245 9.69993 17.0119 10.0125C16.6994 10.3251 16.2754 10.5007 15.8334 10.5007C15.3914 10.5007 14.9675 10.3251 14.6549 10.0125C14.3423 9.69993 14.1667 9.27601 14.1667 8.83398C14.1667 8.39196 14.3423 7.96803 14.6549 7.65547C14.9675 7.34291 15.3914 7.16732 15.8334 7.16732C16.2754 7.16732 16.6994 7.34291 17.0119 7.65547C17.3245 7.96803 17.5001 8.39196 17.5001 8.83398ZM5.83341 8.83398C5.83341 9.27601 5.65782 9.69993 5.34526 10.0125C5.0327 10.3251 4.60878 10.5007 4.16675 10.5007C3.72472 10.5007 3.3008 10.3251 2.98824 10.0125C2.67568 9.69993 2.50008 9.27601 2.50008 8.83398C2.50008 8.39196 2.67568 7.96803 2.98824 7.65547C3.3008 7.34291 3.72472 7.16732 4.16675 7.16732C4.60878 7.16732 5.0327 7.34291 5.34526 7.65547C5.65782 7.96803 5.83341 8.39196 5.83341 8.83398Z"
      stroke={props.stroke || "#EB540A"}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const OfficialGroupIcon = (props: ComponentProps<"svg">) => (
  <svg
    width="25"
    height="20"
    viewBox="0 0 25 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.1665 16.6663H18.3332V14.9997C18.3331 14.4801 18.1712 13.9735 17.87 13.5502C17.5687 13.1269 17.143 12.808 16.6521 12.6378C16.1612 12.4676 15.6295 12.4546 15.1309 12.6005C14.6323 12.7465 14.1915 13.0442 13.8698 13.4522M14.1665 16.6663H5.83317M14.1665 16.6663V14.9997C14.1665 14.453 14.0615 13.9305 13.8698 13.4522M13.8698 13.4522C13.5604 12.6788 13.0263 12.0159 12.3365 11.5489C11.6467 11.082 10.8328 10.8324 9.99984 10.8324C9.16685 10.8324 8.35297 11.082 7.66317 11.5489C6.97338 12.0159 6.4393 12.6788 6.12984 13.4522M5.83317 16.6663H1.6665V14.9997C1.66654 14.4801 1.82845 13.9735 2.12972 13.5502C2.43099 13.1269 2.85666 12.808 3.34755 12.6378C3.83843 12.4676 4.37015 12.4546 4.86879 12.6005C5.36742 12.7465 5.80819 13.0442 6.12984 13.4522M5.83317 16.6663V14.9997C5.83317 14.453 5.93817 13.9305 6.12984 13.4522M12.4998 5.83301C12.4998 6.49605 12.2364 7.13193 11.7676 7.60077C11.2988 8.06961 10.6629 8.33301 9.99984 8.33301C9.3368 8.33301 8.70091 8.06961 8.23207 7.60077C7.76323 7.13193 7.49984 6.49605 7.49984 5.83301C7.49984 5.16996 7.76323 4.53408 8.23207 4.06524C8.70091 3.5964 9.3368 3.33301 9.99984 3.33301C10.6629 3.33301 11.2988 3.5964 11.7676 4.06524C12.2364 4.53408 12.4998 5.16996 12.4998 5.83301ZM5.83317 8.33301C5.83317 8.77503 5.65758 9.19896 5.34502 9.51152C5.03245 9.82408 4.60853 9.99968 4.1665 9.99968C3.72448 9.99968 3.30055 9.82408 2.98799 9.51152C2.67543 9.19896 2.49984 8.77503 2.49984 8.33301C2.49984 7.89098 2.67543 7.46706 2.98799 7.1545C3.30055 6.84193 3.72448 6.66634 4.1665 6.66634C4.60853 6.66634 5.03245 6.84193 5.34502 7.1545C5.65758 7.46706 5.83317 7.89098 5.83317 8.33301Z"
      stroke="url(#paint0_linear_10001_354532)"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle cx="17.6797" cy="12.3105" r="6.63281" fill="#020202" />
    <rect
      x="12.082"
      y="6.70215"
      width="11.2166"
      height="11.2166"
      rx="5.6083"
      fill="url(#paint1_linear_10001_354532)"
    />
    <path
      d="M20.6738 9.48764C21.536 9.46608 21.9671 10.5007 21.3528 11.1043L16.9555 15.5016C16.6861 15.771 16.2334 15.771 15.964 15.5016L13.733 13.2598C12.8061 12.376 14.1749 11.0073 15.0587 11.9342L16.2981 13.1736C16.3843 13.2598 16.5352 13.2598 16.6322 13.1736L20.0272 9.77863C20.1996 9.59541 20.4259 9.49841 20.6738 9.48764Z"
      fill="#F4F4F4"
    />
    <defs>
      <linearGradient
        id="paint0_linear_10001_354532"
        x1="12.6072"
        y1="11.6663"
        x2="-4.30984"
        y2="5.04696"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#6F15C8" />
        <stop offset="0.968627" stopColor="#AC58FF" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_10001_354532"
        x1="19.445"
        y1="13.7125"
        x2="7.48843"
        y2="9.96976"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#6F15C8" />
        <stop offset="0.968627" stopColor="#AC58FF" />
      </linearGradient>
    </defs>
  </svg>
);
