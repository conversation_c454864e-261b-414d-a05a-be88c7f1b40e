"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";

import {
  InfiniteData,
  useInfiniteQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { format, isThisWeek, isThisYear, isToday, isYesterday } from "date-fns";
import { Loader2Icon } from "lucide-react";
import { useInView } from "react-intersection-observer";

import {
  getMessagesAroundWithActions,
  getMessagesWithActionsAfter,
  getMessagesWithActionsBefore,
} from "@/api/client/chat";
import { ArrowDownFilledIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import { SOCKET_MESSAGE } from "@/environments/socket-messages";
import {
  ActionType,
  ChatMessagesWithActionsResponse,
  CommunityMessagesOrActionsType,
  MessageType,
  Reaction,
} from "@/queries/types/chats";
import { useUser } from "@/stores";
import { useSocket } from "@/stores/socket";
import { cn } from "@/utils";
import { isSafari } from "@/utils/is-safari";

import { useCommunity, useCommunityStore } from "../context/community-context";
import { CommunityInput } from "./community-input";
import { CommunityMessage, MyCommunityMessage } from "./community-message";

type QueryKeyType = (
  | string
  | {
      groupId: string;
      messageId?: string | null;
    }
)[];

export const CommunityConversation = () => {
  const queryClient = useQueryClient();
  const { user } = useUser();
  const { isLoading, groupId } = useCommunity();
  const [showScrollToBottomButton, setShowScrollToBottomButton] =
    useState(false);
  const prevMessagesLengthRef = useRef(0);
  const prevParentScrollHeightRef = useRef(0);
  const fetchingPreviousPageRef = useRef(false);
  const parentRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);
  const { ref: topLoadingRef, inView: topInView } = useInView({
    threshold: 0.8,
  });
  const { ref: bottomLoadingRef, inView: bottomInView } = useInView({
    threshold: 0.8,
  });

  const [reply, setReply] = useState<MessageType | null>(null);

  const [initialLoad, setInitialLoad] = useCommunityStore((state) => [
    state.initialLoad,
    state.actions.setInitialLoad,
  ]);
  const messageId = useCommunityStore((state) => state.messageId);
  const resetMessageId = useCommunityStore(
    (state) => state.actions.resetMessageId,
  );

  const { socket, setSeen } = useSocket();

  const {
    data: messagesWithActionsData,
    fetchPreviousPage,
    isFetchingPreviousPage,
    hasPreviousPage,
    fetchNextPage,
    hasNextPage,
  } = useInfiniteQuery<
    ChatMessagesWithActionsResponse,
    Error,
    InfiniteData<ChatMessagesWithActionsResponse>,
    QueryKeyType,
    {
      timeFrom: number;
      messageId: string | null;
      initialLoad?: boolean;
    }
  >({
    queryKey: [
      "chat",
      "group-infinite-messages-with-actions",
      { groupId, messageId },
    ],
    queryFn: async ({ pageParam, direction }) => {
      if (pageParam.messageId) {
        const messagesWithActionsData = await getMessagesAroundWithActions({
          groupId,
          messageId: pageParam.messageId,
        });

        return messagesWithActionsData;
      }

      if (
        direction === "forward" &&
        pageParam.messageId == null &&
        !pageParam.initialLoad
      ) {
        const messagesWithActionsData = await getMessagesWithActionsAfter({
          groupId,
          timeFrom: pageParam.timeFrom,
        });

        return messagesWithActionsData;
      }

      const messagesWithActionsData = await getMessagesWithActionsBefore({
        groupId,
        timeFrom: pageParam.timeFrom,
      });

      return messagesWithActionsData;
    },
    enabled: !isLoading,
    initialPageParam: {
      timeFrom: 0,
      messageId,
      initialLoad: true,
    },
    getPreviousPageParam: (firstPage) => {
      if (firstPage.messages.length >= 40) {
        return {
          timeFrom: +firstPage.messages[0].createdOn,
          messageId: null,
        };
      }

      return undefined;
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (lastPageParam.initialLoad && !messageId) return undefined;

      if (lastPage.messages.length >= 40) {
        return {
          timeFrom: +lastPage.messages[lastPage.messages.length - 1].createdOn,
          messageId: null,
        };
      }

      return undefined;
    },
  });

  const [groupedMessages, messages] = useMemo(() => {
    const flatMessages =
      messagesWithActionsData?.pages.map((page) => page.messages).flat() ?? [];

    const flatActions =
      messagesWithActionsData?.pages.map((page) => page.actions).flat() ?? [];

    const flatMessagesWithType = flatMessages.map((message) => ({
      eventType: "message",
      message: message,
      createdOn: +message.createdOn,
    }));

    const flatActionsWithType = flatActions.map((action) => ({
      eventType: "action",
      action: action,
      createdOn: +action.createdOn,
    }));

    const flatMessagesAndActions = [
      ...flatMessagesWithType,
      ...flatActionsWithType,
    ].sort((a, b) => a.createdOn - b.createdOn);

    const groupedByDate = flatMessagesAndActions.reduce(
      (acc, event) => {
        const date = new Date(+event.createdOn);
        const formattedDate = format(date, "MM/dd/yyyy");

        if (!acc[formattedDate]) {
          acc[formattedDate] = [];
        }

        acc[formattedDate].push(event);
        return acc;
      },
      {} as Record<string, CommunityMessagesOrActionsType[]>,
    );

    return [Object.entries(groupedByDate), flatMessagesAndActions];
  }, [messagesWithActionsData]);

  const handleScrollToBottom = useCallback(() => {
    setTimeout(() => {
      bottomRef.current?.scrollIntoView({
        behavior: "smooth",
      });
    }, 0);
  }, []);

  const handleScrollToBottomIfAtBottom = useCallback(() => {
    setTimeout(() => {
      const scrollElement = parentRef.current;
      if (!scrollElement) return;

      const scroll =
        scrollElement?.scrollTop + scrollElement?.clientHeight >=
        scrollElement?.scrollHeight - 50;

      if (scroll) {
        bottomRef.current?.scrollIntoView();
      }
    }, 0);
  }, []);

  const handleScrollToBottomButton = useCallback(() => {
    if (messageId && hasNextPage) {
      resetMessageId();
    } else {
      setTimeout(() => {
        bottomRef.current?.scrollIntoView({
          behavior: "smooth",
        });
      }, 0);
    }
  }, [hasNextPage, messageId, resetMessageId]);

  useEffect(() => {
    if (initialLoad && messages.length > 0) {
      if (messageId) {
        const element = document.getElementById(`message-${messageId}`);

        if (element) {
          element.scrollIntoView({
            block: "center",
          });

          const observer = new IntersectionObserver(
            (entries) => {
              if (entries[0].isIntersecting) {
                element.classList.add("animate-highlight-bg");
                setTimeout(() => {
                  element.classList.remove("animate-highlight-bg");
                }, 1500);
                observer.disconnect();
              }
            },
            { threshold: 1 },
          );

          observer.observe(element);
        }
        setShowScrollToBottomButton(true);
      } else {
        bottomRef.current?.scrollIntoView();
      }
      setInitialLoad(false);
    }
  }, [messages, initialLoad, messageId, setInitialLoad]);

  useEffect(() => {
    const scrollElement = parentRef.current;
    if (
      topInView &&
      !isFetchingPreviousPage &&
      scrollElement &&
      !fetchingPreviousPageRef.current
    ) {
      const scrollHeight = scrollElement.scrollHeight;
      prevParentScrollHeightRef.current = scrollHeight;
      fetchingPreviousPageRef.current = true;
      fetchPreviousPage();
    }
  }, [topInView, isFetchingPreviousPage, fetchPreviousPage]);

  useEffect(() => {
    if (bottomInView) {
      fetchNextPage();
    }
  }, [bottomInView, fetchNextPage]);

  useEffect(() => {
    if (messages.length > prevMessagesLengthRef.current && !initialLoad) {
      const scrollElement = parentRef.current;
      if (scrollElement && fetchingPreviousPageRef.current) {
        const scrollHeight = scrollElement.scrollHeight;
        const newScrollTop = scrollHeight - prevParentScrollHeightRef.current;
        scrollElement.scrollTo({
          top: newScrollTop,
        });
        if (isSafari()) {
          scrollElement.style.display = "none";
          scrollElement.offsetHeight;
          scrollElement.style.display = "";
        }
        fetchingPreviousPageRef.current = false;
      }
    }
    prevMessagesLengthRef.current = messages.length;
  }, [messages, initialLoad]);

  useEffect(() => {
    const scrollElement = parentRef.current;
    if (scrollElement) {
      const handleScroll = () => {
        if (hasNextPage && messageId) return;

        setShowScrollToBottomButton((prev) => {
          const show =
            scrollElement?.scrollTop + scrollElement?.clientHeight <=
            scrollElement?.scrollHeight - 50;

          if (show !== prev) {
            return show;
          }

          return prev;
        });
      };
      scrollElement.addEventListener("scroll", handleScroll);
      return () => scrollElement.removeEventListener("scroll", handleScroll);
    }
  }, [hasNextPage, messageId]);

  useEffect(() => {
    if (socket && groupId) {
      socket.emit("subscribe", `group-${groupId}`);
    }

    return () => {
      if (socket && groupId) {
        socket.emit("unsubscribe", `group-${groupId}`);
      }
    };
  }, [socket, groupId]);

  useEffect(() => {
    socket?.on(
      SOCKET_MESSAGE.CHAT_MESSAGE,
      async (data: { message: MessageType }) => {
        if (!data.message || !parentRef.current) return;

        if (data.message.groupId !== groupId) return;

        const isAtBottom =
          parentRef.current?.scrollHeight -
            parentRef.current?.scrollTop -
            parentRef.current?.clientHeight ===
          0;

        if (!hasNextPage) {
          queryClient.setQueryData(
            [
              "chat",
              "group-infinite-messages-with-actions",
              {
                groupId,
                messageId,
              },
            ],
            (
              old: InfiniteData<{
                messages: MessageType[];
                actions: ActionType[];
              }>,
            ) => {
              if (!old) return old;

              return {
                ...old,
                pages: old.pages.map((page, index) => {
                  if (index === old.pages.length - 1) {
                    const indexToReplace = page.messages.findLastIndex((m) =>
                      m.id.includes("remove"),
                    );

                    if (indexToReplace !== -1) {
                      page.messages[indexToReplace] = data.message;
                      return {
                        ...page,
                        messages: page.messages,
                      };
                    }

                    return {
                      ...page,
                      messages: [...page.messages, data.message],
                    };
                  }
                  return page;
                }),
              };
            },
          );

          if (isAtBottom === true) {
            handleScrollToBottom();
          }
        }

        setSeen(groupId, Date.now());

        queryClient.invalidateQueries({
          queryKey: ["chat", "conversations"],
        });
        queryClient.invalidateQueries({
          queryKey: ["chat", "direct-messages"],
        });
      },
    );
    socket?.on(`chat-message-reaction`, async (data: Reaction) => {
      queryClient.setQueryData(
        [
          "chat",
          "group-infinite-messages-with-actions",
          { groupId, messageId },
        ],
        (old: InfiniteData<ChatMessagesWithActionsResponse>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page, index) => {
              if (
                page.messages.some((message) => message.id === data.messageId)
              ) {
                return {
                  ...page,
                  messages: page.messages.map((message) => {
                    if (message.id === data.messageId) {
                      const filteredReactions = message.reactions?.filter(
                        (reaction) => reaction.userId !== data.userId,
                      );
                      return {
                        ...message,
                        reactions: [...filteredReactions, data],
                      };
                    }
                    return message;
                  }),
                };
              }

              return page;
            }),
          };
        },
      );
      handleScrollToBottomIfAtBottom();
    });
    socket?.on(`add-chat-reaction`, async (data: Reaction) => {
      queryClient.setQueryData(
        [
          "chat",
          "group-infinite-messages-with-actions",
          { groupId, messageId },
        ],
        (old: InfiniteData<ChatMessagesWithActionsResponse>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page, index) => {
              if (
                page.messages.some((message) => message.id === data.messageId)
              ) {
                return {
                  ...page,
                  messages: page.messages.map((message) => {
                    if (message.id === data.messageId) {
                      const filteredReactions = message.reactions?.filter(
                        (reaction) => reaction.userId !== data.userId,
                      );
                      return {
                        ...message,
                        reactions: [...filteredReactions, data],
                      };
                    }
                    return message;
                  }),
                };
              }

              return page;
            }),
          };
        },
      );
      handleScrollToBottomIfAtBottom();
    });
    socket?.on(`delete-chat-reaction`, async (data: Reaction) => {
      queryClient.setQueryData(
        [
          "chat",
          "group-infinite-messages-with-actions",
          { groupId, messageId },
        ],
        (old: InfiniteData<ChatMessagesWithActionsResponse>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              if (
                page.messages.some((message) => message.id === data.messageId)
              ) {
                return {
                  ...page,
                  messages: page.messages.map((message) => {
                    if (message.id === data.messageId) {
                      const filteredReactions = message.reactions?.filter(
                        (reaction) => reaction.userId !== data.userId,
                      );
                      return {
                        ...message,
                        reactions: filteredReactions,
                      };
                    }
                    return message;
                  }),
                };
              }

              return page;
            }),
          };
        },
      );
      handleScrollToBottomIfAtBottom();
    });

    return () => {
      setSeen(groupId, Date.now());
      socket?.off(SOCKET_MESSAGE.CHAT_MESSAGE);
    };
  }, [
    groupId,
    queryClient,
    socket,
    setSeen,
    handleScrollToBottom,
    handleScrollToBottomIfAtBottom,
    hasNextPage,
    messageId,
  ]);

  return (
    <>
      <div
        className="flex-grow overflow-y-auto overflow-x-hidden py-2"
        ref={parentRef}
      >
        {hasPreviousPage && (
          <div
            className="flex w-full items-center justify-center py-4"
            ref={topLoadingRef}
          >
            <Loader2Icon className="size-6 animate-spin text-brand-orange" />
          </div>
        )}
        {groupedMessages.map(([date, messages]) => {
          const formattedDate = formatTimeDistance(date);

          return (
            <div key={date} className="relative">
              <div className="sticky top-0 z-10 flex justify-center pb-1 pt-5">
                <div className="min-w-[100px] rounded-full border border-dark-gray/50  bg-dark-bk px-3 py-1 text-center text-xs text-off-white">
                  {formattedDate}
                </div>
              </div>
              {messages.map((message, index) => {
                if (message.message) {
                  const previousMessage = messages?.[index - 1];
                  const nextMessage = messages?.[index + 1];

                  if (user && message.message?.userId === user.id) {
                    const isPreviousSameUser =
                      previousMessage?.message?.userId === user.id;
                    const isNextSameUser =
                      nextMessage?.message?.userId === user.id;

                    return (
                      <MyCommunityMessage
                        key={message.message.id}
                        message={message.message}
                        isPreviousSameUser={isPreviousSameUser}
                        isNextSameUser={isNextSameUser}
                        setReply={setReply}
                        scrollToBottom={handleScrollToBottomIfAtBottom}
                      />
                    );
                  }

                  const isPreviousSameUser =
                    previousMessage?.message?.userId ===
                    message?.message?.userId;
                  const isNextSameUser =
                    nextMessage?.message?.userId === message?.message?.userId;

                  return (
                    <CommunityMessage
                      key={message.message.id}
                      message={message.message}
                      isPreviousSameUser={isPreviousSameUser}
                      isNextSameUser={isNextSameUser}
                      setReply={setReply}
                      scrollToBottom={handleScrollToBottomIfAtBottom}
                    />
                  );
                }
              })}
            </div>
          );
        })}
        {hasNextPage && (
          <div
            className="flex w-full items-center justify-center py-4"
            ref={bottomLoadingRef}
          >
            <Loader2Icon className="size-6 animate-spin text-brand-orange" />
          </div>
        )}
        <div
          className="pointer-events-none invisible opacity-0"
          ref={bottomRef}
        />
      </div>
      <div className="sticky bottom-0 z-10 flex-shrink-0">
        <CommunityInput
          reply={reply}
          setReply={setReply}
          scrollToBottom={handleScrollToBottomButton}
          canOptimisticUpdate={!hasNextPage}
        />
        <Button
          onClick={handleScrollToBottomButton}
          variant="outline"
          className={cn(
            "absolute -top-14 left-1/2 size-10 -translate-x-1/2 border-dark-gray/50 bg-dark-bk p-0 transition-opacity duration-200 hover:bg-dark-bk",
            showScrollToBottomButton
              ? "opacity-100"
              : "pointer-events-none  opacity-0",
          )}
        >
          <ArrowDownFilledIcon className="size-5 text-off-white" />
        </Button>
      </div>
    </>
  );
};

export function formatTimeDistance(date: Date | number | string) {
  if (!date) return "";

  if (typeof date === "string" && !isNaN(+date)) {
    date = +date;
  }

  if (!(date instanceof Date)) {
    date = new Date(date);
  }

  if (isToday(date)) {
    return "Today";
  }

  if (isYesterday(date)) {
    return "Yesterday";
  }

  if (isThisWeek(date)) {
    return format(date, "EEEE");
  }

  if (isThisYear(date)) {
    return format(date, "MMMM d");
  }

  return format(date, "MMMM d, yyyy");
}
