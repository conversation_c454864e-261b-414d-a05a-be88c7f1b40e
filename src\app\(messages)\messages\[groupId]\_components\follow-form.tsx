import { <PERSON><PERSON><PERSON>, FC, SetStateAction } from "react";

import { ProgressBarLink } from "@/components/progress-bar";
import { But<PERSON> } from "@/components/ui/button";

export const FollowForm = () => {
  const hint = `You must be followed by this user to be able to send Direct Messages.`;
  return (
    <div className="flex w-full flex-col gap-4 border-r-2 border-t-2 border-dark-gray bg-gray-bg p-6">
      <span className="text-sm">{hint}</span>
      <div className="flex w-full flex-col gap-2.5">
        <ProgressBarLink href={`/messages`}>
          <Button variant="outline" className="w-full">
            Back to Messages
          </Button>
        </ProgressBarLink>
      </div>
    </div>
  );
};
