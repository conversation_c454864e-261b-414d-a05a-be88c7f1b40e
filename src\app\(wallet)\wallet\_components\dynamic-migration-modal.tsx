"use client";

import { useState } from "react";

import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useMigrateDynamicMutation } from "@/queries/profile-mutations";

export const DynamicMigrationModal = () => {
  const [open, setOpen] = useState(true);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const queryClient = useQueryClient();

  const { handleLogOut } = useDynamicContext();

  const { mutateAsync: migrateDynamic, isPending } = useMigrateDynamicMutation({
    onMutate: async () => {},
    onError: (err, variables, context) => {
      console.error(err);
      toast.red("Something went wrong");
    },
    onSuccess: async () => {
      toast.green(
        "Migration was successful, you will be logged out in 5 seconds.",
      );
      queryClient.invalidateQueries({ queryKey: ["user", "me"] });
      setOpen(false);

      await new Promise((resolve) => setTimeout(resolve, 5000));

      await handleLogOut();
    },
  });

  const handleMigrateDynamic = async () => {
    await migrateDynamic();
  };

  if (isTablet) {
    return (
      <Dialog
        open={open}
        onOpenChange={(isOpen) => {
          setOpen(isOpen);
        }}
      >
        <DialogContent className="gap-6">
          <div className="flex items-center gap-3">
            <h3 className="text-base font-semibold leading-[22px] text-off-white">
              Migrate your wallet
            </h3>
          </div>

          <p className="text-sm text-[#B5B5B5]">
            A newer, safer non custodial wallet will replace your current one.
            This wallet will be also automatically set up to claim the airdrop
            easily.
          </p>

          <ol className="flex flex-col gap-4 text-sm text-light-gray-text">
            <li className="flex items-center gap-1">
              You&apos;ll still be able to export your old wallet.
            </li>
            <li className="flex items-center gap-1">
              Migrating your assets will have a small gas cost.
            </li>
            <li className="flex items-center gap-1">
              You will be logged out in case of a successful migration.
            </li>
          </ol>

          <Button
            className="mt-4 w-full"
            type="submit"
            onClick={handleMigrateDynamic}
            loading={isPending}
          >
            Migrate Wallet
          </Button>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
      }}
    >
      <DrawerContent className="gap-4">
        <div className="flex items-center gap-3">
          <h3 className="text-base font-semibold leading-[22px] text-off-white">
            Migrate your wallet
          </h3>
        </div>

        <p className="text-sm text-[#B5B5B5]">
          A newer, safer non custodial wallet will replace your current one.
          This wallet will be also automatically set up to claim the airdrop
          easily.
        </p>

        <ol className="flex flex-col gap-4 text-sm text-light-gray-text">
          <li className="flex items-center gap-1">
            You&apos;ll still be able to export your old wallet.
          </li>
          <li className="flex items-center gap-1">
            Migrating your assets will have a small gas cost.
          </li>
          <li className="flex items-center gap-1">
            You will be logged out in case of a successful migration.
          </li>
        </ol>

        <Button
          className="mt-4 w-full"
          type="submit"
          onClick={handleMigrateDynamic}
          loading={isPending}
        >
          Migrate Wallet
        </Button>
      </DrawerContent>
    </Drawer>
  );
};
