import { Button } from "@/components/ui/button";
import { ThreadUser, User } from "@/types";

export const SubmitPage = ({
  user,
  handleReport,
}: {
  user: User | ThreadUser;
  handleReport: (blockUser: boolean) => void;
}) => (
  <div className="px-6">
    <div className="flex flex-col gap-2 ">
      <h4 className="text-xl font-semibold leading-none text-off-white">
        Thanks for helping make The Arena better for everyone
      </h4>
      <p className="text-sm text-gray-text">
        We know it wasn&apos;t easy, so we appreciate you taking the time to
        answer those questions.
      </p>
    </div>

    <div className="mt-4 flex flex-col gap-2 py-2">
      <h5 className="text-sm font-semibold text-white">
        What&apos;s happening now
      </h5>
      <p className="text-sm text-gray-text">
        We received your report. We&apos;ll hide the reported post from your
        timeline in the meantime.
      </p>
    </div>
    <div className="flex flex-col gap-2 py-2">
      <h5 className="text-sm font-semibold text-white">What&apos;s next</h5>
      <p className="text-sm text-gray-text">
        It&apos;ll take a few days for our team to review your report.
        We&apos;ll notify you if we found a rule violation and we&apos;ll let
        you know the actions we&apos;re taking as a result.
      </p>
    </div>
    <div className="flex flex-col gap-2 py-2">
      <h5 className="text-sm font-semibold text-white">
        Additional things you can do in the meantime
      </h5>
      <p className="text-sm text-gray-text">
        Block @{user.twitterHandle} from following or messaging you. They will
        be able to see your public posts, but will no longer be able to engage
        with them. You also won&apos;t see any posts or notifications from @
        {user.twitterHandle}.
      </p>
    </div>

    <div className="mt-4 flex flex-col gap-4">
      <Button
        variant="outline"
        className="w-full"
        onClick={() => handleReport(true)}
      >
        Block @{user.twitterHandle}
      </Button>
    </div>
  </div>
);
