"use client";

import { useQuery } from "@tanstack/react-query";

import { stageQueries } from "@/queries/stage-queries";
import { cn } from "@/utils";

import { LiveStreamInfoPill } from "../livestream/live-stream-info-pill";
import { LiveStageInfoPill } from "./live-stage-info-pill";

export function LiveStagesSidebar() {
  const { data, isLoading } = useQuery({
    ...stageQueries.liveStages(),
    refetchInterval: 60 * 1000,
  });

  if (isLoading || !data || data?.live.length === 0) return null;

  return (
    <div
      className={cn(
        "relative z-20 h-fit max-h-60 flex-shrink-0 overflow-hidden rounded-[20px] border-dark-gray lg:mt-4 lg:border",
        data.live.length <= 2 && "min-h-fit",
      )}
    >
      <div className="h-full flex-grow overflow-y-auto overscroll-contain rounded-[20px] p-0">
        <div className="sticky top-0 z-10 bg-dark-bk/80 px-6 pb-2 pt-4 backdrop-blur-md ">
          <h2 className="text-base font-semibold leading-5 text-off-white">
            Live
          </h2>
        </div>
        <div className="flex flex-grow flex-col gap-2 p-4 pt-2">
          {data.live.map((item) => {
            if (item.type === "livestream") {
              return (
                <LiveStreamInfoPill
                  key={item.id}
                  livestream={item}
                  variant="desktop"
                />
              );
            }
            return (
              <LiveStageInfoPill key={item.id} stage={item} variant="desktop" />
            );
          })}
        </div>
      </div>
      <div className="mask-top-to-bottom pointer-events-none absolute inset-x-0 bottom-0 z-30 h-8 rounded-b-[20px] bg-gradient-to-t from-[rgba(2,2,2,1)] via-[rgba(2,2,2,0.6)] to-[rgba(2,2,2,0)]" />
    </div>
  );
}
