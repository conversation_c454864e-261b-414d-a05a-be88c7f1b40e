interface PageHeadingProps {
  children: React.ReactNode;
}

export const PageHeading = ({ children }: PageHeadingProps) => {
  return (
    <div className="relative pl-6 pt-11">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 390 229"
        fill="none"
        className="pointer-events-none absolute inset-x-0 -top-5 select-none"
        aria-hidden
      >
        <g
          opacity="0.5"
          filter="url(#filter0_f_457_17639)"
          className="h-full w-full"
        >
          <path
            d="M0 -20H390V204C390 204 332 49.8477 195 49.8477C58 49.8477 0 204 0 204V-20Z"
            fill="url(#paint0_linear_457_17639)"
            className="h-full w-full"
          />
        </g>
        <defs>
          <filter
            id="filter0_f_457_17639"
            x="-25"
            y="-45"
            width="440"
            height="274"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feGaussianBlur
              stdDeviation="12.5"
              result="effect1_foregroundBlur_457_17639"
            />
          </filter>
          <linearGradient
            id="paint0_linear_457_17639"
            x1="195"
            y1="-53.5"
            x2="195"
            y2="298.57"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#161616" />
            <stop offset="1" stopColor="#161616" stopOpacity="0" />
          </linearGradient>
        </defs>
      </svg>
      <h1 className="text-2xl font-semibold">{children}</h1>
    </div>
  );
};
