"use client";

import { FC, memo, useCallback, useRef, useState } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import * as z from "zod";

import { SystemCurrencyCategoryEnum } from "@/api/client/admin-currency";
import { CategoryCheckboxes } from "@/components/category-checkboxes";
import { ImageOutlineIcon } from "@/components/icons/image-outline";
import { toast } from "@/components/toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { TextInput } from "@/components/ui/text-input";
import { compressImageToJpeg } from "@/utils/compress-jpeg";
import { upload } from "@/utils/upload";

export const currencySchema = z.object({
  name: z.string().min(1, "Name is required"),
  symbol: z.string().min(1, "Symbol is required"),
  address: z.string().optional(),
  decimals: z.number().min(0).max(18).default(18),
  image: z.string().default(""),
  systemRate: z.string().default("0"),
  categories: z
    .object({
      [SystemCurrencyCategoryEnum.SUPPORTED]: z.boolean().default(false),
      [SystemCurrencyCategoryEnum.TIPPING]: z.boolean().default(false),
      [SystemCurrencyCategoryEnum.EXCHANGE]: z.boolean().default(false),
    })
    .default({}),
});

export type CurrencyFormData = z.infer<typeof currencySchema>;

export interface CurrencyFormProps {
  defaultValues?: Partial<CurrencyFormData>;
  onSubmit: (data: CurrencyFormData) => Promise<void>;
  isSubmitting?: boolean;
  existingImage?: string;
  children?: React.ReactNode;
}

export const CurrencyForm: FC<CurrencyFormProps> = memo(
  ({
    defaultValues = {},
    onSubmit,
    isSubmitting = false,
    existingImage,
    children,
  }) => {
    const [uploadProgress, setUploadProgress] = useState<number>(0);
    const [isUploading, setIsUploading] = useState<boolean>(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const initialValues: CurrencyFormData = {
      name: "",
      symbol: "",
      address: "",
      decimals: 18,
      image: "",
      systemRate: "0",
      categories: {
        [SystemCurrencyCategoryEnum.SUPPORTED]: false,
        [SystemCurrencyCategoryEnum.TIPPING]: false,
        [SystemCurrencyCategoryEnum.EXCHANGE]: false,
      },
      ...defaultValues,
    };

    const {
      control,
      handleSubmit,
      formState: { errors, isValid, isDirty },
      setValue,
      watch,
    } = useForm<CurrencyFormData>({
      resolver: zodResolver(currencySchema),
      defaultValues: initialValues,
      mode: "onChange",
      reValidateMode: "onChange",
    });

    const imageUrl = watch("image");
    const categories = watch("categories") as Record<
      SystemCurrencyCategoryEnum,
      boolean
    >;

    const handleCategoryChange = useCallback(
      (category: SystemCurrencyCategoryEnum, value: boolean) => {
        if (category === SystemCurrencyCategoryEnum.SUPPORTED) {
          setValue(
            "categories",
            {
              ...categories,
              [SystemCurrencyCategoryEnum.SUPPORTED]: value,
              [SystemCurrencyCategoryEnum.TIPPING]: value
                ? categories[SystemCurrencyCategoryEnum.TIPPING]
                : false,
              [SystemCurrencyCategoryEnum.EXCHANGE]: value
                ? categories[SystemCurrencyCategoryEnum.EXCHANGE]
                : false,
            },
            { shouldDirty: true },
          );
        } else {
          if (!categories[SystemCurrencyCategoryEnum.SUPPORTED]) {
            return;
          }
          setValue(
            "categories",
            {
              ...categories,
              [category]: value,
            },
            { shouldDirty: true },
          );
        }
      },
      [categories, setValue],
    );

    const handleImageClick = useCallback(() => {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    }, []);

    const handleImageUpload = useCallback(
      async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        setIsUploading(true);
        setUploadProgress(0);
        let fileToUpload = files[0];

        try {
          if (
            fileToUpload.type.includes("image") &&
            (fileToUpload.type.includes("image/jpeg") ||
              fileToUpload.type.includes("image/png")) &&
            fileToUpload.size > 500 * 1024
          ) {
            const compressed = await compressImageToJpeg(
              fileToUpload,
              (progress) => {
                setUploadProgress(progress * 0.5);
              },
            );
            if (compressed) {
              fileToUpload = compressed;
            }
          }

          const uploadResult = await upload({
            file: fileToUpload,
            onProgressChange: (progress) => {
              setUploadProgress(50 + progress * 0.5);
            },
          });

          setValue("image", uploadResult.url, { shouldDirty: true });
          toast.green("Image uploaded successfully");
        } catch (error) {
          toast.danger(
            error instanceof Error ? error.message : "Failed to upload image",
          );
        } finally {
          setIsUploading(false);
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        }
      },
      [setValue],
    );

    const handleFormSubmit = useCallback(
      async (data: CurrencyFormData) => {
        await onSubmit(data);
      },
      [onSubmit],
    );

    return (
      <form
        onSubmit={(e) => {
          e.preventDefault();
          const data = watch();
          handleFormSubmit(data);
        }}
        className="flex flex-col"
      >
        <div className="mb-8 flex flex-col items-start">
          <input
            type="file"
            accept="image/*"
            ref={fileInputRef}
            className="hidden"
            onChange={handleImageUpload}
          />
          <div
            className="bg-black relative flex h-[84px] w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-full border-[1.5px] border-gray-text"
            onClick={handleImageClick}
          >
            {imageUrl ? (
              <Avatar className="size-[84px]">
                <AvatarImage src={imageUrl} />
                <AvatarFallback />
              </Avatar>
            ) : existingImage ? (
              <Avatar className="size-[84px]">
                <AvatarImage src={existingImage} />
                <AvatarFallback />
              </Avatar>
            ) : (
              <ImageOutlineIcon className="h-8 w-8 text-gray-text" />
            )}
            {isUploading && (
              <div className="bg-black absolute inset-0 flex items-center justify-center bg-opacity-50">
                <div className="text-xs text-white">
                  {Math.round(uploadProgress)}%
                </div>
              </div>
            )}
          </div>
          <span className="text-gray-400 mt-4 text-sm font-medium uppercase">
            {existingImage ? "UPDATE PHOTO" : "ADD A PHOTO"}
          </span>
        </div>

        <div className="space-y-6">
          <div>
            <Label className="text-gray-400 text-xs font-medium uppercase">
              TICKER
            </Label>
            <Controller
              name="symbol"
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  errorMessage={errors.symbol?.message}
                  placeholder="$TOKEN"
                  className="mt-2 text-off-white"
                />
              )}
            />
          </div>

          <div>
            <Label className="text-gray-400 text-xs font-medium uppercase">
              TOKEN NAME
            </Label>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  errorMessage={errors.name?.message}
                  placeholder="token name"
                  className="mt-2  text-off-white"
                />
              )}
            />
          </div>

          <div>
            <Label className="text-gray-400 text-xs font-medium uppercase">
              CONTRACT ADDRESS
            </Label>
            <Controller
              name="address"
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  errorMessage={errors.address?.message}
                  placeholder="0xExample"
                  className="mt-2  text-off-white"
                />
              )}
            />
          </div>

          <div className="space-y-2">
            <CategoryCheckboxes
              categories={categories}
              onCategoryChange={handleCategoryChange}
            />
          </div>

          {children}
        </div>

        <Button
          type="submit"
          variant="default"
          className="mt-8"
          disabled={isSubmitting || !isDirty}
        >
          Save
        </Button>
      </form>
    );
  },
);

CurrencyForm.displayName = "CurrencyForm";
