import { FC, useEffect, useState } from "react";

import { UseMutateAsyncFunction } from "@tanstack/react-query";
import { useInView } from "react-intersection-observer";

import { ProjectChatEmptyModal } from "@/app/(main)/community/_components/community-phase-modals";
import {
  ArrowBackOutlineIcon,
  PinOutlineIcon,
  UnpinOutlineIcon,
} from "@/components/icons";
import { useSearchProjectChatsInfiniteQuery } from "@/queries/chat-queries";
import { Group, PinConversationRequest } from "@/queries/types/chats";
import { useSocket } from "@/stores/socket";

import { MessageItem, MessageItemLoadingSkeleton } from "./message-item";
import { TypingsType } from "./messages";
import { NoItemsFound } from "./no-rooms-found";

interface GroupMessageProps {
  groups: Group[] | undefined;
  isLoading: boolean;
  typings: TypingsType;
  params: {
    groupId?: string | undefined;
  };
  isFetchingNextPage: boolean;
  pinProjectConversation: UseMutateAsyncFunction<
    any,
    Error,
    PinConversationRequest,
    any
  >;
  fetchNextGroupsPage: any;
  throttledSearch: string;
}

export const GroupMessages: FC<GroupMessageProps> = ({
  groups,
  isLoading,
  typings,
  params,
  isFetchingNextPage,
  pinProjectConversation,
  fetchNextGroupsPage,
  throttledSearch,
}) => {
  const [selectedGroups, setSelectedGroups] = useState<Group[]>([]);
  const { ref, inView } = useInView();
  const {
    data: searchConversationsData,
    isLoading: isSearchConversationsLoading,
  } = useSearchProjectChatsInfiniteQuery(throttledSearch);

  if (searchConversationsData && throttledSearch) {
    groups = searchConversationsData?.pages.map((page) => page.groups).flat();
  }
  const { setSeen } = useSocket();

  useEffect(() => {
    if (inView) {
      fetchNextGroupsPage();
    }
  }, [inView]);

  if (throttledSearch === "" && (!groups || groups.length === 0)) {
    return (
      <div className="mt-28">
        <ProjectChatEmptyModal />
      </div>
    );
  }

  return (
    <>
      <div className="mt-[23px] px-2">
        {throttledSearch !== "" &&
          !isSearchConversationsLoading &&
          groups?.length === 0 && <NoItemsFound tab="groups" />}
        {!isLoading &&
          !isSearchConversationsLoading &&
          groups &&
          groups?.map((group, i) => {
            return (
              <MessageItem
                key={group.id}
                group={group}
                setSeen={() => {
                  setSeen(group.id, Date.now());
                }}
                isCommunity={Boolean(group.communityId && group.community)}
                community={group.community}
                selectGroup={() => {
                  setSelectedGroups((prev) => {
                    if (prev.length === 1) {
                      return prev;
                    }

                    if (prev.find(({ id }) => id === group.id)) {
                      return prev.filter((g) => g.id !== group.id);
                    }

                    return [...prev, group];
                  });
                }}
                isSelected={Boolean(
                  selectedGroups.find(({ id }) => id === group.id),
                )}
                typingUser={
                  typings[group.id] ? typings[group.id].user.name : undefined
                }
                isConversationOpen={group.id === params?.groupId}
                search={throttledSearch}
                isProjectChat
              />
            );
          })}
        {(isLoading ||
          isFetchingNextPage ||
          (throttledSearch !== "" && isSearchConversationsLoading)) && (
          <>
            {Array(9)
              .fill(null)
              .map((_, index) => (
                <MessageItemLoadingSkeleton key={index} />
              ))}
          </>
        )}
        {!isLoading && !isFetchingNextPage && throttledSearch === "" && (
          <div ref={ref} style={{ visibility: "hidden" }}>
            <p>.</p>
          </div>
        )}
        {selectedGroups.length > 0 && (
          <div className="top-pwa fixed inset-x-0 flex h-[54px] items-center justify-between bg-dark-bk px-6 pt-6 text-off-white">
            <div className="flex items-center gap-4">
              <button
                onClick={() => {
                  setSelectedGroups([]);
                }}
              >
                <ArrowBackOutlineIcon className="size-5" />
              </button>
              <div className="text-base leading-5">{selectedGroups.length}</div>
            </div>
            <div>
              <button
                onClick={async () => {
                  const group = selectedGroups[0];
                  setSelectedGroups([]);
                  await pinProjectConversation({
                    groupId: group.id,
                    isPinned: !group.memberLink?.isPinned,
                  });
                }}
              >
                {selectedGroups[0].memberLink?.isPinned ? (
                  <UnpinOutlineIcon className="size-6" />
                ) : (
                  <PinOutlineIcon className="size-6" />
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
};
