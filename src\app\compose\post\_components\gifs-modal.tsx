import { useState } from "react";

import { Loader2Icon } from "lucide-react";

import {
  ArrowBackOutlineIcon,
  GIFOutlineIcon,
  SearchFilledIcon,
} from "@/components/icons";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import useThrottle from "@/hooks/use-throttle";
import { useGIFsQuery } from "@/queries";
import { GIFType } from "@/queries/types/threads";

interface GIFsModalProps {
  onSelect: (gif: GIFType) => void;
  children: React.ReactNode;
}

export const GIFsModal = ({ onSelect, children }: GIFsModalProps) => {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");
  const throttledSearch = useThrottle(search);
  const { data, isLoading } = useGIFsQuery(throttledSearch);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="pb-pwa flex h-full w-full flex-col overflow-y-auto bg-dark-bk p-0 sm:max-h-[700px] sm:max-w-[700px]">
        <div className="sticky top-0 z-10 flex items-center gap-4 border-b border-dark-gray bg-dark-bk px-6 pb-4 pt-[calc(1rem+env(safe-area-inset-top))]">
          <DialogClose className="flex flex-shrink-0">
            <ArrowBackOutlineIcon className="size-5 text-off-white" />
          </DialogClose>
          <div className="relative flex-grow">
            <Input
              value={search}
              onChange={(e) => {
                setSearch(e.target.value);
              }}
              placeholder="Search for GIFs"
              className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-5 placeholder:text-gray-text"
            />
            <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
          </div>
        </div>
        {isLoading && (
          <div className="flex items-center justify-center py-10">
            <Loader2Icon className="size-8 animate-spin text-brand-orange" />
          </div>
        )}
        {!isLoading && data && (
          <>
            <div className="columns-2 gap-2 space-y-2 px-2 sm:columns-3">
              {data?.results.map((gif) => (
                <div
                  key={gif.id}
                  className="relative w-full"
                  onClick={() => {
                    onSelect(gif);
                    setOpen(false);
                  }}
                >
                  <img
                    src={gif.media_formats.gif.url}
                    alt={gif.title}
                    className="w-full"
                  />
                  <div className="bg-black absolute bottom-0 left-0 right-0 bg-opacity-50 p-2 text-xs text-white">
                    {gif.title}
                  </div>
                </div>
              ))}
            </div>
            <div className="py-4 text-center text-sm text-off-white">
              Powered by Tenor
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};
