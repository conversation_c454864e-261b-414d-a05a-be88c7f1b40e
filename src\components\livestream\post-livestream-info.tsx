"use client";

import { useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";

import { useQuery } from "@tanstack/react-query";
import { format, parseISO } from "date-fns";
import { useInView } from "react-intersection-observer";

import {
  livestreamQueries,
  useRemindLivestreamMutation,
  useStartLivestreamMutation,
} from "@/queries";
import { Livestream, LivestreamBadgeType } from "@/queries/types/livestream";
import { useUser } from "@/stores";
import { useLivestreamStore } from "@/stores/livestream";
import { useStageStore } from "@/stores/stage";
import { ThreadPrivacyTypeEnum, ThreadUser } from "@/types";
import { abbreviateNumber, cn, formatTime } from "@/utils";

import {
  BanOutlineIcon,
  CalendarOutlineIcon,
  EyeOutlineIcon,
  LockOutlineIcon,
} from "../icons";
import { formatTimeDistance } from "../stages/utils";
import { toast } from "../toast";
import { TradeTicketsModal } from "../trade-tickets-modal";
import { Button } from "../ui/button";

interface PostLivestreamInfoProps {
  livestream: Livestream;
  host: ThreadUser;
}

export function PostLivestreamInfo({
  livestream,
  host,
}: PostLivestreamInfoProps) {
  const { inView, ref } = useInView({
    threshold: 0.7,
  });
  const router = useRouter();
  const [, setRefresh] = useState(0);
  const { user } = useUser();
  const livestreamId = useLivestreamStore((store) => store.id);
  const actions = useLivestreamStore((store) => store.actions);
  const stageActions = useStageStore((store) => store.actions);
  const { data, isLoading, refetch } = useQuery({
    ...livestreamQueries.livestreamSimpleInfo(livestream.id),
    refetchInterval: (query) => {
      if (
        inView &&
        query.state.data?.livestream.scheduledStartTime &&
        !query.state.data?.livestream.endedOn
      ) {
        return 5 * 1000;
      }

      if (
        inView &&
        query.state.data?.livestream.isActive
        // ||
        // (query.state.data?.livestream.isRecorded &&
        //   !query.state.data?.livestream.isRecordingComplete))
      ) {
        return 5 * 1000;
      }

      return false;
    },
  });

  const activeLivestream = data?.livestream ?? livestream;
  const hostTwitterHandle = data?.host.user.twitterHandle ?? host.twitterHandle;

  const { mutate: startLivestream, isPending: isStartPending } =
    useStartLivestreamMutation({
      onSuccess: () => {
        router.push(`/live/${hostTwitterHandle}`);
        stageActions.reset();
      },
    });

  const { mutate: remindLivestream, isPending: isRemindPending } =
    useRemindLivestreamMutation({
      onSuccess: () => {
        toast.green("You will be reminded when the stream starts");
        refetch();
      },
    });

  const lockIcon = useMemo(
    () => <LockOutlineIcon className="size-5 text-off-white" />,
    [],
  );

  useEffect(() => {
    if (!activeLivestream.scheduledStartTime) return;

    const scheduledTime = new Date(
      activeLivestream.scheduledStartTime,
    ).getTime();
    const now = Date.now();

    if (scheduledTime <= now) return;

    const timeUntilScheduled = scheduledTime - now;
    const timer = setTimeout(() => {
      setRefresh((prev) => prev + 1);
    }, timeUntilScheduled);

    return () => clearTimeout(timer);
  }, [activeLivestream.scheduledStartTime]);

  if (!activeLivestream.isActive && activeLivestream.endedOn) {
    const startDate = parseISO(
      activeLivestream?.startedOn ?? activeLivestream.createdOn,
    ).getTime();
    const endDate = parseISO(activeLivestream.endedOn).getTime();
    const duration = formatTimeDistance(startDate, endDate);

    return (
      <div
        className="group relative isolate flex aspect-video flex-col justify-between gap-2 overflow-hidden rounded-[10px] border border-brand-orange bg-chat-bubble bg-cover bg-center"
        onClick={(e) => {
          e.stopPropagation();
        }}
        style={{
          backgroundImage: activeLivestream.thumbnailUrl
            ? `url(${activeLivestream.thumbnailUrl})`
            : undefined,
        }}
      >
        <div className="p-4 transition duration-200 group-hover:bg-transparent">
          <h5 className="line-clamp-2 text-sm font-semibold text-off-white md:text-lg">
            {activeLivestream.name}
          </h5>
        </div>
        <div className="flex items-center justify-between p-4 transition duration-200 group-hover:bg-transparent">
          <div className="flex items-center gap-1 text-sm font-semibold text-off-white">
            <span>{abbreviateNumber(data?.tunedInCount ?? 0)} viewers</span>・
            <span>{formatTime(activeLivestream.createdOn, "MMM d")}</span>・
            <span>{duration}</span>
          </div>
        </div>
        <div className="pointer-events-none absolute inset-0 -z-10 bg-dark-bk/75" />
        {activeLivestream.isSuspended ? (
          <Button
            variant="secondary"
            className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-[10px] bg-dark-bk py-2 text-xs text-off-white disabled:opacity-90 md:py-3 md:text-sm"
            disabled
          >
            <BanOutlineIcon className="size-5 text-red-600" />
            This stream was suspended
          </Button>
        ) : (
          <Button
            variant="secondary"
            className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 py-2 text-xs disabled:opacity-80 md:py-3 md:text-sm"
            disabled
          >
            Live Stream Ended
          </Button>
        )}
        {/* <div className="flex flex-col gap-2">
          {activeLivestream.privacyType ===
          ThreadPrivacyTypeEnum.SHAREHOLDERS ? (
            <div className="flex items-center gap-1">
              {lockIcon}
              <span className="text-sm font-semibold text-off-white">
                Ticket Gated
              </span>
            </div>
          ) : activeLivestream.privacyType ===
            ThreadPrivacyTypeEnum.BADGEHOLDERS ? (
            <div className="flex items-center gap-1">
              {lockIcon}
              <span className="text-sm font-semibold text-off-white">
                Badge Gated
              </span>
              {activeLivestream.badgeTypes && (
                <Badges badges={activeLivestream.badgeTypes} />
              )}
            </div>
          ) : null}
        </div> */}
      </div>
    );
  }

  if (activeLivestream.scheduledStartTime && !activeLivestream.isActive) {
    const scheduledDate = parseISO(activeLivestream.scheduledStartTime);
    const isScheduledTimeInFuture =
      new Date(activeLivestream.scheduledStartTime).getTime() > Date.now();

    return (
      <div
        className="group relative isolate flex flex-col justify-between gap-2 overflow-hidden rounded-[10px] border border-brand-orange bg-chat-bubble bg-cover bg-center p-4 md:aspect-video"
        onClick={(e) => {
          e.stopPropagation();
        }}
        style={{
          backgroundImage: activeLivestream.thumbnailUrl
            ? `url(${activeLivestream.thumbnailUrl})`
            : undefined,
        }}
      >
        <div className="flex flex-col items-start gap-2">
          <h5 className="line-clamp-2 text-base font-semibold text-off-white md:text-lg">
            {activeLivestream.name}
          </h5>
          {isScheduledTimeInFuture ||
          activeLivestream.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS ||
          activeLivestream.privacyType ===
            ThreadPrivacyTypeEnum.BADGEHOLDERS ? (
            <div className="mb-2 flex flex-col gap-2">
              {isScheduledTimeInFuture ? (
                <div className="flex items-center gap-1">
                  <CalendarOutlineIcon className="size-5 text-off-white" />
                  <span className="text-sm font-semibold text-light-gray-text">
                    {format(scheduledDate, "EEEE, MMM d 'at' h:mm a")}
                  </span>
                </div>
              ) : null}
              {activeLivestream.privacyType ===
              ThreadPrivacyTypeEnum.SHAREHOLDERS ? (
                <div className="flex items-center gap-1">
                  {lockIcon}
                  <span className="text-sm font-semibold text-off-white">
                    Ticket Gated
                  </span>
                </div>
              ) : activeLivestream.privacyType ===
                ThreadPrivacyTypeEnum.BADGEHOLDERS ? (
                <div className="flex items-center gap-1">
                  {lockIcon}
                  <span className="text-sm font-semibold text-off-white">
                    Badge Gated
                  </span>
                  {activeLivestream.badgeTypes && (
                    <Badges badges={activeLivestream.badgeTypes} />
                  )}
                </div>
              ) : null}
            </div>
          ) : null}
        </div>
        <div className="pointer-events-none absolute inset-0 -z-10 bg-dark-bk/75" />

        {isScheduledTimeInFuture ? (
          <>
            {activeLivestream.hostId === user?.id ? (
              <Button disabled className="py-3">
                Start Stream Now
              </Button>
            ) : data?.hasSetReminder ? (
              <Button disabled className="py-3">
                Reminder Set
              </Button>
            ) : (
              <Button
                disabled={isRemindPending}
                className="py-3"
                onClick={() => {
                  remindLivestream({ livestreamId: activeLivestream.id });
                }}
              >
                Set Reminder
              </Button>
            )}
          </>
        ) : (
          <>
            {activeLivestream.hostId === user?.id ? (
              <Button
                disabled={isStartPending}
                className="py-3"
                onClick={() => {
                  startLivestream({ livestreamId: activeLivestream.id });
                }}
              >
                Start Stream Now
              </Button>
            ) : (
              <Button disabled className="py-3">
                Waiting for Host
              </Button>
            )}
          </>
        )}
      </div>
    );
  }

  return (
    <div
      className="group relative isolate flex aspect-video flex-col justify-between gap-2 overflow-hidden rounded-[10px] border border-brand-orange bg-chat-bubble bg-cover bg-center"
      onClick={(e) => {
        e.stopPropagation();
      }}
      style={{
        backgroundImage: activeLivestream.thumbnailUrl
          ? `url(${activeLivestream.thumbnailUrl})`
          : undefined,
      }}
      ref={ref}
    >
      <div className="bg-[linear-gradient(0deg,transparent_0%,rgba(0,0,0,0.75)_100%)] p-4 transition duration-200 group-hover:bg-transparent">
        <h5 className="line-clamp-2 text-sm font-semibold text-off-white md:text-lg">
          {activeLivestream.name}
        </h5>
      </div>
      <div className="flex items-center justify-between bg-[linear-gradient(180deg,transparent_0%,rgba(0,0,0,0.75)_100%)] p-4 transition duration-200 group-hover:bg-transparent">
        <div className="rounded-md bg-danger p-1.5 text-xs font-semibold leading-none text-off-white">
          {data?.livestream.isActive ? "LIVE" : "ENDED"}
        </div>
        <div className="flex items-center gap-1">
          <EyeOutlineIcon
            className="size-5 text-brand-orange md:size-6"
            strokeWidth={1.5}
          />
          <span className="text-xs font-semibold text-off-white md:text-sm">
            {data?.listenersCount ?? 0}
          </span>
        </div>
      </div>
      <div className="pointer-events-none absolute inset-0 -z-10 bg-dark-bk/50 transition duration-200 group-hover:bg-dark-bk/50 lg:bg-transparent" />
      {livestreamId && livestreamId === activeLivestream.id ? (
        <Button
          variant="secondary"
          className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 py-2 text-xs opacity-100 transition duration-200 group-hover:opacity-100 md:py-3 md:text-sm lg:opacity-0"
          onClick={() => {
            router.push(`/live/${hostTwitterHandle}`);
            stageActions.reset();
          }}
        >
          Joined
        </Button>
      ) : activeLivestream.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS &&
        !data?.canJoin ? (
        <TradeTicketsModal
          userHandle={data?.host.user?.twitterHandle ?? ""}
          setIsTicketPurchased={(isTicketPurchased) => {
            if (isTicketPurchased) {
              refetch();
            }
          }}
        >
          <Button
            variant="secondary"
            className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 py-2 text-xs opacity-100 transition duration-200 group-hover:opacity-100 md:py-3 md:text-sm lg:opacity-0"
          >
            Buy a Ticket to Join
          </Button>
        </TradeTicketsModal>
      ) : activeLivestream.privacyType === ThreadPrivacyTypeEnum.BADGEHOLDERS &&
        !data?.canJoin ? (
        <Button
          onClick={() => {}}
          variant="secondary"
          className="absolute left-1/2 top-1/2 w-[200px] -translate-x-1/2 -translate-y-1/2 gap-1 rounded-md py-2 text-xs opacity-80 transition duration-200 group-hover:opacity-80 md:py-3 md:text-sm lg:opacity-0"
        >
          <LockOutlineIcon className="size-5 flex-shrink-0 text-dark-bk" />
          <span className="mr-1">Badge Gated</span>
          {activeLivestream.badgeTypes && (
            <Badges badges={activeLivestream.badgeTypes} />
          )}
        </Button>
      ) : (
        <Button
          variant="secondary"
          className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 py-2 text-xs opacity-100 transition duration-200 group-hover:opacity-100 md:py-3 md:text-sm lg:opacity-0"
          onClick={() => {
            router.push(`/live/${hostTwitterHandle}`);
            actions.setIsNewStream(true);
            stageActions.reset();
          }}
        >
          Join Live Stream
        </Button>
      )}
    </div>
  );
}

interface UserBadgesProps {
  badges: LivestreamBadgeType[];
  className?: string;
}

const orders = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 17];

export const Badges = ({ badges, className }: UserBadgesProps) => {
  const sortedBadges = badges.sort((a, b) => {
    const indexA = orders.indexOf(a.badgeType);
    const indexB = orders.indexOf(b.badgeType);
    return indexA - indexB;
  });

  return (
    <div className="flex flex-shrink-0 items-center gap-1">
      {sortedBadges.map((badge) => (
        <img
          src={`/assets/badges/badge-type-${badge.badgeType}.png`}
          key={badge.id}
          alt="Badge"
          className={cn("h-4 w-auto", className)}
        />
      ))}
    </div>
  );
};
