"use client";

import { useEffect, useState } from "react";
import { usePathname, useSelectedLayoutSegments } from "next/navigation";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

import { AdminPanel } from "./_components/admin-panel";
import { AdminProvider } from "./context/admin-context";

function AdminLayout({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);
  const isLargeTablet = useMediaQuery(BREAKPOINTS.lg);
  const segments = useSelectedLayoutSegments();
  const pathname = usePathname();

  const hasDetailPage = segments.length > 0;

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {}, [pathname]);

  if (!mounted) {
    return null;
  }

  if (!isLargeTablet) {
    return <AdminProvider>{children}</AdminProvider>;
  }

  if (hasDetailPage) {
    return (
      <AdminProvider>
        <AdminPanel />
      </AdminProvider>
    );
  }

  return <AdminProvider>{children}</AdminProvider>;
}

export default AdminLayout;
