import { useMemo } from "react";

import { useQueryClient } from "@tanstack/react-query";
import { parseISO } from "date-fns";

import {
  AirdropOutlineIcon,
  AtOutlineIcon,
  ChatBubblesOutlineIcon,
  HeartOutlineIcon,
  MicOutlineIcon,
  PersonAddOutlineIcon,
  RepostOutlineIcon,
  ShieldOutlineIcon,
  TicketOutlineIcon,
  TipOutlineIcon,
} from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { NotificationType } from "@/queries/types/notifications";
import { formatTimeDistance } from "@/utils";

interface NotificationProps extends NotificationType {}

export const NotificationItem = ({
  createdOn,
  title,
  text,
  isSeen,
  link,
}: NotificationProps) => {
  const queryClient = useQueryClient();
  const date = parseISO(createdOn);

  const isTipNotification =
    title.toLowerCase() === "You got tipped".toLowerCase();

  const isUprisingNotification =
    title.toLowerCase() === "Uprising Rewards".toLowerCase();

  const isStageNotification =
    text?.toLowerCase() === "Join the stage!".toLowerCase();

  const isTradeOrTransfer = ["new trade", "new transfer"].includes(
    title.toLowerCase(),
  );

  const isReportReceivedNotification = title
    .toLowerCase()
    .includes("received your report");

  const Icon = useMemo(() => {
    if (isReportReceivedNotification) return ShieldOutlineIcon;

    if (isTradeOrTransfer) return TicketOutlineIcon;

    if (isTipNotification || isUprisingNotification) return TipOutlineIcon;

    if (title.includes("replied:")) return ChatBubblesOutlineIcon;

    if (title.includes("liked")) return HeartOutlineIcon;

    if (title.includes("reposted") || title.includes("quoted"))
      return RepostOutlineIcon;

    if (title.includes("mentioned you")) return AtOutlineIcon;

    if (isStageNotification) return MicOutlineIcon;

    if (title.includes("Airdrop!")) return AirdropOutlineIcon;

    return PersonAddOutlineIcon;
  }, [isTipNotification, title]);

  const Container = ({ children }: { children: React.ReactNode }) => {
    const className =
      "relative flex items-center gap-[10px] px-6 py-4 hover:bg-[#212121] hover:bg-opacity-[0.2] select-none sm:select-auto";

    if (link) {
      return (
        <ProgressBarLink
          href={link}
          className={className}
          onClick={() => {
            queryClient.refetchQueries({
              queryKey: ["threads"],
            });
          }}
        >
          {children}
        </ProgressBarLink>
      );
    }

    return <div className={className}>{children}</div>;
  };

  return (
    <Container>
      <Icon className="h-6 w-6 flex-shrink-0 text-off-white" />
      <div className="flex-grow overflow-hidden">
        <h4 className="text-sm font-semibold leading-4 text-off-white">
          {isTipNotification || isTradeOrTransfer ? text : title}
        </h4>
        {text && !isTipNotification && !isTradeOrTransfer && (
          <p className="mt-1 truncate whitespace-nowrap text-sm leading-4 text-gray-text">
            {text}
          </p>
        )}
      </div>
      <div className="ml-2 flex w-12 flex-shrink-0 justify-end text-xs text-gray-text">
        {formatTimeDistance(date)}
      </div>
      {!isSeen && (
        <div className="absolute left-[10px] top-1/2 h-1 w-1 -translate-y-1/2 rounded-full bg-[#EB540A]" />
      )}
    </Container>
  );
};
