"use client";

import React, { useEffect, useRef, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import snapshot from "@snapshot-labs/snapshot.js";
import { gql, request } from "graphql-request";
import Markdown from "react-markdown";
import gfm from "remark-gfm";
import { Address, formatEther, Hex, WalletClient } from "viem";

import {
  ArrowBackOutlineIcon,
  ArrowDownFilledIcon,
  ArrowUpFilledIcon,
  CheckmarkOutlineIcon,
  InformationCircleOutlineIcon,
  LogoIcon,
} from "@/components/icons";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import {
  votingPowerContractABI,
  votingPowerContractAddress,
} from "@/environments/stakingABI";
import { cn } from "@/utils";

const hub = "https://hub.snapshot.org";
const endpoint = `${hub}/graphql`;
const snapshot_client = "the-arena.eth";

class EthersV5Adapter {
  walletClient: WalletClient;

  constructor(walletClient: WalletClient) {
    this.walletClient = walletClient;
  }

  async getAddress(): Promise<string> {
    const [address] = await this.walletClient.getAddresses();
    return address;
  }

  async _signTypedData(domain: any, types: any, value: any): Promise<string> {
    const address = await this.getAddress();
    return this.walletClient.signTypedData({
      domain,
      types,
      message: value,
      primaryType: "Vote",
      account: address as Hex,
    });
  }

  async signMessage(message: string): Promise<string> {
    return this.walletClient.signMessage({
      account: (await this.getAddress()) as Hex,
      message,
    });
  }
}

interface Proposal {
  id: string;
  title: string;
  scores: number[];
  body: string;
  choices: string[];
  start: number;
  end: number;
  snapshot: string;
  state: string;
  votes: number;
  author: string;
}

async function fetchUserVote(
  proposalId: string,
  userAddress: string,
): Promise<number | null> {
  const query = gql`
    query ($proposalId: String!, $userAddress: String!) {
      votes(where: { proposal: $proposalId, voter: $userAddress }) {
        choice
      }
    }
  `;

  const variables = { proposalId, userAddress };

  try {
    const data: any = await request(endpoint, query, variables);

    if (data.votes && data.votes.length > 0) {
      return data.votes[0].choice;
    }
    return null;
  } catch (error) {
    console.error("Error fetching user vote:", error);
    return null;
  }
}

async function fetchProposal(proposalId: string): Promise<Proposal | null> {
  const query = gql`
    query ($proposalId: String!) {
      proposal(id: $proposalId) {
        id
        title
        scores
        body
        choices
        start
        end
        snapshot
        state
        votes
        author
      }
    }
  `;

  const variables = { proposalId };

  try {
    const data: any = await request(endpoint, query, variables);
    return data.proposal;
  } catch (error) {
    console.error(error);
    return null;
  }
}

async function voteOnProposal(
  walletClient: WalletClient,
  spaceName: string,
  proposalId: string,
  choice: number,
): Promise<any> {
  const client = new snapshot.Client712(hub);

  const adapterSigner: any = new EthersV5Adapter(walletClient);

  const msg: any = {
    space: spaceName,
    proposal: proposalId,
    type: "single-choice",
    choice: choice,
    reason: "",
  };

  try {
    const result = await client.vote(
      adapterSigner,
      await adapterSigner.getAddress(),
      msg,
    );
    console.log("Vote successful:", result);
    return result;
  } catch (error) {
    console.error("Error voting:", error);
    return null;
  }
}

export default function VoteInfoPage() {
  const { primaryWallet } = useDynamicContext();

  const [proposal, setProposal] = useState<Proposal | null>(null);
  const [selectedChoice, setSelectedChoice] = useState<number | null>(null);
  const [votedChoice, setVotedChoice] = useState<number | null>(null);
  const [votingPower, setVotingPower] = useState("0");
  const [open, setOpen] = useState(false);

  const [showViewMore, setShowViewMore] = useState(false);
  const proposalBodyRef = useRef<HTMLDivElement | null>(null);
  const [userAddress, setUserAddress] = useState<string | null>(null);
  const [expand, setExpand] = useState(false);

  const params = useParams();
  const router = useRouter();
  const proposalId = params.proposalID as string;

  const getVotingPower = async () => {
    if (!primaryWallet) return;
    if (!isEthereumWallet(primaryWallet)) {
      throw new Error("This wallet is not a Ethereum wallet");
    }

    const publicClient = await primaryWallet.getPublicClient();

    const contractVotingPower = await publicClient.readContract({
      address: votingPowerContractAddress as Address,
      abi: votingPowerContractABI,
      functionName: "getVotingPower",
      args: [primaryWallet.address as Hex],
    });

    setVotingPower(formatEther(contractVotingPower));
  };

  const handleVote = async () => {
    if (selectedChoice === null || !proposal) return;
    if (!primaryWallet) return;
    if (!isEthereumWallet(primaryWallet)) {
      throw new Error("This wallet is not a Ethereum wallet");
    }

    const walletClient = await primaryWallet.getWalletClient();

    setUserAddress(primaryWallet.address);

    const result = await voteOnProposal(
      walletClient,
      snapshot_client,
      proposal.id,
      selectedChoice + 1,
    );

    if (result) {
      setVotedChoice(selectedChoice);
    }
  };

  const handleExpand = async () => {
    setExpand(!expand);
  };

  const highestScore = Math.max(...(proposal?.scores || []));

  function formatScore(score: number): string {
    return score >= 1000 ? `${Math.floor(score / 1000)}K` : score.toString();
  }

  useEffect(() => {
    const fetchUserVoteData = async () => {
      if (userAddress && proposal) {
        const userVote = await fetchUserVote(proposal.id, userAddress);
        if (userVote !== null) {
          setVotedChoice(userVote - 1);
        }
      }
    };
    fetchUserVoteData();
  }, [userAddress, proposal]);

  useEffect(() => {
    const autoConnectWallet = async () => {
      if (!primaryWallet) return;

      const address = primaryWallet.address;
      setUserAddress(address);
    };

    autoConnectWallet();
  }, [primaryWallet]);

  useEffect(() => {
    const fetchProposalData = async () => {
      const proposalData = await fetchProposal(proposalId);
      if (proposalData) {
        setProposal(proposalData);
      }
    };
    fetchProposalData();
  }, [proposalId]);

  useEffect(() => {
    if (proposalBodyRef.current) {
      const lineHeight = parseFloat(
        getComputedStyle(proposalBodyRef.current).lineHeight,
      );
      const maxLines = 5.5;
      const maxHeight = lineHeight * maxLines;

      if (proposalBodyRef.current.scrollHeight > maxHeight) {
        setShowViewMore(true);
      }
    }
  }, [proposal, showViewMore]);

  useEffect(() => {
    void getVotingPower();
  }, [primaryWallet]);

  if (!proposal) return <div>Loading...</div>;

  const P = ({ children }: { children: React.ReactNode }) => (
    <p className="mb-3 text-sm font-normal leading-5 ">{children}</p>
  );
  const H3 = ({ children }: { children: React.ReactNode }) => (
    <h3 className="mb-3 text-base font-semibold leading-6 text-off-white">
      {children}
    </h3>
  );
  const H4 = ({ children }: { children: React.ReactNode }) => (
    <h4 className="mb-3 text-base font-semibold leading-6 text-off-white">
      {children}
    </h4>
  );

  return (
    <div className="min-h-screen bg-dark-bk text-white">
      <header className="mb-6 mt-[calc(-0px+env(safe-area-inset-top))] flex items-center justify-between px-4 pt-4">
        <button
          onClick={() => {
            if (window.history.length > 1) {
              router.back();
            } else {
              router.push("/wallet/token-portal");
            }
          }}
          className="flex flex-shrink-0"
        >
          <ArrowBackOutlineIcon className="size-5" />
        </button>
        <h1 className="mx-auto text-base font-semibold leading-5 text-white">
          Vote
        </h1>
        <div className="flex flex-shrink-0" />
      </header>

      <main className="container mx-auto p-6">
        <div className="mb-6 flex items-center justify-between">
          <Button
            variant={proposal.state === "active" ? "active" : undefined}
            className={`text-xs ${
              proposal.state !== "active" ? "bg-purple-gradient" : ""
            } items-end px-3 py-1 text-sm font-semibold text-white`}
          >
            {proposal.state === "active" ? "Active" : "Closed"}
          </Button>
        </div>

        <div className="mb-3 flex items-center">
          <LogoIcon className="mr-2 h-4 w-4 text-brand-orange" />
          <span className="text-xs font-semibold leading-5">
            The Arena Team
          </span>
        </div>

        <div className="mb-6 flex flex-col">
          <h2 className="mb-2 text-xl font-semibold leading-5">
            {proposal.title}
          </h2>
          <p className="mb-2 text-xs font-semibold leading-5 text-light-gray-text">
            {proposal.state === "active"
              ? `Ends in ${Math.ceil((proposal.end - Date.now() / 1000) / 86400)} days`
              : `Ended ${Math.ceil((Date.now() / 1000 - proposal.end) / 86400)} days ago`}
          </p>
          <div ref={proposalBodyRef} className={cn(!expand && "line-clamp-6")}>
            <Markdown
              components={{ p: P as any, h3: H3 as any, h4: H4 as any }}
              remarkPlugins={[gfm]}
            >
              {proposal.body}
            </Markdown>
          </div>
          {showViewMore && (
            <div className="flex items-center justify-center py-4">
              <Button
                variant="outline"
                className="w-40 gap-2"
                onClick={handleExpand}
              >
                {expand ? (
                  <>
                    <span className="text-sm font-semibold">View Less</span>
                    <ArrowUpFilledIcon className="h-[14] w-[14]" />
                  </>
                ) : (
                  <>
                    <span className="text-sm font-semibold">View More</span>
                    <ArrowDownFilledIcon className="h-[14] w-[14]" />
                  </>
                )}
              </Button>
            </div>
          )}
          <a
            href={`https://snapshot.org/#/${snapshot_client}/proposal/${proposal.id}`}
            className="font-nor text-sm text-brand-orange underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            Learn more on snapshot.org
          </a>
        </div>

        {proposal.state === "active" && (
          <div className="mb-6 rounded-lg border border-gray-text p-4 font-semibold leading-5">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-base">Cast your vote</h3>
              {/* <span className="flex ml-auto text-light-gray-text text-sm">
                            <UnionIcon className="ml-1 w-[15px] text-red-600" />
                                678K $ARENA</span> */}
            </div>
            <div className="mb-4 ml-1 space-y-3 text-xs">
              {proposal.choices.map((option, index) => (
                <button
                  key={option}
                  className={`flex w-full items-center justify-between rounded-full p-3 text-left ${
                    selectedChoice === index || votedChoice === index
                      ? "border border-white bg-[#202020]/90"
                      : "border border-dark-gray"
                  }`}
                  onClick={() => {
                    if (votingPower === "0") setOpen(true);
                    setSelectedChoice(index);
                  }}
                  disabled={votedChoice !== null}
                >
                  <span>{option}</span>
                  {(selectedChoice === index && votedChoice === null) ||
                  votedChoice === index ? (
                    <CheckmarkOutlineIcon className="h-5 w-5 text-brand-orange" />
                  ) : null}
                </button>
              ))}
            </div>
            <button
              className={`w-full rounded-full py-3 transition-colors duration-300 ${
                votedChoice !== null
                  ? "bg-green"
                  : votingPower !== "0" && selectedChoice !== null
                    ? "bg-orange-gradient"
                    : "bg-[#202020]/90"
              } font-semibold text-off-white`}
              onClick={handleVote}
              disabled={!(votingPower !== "0") || votedChoice !== null}
            >
              {votedChoice !== null ? "Voted" : "Vote"}
            </button>
          </div>
        )}

        {proposal.state !== "active" && (
          <div className="rounded-lg border border-gray-text p-4 font-semibold leading-5">
            <h3 className="mb-4 text-base">Results</h3>
            {/* <span className="flex ml-auto text-light-gray-text text-sm">
                                <UnionIcon className="ml-1 w-[15px] text-red-600" />
                                    678K $ARENA</span> */}
            <div className="space-y-3 text-xs">
              {proposal.scores.map((score, index) => (
                <div
                  key={proposal.choices[index]}
                  className={`flex items-center justify-between rounded-lg p-3 ${score === highestScore ? "bg-[#202020]/90" : "bg-dark-bk"}`}
                >
                  <span className="flex items-center">
                    {proposal.choices[index]}
                    <span className="ml-2 text-light-gray-text">
                      {formatScore(score)} $ARENA
                    </span>
                  </span>
                  <span>
                    {(
                      (score / proposal.scores.reduce((a, b) => a + b, 0)) *
                      100
                    ).toFixed(2)}
                    %
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
        {primaryWallet && (
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="max-w-[87%] gap-6 overflow-hidden rounded-[10px] border border-[rgba(235,84,10,0.25)] bg-dark-bk p-0 backdrop-blur-sm sm:max-w-[524px] sm:rounded-[10px] sm:border-[rgba(235,84,10,0.25)] sm:bg-dark-bk">
              <div className="flex items-center gap-4">
                <span className="h-[100%] min-w-1 bg-brand-orange" />
                <InformationCircleOutlineIcon className="size-9" />
                <div className="flex-col py-5">
                  <h3 className="mb-3 text-base font-semibold leading-[22px] text-off-white">
                    Stake your $ARENA in order to vote
                  </h3>
                  <p className="text-sm text-[#B5B5B5]">
                    Voting is only possible by staking your tokens. <br /> Go to
                    the “Stake” tab and stake your $ARENA balance to vote.
                  </p>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </main>
    </div>
  );
}
