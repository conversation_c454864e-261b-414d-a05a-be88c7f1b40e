import { MinifiedStageTablet } from "@/components/stages/stage-mobile";

import { BottomNav } from "../(main)/_components/bottom-nav";
import { SideNav } from "../(main)/_components/side-nav";
import { RightSide } from "./_components/right-side";

interface ExploreLayoutProps {
  children: React.ReactNode;
}

function ExploreLayout({ children }: ExploreLayoutProps) {
  return (
    <div className="mx-auto flex min-h-[100svh] max-w-[1350px] flex-col flex-nowrap items-stretch justify-center sm:flex-row">
      <header className="relative z-[3] hidden sm:flex sm:flex-shrink-0 sm:flex-grow sm:flex-col sm:items-end">
        <SideNav />
      </header>
      <div className="flex flex-shrink flex-grow-[2] justify-start gap-4 xl:gap-7">
        <div className="relative w-full sm:w-[610px] sm:border-x sm:border-dark-gray">
          {children}
          <MinifiedStageTablet />
        </div>
        <div className="hidden lg:flex lg:w-[290px] lg:flex-col xl:w-[380px]">
          <RightSide />
        </div>
      </div>
      <BottomNav />
    </div>
  );
}

export default ExploreLayout;
