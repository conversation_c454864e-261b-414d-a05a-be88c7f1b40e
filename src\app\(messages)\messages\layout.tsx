"use client";

import { useEffect, useState } from "react";
import { useSelectedLayoutSegments } from "next/navigation";

import { MinifiedStageTablet } from "@/components/stages/stage-mobile";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useStageStore } from "@/stores/stage";
import { useStageRecordingPlayerStore } from "@/stores/stage-recording-player";
import { cn } from "@/utils";

import { Messages } from "./_components/messages";

function MessagesLayout({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);
  const isLargeTablet = useMediaQuery(BREAKPOINTS.lg);
  const segments = useSelectedLayoutSegments();
  const isStageOn = useStageStore((state) => Boolean(state.token));
  const isPlayerOn = useStageRecordingPlayerStore((state) =>
    Boolean(state.url),
  );

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex flex-shrink flex-grow-[2] justify-start">
        <div className="relative w-full sm:w-[610px] sm:border-x sm:border-dark-gray lg:w-[380px]" />
        <div className="hidden lg:flex lg:w-[536px] lg:flex-col lg:border-r lg:border-dark-gray xl:w-[638px]" />
      </div>
    );
  }

  if (!isLargeTablet) {
    return (
      <div className="flex flex-shrink flex-grow-[2] justify-start">
        <div className="pt-pwa pb-pwa relative flex max-h-screen w-full flex-grow flex-col sm:w-[610px] sm:border-x sm:border-dark-gray lg:w-[380px]">
          {segments.length > 0 ? (
            children
          ) : (
            <>
              <div className="flex-grow">
                <Messages />
              </div>
              <MinifiedStageTablet />
            </>
          )}
        </div>
        <div className="hidden lg:flex lg:w-[536px] lg:flex-col lg:border-r lg:border-dark-gray xl:w-[638px]"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-shrink flex-grow-[2] justify-start">
      <div
        className={cn(
          "relative max-h-screen w-full overflow-y-auto sm:w-[610px] sm:border-x sm:border-dark-gray lg:w-[380px]",
          isStageOn || isPlayerOn ? "h-[calc(100%-65px)]" : "h-full",
        )}
      >
        <Messages />
      </div>
      <div className="hidden lg:flex lg:w-[536px] lg:flex-col lg:border-r lg:border-dark-gray xl:w-[638px]">
        <div className="fixed top-0 flex h-full max-h-screen w-full flex-col lg:w-[536px] xl:w-[638px]">
          {children}
        </div>
      </div>
    </div>
  );
}

export default MessagesLayout;
