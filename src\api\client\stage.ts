import { axios, withKeepAlive } from "@/lib/axios";
import {
  CreateStageParams,
  CreateStageResponse,
  DeletePinPostParams,
  DeletePinPostResponse,
  DeleteStageParams,
  DeleteStageResponse,
  EditStageParams,
  EditStageResponse,
  EndStageParams,
  EndStageResponse,
  GetCurrentlyListeningResponse,
  GetEmotesResponse,
  GetIsUserInvitedParams,
  GetIsUserInvitedResponse,
  GetMostRecentTipResponse,
  GetStageInfoResponse,
  GetStageInfoSimpleResponse,
  GetStageSpeakersResponse,
  JoinStageParams,
  JoinStageResponse,
  LiveStagesResponse,
  PostAcceptInvitationParams,
  PostAcceptInvitationResponse,
  PostBlockUserParams,
  PostBlockUserResponse,
  PostCancelInvitationParams,
  PostCancelInvitationResponse,
  PostDropDownToListenerParams,
  PostDropDownToListenerResponse,
  PostInviteParams,
  PostInviteResponse,
  PostLeaveStageParams,
  PostLeaveStageResponse,
  PostPinPostParams,
  PostPinPostResponse,
  PostRemindStageParams,
  PostRemindStageResponse,
  PostStartStageParams,
  PostStartStageResponse,
  PostTipParams,
  PostTipResponse,
  PostToggleMuteParams,
  PostToggleMuteResponse,
  PostToggleRaisedHandParams,
  PostToggleRaisedHandResponse,
  PostUpdateAttributesParams,
  PostUpdateSpeakingDurationParams,
  PostUpdateSpeakingDurationResponse,
  PutUpdateEmotesParams,
  PutUpdateEmotesResponse,
  RequestToSpeakParams,
  RequestToSpeakResponse,
  UpdateRoleParams,
  UpdateRoleResponse,
} from "@/queries/types";

export const postCreateStage = async (data: CreateStageParams) => {
  const response = await axios.post<CreateStageResponse>(`/stages`, data);

  return response.data;
};

export const postEditStage = async (data: EditStageParams) => {
  const response = await axios.post<EditStageResponse>(`/stages/edit`, data);

  return response.data;
};

export const postJoinStage = async (data: JoinStageParams) => {
  const response = await axios.post<JoinStageResponse>(`/stages/join`, data);

  return response.data;
};

export const postUpdateRole = async (data: UpdateRoleParams) => {
  const response = await axios.post<UpdateRoleResponse>(
    `/stages/update-role`,
    data,
  );

  return response.data;
};

export const postRequestToSpeak = async (data: RequestToSpeakParams) => {
  const response = await axios.post<RequestToSpeakResponse>(
    `/stages/request-to-speak`,
    data,
  );

  return response.data;
};

export const postEndStage = async (data: EndStageParams) => {
  const response = await axios.post<EndStageResponse>(
    `/stages/end-stage`,
    data,
  );

  return response.data;
};

export const postLeaveStage = async (data: PostLeaveStageParams) => {
  const response = await axios.post<PostLeaveStageResponse>(
    `/stages/leave`,
    data,
  );

  return response.data;
};

export const getLiveStages = async () => {
  const response = await axios.get<LiveStagesResponse>("/stages/live-stages");

  return response.data;
};

export const getStageInfo = async (stageId: string) => {
  const response = await axios.get<GetStageInfoResponse>(
    `/stages/get-stage-info`,
    {
      params: { stageId },
    },
  );

  return response.data;
};

export const getStageInfoSimple = async (stageId: string) => {
  const response = await axios.get<GetStageInfoSimpleResponse>(
    `/stages/get-stage-info-simple`,
    {
      params: { stageId },
    },
  );

  return response.data;
};

export const getStageSpeakers = async (stageId: string) => {
  const response = await axios.get<GetStageSpeakersResponse>(
    `/stages/get-stage-speakers`,
    {
      params: { stageId },
    },
  );

  return response.data;
};

export const getMostRecentTip = async () => {
  const response = await axios.get<GetMostRecentTipResponse>(
    `/stages/mostRecentTip`,
  );

  return response.data;
};

export const getCurrentlyListening = async () => {
  const response = await axios.get<GetCurrentlyListeningResponse>(
    `/stages/currently-listening`,
  );

  return response.data;
};

export const postTip = async (data: PostTipParams) => {
  const response = await axios.post<PostTipResponse>(`/stages/tip`, data);

  return response.data;
};

export const postInvite = async (data: PostInviteParams) => {
  const response = await axios.post<PostInviteResponse>(`/stages/invite`, data);

  return response.data;
};

export const postCancelInvitation = async (
  data: PostCancelInvitationParams,
) => {
  const response = await axios.post<PostCancelInvitationResponse>(
    `/stages/cancel-invite`,
    data,
  );

  return response.data;
};

export const postAcceptInvitation = async (
  data: PostAcceptInvitationParams,
) => {
  const response = await axios.post<PostAcceptInvitationResponse>(
    `/stages/accept-invitation`,
    data,
  );

  return response.data;
};

export const deleteDeclineInvitation = async (
  data: PostAcceptInvitationParams,
) => {
  const response = await axios.delete<PostAcceptInvitationResponse>(
    `/stages/decline-invitation`,
    { data },
  );

  return response.data;
};

export const getIsUserInvited = async (params: GetIsUserInvitedParams) => {
  const response = await axios.get<GetIsUserInvitedResponse>(
    `/stages/is-user-invited`,
    {
      params,
    },
  );

  return response.data;
};

export const postBlockUser = async (data: PostBlockUserParams) => {
  const response = await axios.post<PostBlockUserResponse>(
    `/stages/block`,
    data,
  );

  return response.data;
};

export const postUpdateSpeakingDuration = (
  data: PostUpdateSpeakingDurationParams,
) => {
  return axios.post<PostUpdateSpeakingDurationResponse>(
    `/stages/update-speaking-duration`,
    data,
    withKeepAlive(),
  );
};

export const postToggleMute = async (data: PostToggleMuteParams) => {
  const response = await axios.post<PostToggleMuteResponse>(
    `/stages/toggle-mute`,
    data,
  );

  return response.data;
};

export const postPinPost = async (data: PostPinPostParams) => {
  const response = await axios.post<PostPinPostResponse>(
    `/stages/pin-post`,
    data,
  );

  return response.data;
};

export const postStartStage = async (data: PostStartStageParams) => {
  const response = await axios.post<PostStartStageResponse>(
    `/stages/start`,
    data,
  );

  return response.data;
};

export const deletePinPost = async (data: DeletePinPostParams) => {
  const response = await axios.delete<DeletePinPostResponse>(
    `/stages/unpin-post`,
    { data },
  );

  return response.data;
};

export const postToggleRaisedHand = async (
  data: PostToggleRaisedHandParams,
) => {
  const response = await axios.post<PostToggleRaisedHandResponse>(
    `/stages/toggle-raised-hand`,
    data,
  );

  return response.data;
};

export const postRemindStage = async (data: PostRemindStageParams) => {
  const response = await axios.post<PostRemindStageResponse>(
    `/stages/remind`,
    data,
  );

  return response.data;
};

export const postDropDownToListener = async (
  data: PostDropDownToListenerParams,
) => {
  const response = await axios.post<PostDropDownToListenerResponse>(
    `/stages/drop-down-to-listener`,
    data,
  );

  return response.data;
};

export const deleteStage = async (data: DeleteStageParams) => {
  const response = await axios.delete<DeleteStageResponse>(`/stages/delete`, {
    data,
  });

  return response.data;
};

export const postUpdateAttributes = async (
  data: PostUpdateAttributesParams,
) => {
  const response = await axios.post<boolean>(
    `/stages/update-participant-attributes`,
    data,
  );

  return response.data;
};

export const getEmotes = async () => {
  const response = await axios.get<GetEmotesResponse>(
    `/stages/user-stage-emotes`,
  );

  return response.data;
};

export const putUpdateEmotes = async (data: PutUpdateEmotesParams) => {
  const response = await axios.put<PutUpdateEmotesResponse>(
    `/stages/user-stage-emotes`,
    data,
  );

  return response.data;
};
