"use client";

import { useQuery } from "@tanstack/react-query";
import { RotateCcwIcon, RotateCwIcon } from "lucide-react";

import { stageQueries } from "@/queries/stage-queries";
import { useStageRecordingPlayerStore } from "@/stores/stage-recording-player";
import { formatTimeDistance } from "@/utils";

import {
  ChevronDownFilled,
  LogoIcon,
  PauseFilledIcon,
  PlayFilledIcon,
} from "../icons";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { Slider } from "../ui/slider";

export const StagePlayerContent = () => {
  const id = useStageRecordingPlayerStore((state) => state.id!);
  const { data } = useQuery({
    ...stageQueries.stageInfo(id),
  });

  const isPlaying = useStageRecordingPlayerStore((state) => state.isPlaying);
  const progress = useStageRecordingPlayerStore((state) => state.progress);
  const duration = useStageRecordingPlayerStore((state) => state.duration);
  const currentTime = useStageRecordingPlayerStore(
    (state) => state.currentTime,
  );
  const isLoaded = useStageRecordingPlayerStore((state) => state.isLoaded);
  const audioRef = useStageRecordingPlayerStore((state) => state.audioRef);
  const actions = useStageRecordingPlayerStore((state) => state.actions);

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    actions.setIsPlaying(!isPlaying);
  };

  const handleProgressChange = (newValue: number[]) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = (newValue[0] / 100) * audio.duration;
    audio.currentTime = newTime;
    actions.setProgress(newValue[0]);
    actions.setCurrentTime(newTime);
  };

  const skipTime = (seconds: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.currentTime += seconds;
  };

  const formatTime = (time: number): string => {
    if (isNaN(time)) return "0:00";
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  if (!isLoaded || !data) return null;

  return (
    <>
      <div className="relative flex w-full flex-shrink-0 flex-col overflow-hidden bg-gray-bg p-6 pb-2">
        <LogoIcon className="pointer-events-none absolute -right-6 top-4 w-[120px] select-none text-light-gray-text opacity-5" />
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            className="-ml-2 -mt-2 bg-none p-2 hover:bg-none"
            onClick={() => {
              actions.setIsRecordingPlayerFullScreen(false);
            }}
          >
            <ChevronDownFilled className="size-6 text-off-white" />
          </Button>
          <Button
            variant="ghost"
            className="-mr-3 -mt-1 bg-none px-3 py-1 text-base text-danger hover:bg-none"
            onClick={() => {
              actions.reset();
            }}
          >
            Close
          </Button>
        </div>
        <p className="mt-3 text-sm font-semibold text-gray-text">
          {formatTimeDistance(data.stage.createdOn, "ago")} -{" "}
          {data?.tunedInCount ?? 0} tuned in
        </p>
        <h3 className="mt-1 text-base font-semibold leading-[22px] text-off-white">
          {data?.stage.name}
        </h3>
        <p className="mt-1 text-sm text-gray-text">Hosted by</p>
      </div>
      <div className="relative flex flex-grow flex-col items-center justify-center">
        <div className="flex flex-col items-center">
          <Avatar className="size-[60px]">
            <AvatarImage src={data.host.user.twitterPicture} />
            <AvatarFallback />
          </Avatar>
          <h4 className="mt-3 text-base font-semibold text-off-white">
            {data.host.user.twitterName}
          </h4>
          <div className="text-sm text-gray-text">Host</div>
        </div>
      </div>
      <div className="pb-pwa flex flex-col border-t border-gray-border">
        <div className="flex flex-col px-6 py-10 ">
          <Slider
            value={[progress]}
            onValueChange={handleProgressChange}
            max={100}
            step={0.1}
            className="w-full"
          />
          <div className="text-gray-400 mt-4 flex justify-between text-xs">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
          <div className="mt-4 flex items-center justify-center">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={() => skipTime(-15)}
                aria-label="Rewind 15 seconds"
                className="size-10"
              >
                <RotateCcwIcon className="size-5" />
              </Button>
              <Button
                className="size-12 items-center justify-center rounded-none bg-none p-0"
                onClick={togglePlayPause}
                aria-label={isPlaying ? "Pause" : "Play"}
              >
                {isPlaying ? (
                  <PauseFilledIcon className="size-8 text-off-white" />
                ) : (
                  <PlayFilledIcon className="size-8 text-off-white" />
                )}
              </Button>
              <Button
                variant="ghost"
                onClick={() => skipTime(15)}
                aria-label="Forward 15 seconds"
                className="size-10"
              >
                <RotateCwIcon className="size-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
