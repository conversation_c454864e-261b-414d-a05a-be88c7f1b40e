import { getAxios } from "@/lib/axios-server";
import { SendMessageRequest } from "@/queries/types/chats";

interface GetConversationsParams {
  page: number;
  pageSize: number;
}

export const getConversations = async ({
  page,
  pageSize,
}: GetConversationsParams) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get(
    `/chat/conversations?${searchParams.toString()}`,
  );

  return response.data;
};

interface GetConversationByProfileParams {
  userId: string;
}

export const getConversationByProfile = async ({
  userId,
}: GetConversationByProfileParams) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    userId,
  });

  const response = await axios.get(
    `/chat/group/by/user?${searchParams.toString()}`,
  );
  return response.data;
};

interface GetGroupParams {
  groupId: string;
  twitterHandle: string;
}

export const getGroup = async ({ groupId, twitterHandle }: GetGroupParams) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    groupId,
    twitterHandle,
  });

  const response = await axios.get(`/chat/group?${searchParams.toString()}`);
  return response.data;
};

interface GetMessagesBeforeParams {
  groupId: string;
  timeFrom: number;
}

export const getMessagesBefore = async ({
  groupId,
  timeFrom,
}: GetMessagesBeforeParams) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    groupId,
    timeFrom: timeFrom.toString(),
  });

  const response = await axios.get(
    `/chat/messages/b?${searchParams.toString()}`,
  );
  return response.data;
};

interface GetMessagesAfterParams {
  groupId: string;
  timeFrom: number;
}

export const getMessagesAfter = async ({
  groupId,
  timeFrom,
}: GetMessagesAfterParams) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    groupId,
    timeFrom: timeFrom.toString(),
  });

  const response = await axios.get(
    `/chat/messages/a?${searchParams.toString()}`,
  );
  return response.data;
};

export const postSendMessage = async (request: SendMessageRequest) => {
  const axios = getAxios();

  const response = await axios.post(`/chat/message`, request);
  return response.data;
};

export const getUpdateStatus = async ({ userId }: { userId: string }) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    userId,
  });

  const response = await axios.get(`/chat/status?${searchParams.toString()}`);
  return response.data;
};

export const postPinConversation = async ({
  groupId,
  isPinned,
}: {
  groupId: string;
  isPinned: boolean;
}) => {
  const axios = getAxios();

  const response = await axios.post(`/chat/group/pin`, {
    groupId,
    isPinned,
  });
  return response.data;
};
