import { Dispatch, SetStateAction, use<PERSON>emo, useState } from "react";
import { useParams } from "next/navigation";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { useQueryClient } from "@tanstack/react-query";
import { Loader2Icon, MinusIcon, PlusIcon } from "lucide-react";
import { encodeFunctionD<PERSON>, Hex } from "viem";

import { postTradeNotification } from "@/api/client/trade";
import {
  ArrowBackOutlineIcon,
  ChatBubblesOutlineIcon,
  MicOutlineIcon,
} from "@/components/icons";
import { TriangleDownOutlineIcon } from "@/components/icons/triangle-down-outline";
import { TriangleUpOutlineIcon } from "@/components/icons/triangle-up-outline";
import { toast } from "@/components/toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  Dialog<PERSON>ontent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import { Input } from "@/components/ui/input";
import {
  BACKEND_FRIENDS_CONTRACT,
  BACKEND_FRIENDS_CONTRACT_ABI,
} from "@/environments/BACKEND_FRIENDS_CONTRACT";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import {
  useFeePercentQuery,
  usePriceQuery,
  useSharesStatsQuery,
  useTokenBalancesQuery,
  useUserByHandleQuery,
} from "@/queries";
import { useUser } from "@/stores";
import {
  abbreviateNumber,
  cn,
  divideBigInt,
  formatAvax,
  formatAvax6Digits,
  formatEther,
  numberFormatter,
} from "@/utils";
import { handleNumericInput, parseFormattedValue } from "@/utils/number";

const FRACTIONAL_TICKET_TO_FRACTION_SCALER = 100n;
const AMOUNT_STEP = 1n;
const MAX_BUY_AMOUNT = 100n;

function bigintToDecimalString(amount: bigint) {
  const integerPart = amount / FRACTIONAL_TICKET_TO_FRACTION_SCALER;
  const fractionalPart = amount % FRACTIONAL_TICKET_TO_FRACTION_SCALER;

  // Uncomment if we don't want display extra zeros
  // if (fractionalPart === 0n) {
  //   return integerPart.toString();
  // }
  //

  return (
    integerPart.toString() + "." + fractionalPart.toString().padStart(2, "0")
  );
}

function decimalStringToBigInt(decimalString: string) {
  const [integerPart, fractionalPart = "0"] = decimalString.split(".");
  const normalizedFractionalPart = fractionalPart.padEnd(2, "0").slice(0, 2);
  return BigInt(integerPart + normalizedFractionalPart);
}

export const TradeTicketsModal = ({
  children,
  userHandle,
  setIsTicketPurchased,
  onSuccess,
}: {
  children: React.ReactNode;
  userHandle?: string;
  setIsTicketPurchased?: Dispatch<SetStateAction<boolean>>;
  onSuccess?: (amount: string) => void;
}) => {
  const queryClient = useQueryClient();
  const params = useParams() as { userHandle: string };
  if (!userHandle) {
    userHandle = params.userHandle;
  }
  const [open, setOpen] = useState(false);
  const [tradeAmount, setTradeAmount] = useState<bigint>(
    FRACTIONAL_TICKET_TO_FRACTION_SCALER,
  );
  const [input, setInput] = useState<string>("1.00");

  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const { user } = useUser();
  const { data, isLoading: isUserDataLoading } =
    useUserByHandleQuery(userHandle);

  const { data: priceWithoutFee, isLoading: isPriceLoading } = usePriceQuery({
    address: data?.user?.addressBeforeDynamicMigration || data?.user?.address,
    amount: tradeAmount,
  });
  const { data: statsData, isLoading: isStatsDataLoading } =
    useSharesStatsQuery({
      userId: data?.user?.id,
    });
  const { balances } = useTokenBalancesQuery({
    address: user?.address,
    currencies: [{ symbol: "AVAX" }],
  });
  const { data: fees } = useFeePercentQuery({
    address: data?.user?.addressBeforeDynamicMigration || data?.user?.address,
  });

  const { primaryWallet } = useDynamicContext();

  const isHoldingUserTicket = useMemo(() => {
    if (isStatsDataLoading || isUserDataLoading) return false;

    if (!data?.user?.id) return false;

    if (!statsData?.holdingsByUser) return false;

    return parseFloat((statsData?.holdingsByUser).toString()) > 0;
  }, [data, isStatsDataLoading, statsData, isUserDataLoading]);

  const ticketBalance = useMemo(() => {
    if (isStatsDataLoading || isUserDataLoading) return 0;

    if (!data?.user?.id) return 0;

    if (!statsData?.holdingsByUser) return 0;

    return parseFloat((statsData?.holdingsByUser).toString());
  }, [data, statsData, isStatsDataLoading, isUserDataLoading]);

  const isSellDisabled = useMemo(() => {
    if (ticketBalance === 0) return true;

    return ticketBalance < Number(bigintToDecimalString(tradeAmount));
  }, [ticketBalance, tradeAmount]);

  const balance = useMemo(() => {
    if (!balances || !balances.AVAX) return "0.00";

    return formatEther(balances.AVAX.balance.toString());
  }, [balances]);

  const ticketPrice = useMemo(() => {
    if (isUserDataLoading || isStatsDataLoading) return "";

    const formattedEther = formatAvax(
      statsData?.stats?.keyPrice ?? data?.user?.keyPrice ?? "",
    );

    if (parseFloat(formattedEther) >= 1) {
      return parseFloat(formattedEther).toFixed(2);
    }

    return formattedEther;
  }, [
    statsData?.stats?.keyPrice,
    data?.user?.keyPrice,
    isUserDataLoading,
    isStatsDataLoading,
  ]);

  const [isNegative, percentageIncrease] = useMemo(() => {
    const keyPrice = BigInt(statsData?.stats?.keyPrice || 0);
    const lastKeyPrice = BigInt(data?.user?.lastKeyPrice || 0);

    const percentage = lastKeyPrice
      ? 100 * (divideBigInt(keyPrice, lastKeyPrice) - 1)
      : keyPrice
        ? 100
        : 0;

    return [percentage < 0, abbreviateNumber(percentage, 2, false)];
  }, [statsData?.stats?.keyPrice, data?.user?.lastKeyPrice]);

  const addFee = (ticketPrice: bigint, fee: bigint): bigint => {
    const BUY_PRICE_EXTRA_AMOUNT = 10n;
    const priceWithFee =
      ((ticketPrice + BUY_PRICE_EXTRA_AMOUNT) *
        (1_000_000_000_000_000_000n + fee)) /
      1_000_000_000_000_000_000n;
    const roundingError = 3n;

    return priceWithFee + roundingError;
  };

  const [buyPrice, sellPrice, buyPriceFormatted, sellPriceFormatted] =
    useMemo(() => {
      if (!priceWithoutFee || !fees) return [null, null];

      const buyPriceWithFee = addFee(
        priceWithoutFee[0],
        fees.rawTotalFee,
      ).toString();
      const sellPriceWithFee = addFee(
        priceWithoutFee[1],
        -fees.rawTotalFee,
      ).toString();

      let formattedBuyEther = formatAvax6Digits(buyPriceWithFee);
      let formattedSellEther = formatAvax6Digits(sellPriceWithFee);

      return [
        buyPriceWithFee,
        sellPriceWithFee,
        formattedBuyEther,
        formattedSellEther,
      ];
    }, [priceWithoutFee]);

  const [isBuySharePendingDynamic, setIsBuySharePendingDynamic] =
    useState(false);
  const [isSellSharePendingDynamic, setIsSellSharePendingDynamic] =
    useState(false);

  const handleDecreaseTradeAmount = () => {
    if (tradeAmount > AMOUNT_STEP * FRACTIONAL_TICKET_TO_FRACTION_SCALER) {
      const updatedTradeAmount =
        tradeAmount - AMOUNT_STEP * FRACTIONAL_TICKET_TO_FRACTION_SCALER;
      setTradeAmount(updatedTradeAmount);
      setInput(bigintToDecimalString(updatedTradeAmount));
    }
  };

  const handleIncreaseTradeAmount = () => {
    if (
      tradeAmount <
      (MAX_BUY_AMOUNT - AMOUNT_STEP) * FRACTIONAL_TICKET_TO_FRACTION_SCALER
    ) {
      const updatedTradeAmount =
        tradeAmount + AMOUNT_STEP * FRACTIONAL_TICKET_TO_FRACTION_SCALER;
      setTradeAmount(updatedTradeAmount);
      setInput(bigintToDecimalString(updatedTradeAmount));
    }
  };

  const handleInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target;
    const position = input.selectionStart ?? 0;
    const rawValue = input.value;

    const { value, cursorPosition } = handleNumericInput(rawValue, position, 2);

    setInput(value);

    const parsedValue = parseFormattedValue(value);
    const updatedTradeAmount = decimalStringToBigInt(parsedValue.toString());
    setTradeAmount(updatedTradeAmount);

    // Update cursor position after React updates the DOM
    setTimeout(() => {
      if (input) {
        input.setSelectionRange(cursorPosition, cursorPosition);
      }
    }, 0);
  };

  const handleBuy = async () => {
    if (!data) return;
    if (!buyPrice) return;

    if (
      tradeAmount === 0n ||
      tradeAmount > MAX_BUY_AMOUNT * FRACTIONAL_TICKET_TO_FRACTION_SCALER
    ) {
      toast.danger("Please enter a number between 0.01 and 100");
      return;
    }

    if (!balances?.AVAX) {
      toast.danger("Balance not loaded");
      return;
    }
    if (BigInt(buyPrice) > balances.AVAX.balance) {
      toast.danger("Insufficient balance");
      return;
    }

    let sharesSubject;
    if (data.user?.address === data.user?.dynamicAddress?.toLowerCase()) {
      if (data.user?.addressBeforeDynamicMigration) {
        sharesSubject = data.user?.addressBeforeDynamicMigration;
      } else {
        sharesSubject = data.user?.address;
      }
    } else {
      sharesSubject = data.user?.address;
    }

    if (user && user?.address === user?.dynamicAddress?.toLowerCase()) {
      console.log("Attempt to handle buy through Dynamic wallet");
      if (!primaryWallet) {
        throw new Error("Dynamic wallet is not initialized");
      }
      if (!isEthereumWallet(primaryWallet)) {
        throw new Error("This wallet is not a Ethereum wallet");
      }

      setIsBuySharePendingDynamic(true);

      let traderAddress;
      if (user?.addressBeforeDynamicMigration) {
        traderAddress = user?.addressBeforeDynamicMigration;
      } else {
        traderAddress = user?.address;
      }

      const walletClient = await primaryWallet.getWalletClient();
      try {
        const amount = tradeAmount;
        let functionName, args;
        if (user.referrerAddress) {
          functionName = "buyFractionalSharesWithReferrerForUser";
          args = [sharesSubject, traderAddress, amount, user.referrerAddress];
        } else {
          functionName = "buyFractionalShares";
          args = [sharesSubject, traderAddress, amount];
        }

        await walletClient.sendTransaction({
          account: primaryWallet.address as Hex,
          chain: walletClient.chain,
          to: BACKEND_FRIENDS_CONTRACT.addressMainnet as Hex,
          value: BigInt(buyPrice),
          data: encodeFunctionData({
            abi: BACKEND_FRIENDS_CONTRACT_ABI,
            functionName,
            args,
          }),
        });

        try {
          const isBuy = true;
          await postTradeNotification({
            subjectAddress: data.user.address,
            isBuy,
            amount: bigintToDecimalString(tradeAmount),
          });
        } catch (e: unknown) {
          console.error({ e });
        }

        toast.green(
          `Successfully bought ${bigintToDecimalString(tradeAmount)} tickets`,
        );
        if (setIsTicketPurchased) {
          setIsTicketPurchased(true);
        }
        if (onSuccess) {
          onSuccess(bigintToDecimalString(tradeAmount));
        }
      } catch (e: unknown) {
        if (e instanceof Error) {
          if (e.name === "TransactionExecutionError") {
            toast.red(
              e.message || "Transaction execution failed. Please try again.",
            );
          } else {
            toast.red(e.message || "An error occurred");
          }
        } else {
          toast.red("An unknown error occurred");
        }

        console.error({ e });
      } finally {
        queryClient.removeQueries({
          queryKey: ["wallet", "price"],
        });
        queryClient.invalidateQueries({
          queryKey: ["chat", "group", "profile", data.user.id],
        });
        queryClient.invalidateQueries({
          queryKey: ["shares", "holdings"],
        });
        queryClient.invalidateQueries({
          queryKey: ["wallet", "balance", user?.address, "AVAX"],
        });

        setIsBuySharePendingDynamic(false);
      }
    } else {
      toast.red("Dynamic wallet not found. You may not have migrated.");
    }

    setOpen(false);
  };

  const handleSell = async () => {
    if (!data) return;

    if (
      tradeAmount === 0n ||
      tradeAmount > MAX_BUY_AMOUNT * FRACTIONAL_TICKET_TO_FRACTION_SCALER
    ) {
      toast.danger("Please enter a number between 0.01 and 100");
      return;
    }

    let sharesSubject;
    if (data.user?.address === data.user?.dynamicAddress?.toLowerCase()) {
      if (data.user?.addressBeforeDynamicMigration) {
        sharesSubject = data.user?.addressBeforeDynamicMigration;
      } else {
        sharesSubject = data.user?.address;
      }
    } else {
      sharesSubject = data.user?.address;
    }

    if (user && user?.address === user?.dynamicAddress?.toLowerCase()) {
      console.log("Attempt to handle sell through Dynamic wallet");
      if (!primaryWallet) {
        throw new Error("Dynamic wallet is not initialized");
      }
      if (!isEthereumWallet(primaryWallet)) {
        throw new Error("This wallet is not a Ethereum wallet");
      }

      setIsSellSharePendingDynamic(true);

      let traderAddress;
      if (user?.addressBeforeDynamicMigration) {
        traderAddress = user?.addressBeforeDynamicMigration;
      } else {
        traderAddress = user?.address;
      }

      const walletClient = await primaryWallet.getWalletClient();
      try {
        const amount = tradeAmount;
        let functionName, args;
        if (user?.referrerAddress) {
          functionName = "sellFractionalSharesWithReferrerForUser";
          args = [sharesSubject, traderAddress, amount, user?.referrerAddress];
        } else {
          functionName = "sellFractionalShares";
          args = [sharesSubject, traderAddress, amount];
        }
        await walletClient.sendTransaction({
          account: primaryWallet.address as Hex,
          chain: walletClient.chain,
          to: BACKEND_FRIENDS_CONTRACT.addressMainnet as Hex,
          data: encodeFunctionData({
            abi: BACKEND_FRIENDS_CONTRACT_ABI,
            functionName,
            args,
          }),
        });

        try {
          const isBuy = false;
          await postTradeNotification({
            subjectAddress: data.user?.address,
            isBuy,
            amount: bigintToDecimalString(tradeAmount),
          });
        } catch (e: unknown) {
          console.error({ e });
        }

        toast.green(
          `Successfully sold ${bigintToDecimalString(tradeAmount)} tickets`,
        );
      } catch (e: unknown) {
        if (e instanceof Error) {
          if (e.name === "TransactionExecutionError") {
            toast.red(
              e.message || "Transaction execution failed. Please try again.",
            );
          } else {
            toast.red(e.message || "An error occurred");
          }
        } else {
          toast.red("An unknown error occurred");
        }

        console.error({ e });
      } finally {
        queryClient.removeQueries({
          queryKey: ["wallet", "price"],
        });
        queryClient.invalidateQueries({
          queryKey: ["chat", "group", "profile", data.user.id],
        });
        queryClient.invalidateQueries({
          queryKey: ["shares", "holdings"],
        });
        queryClient.invalidateQueries({
          queryKey: ["wallet", "balance", user?.address, "AVAX"],
        });

        setIsSellSharePendingDynamic(false);
      }
    } else {
      toast.red("Dynamic wallet not found. You may not have migrated.");
    }

    setOpen(false);
  };

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <DialogContent className="gap-6">
          <TradeTicketsContent
            balance={balance}
            data={data}
            ticketPrice={ticketPrice}
            isNegative={isNegative}
            percentageIncrease={percentageIncrease}
            handleBuy={handleBuy}
            isBuySharePending={isBuySharePendingDynamic}
            buyPrice={buyPriceFormatted || ""}
            handleSell={handleSell}
            isSellSharePending={isSellSharePendingDynamic}
            sellPrice={sellPriceFormatted || ""}
            isHoldingUserTicket={isHoldingUserTicket}
            ticketBalance={ticketBalance}
            totalCommission={fees ? fees.totalFee : 0}
            handleDecreaseAmount={handleDecreaseTradeAmount}
            handleIncreaseAmount={handleIncreaseTradeAmount}
            input={input}
            handleInputChange={handleInputChange}
            isSellShareDisabled={isSellDisabled}
            isPriceLoading={isPriceLoading}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>{children}</DrawerTrigger>
      <DrawerContent className="gap-6">
        <TradeTicketsContent
          balance={balance}
          data={data}
          ticketPrice={ticketPrice}
          isNegative={isNegative}
          percentageIncrease={percentageIncrease}
          handleBuy={handleBuy}
          isBuySharePending={isBuySharePendingDynamic}
          buyPrice={buyPriceFormatted || ""}
          handleSell={handleSell}
          isSellSharePending={isSellSharePendingDynamic}
          sellPrice={sellPriceFormatted || ""}
          isHoldingUserTicket={isHoldingUserTicket}
          ticketBalance={ticketBalance}
          totalCommission={fees ? fees.totalFee : 0}
          handleDecreaseAmount={handleDecreaseTradeAmount}
          handleIncreaseAmount={handleIncreaseTradeAmount}
          input={input}
          handleInputChange={handleInputChange}
          isSellShareDisabled={isSellDisabled}
          isPriceLoading={isPriceLoading}
        />
      </DrawerContent>
    </Drawer>
  );
};

interface TradeTicketsContentProps {
  balance: string;
  data: any;
  ticketPrice: string;
  isNegative: boolean;
  percentageIncrease: string;
  isBuySharePending: boolean;
  isPriceLoading: boolean;
  buyPrice: string | null;
  isSellSharePending: boolean;
  isSellShareDisabled: boolean;
  sellPrice: string | null;
  handleBuy: () => void;
  handleSell: () => void;
  isHoldingUserTicket: boolean;
  ticketBalance: number;
  totalCommission: number;
  handleDecreaseAmount: () => void;
  handleIncreaseAmount: () => void;
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const TradeTicketsContent = ({
  balance,
  data,
  ticketPrice,
  isNegative,
  percentageIncrease,
  handleBuy,
  isBuySharePending,
  buyPrice,
  handleSell,
  isSellSharePending,
  isSellShareDisabled,
  sellPrice,
  isHoldingUserTicket,
  ticketBalance,
  totalCommission,
  handleDecreaseAmount,
  handleIncreaseAmount,
  input,
  handleInputChange,
  isPriceLoading,
}: TradeTicketsContentProps) => {
  return (
    <>
      <div className="flex items-center gap-3">
        <DialogClose className="hidden p-1 sm:inline-block">
          <ArrowBackOutlineIcon className="size-5 text-off-white" />
        </DialogClose>
        <div className="flex flex-col">
          <h4 className="text-base font-semibold leading-[22px] text-off-white">
            Buy tickets to get access to {data?.user.twitterName}’s exclusive
            content
          </h4>
        </div>
      </div>
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-2 p-1 text-[#DEDEDE]">
          <div className="flex items-center gap-[10px]">
            <ChatBubblesOutlineIcon className="size-4" />
            <span className="text-sm">
              Get access to their ticket holder’s room
            </span>
          </div>
          <div className="flex items-center gap-[10px]">
            <MicOutlineIcon className="size-4" />
            <span className="text-sm">
              Get access to their ticket holder’s Stages
            </span>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <div className=" mb-2 flex justify-between rounded-xl border border-gray-text p-4">
          <div className=" flex items-center gap-3">
            <Avatar className=" size-9">
              <AvatarImage src={data?.user.twitterPicture} />
              <AvatarFallback />
            </Avatar>
            <div className=" flex flex-col gap-1">
              <div className=" text-sm font-semibold leading-4 text-white">
                @{data?.user.twitterHandle}
              </div>
              <div className=" text-xs/[20px] text-gray-text">
                {numberFormatter.format(data?.user.followerCount || 0)}{" "}
                Followers
              </div>
            </div>
          </div>
          <div className=" flex flex-col items-end justify-between gap-1">
            <div className=" flex items-center gap-1 text-[13px]/[20px] font-semibold">
              <img
                src="/assets/coins/avax.png"
                className=" size-3 rounded-full"
                alt={`AVAX logo`}
              />
              <span>{ticketPrice}</span>
            </div>
            <div className=" flex items-center gap-1 text-xs font-semibold text-[#00F470]">
              <span
                className={cn(
                  "flex items-center gap-[4px] text-xs/[20px]",
                  isNegative ? "text-danger" : "text-[#40B877]",
                )}
              >
                {percentageIncrease !== "0" && (
                  <>
                    {isNegative ? (
                      <TriangleDownOutlineIcon className="h-4 w-4" />
                    ) : (
                      <TriangleUpOutlineIcon className="h-4 w-4" />
                    )}
                  </>
                )}
                <span>{percentageIncrease}%</span>
              </span>
            </div>
          </div>
        </div>
        <div className="flex flex-row justify-between gap-2">
          <div className="flex items-center">
            <span className="text-xs text-light-gray-text">BALANCE:</span>
            <div className="ml-2 flex items-center gap-1">
              <img
                src="/assets/coins/avax.png"
                className="size-3 rounded-full"
                alt={`AVAX logo`}
              />
              <span className="text-xs font-medium text-off-white">
                {balance}
              </span>
            </div>
          </div>
          <div>
            <span className="text-xs text-light-gray-text">TOTAL FEE:</span>
            <span className="text-xs text-off-white">{` ${totalCommission}%`}</span>
          </div>
        </div>
        <div className="flex h-[52px] w-full select-none flex-row items-center justify-between rounded-lg border-[1px] border-gray-text px-4">
          <MinusIcon onClick={handleDecreaseAmount} className="w-[19px]" />
          <Input
            type="text"
            className="border-none px-4 text-center text-sm text-off-white focus-visible:ring-0 focus-visible:ring-offset-0"
            value={input}
            onChange={handleInputChange}
          />
          <PlusIcon className="w-[19px]" onClick={handleIncreaseAmount} />
        </div>
      </div>
      {/* <div className="flex flex-col gap-4">
        <h4 className="text-base font-semibold leading-[22px] text-off-white">
          Buy tickets to get access to {data?.user.twitterName}’s exclusive
          content
        </h4>
        <div className="flex flex-col gap-2 p-1 text-[#DEDEDE]">
          <div className="flex items-center gap-[10px]">
            <ChatBubblesOutlineIcon className="size-4" />
            <span className="text-sm">
              Get access to their community chat thread
            </span>
          </div>
          <div className="flex items-center gap-[10px]">
            <MicFilledIcon className="size-4" />
            <span className="text-sm">
              Listen in to private spaces they host
            </span>
          </div>
        </div>
      </div> */}
      <div className="flex items-center gap-2">
        <Button
          className="flex-1 gap-1"
          onClick={handleBuy}
          loading={isBuySharePending}
        >
          {isPriceLoading ? (
            <Loader2Icon className="size-5 animate-spin text-off-white" />
          ) : (
            <>
              Buy{" "}
              <img
                src="/assets/coins/avax.png"
                className="ml-0.5 size-3 rounded-full brightness-110 grayscale"
                alt={`AVAX logo`}
              />
              <span className="text-xs font-medium leading-5 text-off-white">
                {buyPrice}
              </span>
            </>
          )}
        </Button>
        <Button
          variant="outline"
          className="flex-1 gap-1"
          onClick={handleSell}
          loading={isSellSharePending}
          disabled={isSellShareDisabled}
        >
          {isPriceLoading ? (
            <Loader2Icon className="size-5 animate-spin text-off-white" />
          ) : (
            <>
              Sell{" "}
              <img
                src="/assets/coins/avax.png"
                className="ml-0.5 size-3 rounded-full brightness-110 grayscale"
                alt={`AVAX logo`}
              />
              <span className="text-xs font-medium leading-5 text-off-white">
                {sellPrice}
              </span>
            </>
          )}
        </Button>
      </div>
    </>
  );
};
