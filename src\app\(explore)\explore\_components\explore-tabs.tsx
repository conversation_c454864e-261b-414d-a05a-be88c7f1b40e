"use client";

import {
  startTransition,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

import { Tabs, TabsContent, TabsList } from "@radix-ui/react-tabs";
import { useMotionValue } from "framer-motion";

import { TabsFlatTrigger } from "@/components/ui/tabs";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import useThrottle from "@/hooks/use-throttle";
import {
  useNewCommunitiesQuery,
  useNewUsersQuery,
  useOfficialTopCommunitiesQuery,
  useRecentCommunityTradesQuery,
  useRecentTradesQuery,
  useTopCommunitiesQuery,
  useTopUsersQuery,
  useTrendingUsersQuery,
} from "@/queries";
import { useTrendingGroupsQuery } from "@/queries/groups-queries";
import { cn } from "@/utils";

import { GroupsTabs } from "./groups-tabs";
import { UsersTabs } from "./users-tabs";

export const ExploreTabs = () => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isLargeScreen = useMediaQuery(BREAKPOINTS.lg);

  const tabRef = useRef<HTMLDivElement>(null);

  const [searchValue] = useState(() => searchParams.get("search") || "");
  const throttledSearchValue = useThrottle(searchValue);

  const backgroundColor = useMotionValue("rgb(20 20 20 / 0)");

  const [currentTab, setCurrentTab] = useState<"users" | "tokens">(
    (localStorage.getItem("currentExploreTab") as "users" | "tokens") ||
      "users",
  );

  const { data: trendingUsersData, isLoading: isTrendingUsersLoading } =
    useTrendingUsersQuery();

  const { data: trendingGroupsData, isLoading: isTrendingGroupsLoading } =
    useTrendingGroupsQuery();

  useEffect(() => {
    localStorage.setItem("currentExploreTab", currentTab);
  }, [currentTab]);

  // Prefetch tabs data
  useTopUsersQuery({
    notifyOnChangeProps: [],
  });
  useNewUsersQuery({
    notifyOnChangeProps: [],
  });
  useRecentTradesQuery({
    notifyOnChangeProps: [],
  });

  useTopCommunitiesQuery({
    notifyOnChangeProps: [],
  });
  // useOfficialTopCommunitiesQuery({
  //   notifyOnChangeProps: [],
  // });
  useNewCommunitiesQuery({
    notifyOnChangeProps: [],
  });
  useRecentCommunityTradesQuery({
    notifyOnChangeProps: [],
  });

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set(name, value);

      return params.toString();
    },
    [searchParams],
  );

  const handleTabChange = (value: string) => {
    scrollTo({
      top: 0,
    });
    if (value === "users") {
      setCurrentTab("users");
    } else {
      setCurrentTab("tokens");
    }
  };

  const deleteQueryString = useCallback(
    (name: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.delete(name);

      return params.toString();
    },
    [searchParams],
  );

  useEffect(() => {
    startTransition(() => {
      if (throttledSearchValue) {
        router.replace(
          `${pathname}?${createQueryString("search", throttledSearchValue)}`,
        );
      } else {
        router.replace(`${pathname}?${deleteQueryString("search")}`);
      }
    });
  }, [throttledSearchValue]);

  useEffect(() => {
    const handleScroll = () => {
      if (isLargeScreen) return;
      const topSafeArea = +getComputedStyle(document.documentElement)
        .getPropertyValue("--sat")
        .replace("px", "");

      if (
        tabRef.current &&
        tabRef.current.getBoundingClientRect().top <= 68 + topSafeArea
      ) {
        backgroundColor.set("rgb(20 20 20 / 0.88)");
      } else {
        backgroundColor.set("rgb(20 20 20 / 0)");
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [backgroundColor]);

  return (
    <Tabs value={currentTab} onValueChange={handleTabChange}>
      <TabsList
        className={cn(
          "flex gap-4 pl-7 pt-8",
          isLargeScreen &&
            "bg-[rgba(20,20,20,0.88)] shadow-[0px_-5px_14px_20px_rgba(0,0,0,0.25)] backdrop-blur-[9px]",
        )}
      >
        <TabsFlatTrigger value="tokens">
          <div className="font-inter text-[26px] font-semibold leading-[30px]">
            Tokens
          </div>
        </TabsFlatTrigger>
        <TabsFlatTrigger value="users">
          <div className="font-inter text-[26px] font-semibold leading-[30px]">
            Users
          </div>
        </TabsFlatTrigger>
      </TabsList>
      <TabsContent value="users">
        <UsersTabs
          trendingUsersData={trendingUsersData}
          isTrendingUsersLoading={isTrendingUsersLoading}
        ></UsersTabs>
      </TabsContent>
      <TabsContent value="tokens">
        <GroupsTabs
          trendingGroupsData={trendingGroupsData}
          isTrendingGroupsLoading={isTrendingGroupsLoading}
        ></GroupsTabs>
      </TabsContent>
    </Tabs>
  );
};
