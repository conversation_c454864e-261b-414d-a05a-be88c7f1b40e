"use client";

import { InfiniteD<PERSON>, Query<PERSON><PERSON>, useQueryClient } from "@tanstack/react-query";

import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useFollowMutation, useUnfollowMutation } from "@/queries";
import { Follower, FollowersResponse } from "@/queries/types/follow";
import { useUser } from "@/stores";

export const UserListItem = ({
  follower,
  followedByloggedInUser,
}: Follower) => {
  const { user: me } = useUser();
  const queryClient = useQueryClient();
  const { mutateAsync: follow } = useFollowMutation({
    onMutate: async () => {
      toast.green(`You're now following ${follower.twitterName}!`);

      await queryClient.cancelQueries({
        queryKey: ["trade", "users", "trending"],
      });

      const previousFollowersLists = queryClient.getQueriesData({
        queryKey: ["followers", "list"],
      });

      queryClient.setQueriesData<InfiniteData<FollowersResponse>>(
        {
          queryKey: ["followers", "list"],
        },
        (old) => {
          if (!old) return old;
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                followersWithFollowedByloggedInUser:
                  page.followersWithFollowedByloggedInUser.map((f) => {
                    if (f.follower.id === follower.id) {
                      return {
                        ...f,
                        followedByloggedInUser: true,
                      };
                    }
                    return f;
                  }),
              };
            }),
          };
        },
      );

      return { previousFollowersLists };
    },
    onError(err, variables, context) {
      toast.danger(`Failed to follow ${follower.twitterName}.`);
      context?.previousFollowersLists?.map((key: QueryKey, value: unknown) => {
        queryClient.setQueryData(key, value);
      });
    },
  });

  const { mutateAsync: unfollow } = useUnfollowMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["trade", "users", "trending"],
      });

      const previousFollowersLists = queryClient.getQueriesData({
        queryKey: ["followers", "list"],
      });

      queryClient.setQueriesData<InfiniteData<FollowersResponse>>(
        {
          queryKey: ["followers", "list"],
        },
        (old) => {
          if (!old) return old;
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                followersWithFollowedByloggedInUser:
                  page.followersWithFollowedByloggedInUser.map((f) => {
                    if (f.follower.id === follower.id) {
                      return {
                        ...f,
                        followedByloggedInUser: false,
                      };
                    }
                    return f;
                  }),
              };
            }),
          };
        },
      );

      return { previousFollowersLists };
    },
    onError(err, variables, context) {
      toast.danger(`Failed to unfollow ${follower.twitterName}.`);
      context?.previousFollowersLists?.map((key: QueryKey, value: unknown) => {
        queryClient.setQueryData(key, value);
      });
    },
  });

  function handleFollow() {
    if (followedByloggedInUser) {
      unfollow({ userId: follower.id });
    } else {
      follow({ userId: follower.id });
    }
  }

  return (
    <div className="flex w-full justify-between gap-4 px-6 py-4">
      <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
        <ProgressBarLink href={`/${follower.twitterHandle}`}>
          <Avatar className="size-[42px]">
            <AvatarImage src={follower.twitterPicture} />
            <AvatarFallback />
          </Avatar>
        </ProgressBarLink>
        <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
          <div className="flex gap-1.5">
            <ProgressBarLink
              href={`/${follower.twitterHandle}`}
              className="truncate text-[#F4F4F4]"
            >
              {follower.twitterName}
            </ProgressBarLink>
          </div>
          <ProgressBarLink
            href={`/${follower.twitterHandle}`}
            className="truncate text-[#808080]"
          >
            @{follower.twitterHandle}
          </ProgressBarLink>
        </div>
      </div>
      {me?.id !== follower.id && (
        <div className="flex flex-shrink-0 flex-col items-end justify-center gap-[2px]">
          <Button
            variant={followedByloggedInUser ? "outline" : "secondary"}
            onClick={handleFollow}
            className="h-[34px] w-24"
          >
            {followedByloggedInUser ? "Unfollow" : "Follow"}
          </Button>
        </div>
      )}
    </div>
  );
};
