import { getAxios } from "@/lib/axios-server";
import { FollowUserData } from "@/queries/types";

export const postFollowUser = async ({ userId }: FollowUserData) => {
  const axios = getAxios();

  const response = await axios.post(`/follow/follow`, {
    userId,
  });
  return response.data;
};

export const postUnfollowUser = async ({ userId }: FollowUserData) => {
  const axios = getAxios();

  const response = await axios.post(`/follow/unfollow`, {
    userId,
  });
  return response.data;
};
