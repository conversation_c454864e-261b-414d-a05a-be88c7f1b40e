import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ARENA, AVAX } from "@/environments/tokens";
import { numberFormatter } from "@/utils";

interface CoinParams {
  image?: string;
  symbol: string;
  amount: string;
  name: string;
  systemRate: string;
}

export const RewardCoin = ({
  image,
  symbol,
  systemRate,
  amount,
  name,
}: CoinParams) => {
  const tokens = [AVAX, ARENA];
  const token = tokens.find((t) => t.name === symbol);
  const usdBalanceEquivalent = Number(
    (Number(amount) * Number(systemRate)).toFixed(2),
  );

  return (
    <div className="flex items-center gap-[10px]">
      <Avatar className="size-[30px]">
        <AvatarImage src={token?.icon || image} />
        <AvatarFallback />
      </Avatar>
      <div className="flex w-full flex-col">
        <div className="flex w-full items-center justify-between text-sm leading-3">
          <h4 className="font-semibold text-off-white">{symbol}</h4>
          <h4 className="font-semibold text-off-white">
            {amount === "??????"
              ? "??????"
              : numberFormatter.format(Number(amount))}
          </h4>
        </div>
        <div className="mt-[6px] flex w-full items-center justify-between text-sm leading-3">
          <p className="text-xs text-gray-text">{name}</p>
          <p className="text-gray-text">
            {amount === "??????"
              ? "??????"
              : `$${numberFormatter.format(usdBalanceEquivalent)}`}
          </p>
        </div>
      </div>
    </div>
  );
};
