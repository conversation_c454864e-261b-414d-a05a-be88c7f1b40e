import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import {
  getAPY,
  getRestakeAgreed,
  getRewards,
  getStakersLeaderboardRank,
  getTokenPortlaClaimableBalance,
  getTopStakersLeaderboard,
  postTokenPortalClaim,
} from "@/api/client/token-portal";

export const useTokenPortalClaimableBalanceQuery = () => {
  return useQuery({
    queryKey: ["token-portal", "claimable_balance"],
    queryFn: getTokenPortlaClaimableBalance,
  });
};

export const useTokenPortalRankQuery = () => {
  return useQuery({
    queryKey: ["token-portal", "rank"],
    queryFn: postTokenPortalClaim,
  });
};

export const useStakersLeaderboardRankQuery = () => {
  return useQuery({
    queryKey: ["stakers-leaderboard", "rank"],
    queryFn: getStakersLeaderboardRank,
  });
};

export const useTopStakersLeaderboardQuery = () => {
  return useInfiniteQuery({
    queryKey: ["stakers-leaderboard", "top"],
    queryFn: ({ pageParam }) => {
      return getTopStakersLeaderboard(pageParam);
    },
    initialPageParam: {
      page: 1,
      pageSize: 40,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (lastPage.stakers.length === lastPageParam.pageSize) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
    refetchOnMount: "always",
  });
};

export const useAPY = () => {
  return useQuery({
    queryKey: ["token-portal", "apy"],
    queryFn: getAPY,
  });
};

export const useRestakeAgreed = () => {
  return useQuery({
    queryKey: ["token-portal", "isRestakeAgreed"],
    queryFn: getRestakeAgreed,
  });
};

export const useRewards = () => {
  return useQuery({
    queryKey: ["token-portal", "rewards"],
    queryFn: getRewards,
  });
};
