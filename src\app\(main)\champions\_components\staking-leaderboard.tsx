"use client";

import { memo, useEffect, useMemo } from "react";

import { useInView } from "react-intersection-observer";
import { Virtuoso } from "react-virtuoso";

import { UserListItemLoadingSkeleton } from "@/components/user-list-item-loading-skeleton";
import { useTopStakersLeaderboardQuery } from "@/queries/token-portal-queries";

import { ChampionUserItem } from "./champion-user-item";

export const StakingLeaderboard = memo(function StakingLeaderboard() {
  const { ref, inView } = useInView();
  const {
    data,
    isLoading: isTopStakersLoading,
    fetchNextPage,
  } = useTopStakersLeaderboardQuery();

  const topStakers = useMemo(() => {
    return data?.pages.map((page) => page.stakers).flat();
  }, [data]);

  useEffect(() => {
    if (inView) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage]);

  return (
    <>
      <p className="mb-[8px] mt-[8px] px-6 text-sm text-[#808080]">
        Average Staked Value
      </p>
      <div>
        {isTopStakersLoading &&
          Array.from({ length: 15 }).map((_, index) => {
            return <UserListItemLoadingSkeleton key={index} />;
          })}
        {!isTopStakersLoading && topStakers?.length !== 0 && (
          <Virtuoso
            useWindowScroll
            data={topStakers || []}
            increaseViewportBy={500}
            itemContent={(index, staker) => {
              return (
                <ChampionUserItem
                  rank={index + 1}
                  twitterHandle={staker.twitterHandle}
                  twitterName={staker.twitterName}
                  twitterPicture={staker.twitterPicture}
                  ewmaScore={staker.ewmaScore}
                  lastEwmaScore={staker.lastEwmaScore}
                />
              );
            }}
          />
        )}
      </div>
      <div ref={ref} style={{ visibility: "hidden" }}>
        <p>.</p>
      </div>
    </>
  );
});
