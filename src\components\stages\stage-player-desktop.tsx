"use client";

import { useSelectedLayoutSegments } from "next/navigation";

import { useQuery } from "@tanstack/react-query";
import { AnimatePresence, motion } from "framer-motion";

import { stageQueries } from "@/queries/stage-queries";
import { useStageRecordingPlayerStore } from "@/stores/stage-recording-player";

import { BarChartFilledIcon, PauseFilledIcon, PlayFilledIcon } from "../icons";
import { StagePlayerContent } from "./stage-player-content";

export const StagePlayerDesktop = () => {
  const segments = useSelectedLayoutSegments();

  const isMessagesPage = segments.length > 0 && segments[0] === "(messages)";

  if (isMessagesPage) {
    return (
      <div className="relative mx-auto flex max-w-[1350px] flex-nowrap items-stretch justify-end">
        <div className="relative z-[3] hidden sm:flex sm:flex-shrink-0 sm:flex-grow sm:flex-col sm:items-end">
          <div className="flex w-[88px] flex-col items-end xl:w-[247px]" />
        </div>
        <div className="flex flex-shrink flex-grow-[2] justify-start">
          <div className="relative w-full sm:w-[610px] lg:w-[380px]">
            <div className="fixed bottom-1 w-[380px] justify-end">
              <MiniStagePlayer />
              <FullStagePlayer />
            </div>
          </div>
          <div className="hidden lg:flex lg:w-[536px] lg:flex-col xl:w-[638px]" />
        </div>
      </div>
    );
  }

  return (
    <div className="relative mx-auto flex max-w-[1350px] flex-nowrap items-stretch justify-end">
      <div className="relative z-[3] hidden sm:flex sm:flex-shrink-0 sm:flex-grow sm:flex-col sm:items-end">
        <div className="flex w-[88px] flex-col items-end xl:w-[247px]" />
      </div>
      <div className="flex flex-shrink flex-grow-[2] justify-start gap-4 xl:gap-7">
        <div className="relative w-[500px] xl:w-[615px]" />
        <div className="flex w-[290px] flex-col xl:w-[380px]">
          <div className="fixed bottom-5 w-[374px] justify-end">
            <MiniStagePlayer />
            <FullStagePlayer />
          </div>
        </div>
      </div>
    </div>
  );
};

const FullStagePlayer = () => {
  const isFullScreen = useStageRecordingPlayerStore(
    (state) => state.isRecordingPlayerFullScreen,
  );

  return (
    <AnimatePresence>
      {isFullScreen && (
        <motion.div
          initial={{
            height: 73,
            opacity: 1,
          }}
          animate={{
            height: 780,
            opacity: 1,
            transition: {
              opacity: { duration: 0.1 },
            },
          }}
          exit={{
            height: 73,
            opacity: 0,
            overflow: "hidden",
            transition: {
              opacity: { duration: 0.2, delay: 0.2 },
            },
          }}
          transition={{ type: "spring", bounce: 0, duration: 0.4 }}
          className="absolute bottom-0 flex w-full flex-col overflow-hidden rounded-[20px] border border-[#4C4C4C] bg-dark-bk"
        >
          <StagePlayerContent />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

const MiniStagePlayer = () => {
  const isPlayerOn = useStageRecordingPlayerStore((state) =>
    Boolean(state.url),
  );
  const id = useStageRecordingPlayerStore((state) => state.id!);
  const { data, isLoading } = useQuery({
    ...stageQueries.stageInfo(id),
    enabled: isPlayerOn,
  });
  const isPlaying = useStageRecordingPlayerStore((state) => state.isPlaying);
  const audioRef = useStageRecordingPlayerStore((state) => state.audioRef);
  const actions = useStageRecordingPlayerStore((state) => state.actions);

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    actions.setIsPlaying(!isPlaying);
  };

  if (!isPlayerOn || !data || isLoading) return null;

  return (
    <div
      className="flex cursor-pointer select-none items-center justify-between rounded-[20px] border-t-2 border-brand-orange bg-purple-gradient px-6 py-3"
      onClick={() => {
        actions.setIsRecordingPlayerFullScreen(true);
      }}
    >
      <div className="flex min-w-0 flex-col">
        <div className="flex items-center gap-1">
          <BarChartFilledIcon className="w-3.5 text-brand-orange" />
          <span className="mt-0.5 text-xs text-off-white">
            {data.host.user.twitterName} (Host)
          </span>
        </div>
        <div className="mt-0.5 truncate text-sm font-semibold text-off-white">
          {data.stage.name}
        </div>
      </div>
      <button
        className="flex-shrink-0 rounded-full p-2"
        onClick={(e) => {
          e.stopPropagation();
          togglePlayPause();
        }}
      >
        {isPlaying ? (
          <PauseFilledIcon className="size-5 text-off-white" />
        ) : (
          <PlayFilledIcon className="size-5 text-off-white" />
        )}
      </button>
    </div>
  );
};
