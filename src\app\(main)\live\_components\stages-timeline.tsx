"use client";

import { useEffect, useMemo, useRef, useState } from "react";

import { useQueryClient } from "@tanstack/react-query";
import { StateSnapshot, Virtuoso, VirtuosoHandle } from "react-virtuoso";

import { ArrowDownFilledIcon } from "@/components/icons";
import { PostLoadingSkeleton } from "@/components/post-loading-skeleton";
import { usePullToRefresh } from "@/hooks/use-pull-to-refresh";
import { useStagesThreadsInfiniteQuery } from "@/queries";
import { useStagesTimelineStore } from "@/stores/stages-timeline";
import { Thread } from "@/types";
import { cn } from "@/utils";

import { StageTimelinePost } from "./stage-timeline-post";

export const StagesTimeline = () => {
  const queryClient = useQueryClient();
  const timelineState = useRef<StateSnapshot>();
  const containerRef = useRef<HTMLDivElement>(null);
  const virtuoso = useRef<VirtuosoHandle | null>(null);
  const timelineRef = useStagesTimelineStore(
    (state) => state.stagesTimelineRef,
  );
  const [startReached, setStartReached] = useState(true);
  const state = usePullToRefresh(
    containerRef,
    () => {
      queryClient.resetQueries({
        queryKey: ["home", "threads", "stages-feed"],
      });
    },
    startReached,
  );

  const { snapshot, setSnapshot } = useStagesTimelineStore((state) => ({
    snapshot: state.stagesSnapshot,
    setSnapshot: state.setStagesSnapshot,
  }));

  const { data, fetchNextPage, isLoading, isFetchingNextPage } =
    useStagesThreadsInfiniteQuery();

  const threads = useMemo(() => {
    if (!data) return [];

    return data.pages.reduce((prev, current) => {
      return [...prev, ...current.threads];
    }, [] as Thread[]);
  }, [data]);

  useEffect(() => {
    return () => {
      if (timelineState.current) {
        setSnapshot(timelineState.current);
        timelineRef.current = null;
      }
    };
  }, []);

  return (
    <>
      {state !== "idle" && (
        <div className="absolute left-1/2 top-[calc(8.75rem+env(safe-area-inset-top))] -translate-x-1/2">
          <ArrowDownFilledIcon
            className={cn(
              "h-6 w-6 text-gray-text transition-transform",
              state === "release" && "rotate-180",
            )}
          />
        </div>
      )}
      <div ref={containerRef}>
        <Virtuoso
          ref={(node) => {
            virtuoso.current = node;
            timelineRef.current = node;
          }}
          useWindowScroll
          data={threads}
          endReached={() => {
            fetchNextPage();
          }}
          atTopStateChange={(state) => {
            setStartReached(state);
          }}
          increaseViewportBy={3000}
          overscan={2000}
          restoreStateFrom={snapshot}
          itemContent={(index, thread) => {
            return <StageTimelinePost thread={thread} />;
          }}
          isScrolling={() => {
            virtuoso.current?.getState((state) => {
              timelineState.current = state;
            });
          }}
          components={{
            Footer: () => {
              if (isLoading || isFetchingNextPage) {
                return (
                  <>
                    {Array.from({ length: 7 }).map((_, i) => (
                      <PostLoadingSkeleton key={i} />
                    ))}
                  </>
                );
              }
              return null;
            },
          }}
        />
      </div>
    </>
  );
};
