"use server";

import { cookies } from "next/headers";

import { env } from "@/env";
import { axios } from "@/lib/axios";

interface LoginEmailProps {
  token: string;
  ref: string | null;
}

export async function loginEmail({ token, ref }: LoginEmailProps) {
  try {
    const { data } = await axios.post(
      `${env.NEXT_PUBLIC_API_URL}/auth/dynamic-jwt-exchange-email-login`,
      { token, ref },
    );

    if (data.errorCode) {
      throw data;
    }

    // 30 days
    const expires = new Date(Date.now() + 30 * 1000 * 60 * 60 * 24);

    cookies().set("token", data.token, { httpOnly: true, expires });
    cookies().set(
      "user",
      JSON.stringify({
        ...data.user,
        loggedInAt: new Date(),
      }),
      {
        httpOnly: true,
        expires,
      },
    );
    cookies().set("twitterUser", JSON.stringify(data.twitterUser), {
      httpOnly: true,
      expires,
    });

    return { data };
  } catch (error: any) {
    return { error: error.message };
  }
}
