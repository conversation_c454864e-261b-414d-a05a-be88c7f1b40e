import { SystemCurrency } from "@/api/client/admin-currency";
import { axios } from "@/lib/axios";

export enum RewardCurrencyStatusEnum {
  UPCOMING = "upcoming",
  PREVIOUS = "previous",
  STAKING = "staking",
}

export interface RewardCurrencies {
  id: number;
  systemCurrency: SystemCurrency;
  status: RewardCurrencyStatusEnum;
  amount: number;
}

export interface CreateRewardDto {
  symbol: string;
  name: string;
  amount: number;
  status: RewardCurrencyStatusEnum;
  decimals?: number;
  contractAddress?: string;
  image: string;
}

export interface UpdateRewardDto {
  systemCurrencyId?: number;
  status?: RewardCurrencyStatusEnum;
  amount?: number;
}

export const getRewards = async () => {
  const response = await axios.get<RewardCurrencies[]>(`/admin/rewards`);
  return response.data;
};

export const getRewardById = async (id: string | number) => {
  const response = await axios.get<RewardCurrencies>(`/admin/rewards/${id}`);
  return response.data;
};

export const createReward = async (reward: CreateRewardDto) => {
  const response = await axios.post<RewardCurrencies>("/admin/rewards", reward);
  return response.data;
};

export const updateReward = async (
  id: string | number,
  reward: UpdateRewardDto,
) => {
  const response = await axios.put<RewardCurrencies>(
    `/admin/rewards/${id}`,
    reward,
  );
  return response.data;
};

export const deleteReward = async (id: string | number) => {
  const response = await axios.delete<{ success: boolean }>(
    `/admin/rewards/${id}`,
  );
  return response.data;
};
