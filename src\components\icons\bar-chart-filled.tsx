import { ComponentProps } from "react";

export const BarChartFilledIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 15 14"
    fill="none"
    {...props}
  >
    <path
      d="M0 7C0 6.69058 0.112882 6.39383 0.313814 6.17504C0.514746 5.95625 0.787268 5.83333 1.07143 5.83333H3.21429C3.49845 5.83333 3.77097 5.95625 3.9719 6.17504C4.17283 6.39383 4.28571 6.69058 4.28571 7V12.8333C4.28571 13.1428 4.17283 13.4395 3.9719 13.6583C3.77097 13.8771 3.49845 14 3.21429 14H1.07143C0.787268 14 0.514746 13.8771 0.313814 13.6583C0.112882 13.4395 0 13.1428 0 12.8333V7ZM5.35714 1.16667C5.35714 0.857247 5.47003 0.560501 5.67096 0.341709C5.87189 0.122917 6.14441 0 6.42857 0H8.57143C8.85559 0 9.12811 0.122917 9.32904 0.341709C9.52997 0.560501 9.64286 0.857247 9.64286 1.16667V12.8333C9.64286 13.1428 9.52997 13.4395 9.32904 13.6583C9.12811 13.8771 8.85559 14 8.57143 14H6.42857C6.14441 14 5.87189 13.8771 5.67096 13.6583C5.47003 13.4395 5.35714 13.1428 5.35714 12.8333V1.16667ZM10.7143 4.66667C10.7143 4.35725 10.8272 4.0605 11.0281 3.84171C11.229 3.62292 11.5016 3.5 11.7857 3.5H13.9286C14.2127 3.5 14.4853 3.62292 14.6862 3.84171C14.8871 4.0605 15 4.35725 15 4.66667V12.8333C15 13.1428 14.8871 13.4395 14.6862 13.6583C14.4853 13.8771 14.2127 14 13.9286 14H11.7857C11.5016 14 11.229 13.8771 11.0281 13.6583C10.8272 13.4395 10.7143 13.1428 10.7143 12.8333V4.66667Z"
      fill="currentColor"
    />
  </svg>
);
