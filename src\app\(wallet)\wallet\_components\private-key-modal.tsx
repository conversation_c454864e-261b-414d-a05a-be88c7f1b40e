import { useMemo, useState } from "react";

import { useEmbeddedReveal } from "@dynamic-labs/sdk-react-core";
import CopyToClipboard from "react-copy-to-clipboard";
import QRCode from "react-qr-code";

import { CopyOutlineIcon } from "@/components/icons";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Drawer } from "@/components/ui/drawer";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { Drawer as DrawerPrimitive } from "@/components/ui/vaul";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { usePrivateKeys } from "@/queries/twitter-queries";
import { cn } from "@/utils";

interface PrivateKeyModalProps {
  open: boolean;
  setOpen: (value: boolean) => void;
}

const walletName = {
  privateKey: "Pre-Dynamic Wallet Private Key",
  oldPrivateKey: "Your Old Private Key",
  solanaPrivateKey: "Solana Private Key",
} as const;

export const PrivateKeyModal = ({ open, setOpen }: PrivateKeyModalProps) => {
  const [selectOpen, setSelectOpen] = useState(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const { data, isLoading } = usePrivateKeys({
    // disable the query when the modal is closed
    enabled: open,
  });

  const [walletKey, setWalletKey] = useState("privateKey");

  const privateKey = useMemo(() => {
    if (!data) return null;

    return data[walletKey as keyof typeof data];
  }, [data, walletKey]);

  const { initExportProcess } = useEmbeddedReveal();

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="gap-4">
          <h2 className="text-base font-semibold leading-[22px] text-off-white">
            Current Wallet Private Key
          </h2>
          <p className="text-sm text-[#B5B5B5]">
            Export your private key to use it in your wallet of choice. Do not
            share this information with anyone!
          </p>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => {
              initExportProcess();
            }}
          >
            Export Dynamic Private Key
          </Button>
          {privateKey && !isLoading && (
            <>
              <Select
                value={walletKey}
                onValueChange={(value) => {
                  setWalletKey(value);
                }}
                open={selectOpen}
                onOpenChange={setSelectOpen}
              >
                <SelectTrigger className="w-full">
                  {walletName[walletKey as keyof typeof walletName]}
                </SelectTrigger>
                <SelectContent className="max-h-48">
                  {data?.privateKey && (
                    <SelectItem value="privateKey">
                      {walletName.privateKey}
                    </SelectItem>
                  )}
                  {data?.oldPrivateKey && (
                    <SelectItem value="oldPrivateKey">
                      {walletName.oldPrivateKey}
                    </SelectItem>
                  )}
                  {data?.solanaPrivateKey && (
                    <SelectItem value="solanaPrivateKey">
                      {walletName.solanaPrivateKey}
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <div className="mx-auto size-[219px] rounded-[20px] bg-white p-4">
                <QRCode
                  size={256}
                  value={privateKey}
                  viewBox={`0 0 256 256`}
                  className="h-full w-full"
                />
              </div>
              <div className="mt-2 flex min-w-0 flex-col gap-2">
                <Label>Private Key</Label>
                <div className="flex items-center justify-between gap-4 rounded-lg bg-[#313131] px-4 py-3 text-sm text-[#656565]">
                  <span className="flex-grow truncate">{privateKey}</span>
                  <CopyToClipboard
                    text={privateKey}
                    onCopy={() => {
                      toast.green("Copied to clipboard");
                    }}
                  >
                    <button className="flex-shrink-0">
                      <CopyOutlineIcon className="size-5 text-[#9E9E9E]" />
                    </button>
                  </CopyToClipboard>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerPrimitive.Portal>
        <DrawerPrimitive.Overlay onClick={selectOpen ? () => {} : undefined} />
        <DrawerPrimitive.Content
          className={cn(
            "fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[20px] border border-[#3B3B3B]/30 bg-[#0F0F0F]/90 p-6 pb-12 shadow-[0px_4px_16px_0px_rgba(0,0,0,0.25)] backdrop-blur-sm focus-visible:outline-none",
            "gap-4",
          )}
        >
          <h2 className="text-base font-semibold leading-[22px] text-off-white">
            Current Wallet Private Key
          </h2>
          <p className="text-sm text-[#B5B5B5]">
            Export your private key to use it in your wallet of choice. Do not
            share this information with anyone!
          </p>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => {
              initExportProcess();
            }}
          >
            Export Dynamic Private Key
          </Button>
          {privateKey && !isLoading && (
            <>
              <Select
                value={walletKey}
                onValueChange={(value) => {
                  setWalletKey(value);
                }}
                open={selectOpen}
                onOpenChange={setSelectOpen}
              >
                <SelectTrigger className="w-full">
                  {walletName[walletKey as keyof typeof walletName]}
                </SelectTrigger>
                <SelectContent className="max-h-48">
                  {data?.privateKey && (
                    <SelectItem value="privateKey">
                      {walletName.privateKey}
                    </SelectItem>
                  )}
                  {data?.oldPrivateKey && (
                    <SelectItem value="oldPrivateKey">
                      {walletName.oldPrivateKey}
                    </SelectItem>
                  )}
                  {data?.solanaPrivateKey && (
                    <SelectItem value="solanaPrivateKey">
                      {walletName.solanaPrivateKey}
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <div className="mx-auto size-[219px] rounded-[20px] bg-white p-4">
                <QRCode
                  size={256}
                  value={privateKey}
                  viewBox={`0 0 256 256`}
                  className="h-full w-full"
                />
              </div>
              <div className="mt-2 flex flex-col gap-2">
                <Label>Private Key</Label>
                <div className="flex items-center justify-between gap-4 rounded-lg bg-[#313131] px-4 py-3 text-sm text-[#656565]">
                  <span className="flex-grow truncate">{privateKey}</span>
                  <CopyToClipboard
                    text={privateKey}
                    onCopy={() => {
                      toast.green("Copied to clipboard");
                    }}
                  >
                    <button className="flex-shrink-0">
                      <CopyOutlineIcon className="size-5 text-[#9E9E9E]" />
                    </button>
                  </CopyToClipboard>
                </div>
              </div>
            </>
          )}
        </DrawerPrimitive.Content>
      </DrawerPrimitive.Portal>
    </Drawer>
  );
};
