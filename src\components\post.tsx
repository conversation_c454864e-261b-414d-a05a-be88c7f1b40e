import { memo, useCallback, useMemo, useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";

import { useQuery } from "@tanstack/react-query";
import { parseISO } from "date-fns";
import DOMPurify from "isomorphic-dompurify";
import CopyToClipboard from "react-copy-to-clipboard";

import { ReportModal } from "@/app/(main)/[userHandle]/_components/report-modal";
import { HANDLE_PHASE } from "@/app/(main)/community/_components/consts";
import { Button } from "@/components/ui/button";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import {
  livestreamQueries,
  useIsBlockedByUserQuery,
  useIsUserBlockedQuery,
  usePinPostLivestreamMutation,
  usePinPostToStageMutation,
} from "@/queries";
import { useExchangeCurrenciesQuery } from "@/queries/exchange-currencies-query";
import { stageQueries } from "@/queries/stage-queries";
import { usePostStore, useUser } from "@/stores";
import { useFeatureFlagsStore } from "@/stores/flags";
import { useLivestreamStore } from "@/stores/livestream";
import { useStageStore } from "@/stores/stage";
import { Thread, UserFlaggedEnum } from "@/types";
import {
  abbreviateNumber,
  checkContent,
  cn,
  formatTimeDistance,
  insertSwapLinks,
} from "@/utils";

import { ConfirmationModal } from "./confirmation-modal";
import { CommunityFeedModerationModal } from "./feed-moderation-modal";
import {
  BookmarkFilledIcon,
  BookmarkOutlineIcon,
  ChatAltDoubleOutlineIcon,
  ChatBubblesOutlineIcon,
  CopyOutlineIcon,
  EllipsisHorizontalFilledIcon,
  FlagOutlineIcon,
  HeartFilledIcon,
  HeartOutlineIcon,
  LinkOutlineIcon,
  MenuAlt2OutlineIcon,
  PencilAltOutlineIcon,
  PinOutlineIcon,
  PresentationChatBarOutlineIcon,
  RepostOutlineIcon,
  TipOutlineIcon,
  TrashOutlineIcon,
  UnpinOutlineIcon,
} from "./icons";
import { GroupIcon, OfficialGroupIcon } from "./icons-v2/group-logo";
import { ChartSquareBarOutlineIcon } from "./icons/chart-square-bar-outline";
import { ImagePreviewModal } from "./image-preview-modal";
import { useLivestreamEditor } from "./livestream/hooks/use-livestream-editor";
import { PostLivestreamInfo } from "./livestream/post-livestream-info";
import { ProgressBarLink } from "./progress-bar";
import { QuotePost } from "./quote-post";
import { ROLES } from "./stages/constants";
import { useStageEditor } from "./stages/hooks/use-stage-editor";
import { PostStageInfo } from "./stages/post-stage-info";
import { TipModal } from "./tip-modal";
import { toast } from "./toast";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Drawer, DrawerContent, DrawerTrigger } from "./ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { Video } from "./video";

type FeedContext =
  | "profile"
  | "community"
  | "global"
  | "trending"
  | "trenches"
  | null;

interface PostUIProps {
  thread: Thread;
  handleLike: ({ threadId }: { threadId: string }) => void;
  handleBookmark: ({ threadId }: { threadId: string }) => void;
  handleRepost: ({ threadId }: { threadId: string }) => void;
  handleDelete?: ({ threadId }: { threadId: string }) => void;
  handlePin?: ({
    threadId,
    isPinned,
  }: {
    threadId: string;
    isPinned: boolean;
  }) => void;
  handleModerateCommunityPin?: ({
    communityId,
    threadId,
    pinnedInCommunity,
  }: {
    communityId: string;
    threadId: string;
    pinnedInCommunity: boolean;
  }) => void;
  onClick?: () => void;
  pinnable?: boolean;
  comment?: boolean;
  feedContext?: FeedContext;
}

export const PostUI = ({
  thread,
  handleBookmark,
  handleLike,
  handleRepost,
  onClick,
  handleDelete,
  handlePin,
  handleModerateCommunityPin,
  pinnable = false,
  comment = false,
  feedContext,
}: PostUIProps) => {
  const router = useRouter();
  const { user } = useUser();
  const { data: currencies = [] } = useExchangeCurrenciesQuery();
  const currencySymbols = currencies.map((currency) => currency.symbol);
  const isRepost = thread.threadType === "repost";
  const isRepostDeleted = isRepost && !thread.repost;
  const activePost = isRepost && thread.repost ? thread.repost : thread;
  const isAnswer = isRepost && activePost.answerId && activePost.answer;
  const isCommunityPost = Boolean(
    activePost.communityId && activePost.community,
  );

  const repost = activePost.repost;
  const isQuote = activePost.threadType === "quote" && repost;
  const isQuoteDeleted = activePost.threadType === "quote" && !thread.repost;
  const isStage = activePost.threadType === "stage";
  const isLivestream = activePost.threadType === "livestream";
  const date = parseISO(activePost.createdDate);
  const [sanitizedContent, isTruncated, youtubeEmbedUrl] = checkContent({
    content: activePost.content,
  });
  const showStages = useFeatureFlagsStore((state) => state.stages);

  const { data: isUserBlocked, isLoading: isUserBlockedLoading } =
    useIsUserBlockedQuery(activePost.repost?.userId);
  const { data: isBlockedByUser, isLoading: isBlockedByUserLoading } =
    useIsBlockedByUserQuery(activePost.repost?.userId);
  const isSuspended =
    activePost.repost?.user?.flag === UserFlaggedEnum.SUSPENDED;

  const [contentWithSwapLinks] = insertSwapLinks(
    sanitizedContent,
    activePost.id,
    currencySymbols,
  );

  const shouldShowPinned =
    pinnable &&
    ((feedContext === "profile" && thread.isPinned) ||
      (feedContext === "community" && thread.pinnedInCommunity));

  if (isRepostDeleted) {
    return (
      <div
        className={cn(
          "flex cursor-pointer flex-col border-b border-dark-gray p-6",
        )}
      >
        <ProgressBarLink
          href={`/${thread.user?.twitterHandle}`}
          className="mb-3 flex items-center gap-[10px] text-xs text-[#878787]"
        >
          <RepostOutlineIcon className="h-5 w-5" />
          <span>
            {thread.user?.twitterHandle === user?.twitterHandle
              ? "You"
              : thread.userName}{" "}
            reposted
          </span>
        </ProgressBarLink>
        <DeletedPost />
      </div>
    );
  }

  return (
    <div
      onClick={() => {
        if (onClick) {
          onClick();
          return;
        }
        if (isAnswer) {
          router.push(
            `/${activePost.user?.twitterHandle}/nested/${activePost.id}`,
          );
        } else {
          router.push(
            `/${activePost.user?.twitterHandle}/status/${activePost.id}`,
          );
        }
      }}
      className={cn(
        "flex cursor-pointer flex-col border-b border-dark-gray p-6 transition-colors hover:bg-dark-gray/[0.08]",
        isCommunityPost && "pt-4",
        !isRepost &&
          !shouldShowPinned &&
          (!isCommunityPost || feedContext === "community") &&
          "flex-row gap-3",
      )}
    >
      {isRepost && (
        <div className="flex justify-between">
          <ProgressBarLink
            href={`/${thread.user?.twitterHandle}`}
            className="mb-3 flex items-center gap-[10px] text-xs text-[#878787]"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <RepostOutlineIcon className="h-5 w-5" />
            <span>
              {thread.user?.twitterHandle === user?.twitterHandle
                ? "You"
                : thread.userName}{" "}
              reposted
            </span>
          </ProgressBarLink>
          {feedContext !== "community" && isCommunityPost && (
            <ProgressBarLink
              href={`/community/${activePost.community && activePost.community.tokenPhase >= HANDLE_PHASE ? activePost.community?.name : activePost.community?.contractAddress}`}
              className="mb-2 flex items-center justify-end gap-1 text-sm font-semibold text-[#f3f3f3]"
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              {activePost.community?.isOfficial ? (
                <OfficialGroupIcon />
              ) : (
                <GroupIcon />
              )}
              <span>${activePost.community?.ticker}</span>
            </ProgressBarLink>
          )}
        </div>
      )}
      {shouldShowPinned && (
        <div className="flex justify-between">
          <div className="mb-3 flex items-center gap-[10px] text-xs text-[#878787]">
            <PinOutlineIcon className="h-5 w-5" />
            <span>Pinned post</span>
          </div>
          {feedContext !== "community" && isCommunityPost && (
            <ProgressBarLink
              href={`/community/${activePost.community && activePost.community.tokenPhase >= HANDLE_PHASE ? activePost.community?.name : activePost.community?.contractAddress}`}
              className="mb-2 flex items-center justify-end gap-1 text-sm font-semibold text-[#f3f3f3]"
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              {activePost.community?.isOfficial ? (
                <OfficialGroupIcon />
              ) : (
                <GroupIcon />
              )}
              <span>${activePost.community?.ticker}</span>
            </ProgressBarLink>
          )}
        </div>
      )}
      {!isRepost &&
        !shouldShowPinned &&
        feedContext !== "community" &&
        isCommunityPost && (
          <ProgressBarLink
            href={`/community/${activePost.community && activePost.community.tokenPhase >= HANDLE_PHASE ? activePost.community?.name : activePost.community?.contractAddress}`}
            className="mb-2 flex items-center justify-end gap-1 text-sm font-semibold text-[#f3f3f3]"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            {activePost.community?.isOfficial ? (
              <OfficialGroupIcon />
            ) : (
              <GroupIcon />
            )}
            <span>${activePost.community?.ticker}</span>
          </ProgressBarLink>
        )}
      <div className="flex w-full gap-3">
        <div className="flex-shrink-0">
          <div
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <ProgressBarLink href={`/${activePost.user?.twitterHandle}`}>
              <Avatar className="h-[42px] w-[42px]">
                <AvatarImage src={activePost.user?.twitterPicture} />
                <AvatarFallback />
              </Avatar>
            </ProgressBarLink>
          </div>
        </div>
        <div className="flex w-full min-w-0 flex-col gap-2">
          <div>
            <div className="flex items-center justify-between gap-4 text-sm leading-4 text-[#878787]">
              <div
                className="flex min-w-0 items-center"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <ProgressBarLink
                  href={`/${activePost.user?.twitterHandle}`}
                  className={cn(
                    "whitespace-nowrap font-semibold text-[#F4F4F4]",
                    activePost.userName.length > 17 && "truncate",
                  )}
                >
                  {activePost.userName}
                </ProgressBarLink>
                ・
                <ProgressBarLink
                  href={`/${activePost.user?.twitterHandle}`}
                  className="truncate"
                >
                  @{activePost.user?.twitterHandle}
                </ProgressBarLink>
                ・
                <span className="flex-shrink-0">
                  {formatTimeDistance(date)}
                </span>
              </div>
              {!isRepost && (user?.id === activePost.userId || user?.isMod) ? (
                <PostMenuActionsUI
                  thread={activePost}
                  handleDelete={handleDelete}
                  handlePin={handlePin}
                  handleModerateCommunityPin={handleModerateCommunityPin}
                />
              ) : (
                <GeneralPostMenuActionsUI
                  thread={activePost}
                  comment={comment}
                  handleDelete={handleDelete}
                  handleModerateCommunityPin={handleModerateCommunityPin}
                />
              )}
            </div>
            {isAnswer && (
              <div
                className="text-sm leading-4 text-gray-text"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                Replying to{" "}
                <ProgressBarLink
                  href={`/${activePost.answer?.user?.twitterHandle}`}
                  className="truncate text-brand-orange"
                >
                  @{activePost.answer?.user?.twitterHandle}
                </ProgressBarLink>
              </div>
            )}
          </div>
          {isStage && activePost.stage != null && showStages ? (
            <PostStageInfo
              stage={activePost.stage}
              host={{
                ...activePost.user,
                twitterName:
                  activePost?.user?.twitterName ?? activePost.userName,
              }}
            />
          ) : isLivestream && activePost.livestream != null ? (
            <PostLivestreamInfo
              livestream={activePost.livestream}
              host={{
                ...activePost.user,
                twitterName:
                  activePost?.user?.twitterName ?? activePost.userName,
              }}
            />
          ) : (
            <div className="flex flex-col text-sm">
              <div
                dangerouslySetInnerHTML={{ __html: contentWithSwapLinks }}
                className="post-content select-none text-[#F4F4F4]"
              />
              {isTruncated && <div className="text-[#EA540A]">Show more</div>}
            </div>
          )}
          {activePost.images && activePost.images.length > 0 && (
            <div
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <ImagePreviewModal url={activePost.images[0].url}>
                <img
                  src={activePost.images[0].url}
                  alt=""
                  className="max-h-[510px] w-full rounded-2xl object-cover"
                />
              </ImagePreviewModal>
            </div>
          )}
          {activePost.videos && activePost.videos.length > 0 && (
            <Video
              src={activePost.videos[0].url}
              className="max-h-[500px] rounded-2xl"
              onClick={(e) => {
                e.stopPropagation();
              }}
            />
          )}
          {youtubeEmbedUrl && (
            <iframe
              src={youtubeEmbedUrl}
              title="YouTube video player"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              referrerPolicy="strict-origin-when-cross-origin"
              allowFullScreen
              className="aspect-video w-full rounded-2xl"
            />
          )}
          {isQuote &&
            repost &&
            !isUserBlockedLoading &&
            !isBlockedByUserLoading &&
            (isUserBlocked || isBlockedByUser || isSuspended ? (
              <div>
                <QuotePost {...repost} />
              </div>
            ) : (
              <div
                onClick={(e) => {
                  e.stopPropagation();
                  router.push(
                    `/${repost.user?.twitterHandle}/status/${repost.id}`,
                  );
                }}
              >
                <QuotePost {...repost} />
              </div>
            ))}
          {isQuoteDeleted && <DeletedPost />}
          <div className="mt-1">
            <PostActionsUI
              thread={activePost}
              handleLike={handleLike}
              handleBookmark={handleBookmark}
              handleRepost={handleRepost}
              isDisabled={
                isUserBlocked ||
                isBlockedByUser ||
                isUserBlockedLoading ||
                isBlockedByUserLoading ||
                isSuspended ||
                false
              }
            />
          </div>
        </div>
      </div>
    </div>
  );
};

type MainPostUIProps = PostUIProps & {
  linkType?: "main" | "nested";
};

export const MainPostUI = ({
  thread,
  handleBookmark,
  handleLike,
  handleRepost,
  handleDelete,
  linkType,
  onClick,
  handleModerateCommunityPin,
}: MainPostUIProps) => {
  const router = useRouter();
  const { data: currencies = [] } = useExchangeCurrenciesQuery();
  const currencySymbols = currencies.map((currency) => currency.symbol);
  const isRepost = thread.threadType === "repost";
  const isStage = thread.threadType === "stage";
  const isLivestream = thread.threadType === "livestream";
  const activePost = isRepost && thread.repost ? thread.repost : thread;
  const isCommunityPost = Boolean(
    activePost.communityId && activePost.community,
  );
  const [content, _isTrucated, youtubeEmbedUrl] = checkContent({
    content: activePost.content,
    truncate: false,
  });
  const showStages = useFeatureFlagsStore((state) => state.stages);

  const { data: isUserBlocked, isLoading: isUserBlockedLoading } =
    useIsUserBlockedQuery(activePost.repost?.userId);
  const { data: isBlockedByUser, isLoading: isBlockedByUserLoading } =
    useIsBlockedByUserQuery(activePost.repost?.userId);
  const isSuspended =
    activePost.repost?.user?.flag === UserFlaggedEnum.SUSPENDED;

  const [contentWithSwapLinks, swapToken] = insertSwapLinks(
    content,
    activePost.id,
    currencySymbols,
  );

  const repost = activePost.repost;
  const isQuote = activePost.threadType === "quote" && repost;
  const isQuoteDeleted = activePost.threadType === "quote" && !thread.repost;
  const date = parseISO(activePost.createdDate);

  return (
    <div
      onClick={
        linkType
          ? () => {
              if (onClick) {
                onClick();
                return;
              }

              if (linkType === "main") {
                router.push(
                  `/${activePost.user?.twitterHandle}/status/${activePost.id}`,
                );
              } else {
                router.push(
                  `/${activePost.user?.twitterHandle}/nested/${activePost.id}`,
                );
              }
            }
          : undefined
      }
      className={cn(
        "border-b border-dark-gray p-6",
        !isRepost && !isCommunityPost && "flex gap-3",
        linkType && "cursor-pointer hover:bg-dark-gray/[0.08]",
      )}
    >
      {isRepost && (
        <>
          <div className="mb-3 flex items-center gap-[10px] text-xs text-[#878787]">
            <RepostOutlineIcon className="h-5 w-5" />
            <span>{thread.userName} reposted</span>
          </div>
          {isCommunityPost && (
            <ProgressBarLink
              href={`/community/${activePost.community && activePost.community.tokenPhase >= 4 ? activePost.community?.name : activePost.community?.contractAddress}`}
              className="mb-2 flex items-center justify-end gap-1 text-sm font-semibold text-[#f3f3f3]"
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              {activePost.community?.isOfficial ? (
                <OfficialGroupIcon />
              ) : (
                <GroupIcon />
              )}
              <span>${activePost.community?.ticker}</span>
            </ProgressBarLink>
          )}
        </>
      )}
      {!isRepost && !activePost.isDeleted && isCommunityPost && (
        <ProgressBarLink
          href={`/community/${activePost.community && activePost.community.tokenPhase >= 4 ? activePost.community?.name : activePost.community?.contractAddress}`}
          className="mb-2 flex items-center justify-end gap-1 text-sm font-semibold text-[#f3f3f3]"
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {activePost.community?.isOfficial ? (
            <OfficialGroupIcon />
          ) : (
            <GroupIcon />
          )}
          <span>${activePost.community?.ticker}</span>
        </ProgressBarLink>
      )}
      {!activePost.isDeleted ? (
        <div className="flex w-full min-w-0 flex-col gap-3">
          <div className="flex gap-3">
            <div className="flex-shrink-0">
              <ProgressBarLink
                href={`/${activePost.user?.twitterHandle}`}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <Avatar className="h-[42px] w-[42px]">
                  <AvatarImage src={activePost?.user?.twitterPicture} />
                  <AvatarFallback />
                </Avatar>
              </ProgressBarLink>
            </div>
            <div className="flex w-full min-w-0 items-center justify-between gap-4 text-sm leading-4 text-[#878787]">
              <div className="flex min-w-0 items-center">
                <ProgressBarLink
                  href={`/${activePost.user?.twitterHandle}`}
                  className={cn(
                    "whitespace-nowrap font-semibold text-[#F4F4F4]",
                    activePost.userName.length > 17 && "truncate",
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  {activePost.userName}
                </ProgressBarLink>
                ・
                <ProgressBarLink
                  href={`/${activePost.user?.twitterHandle}`}
                  className="truncate"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  @{activePost.user?.twitterHandle}
                </ProgressBarLink>
                ・
                <span className="flex-shrink-0">
                  {formatTimeDistance(date)}
                </span>
              </div>
              <GeneralPostMenuActionsUI
                thread={activePost}
                comment={false}
                handleDelete={handleDelete}
                handleModerateCommunityPin={handleModerateCommunityPin}
                triggeredFromMainPostUI={true}
              />
            </div>
          </div>
          {isStage && activePost.stage != null && showStages ? (
            <PostStageInfo
              stage={activePost.stage}
              host={{
                ...activePost.user,
                twitterName:
                  activePost?.user?.twitterName ?? activePost.userName,
              }}
            />
          ) : isLivestream && activePost.livestream != null ? (
            <PostLivestreamInfo
              livestream={activePost.livestream}
              host={{
                ...activePost.user,
                twitterName:
                  activePost?.user?.twitterName ?? activePost.userName,
              }}
            />
          ) : (
            <div
              dangerouslySetInnerHTML={{ __html: contentWithSwapLinks }}
              className="post-content text-sm text-[#F4F4F4]"
            />
          )}
          {activePost.images && activePost.images.length > 0 && (
            <ImagePreviewModal url={activePost.images[0].url}>
              <img
                src={activePost.images[0].url}
                alt=""
                className="max-h-[1000px] w-full rounded-2xl object-cover"
              />
            </ImagePreviewModal>
          )}
          {activePost.videos && activePost.videos.length > 0 && (
            <Video
              src={activePost.videos[0].url}
              className="max-h-[500px] rounded-2xl"
            />
          )}
          {youtubeEmbedUrl && (
            <iframe
              src={youtubeEmbedUrl}
              title="YouTube video player"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              referrerPolicy="strict-origin-when-cross-origin"
              allowFullScreen
              className="aspect-video w-full rounded-2xl"
            />
          )}
          {isQuote &&
            repost &&
            !isUserBlockedLoading &&
            !isBlockedByUserLoading &&
            (isUserBlocked || isBlockedByUser || isSuspended ? (
              <div>
                <QuotePost {...repost} />
              </div>
            ) : (
              <div
                onClick={(e) => {
                  e.stopPropagation();
                  router.push(
                    `/${repost.user?.twitterHandle}/status/${repost.id}`,
                  );
                }}
              >
                <QuotePost {...repost} />
              </div>
            ))}
          {isQuoteDeleted && <DeletedPost />}
          <div className="mt-1">
            <PostActionsUI
              thread={activePost}
              handleLike={handleLike}
              handleBookmark={handleBookmark}
              handleRepost={handleRepost}
              isDisabled={
                isUserBlocked ||
                isBlockedByUser ||
                isUserBlockedLoading ||
                isBlockedByUserLoading ||
                isSuspended ||
                false
              }
            />
          </div>

          {swapToken && (
            <div
              onClick={() => {
                router.push(swapToken.tokenURL);
              }}
              className="cursor-pointer"
            >
              <Button className="mt-1 w-full">
                {"BUY $" + swapToken.tokenName}
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="flex flex-col text-sm">
          <div className="text-gray-text">
            Post unavailable. This post has voilated The Arena&apos;s{" "}
            <ProgressBarLink
              href="/terms-of-use"
              className="font-semibold text-white"
            >
              terms of use.
            </ProgressBarLink>
          </div>
        </div>
      )}
    </div>
  );
};

export const ReplyPostUI = ({
  thread,
  usersHandles,
}: {
  thread: Thread;
  usersHandles?: string[] | null;
}) => {
  const { data: currencies = [] } = useExchangeCurrenciesQuery();
  const currencySymbols = currencies.map((currency) => currency.symbol);
  const date = parseISO(thread.createdDate);

  const [content, _isTrucated, youtubeEmbedUrl] = checkContent({
    content: thread.content,
    truncate: false,
  });

  const [contentWithSwapLinks] = insertSwapLinks(
    content,
    thread.id,
    currencySymbols,
  );

  return (
    <div className="p-6 pb-1">
      <div className="flex w-full gap-3">
        <div className="relative flex-shrink-0">
          <Avatar className="h-[42px] w-[42px]">
            <AvatarImage src={thread.user?.twitterPicture} />
            <AvatarFallback />
          </Avatar>
          <div className="absolute left-1/2 top-0 -z-10 h-full w-px -translate-x-1/2 bg-gray-text" />
        </div>
        <div className="flex w-full min-w-0 flex-col gap-2">
          <div className="flex text-sm leading-4 text-gray-text">
            <span className="flex-shrink-0 font-semibold text-off-white">
              {thread.userName}
            </span>
            ・
            <span className="max-w-36 truncate">
              @{thread.user?.twitterHandle}
            </span>
            ・<span className="flex-shrink-0">{formatTimeDistance(date)}</span>
          </div>
          <div
            dangerouslySetInnerHTML={{ __html: contentWithSwapLinks }}
            className="post-content text-sm text-off-white"
          />
          {thread.images && thread.images.length > 0 && (
            <ImagePreviewModal url={thread.images[0].url}>
              <img
                src={thread.images[0].url}
                alt=""
                className="max-h-[1000px] w-full rounded-2xl object-cover"
              />
            </ImagePreviewModal>
          )}
          {thread.videos && thread.videos.length > 0 && (
            <Video src={thread.videos[0].url} />
          )}
          {youtubeEmbedUrl && (
            <iframe
              src={youtubeEmbedUrl}
              title="YouTube video player"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              referrerPolicy="strict-origin-when-cross-origin"
              allowFullScreen
              className="aspect-video w-full rounded-2xl"
            />
          )}
          <div className="text-sm text-gray-text">
            Replying to{" "}
            <span className="text-brand-orange">
              {usersHandles && usersHandles.length > 0 ? (
                <>
                  {usersHandles.length <= 3 ? (
                    usersHandles.map((userHandle, index) => (
                      <span key={userHandle}>
                        @{userHandle}
                        {index !== usersHandles.length - 1 ? ", " : ""}
                      </span>
                    ))
                  ) : (
                    <>
                      {usersHandles.slice(0, 2).map((userHandle, index) => (
                        <span key={userHandle}>
                          @{userHandle}
                          {index !== 1 ? ", " : " "}
                        </span>
                      ))}
                      <span>and {usersHandles.length - 2} others</span>
                    </>
                  )}
                </>
              ) : (
                <span>@{thread.user?.twitterHandle}</span>
              )}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

interface PostActionsProps {
  thread: Thread;
  handleLike: ({ threadId }: { threadId: string }) => void;
  handleBookmark: ({ threadId }: { threadId: string }) => void;
  handleRepost: ({ threadId }: { threadId: string }) => void;
  isDisabled: boolean;
}

export const PostActionsUI = ({
  thread,
  handleLike,
  handleBookmark,
  handleRepost,
  isDisabled,
}: PostActionsProps) => {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const setReply = usePostStore((s) => s.setReply);
  const setQuote = usePostStore((s) => s.setQuote);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const memoizedThread = useMemo(() => thread, [thread]);

  const memoizedHandleLike = useCallback(() => {
    handleLike({ threadId: thread.id });
  }, [handleLike, thread.id]);

  const memoizedHandleBookmark = useCallback(() => {
    handleBookmark({ threadId: thread.id });
  }, [handleBookmark, thread.id]);

  const memoizedHandleRepost = useCallback(() => {
    handleRepost({ threadId: thread.id });
  }, [handleRepost, thread.id]);

  return (
    <div
      className="flex items-center gap-4 text-[#808080]"
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <button
        className="focus-visible:outline-blue-400 flex items-center gap-1 rounded focus:outline-none focus-visible:outline-1 focus-visible:outline-offset-2"
        onClick={() => {
          setReply(thread);
          router.push(`/compose/post`);
        }}
        disabled={isDisabled}
      >
        <ChatBubblesOutlineIcon className="h-5 w-5" />
        <span className="text-xs text-off-white">
          {abbreviateNumber(thread.answerCount)}
        </span>
      </button>
      {isTablet && (
        <DropdownMenu open={open} onOpenChange={setOpen}>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "focus-visible:outline-blue-400 hidden items-center gap-1 rounded focus:outline-none focus-visible:outline-1 focus-visible:outline-offset-2 sm:flex",
                thread.reposted && "font-semibold text-white",
              )}
              disabled={isDisabled}
            >
              <RepostOutlineIcon
                className={cn("h-5 w-5", thread.reposted && "text-green")}
              />
              <span className="text-xs text-off-white">
                {abbreviateNumber(thread.repostCount)}
              </span>
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[220px]">
            <DropdownMenuItem
              className="gap-4"
              onSelect={() => {
                memoizedHandleRepost();
                setOpen(false);
              }}
              disabled={isDisabled}
            >
              <RepostOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
              {thread.reposted ? "Undo repost" : "Repost"}
            </DropdownMenuItem>
            <DropdownMenuItem
              className="gap-4"
              onSelect={() => {
                setQuote(thread);
                router.push(`/compose/post`);
                setOpen(false);
              }}
              disabled={isDisabled}
            >
              <MenuAlt2OutlineIcon className="size-5 text-gray-text" /> Quote
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      {!isTablet && (
        <Drawer open={open} onOpenChange={setOpen}>
          <DrawerTrigger asChild>
            <button
              className={cn(
                "focus-visible:outline-blue-400 flex items-center gap-1 rounded focus:outline-none focus-visible:outline-1 focus-visible:outline-offset-2 sm:hidden",
                thread.reposted && "font-semibold text-white",
              )}
              disabled={isDisabled}
            >
              <RepostOutlineIcon
                className={cn("h-5 w-5", thread.reposted && "text-green")}
              />
              <span className="text-xs text-off-white">
                {abbreviateNumber(thread.repostCount)}
              </span>
            </button>
          </DrawerTrigger>
          <DrawerContent className="px-4 pt-4">
            <button
              className="flex items-center gap-2 p-2 text-base leading-5"
              onClick={() => {
                memoizedHandleRepost();
                setOpen(false);
              }}
              disabled={isDisabled}
            >
              <RepostOutlineIcon className="size-6 text-gray-text" />{" "}
              {thread.reposted ? "Undo repost" : "Repost"}
            </button>
            <button
              className="flex items-center gap-2 p-2 text-base leading-5"
              onClick={() => {
                setQuote(thread);
                router.push(`/compose/post`);
                setOpen(false);
              }}
              disabled={isDisabled}
            >
              <MenuAlt2OutlineIcon className="size-6 text-gray-text" /> Quote
            </button>
          </DrawerContent>
        </Drawer>
      )}
      <button
        className="focus-visible:outline-blue-400 flex items-center gap-1 rounded focus:outline-none focus-visible:outline-1 focus-visible:outline-offset-2"
        onClick={memoizedHandleLike}
        disabled={isDisabled}
      >
        {thread.like ? (
          <HeartFilledIcon className="h-5 w-5 fill-brand-orange" />
        ) : (
          <HeartOutlineIcon className="h-5 w-5" />
        )}
        <span
          className={cn(
            "text-xs  text-off-white",
            thread.like && "font-semibold",
          )}
        >
          {abbreviateNumber(thread.likeCount)}
        </span>
      </button>
      <button
        className="focus-visible:outline-blue-400 flex items-center gap-1 rounded focus:outline-none focus-visible:outline-1 focus-visible:outline-offset-2"
        onClick={memoizedHandleBookmark}
        disabled={isDisabled}
      >
        {thread.bookmark ? (
          <BookmarkFilledIcon className="h-5 w-5 fill-off-white" />
        ) : (
          <BookmarkOutlineIcon className="h-5 w-5" />
        )}
        <span
          className={cn(
            "text-xs text-off-white",
            thread.bookmark && "font-semibold",
          )}
        >
          {abbreviateNumber(thread.bookmarkCount)}
        </span>
      </button>
      <TipModal thread={memoizedThread}>
        <button
          className="focus-visible:outline-blue-400 ml-auto flex items-center gap-1 rounded focus:outline-none focus-visible:outline-1 focus-visible:outline-offset-2"
          disabled={isDisabled}
        >
          <TipOutlineIcon className="h-5 w-5" />
          {thread.tipAmount &&
          numberFormatter.format(thread.tipAmount) != "0" ? (
            <span className="text-xs text-off-white">
              {abbreviateNumber(
                thread.tipAmount < 0.1 ? 0.1 : thread.tipAmount,
              )}
              $
            </span>
          ) : null}
        </button>
      </TipModal>
    </div>
  );
};

interface PostMenuActionsUIProps {
  thread: Thread;
  handleDelete?: ({ threadId }: { threadId: string }) => void;
  handlePin?: ({
    threadId,
    isPinned,
  }: {
    threadId: string;
    isPinned: boolean;
  }) => void;
  handleModerateCommunityPin?: ({
    communityId,
    threadId,
    pinnedInCommunity,
  }: {
    communityId: string;
    threadId: string;
    pinnedInCommunity: boolean;
  }) => void;
}

export const PostMenuActionsUI = memo(
  ({
    thread,
    handleDelete,
    handlePin,
    handleModerateCommunityPin,
  }: PostMenuActionsUIProps) => {
    const { user } = useUser();
    const pathname = usePathname();
    const router = useRouter();
    const isTablet = useMediaQuery(BREAKPOINTS.sm);
    const [open, setOpen] = useState(false);
    const [isUnpinOpen, setIsUnpinOpen] = useState(false);
    const [isModerateUnpinOpen, setIsModerateUnpinOpen] = useState(false);
    const [isDeleteOpen, setIsDeleteOpen] = useState(false);
    const [isReportOpen, setIsReportOpen] = useState(false);
    const isInProfilePage = user
      ? pathname === `/${user?.twitterHandle}`
      : false;
    const sanitizedContent = DOMPurify.sanitize(thread.content || "");
    const htmlRemovedContent = stripHtmlTags(sanitizedContent);
    const stageId = useStageStore((s) => s.id);
    const livestreamId = useLivestreamStore((s) => s.id);
    const myRole = useStageStore((s) => s.myRole);
    const myRoleLivestream = useLivestreamStore((s) => s.myRole);
    const isInRoomAndThreadIsSharable =
      (stageId || livestreamId) &&
      (!(thread.stage ? thread.stage.id : null) ||
        !(thread.livestream ? thread.livestream.id : null));

    const canPinPostToStage =
      isInRoomAndThreadIsSharable &&
      (myRole === ROLES.HOST || myRole === ROLES.COHOST);
    const canModeratePin =
      Boolean(thread.communityId && thread.community) &&
      user &&
      (user.id === thread.community?.ownerId ||
        thread.community?.memberLink?.role === "moderator");
    const canPinPostToLivestream =
      isInRoomAndThreadIsSharable &&
      (myRoleLivestream === "HOST" || myRoleLivestream === "MODERATOR");

    const { mutate: pinPost } = usePinPostToStageMutation({
      onSuccess: () => {
        toast.green("Post shared to stage");
      },
    });

    const { mutate: pinPostLivestream } = usePinPostLivestreamMutation({
      onSuccess: () => {
        toast.green("Post shared to livestream");
      },
    });

    const [_, setStageEditor] = useStageEditor();
    const [_l, setLivestreamEditor] = useLivestreamEditor();

    const handleUnpinConfirm = useCallback(() => {
      handlePin && handlePin({ threadId: thread.id, isPinned: false });
      setIsUnpinOpen(false);
    }, [handlePin, thread.id]);

    const handleDeleteConfirm = useCallback(() => {
      handleDelete && handleDelete({ threadId: thread.id });
      setIsDeleteOpen(false);
    }, [handleDelete, thread.id]);

    const handleModerateUnpinConfirm = useCallback(() => {
      handleModerateCommunityPin &&
        handleModerateCommunityPin({
          communityId: thread.communityId || "",
          threadId: thread.id,
          pinnedInCommunity: false,
        });
      setIsModerateUnpinOpen(false);
    }, [handleModerateCommunityPin, thread.id]);

    const handlePinToStage = useCallback(() => {
      if (!stageId) return;

      pinPost({
        postId: thread.id,
        stageId: stageId,
      });
      setOpen(false);
    }, [pinPost, thread.id, stageId]);

    const handlePinToLivestream = useCallback(() => {
      if (!livestreamId) return;

      pinPostLivestream({
        postId: thread.id,
        livestreamId: livestreamId,
      });
    }, [pinPostLivestream, thread.id, livestreamId]);

    const isStage = thread.threadType === "stage" && thread.stage;
    const isLivestream =
      thread.threadType === "livestream" && thread.livestream;

    const { data: stageData } = useQuery({
      ...stageQueries.stageInfoSimple(thread?.stage?.id ?? ""),
      enabled: !!thread?.stage?.id,
    });
    const { data: livestreamData } = useQuery({
      ...livestreamQueries.livestreamSimpleInfo(thread?.livestream?.id ?? ""),
      enabled: !!thread?.livestream?.id,
    });

    const isMyInActiveScheduledStage =
      isStage &&
      stageData?.stage.scheduledStartTime &&
      !stageData?.stage.isActive &&
      new Date(stageData?.stage.scheduledStartTime) > new Date() &&
      stageData?.stage.hostId === user?.id;

    const isMyInActiveScheduledLivestream =
      isLivestream &&
      livestreamData?.livestream?.scheduledStartTime &&
      !livestreamData?.livestream?.isActive &&
      new Date(livestreamData?.livestream?.scheduledStartTime) > new Date() &&
      livestreamData?.livestream?.hostId === user?.id;

    const handleEditStage = useCallback(() => {
      setOpen(false);
      // router.push(`/compose/stage?stageId=${thread.stage?.id}`);
      setStageEditor({ composeStage: true, stageId: thread.stage?.id });
    }, [router, thread.stage?.id]);

    const handleEditLivestream = useCallback(() => {
      setOpen(false);
      setLivestreamEditor({
        composeLivestream: true,
        livestreamId: thread.livestream?.id,
      });
    }, [router, thread.livestream?.id]);

    return (
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        {isTablet && (
          <DropdownMenu open={open} onOpenChange={setOpen}>
            <DropdownMenuTrigger asChild>
              <button className="outline-none">
                <EllipsisHorizontalFilledIcon className="h-5 w-5 fill-off-white" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[220px]">
              <DropdownMenuItem className="w-full gap-4" asChild>
                <Link
                  href={`/${thread.user?.twitterHandle}/status/${thread.id}/postreactions/likes`}
                >
                  <ChartSquareBarOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Post Reactions</span>
                </Link>
              </DropdownMenuItem>
              {isInProfilePage ? (
                thread.isPinned ? (
                  <DropdownMenuItem
                    className="gap-4"
                    onSelect={() => {
                      setIsUnpinOpen(true);
                    }}
                  >
                    <UnpinOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>
                      {canModeratePin ? "Unpinin from my Profile" : "Unpin"}
                    </span>
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem
                    className="gap-4"
                    onSelect={() => {
                      handlePin &&
                        handlePin({ threadId: thread.id, isPinned: true });
                    }}
                  >
                    <PinOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>{canModeratePin ? "Pin to my Profile" : "Pin"}</span>
                  </DropdownMenuItem>
                )
              ) : null}
              {canModeratePin ? (
                thread.pinnedInCommunity ? (
                  <DropdownMenuItem
                    className="gap-4"
                    onSelect={() => {
                      setIsModerateUnpinOpen(true);
                    }}
                  >
                    <UnpinOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Unpin from token feed</span>
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem
                    className="gap-4"
                    onSelect={() => {
                      handleModerateCommunityPin &&
                        handleModerateCommunityPin({
                          communityId: thread.communityId || "",
                          threadId: thread.id,
                          pinnedInCommunity: true,
                        });
                    }}
                  >
                    <PinOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Pin to token feed</span>
                  </DropdownMenuItem>
                )
              ) : null}
              <DropdownMenuItem
                className="gap-4"
                onSelect={() => {
                  setIsDeleteOpen(true);
                }}
              >
                <TrashOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Delete</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="w-full gap-4" asChild>
                <CopyToClipboard
                  text={
                    typeof window !== "undefined"
                      ? `${window.location.origin}/${thread.user?.twitterHandle}/status/${thread.id}`
                      : ""
                  }
                  onCopy={() => {
                    toast.green("Copied the link to the clipboard");
                    setOpen(false);
                  }}
                >
                  <button>
                    <LinkOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Copy link</span>
                  </button>
                </CopyToClipboard>
              </DropdownMenuItem>
              {isMyInActiveScheduledStage ? (
                <DropdownMenuItem className="w-full gap-4" asChild>
                  <button onClick={handleEditStage}>
                    <PencilAltOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Edit Stage</span>
                  </button>
                </DropdownMenuItem>
              ) : isMyInActiveScheduledLivestream ? (
                <DropdownMenuItem
                  className="w-full gap-4"
                  onSelect={handleEditLivestream}
                >
                  <PencilAltOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Edit Livestream</span>
                </DropdownMenuItem>
              ) : htmlRemovedContent.length > 0 ? (
                <DropdownMenuItem className="w-full gap-4" asChild>
                  <CopyToClipboard
                    text={htmlRemovedContent}
                    onCopy={() => {
                      toast.green("Copied the text to the clipboard");
                      setOpen(false);
                    }}
                  >
                    <button>
                      <CopyOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Copy Text</span>
                    </button>
                  </CopyToClipboard>
                </DropdownMenuItem>
              ) : null}
              {canPinPostToStage ? (
                <DropdownMenuItem
                  className="w-full gap-4"
                  onClick={handlePinToStage}
                >
                  <PresentationChatBarOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Share to your Stage</span>
                </DropdownMenuItem>
              ) : null}
              {user?.id !== thread.user?.id && (
                <DropdownMenuItem
                  className="w-full gap-4"
                  onSelect={() => {
                    setOpen(false);
                    setIsReportOpen(true);
                  }}
                >
                  <FlagOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Report Post</span>
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        {!isTablet && (
          <Drawer open={open} onOpenChange={setOpen}>
            <DrawerTrigger asChild>
              <button className="outline-none">
                <EllipsisHorizontalFilledIcon className="h-5 w-5 fill-off-white" />
              </button>
            </DrawerTrigger>
            <DrawerContent className="px-4 pt-4">
              <Link
                href={`/${thread.user?.twitterHandle}/status/${thread.id}/postreactions/likes`}
                className="flex items-center gap-2 p-2 text-base leading-5"
              >
                <ChartSquareBarOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Post Reactions</span>
              </Link>
              {isInProfilePage ? (
                thread.isPinned ? (
                  <button
                    className="flex items-center gap-2 p-2 text-base leading-5"
                    onClick={() => {
                      setIsUnpinOpen(true);
                      setOpen(false);
                    }}
                  >
                    <UnpinOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>
                      {canModeratePin ? "Unpinin from my Profile" : "Unpin"}
                    </span>
                  </button>
                ) : (
                  <button
                    className="flex items-center gap-2 p-2 text-base leading-5"
                    onClick={() => {
                      handlePin &&
                        handlePin({ threadId: thread.id, isPinned: true });
                      setOpen(false);
                    }}
                  >
                    <PinOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>{canModeratePin ? "Pin to my Profile" : "Pin"}</span>
                  </button>
                )
              ) : null}
              {canModeratePin ? (
                thread.pinnedInCommunity ? (
                  <button
                    className="flex items-center gap-2 p-2 text-base leading-5"
                    onClick={() => {
                      setIsModerateUnpinOpen(true);
                      setOpen(false);
                    }}
                  >
                    <UnpinOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Unpin from token feed</span>
                  </button>
                ) : (
                  <button
                    className="flex items-center gap-2 p-2 text-base leading-5"
                    onClick={() => {
                      handleModerateCommunityPin &&
                        handleModerateCommunityPin({
                          communityId: thread.communityId || "",
                          threadId: thread.id,
                          pinnedInCommunity: true,
                        });
                      setOpen(false);
                    }}
                  >
                    <PinOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Pin to token feed</span>
                  </button>
                )
              ) : null}
              <button
                className="flex items-center gap-2 p-2 text-base leading-5"
                onClick={() => {
                  setIsDeleteOpen(true);
                  setOpen(false);
                }}
              >
                <TrashOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Delete</span>
              </button>

              <CopyToClipboard
                text={
                  typeof window !== "undefined"
                    ? `${window.location.origin}/${thread.user?.twitterHandle}/status/${thread.id}`
                    : ""
                }
                onCopy={() => {
                  toast.green("Copied the link to the clipboard");
                  setOpen(false);
                }}
              >
                <button className="flex items-center gap-2 p-2 text-base leading-5">
                  <LinkOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Copy link</span>
                </button>
              </CopyToClipboard>
              {isMyInActiveScheduledStage ? (
                <button
                  className="flex items-center gap-2 p-2 text-base leading-5"
                  onClick={handleEditStage}
                >
                  <PencilAltOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Edit Stage</span>
                </button>
              ) : isMyInActiveScheduledLivestream ? (
                <button
                  className="flex items-center gap-2 p-2 text-base leading-5"
                  onClick={handleEditLivestream}
                >
                  <PencilAltOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Edit Livestream</span>
                </button>
              ) : htmlRemovedContent.length > 0 ? (
                <CopyToClipboard
                  text={htmlRemovedContent}
                  onCopy={() => {
                    toast.green("Copied the text to the clipboard");
                    setOpen(false);
                  }}
                >
                  <button className="flex items-center gap-2 p-2 text-base leading-5">
                    <CopyOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Copy Text</span>
                  </button>
                </CopyToClipboard>
              ) : null}
              {canPinPostToStage ? (
                <button
                  className="flex items-center gap-2 p-2 text-base leading-5"
                  onClick={handlePinToStage}
                >
                  <PresentationChatBarOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Share to your Stage</span>
                </button>
              ) : null}
              {user?.id !== thread.user?.id && (
                <button
                  className="flex items-center gap-2 p-2 text-base leading-5"
                  onClick={() => {
                    setOpen(false);
                    setIsReportOpen(true);
                  }}
                >
                  <FlagOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Report Post</span>
                </button>
              )}
            </DrawerContent>
          </Drawer>
        )}
        <ConfirmationModal
          open={isUnpinOpen}
          setOpen={setIsUnpinOpen}
          title="Unpin post from profile?"
          confirmButtonLabel="Unpin"
          onConfirm={handleUnpinConfirm}
        >
          This post will no longer appear automatically at the top of your
          profile.
        </ConfirmationModal>
        <ConfirmationModal
          open={isDeleteOpen}
          setOpen={setIsDeleteOpen}
          title="Delete post?"
          confirmButtonLabel="Delete"
          destructive
          onConfirm={handleDeleteConfirm}
        >
          This can&apos;t be undone and it will be removed from your profile,
          the timeline of any accounts that follow you.
        </ConfirmationModal>
        <ReportModal
          open={isReportOpen}
          setOpen={setIsReportOpen}
          user={thread.user}
          threadId={thread.id}
        />
        <ConfirmationModal
          open={isModerateUnpinOpen}
          setOpen={setIsModerateUnpinOpen}
          title="Unpin post from token feed?"
          confirmButtonLabel="Unpin"
          onConfirm={handleModerateUnpinConfirm}
        >
          This post will no longer appear automatically at the top of token
          feed.
        </ConfirmationModal>
      </div>
    );
  },
);

PostMenuActionsUI.displayName = "PostMenuActionsUI";

export const GeneralPostMenuActionsUI = ({
  thread,
  comment,
  handleDelete,
  handleModerateCommunityPin,
  triggeredFromMainPostUI,
}: {
  thread: Thread;
  comment: boolean;
  handleDelete?: ({ threadId }: { threadId: string }) => void;
  handleModerateCommunityPin?: ({
    communityId,
    threadId,
    pinnedInCommunity,
  }: {
    communityId: string;
    threadId: string;
    pinnedInCommunity: boolean;
  }) => void;
  triggeredFromMainPostUI?: boolean;
}) => {
  const { user } = useUser();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [isReportOpen, setIsReportOpen] = useState(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const sanitizedContent = DOMPurify.sanitize(thread.content || "");
  const htmlRemovedContent = stripHtmlTags(sanitizedContent);
  const stageId = useStageStore((s) => s.id);
  const livestreamId = useLivestreamStore((s) => s.id);
  const myRole = useStageStore((s) => s.myRole);
  const myRoleLivestream = useLivestreamStore((s) => s.myRole);
  const isInRoomAndThreadIsSharable =
    stageId ||
    (livestreamId &&
      (!(thread.stage ? thread.stage.id : null) ||
        !(thread.livestream ? thread.livestream.id : null)));
  const canPinPostToStage =
    isInRoomAndThreadIsSharable &&
    (myRole === ROLES.HOST || myRole === ROLES.COHOST);
  const canModeratePin =
    Boolean(thread.communityId && thread.community) &&
    user &&
    (user.id === thread.community?.ownerId ||
      thread.community?.memberLink?.role === "moderator");
  const canPerformFeedModeration = canModeratePin && user.id !== thread.userId;
  const canPinPostToLivestream =
    isInRoomAndThreadIsSharable &&
    (myRoleLivestream === "HOST" || myRoleLivestream === "MODERATOR");

  const { mutate: pinPost } = usePinPostToStageMutation({
    onSuccess: () => {
      toast.green("Post shared to stage");
    },
  });

  const { mutate: pinPostLivestream } = usePinPostLivestreamMutation({
    onSuccess: () => {
      toast.green("Post shared to livestream");
    },
  });

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isFeedModerationModalOpen, setIsFeedModerationModalOpen] =
    useState(false);
  const [isModerateUnpinOpen, setIsModerateUnpinOpen] = useState(false);

  const handlePinToStage = useCallback(() => {
    if (!stageId) return;

    pinPost({
      postId: thread.id,
      stageId: stageId,
    });
    setOpen(false);
  }, [pinPost, thread.id, stageId]);

  const handleModerateUnpinConfirm = useCallback(() => {
    handleModerateCommunityPin &&
      handleModerateCommunityPin({
        communityId: thread.communityId || "",
        threadId: thread.id,
        pinnedInCommunity: false,
      });
    setIsModerateUnpinOpen(false);
  }, [handleModerateCommunityPin, thread.id]);

  const handlePinToLivestream = useCallback(() => {
    if (!livestreamId) return;

    pinPostLivestream({
      postId: thread.id,
      livestreamId: livestreamId,
    });
    setOpen(false);
  }, [pinPostLivestream, thread.id, livestreamId]);

  const [_, setStageEditor] = useStageEditor();
  const [_l, setLivestreamEditor] = useLivestreamEditor();
  const { data: stageData } = useQuery({
    ...stageQueries.stageInfoSimple(thread?.stage?.id ?? ""),
    enabled: !!thread?.stage?.id,
  });
  const { data: livestreamData } = useQuery({
    ...livestreamQueries.livestreamSimpleInfo(thread?.livestream?.id ?? ""),
    enabled: !!thread?.livestream?.id,
  });

  const isStage = thread.threadType === "stage" && thread.stage;
  const isLivestream = thread.threadType === "livestream" && thread.livestream;

  const isMyInActiveScheduledStage =
    isStage &&
    stageData?.stage.scheduledStartTime &&
    !stageData?.stage.isActive &&
    new Date(stageData?.stage.scheduledStartTime) > new Date() &&
    stageData?.stage.hostId === user?.id;

  const isMyInActiveScheduledLivestream =
    isLivestream &&
    livestreamData?.livestream?.scheduledStartTime &&
    !livestreamData?.livestream?.isActive &&
    new Date(livestreamData?.livestream?.scheduledStartTime) > new Date() &&
    livestreamData?.livestream?.hostId === user?.id;

  const handleEditStage = useCallback(() => {
    setOpen(false);
    // router.push(`/compose/stage?stageId=${thread.stage?.id}`);
    setStageEditor({ composeStage: true, stageId: thread.stage?.id });
  }, [router, thread.stage?.id]);

  const handleEditLivestream = useCallback(() => {
    setOpen(false);
    setLivestreamEditor({
      composeLivestream: true,
      livestreamId: thread.livestream?.id,
    });
  }, [router, thread.livestream?.id]);

  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      {isTablet && (
        <DropdownMenu open={open} onOpenChange={setOpen}>
          <DropdownMenuTrigger asChild>
            <button className="outline-none">
              <EllipsisHorizontalFilledIcon className="h-5 w-5 fill-off-white" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[220px]">
            <DropdownMenuItem className="w-full gap-4" asChild>
              <Link
                href={`/${thread.user?.twitterHandle}/status/${thread.id}/postreactions/likes`}
              >
                <ChartSquareBarOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Post Reactions</span>
              </Link>
            </DropdownMenuItem>
            {canModeratePin ? (
              thread.pinnedInCommunity ? (
                <DropdownMenuItem
                  className="gap-4"
                  onSelect={() => {
                    setIsModerateUnpinOpen(true);
                  }}
                >
                  <UnpinOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Unpin from token feed</span>
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem
                  className="gap-4"
                  onSelect={() => {
                    handleModerateCommunityPin &&
                      handleModerateCommunityPin({
                        communityId: thread.communityId || "",
                        threadId: thread.id,
                        pinnedInCommunity: true,
                      });
                  }}
                >
                  <PinOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Pin to token feed</span>
                </DropdownMenuItem>
              )
            ) : null}
            {!(thread.threadType == "repost") &&
            (user?.id === thread.userId || user?.isMod) ? (
              <DropdownMenuItem
                className="gap-4"
                onSelect={() => {
                  setIsDeleteOpen(true);
                }}
              >
                <TrashOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Delete</span>
              </DropdownMenuItem>
            ) : null}
            <DropdownMenuItem className="w-full gap-4" asChild>
              <CopyToClipboard
                text={
                  typeof window !== "undefined" && comment
                    ? `${window.location.origin}/${thread.user?.twitterHandle}/nested/${thread.id}`
                    : typeof window !== "undefined" && !comment
                      ? `${window.location.origin}/${thread.user?.twitterHandle}/status/${thread.id}`
                      : ""
                }
                onCopy={() => {
                  toast.green("Copied the link to the clipboard");
                  setOpen(false);
                }}
              >
                <button>
                  <LinkOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Copy link</span>
                </button>
              </CopyToClipboard>
            </DropdownMenuItem>
            {isMyInActiveScheduledStage ? (
              <DropdownMenuItem className="w-full gap-4" asChild>
                <button onClick={handleEditStage}>
                  <PencilAltOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Edit Stage</span>
                </button>
              </DropdownMenuItem>
            ) : isMyInActiveScheduledLivestream ? (
              <DropdownMenuItem className="w-full gap-4" asChild>
                <button onClick={handleEditLivestream}>
                  <PencilAltOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Edit Livestream</span>
                </button>
              </DropdownMenuItem>
            ) : htmlRemovedContent.length > 0 ? (
              <DropdownMenuItem className="w-full gap-4" asChild>
                <CopyToClipboard
                  text={htmlRemovedContent}
                  onCopy={() => {
                    toast.green("Copied the text to the clipboard");
                    setOpen(false);
                  }}
                >
                  <button>
                    <CopyOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Copy Text</span>
                  </button>
                </CopyToClipboard>
              </DropdownMenuItem>
            ) : null}
            {canPinPostToStage ? (
              <DropdownMenuItem
                className="w-full gap-4"
                onClick={handlePinToStage}
              >
                <PresentationChatBarOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Share to your Stage</span>
              </DropdownMenuItem>
            ) : null}
            {user?.id !== thread.user?.id && (
              <DropdownMenuItem
                className="w-full gap-4"
                onSelect={() => {
                  setOpen(false);
                  setIsReportOpen(true);
                }}
              >
                <FlagOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Report Post</span>
              </DropdownMenuItem>
            )}
            {canPerformFeedModeration ? (
              <DropdownMenuItem
                className="gap-4"
                onSelect={() => setIsFeedModerationModalOpen((prev) => !prev)}
              >
                <ChatAltDoubleOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Feed Moderation</span>
              </DropdownMenuItem>
            ) : null}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      {!isTablet && (
        <Drawer open={open} onOpenChange={setOpen}>
          <DrawerTrigger asChild>
            <button className="outline-none">
              <EllipsisHorizontalFilledIcon className="h-5 w-5 fill-off-white" />
            </button>
          </DrawerTrigger>
          <DrawerContent className="px-4 pt-4">
            <Link
              href={`/${thread.user?.twitterHandle}/status/${thread.id}/postreactions/likes`}
              className="flex items-center gap-2 p-2 text-base leading-5"
            >
              <ChartSquareBarOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
              <span>Post Reactions</span>
            </Link>
            {canModeratePin ? (
              thread.pinnedInCommunity ? (
                <button
                  className="flex items-center gap-2 p-2 text-base leading-5"
                  onClick={() => {
                    setIsModerateUnpinOpen(true);
                    setOpen(false);
                  }}
                >
                  <UnpinOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Unpin from token feed</span>
                </button>
              ) : (
                <button
                  className="flex items-center gap-2 p-2 text-base leading-5"
                  onClick={() => {
                    handleModerateCommunityPin &&
                      handleModerateCommunityPin({
                        communityId: thread.communityId || "",
                        threadId: thread.id,
                        pinnedInCommunity: true,
                      });
                    setOpen(false);
                  }}
                >
                  <PinOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Pin to token feed</span>
                </button>
              )
            ) : null}
            {!(thread.threadType == "repost") &&
            (user?.id === thread.userId || user?.isMod) ? (
              <button
                className="flex items-center gap-2 p-2 text-base leading-5"
                onClick={() => {
                  setIsDeleteOpen(true);
                  setOpen(false);
                }}
              >
                <TrashOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Delete</span>
              </button>
            ) : null}
            <CopyToClipboard
              text={
                typeof window !== "undefined" && comment
                  ? `${window.location.origin}/${thread.user?.twitterHandle}/nested/${thread.id}`
                  : typeof window !== "undefined" && !comment
                    ? `${window.location.origin}/${thread.user?.twitterHandle}/status/${thread.id}`
                    : ""
              }
              onCopy={() => {
                toast.green("Copied the link to the clipboard");
                setOpen(false);
              }}
            >
              <button className="flex items-center gap-2 p-2 text-base leading-5">
                <LinkOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Copy link</span>
              </button>
            </CopyToClipboard>
            {isMyInActiveScheduledStage ? (
              <button
                className="flex items-center gap-2 p-2 text-base leading-5"
                onClick={handleEditStage}
              >
                <PencilAltOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Edit Stage</span>
              </button>
            ) : isMyInActiveScheduledLivestream ? (
              <button
                className="flex items-center gap-2 p-2 text-base leading-5"
                onClick={handleEditStage}
              >
                <PencilAltOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Edit Stage</span>
              </button>
            ) : htmlRemovedContent.length > 0 ? (
              <CopyToClipboard
                text={htmlRemovedContent}
                onCopy={() => {
                  toast.green("Copied the text to the clipboard");
                  setOpen(false);
                }}
              >
                <button className="flex items-center gap-2 p-2 text-base leading-5">
                  <CopyOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Copy Text</span>
                </button>
              </CopyToClipboard>
            ) : null}
            {canPinPostToStage ? (
              <button
                className="flex items-center gap-2 p-2 text-base leading-5"
                onClick={handlePinToStage}
              >
                <PresentationChatBarOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Share to your Stage</span>
              </button>
            ) : null}
            {user?.id !== thread.user?.id && (
              <button
                className="flex items-center gap-2 p-2 text-base leading-5"
                onClick={() => {
                  setOpen(false);
                  setIsReportOpen(true);
                }}
              >
                <FlagOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Report Post</span>
              </button>
            )}
            {canPerformFeedModeration ? (
              <button
                className="flex items-center gap-2 p-2 text-base leading-5"
                onClick={() => {
                  setIsFeedModerationModalOpen((prev) => !prev);
                  setOpen(false);
                }}
              >
                <ChatAltDoubleOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Feed Moderation</span>
              </button>
            ) : null}
          </DrawerContent>
        </Drawer>
      )}
      <ConfirmationModal
        open={isDeleteOpen}
        setOpen={setIsDeleteOpen}
        title="Delete post?"
        confirmButtonLabel="Delete"
        destructive
        onConfirm={() => {
          handleDelete && handleDelete({ threadId: thread.id });
          setIsDeleteOpen(false);
          if (triggeredFromMainPostUI) router.back();
        }}
      >
        This can&apos;t be undone and it will be removed from your profile, the
        timeline of any accounts that follow you.
      </ConfirmationModal>
      <ReportModal
        open={isReportOpen}
        setOpen={setIsReportOpen}
        user={thread.user}
        threadId={thread.id}
      />
      <ConfirmationModal
        open={isModerateUnpinOpen}
        setOpen={setIsModerateUnpinOpen}
        title="Unpin post from token feed?"
        confirmButtonLabel="Unpin"
        onConfirm={handleModerateUnpinConfirm}
      >
        This post will no longer appear automatically at the top of token feed.
      </ConfirmationModal>
      {isFeedModerationModalOpen && (
        <CommunityFeedModerationModal
          thread={thread}
          open={isFeedModerationModalOpen}
          setOpen={setIsFeedModerationModalOpen}
          triggeredFromMainPostUI={triggeredFromMainPostUI}
        />
      )}
    </div>
  );
};

export const numberFormatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 2,
});

const DeletedPost = () => {
  return (
    <div
      className="flex w-full items-center justify-center rounded-lg border border-dark-gray bg-dark-bk px-3 py-4 text-sm"
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      The original post was deleted
    </div>
  );
};

const stripHtmlTags = (message: any) => {
  const _message = message.replace(/<br>/g, "\n");
  const parser = new DOMParser();
  const html = parser.parseFromString(_message, "text/html");
  const text = html.body.textContent || _message;

  return text;
};
