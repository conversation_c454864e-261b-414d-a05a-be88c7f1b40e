"use client";

import { create<PERSON>ontext, use<PERSON><PERSON>back, useContext, useMemo } from "react";

import {
  ChatMessage,
  ReceivedChatMessage,
  useChat,
  useDataChannel,
  useLocalParticipant,
  useTrackToggle,
} from "@livekit/components-react";
import { useQueryClient } from "@tanstack/react-query";
import { DataPublishOptions, Track } from "livekit-client";

import { toast } from "@/components/toast";
import { stageQueries } from "@/queries/stage-queries";
import { useStageStore } from "@/stores/stage";

import { Role } from "../constants";
import { toastEmote } from "../toast-emote";

interface SendEmotePayload {
  emote: string;
  id: number;
  from: {
    id: string;
    name: string;
    avatar: string;
    username: string;
  };
  to?: string;
}

interface SendTipPayload {
  currency: string;
  amount: number;
  id: string;
  from: {
    name: string;
    avatar: string;
    username: string;
  };
  to: string;
}

interface MuteMicPayload {
  identity: string;
}

interface DataChannelsContextType {
  sendEmote: (payload: SendEmotePayload, options?: DataPublishOptions) => void;
  sendTip: (payload: SendTipPayload, options?: DataPublishOptions) => void;
  sendInvalidateStageInfo: () => void;
  sendMuteMic: (payload: MuteMicPayload, options?: DataPublishOptions) => void;
  chat: {
    send: (message: string) => Promise<ReceivedChatMessage>;
    update: (
      message: string,
      originalMessageOrId: string | ChatMessage,
    ) => Promise<{
      readonly message: string;
      readonly editTimestamp: number;
      readonly id: string;
      readonly timestamp: number;
    }>;
    chatMessages: ReceivedChatMessage[];
    isSending: boolean;
  };
}

const DataChannelsContext = createContext<DataChannelsContextType | null>(null);

export const DataChannelsProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const local = useLocalParticipant();
  const queryClient = useQueryClient();
  const encoder = new TextEncoder();
  const decoder = new TextDecoder();
  const id = useStageStore((state) => state.id!);
  const actions = useStageStore((state) => state.actions);
  const role = useMemo(() => {
    return local.localParticipant.attributes.role as Role;
  }, [local.localParticipant.attributes.role]);
  const { toggle } = useTrackToggle({
    source: Track.Source.Microphone,
  });

  const { send: sendEmoteRaw } = useDataChannel("emotes", (msg) => {
    const message = JSON.parse(decoder.decode(msg.payload));
    if (message.to) {
      toastEmote({ emote: message.emote, name: message.from.name });
    } else {
      actions.addEmote(msg.from?.identity ?? "", message.emote);
    }
  });
  const { send: sendTipRaw } = useDataChannel("tips", (msg) => {
    const message = JSON.parse(decoder.decode(msg.payload));

    actions.addTip(message.to, message);
  });
  useDataChannel("invitation", () => {
    queryClient.invalidateQueries({
      queryKey: stageQueries.isUserInvitedKey({
        stageId: id,
        userId: local.localParticipant.identity,
      }),
    });
  });
  useDataChannel("data-event", (msg) => {
    const message = decoder.decode(msg.payload);
    switch (message) {
      case "stage_ended":
        toast.green("Stage ended");
        break;
      case "user_blocked":
        toast.danger("You were blocked from joining this Stage.");
        break;
      default:
        break;
    }
  });

  const { send: sendMuteMicRaw } = useDataChannel("mute-mic", (msg) => {
    const message = JSON.parse(decoder.decode(msg.payload));
    if (message.identity === local.localParticipant.identity) {
      toggle(false);
    }
  });

  const { send: sendInvalidateStageInfoRaw } = useDataChannel(
    "invalidate-stage-info",
    (msg) => {
      const message = JSON.parse(decoder.decode(msg.payload));
      if (message === true && (role === "HOST" || role === "COHOST")) {
        queryClient.invalidateQueries({
          queryKey: stageQueries.stageInfoKey(id),
        });
        actions.setHasUnseenRequests(true);
        return;
      }
    },
  );
  const chat = useChat();

  const sendEmote = useCallback(
    (payload: SendEmotePayload, options?: DataPublishOptions) => {
      const data = encoder.encode(JSON.stringify(payload));
      sendEmoteRaw(
        data,
        options ?? {
          reliable: true,
          destinationIdentities: payload.to ? [payload.to] : undefined,
        },
      );
      if (!payload.to) {
        actions.addEmote(payload.from.id, payload.emote);
      }
    },
    [sendEmoteRaw, encoder, actions],
  );

  const sendTip = useCallback(
    (payload: SendTipPayload, options?: DataPublishOptions) => {
      const data = encoder.encode(JSON.stringify(payload));
      sendTipRaw(
        data,
        options ?? {
          reliable: true,
        },
      );
      actions.addTip(payload.to, payload);
    },
    [sendTipRaw, encoder, actions],
  );

  const sendMuteMic = useCallback(
    (payload: MuteMicPayload, options?: DataPublishOptions) => {
      if (role !== "HOST" && role !== "COHOST") return;
      const data = encoder.encode(JSON.stringify(payload));
      sendMuteMicRaw(
        data,
        options ?? {
          reliable: true,
          destinationIdentities: [payload.identity],
        },
      );
    },
    [sendMuteMicRaw, role, encoder],
  );

  const sendInvalidateStageInfo = useCallback(() => {
    const data = encoder.encode(JSON.stringify(true));
    sendInvalidateStageInfoRaw(data, {
      reliable: true,
    });
    queryClient.invalidateQueries({
      queryKey: stageQueries.stageInfoKey(id),
    });
  }, [sendInvalidateStageInfoRaw, encoder, queryClient, id]);

  const value = useMemo(
    () => ({
      sendEmote,
      sendTip,
      sendInvalidateStageInfo,
      chat,
      sendMuteMic,
    }),
    [sendEmote, sendTip, sendInvalidateStageInfo, chat, sendMuteMic],
  );

  return (
    <DataChannelsContext.Provider value={value}>
      {children}
    </DataChannelsContext.Provider>
  );
};

export function useDataChannelsContext() {
  const context = useContext(DataChannelsContext);
  if (!context) {
    throw new Error("Missing DataChannelsContext");
  }
  return context;
}
