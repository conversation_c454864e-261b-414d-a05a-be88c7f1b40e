import { ArenaLogo } from "@/components/icons";
import { Button } from "@/components/ui/button";

interface ChampionSocialBenefitsParams {
  onClick: () => void;
}

export const ChampionSocialBenefits = ({
  onClick,
}: ChampionSocialBenefitsParams) => (
  <div className="mt-10 max-h-[66px]">
    <div className="relative isolate flex flex-row items-center justify-between overflow-hidden rounded-[10px] bg-gradient-to-r from-[#552b80] via-[#111010] to-[#111010] p-4 sm:px-7">
      <div className="text-sm font-semibold text-off-white">
        <div>Champion Social Benefits</div>
        <div className="font-normal">Coming Soon.</div>
      </div>
      <div className="h-full">
        <Button
          variant="outline"
          className="rounded-full border border-off-white px-4 py-2.5 leading-3"
          onClick={onClick}
        >
          Learn More
        </Button>
        <ArenaLogo className="absolute -right-10 -top-20 -z-10 w-[270px] opacity-15" />
        <div className="absolute inset-0 -z-10 rounded-[10px] border border-off-white opacity-10" />
      </div>
    </div>
  </div>
);
