interface User {
  threadCount: number;
  followerCount: number;
  followingsCount: number;
  twitterFollowers: number;
  id: string;
  createdOn: string;
  twitterId: string;
  twitterHandle: string;
  twitterName: string;
  twitterPicture: string;
  bannerUrl: null | string;
  address: string;
  ethereumAddress: null | string;
  prevAddress: null | string;
  addressConfirmed: boolean;
  twitterDescription: string;
  signedUp: boolean;
  subscriptionCurrency: string;
  subscriptionCurrencyAddress: null | string;
  subscriptionPrice: string;
  keyPrice: string;
  lastKeyPrice: string;
  subscriptionsEnabled: boolean;
  userConfirmed: boolean;
  twitterConfirmed: boolean;
  flag: number;
  ixHandle: string;
  handle: string | null;
}

export interface Trade {
  id: string;
  createdOn: string;
  blockNum: string;
  index: number;
  transactionId: string;
  trader: string;
  subject: string;
  isBuy: boolean;
  shareAmount: string;
  amount: string;
  protocolAmount: string;
  subjectAmount: string;
  referralAmount: string;
  supply: string;
  buyPrice: string;
  subjectId: string;
  traderId: string;
  subjectUser: User;
  traderUser: User;
}

export interface TradeRecentResponse {
  trades: Trade[];
}
