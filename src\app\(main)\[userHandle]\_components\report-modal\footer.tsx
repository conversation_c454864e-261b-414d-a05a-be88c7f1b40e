import { Button } from "@/components/ui/button";

export const Footer = ({
  page,
  reason,
  description,
  descriptionLength,
  maxDescriptionLength,
  isReportUserPending,
  isReportThreadPending,
  setPage,
  handleReport,
}: {
  page: string;
  reason: string | null;
  description: string;
  descriptionLength: number;
  maxDescriptionLength: number;
  isReportUserPending: boolean;
  isReportThreadPending: boolean;
  setPage: (page: "reason" | "details" | "submit") => void;
  handleReport: (blockUser: boolean) => void;
}) => (
  <div className="sticky bottom-0 mt-auto bg-dark-bk p-6 sm:bg-[rgba(15,15,15)]">
    {page === "reason" ? (
      <Button
        variant="outline"
        className="w-full"
        disabled={!reason}
        onClick={() => setPage("details")}
      >
        Next
      </Button>
    ) : page === "details" ? (
      <Button
        variant="outline"
        className="w-full"
        disabled={
          !reason ||
          (reason === "Other" && !description) ||
          descriptionLength > maxDescriptionLength
        }
        onClick={() => setPage("submit")}
      >
        Next
      </Button>
    ) : (
      <Button
        variant="default"
        className="w-full"
        disabled={
          !reason ||
          (reason === "Other" && !description) ||
          isReportUserPending ||
          isReportThreadPending
        }
        onClick={() => handleReport(false)}
      >
        Done
      </Button>
    )}
  </div>
);
