import React, { useState } from "react";

import { LiveKitRoom } from "@livekit/components-react";
import { Toaster } from "sonner";

import { Button } from "@/components/ui/button";

import { CustomTipModal } from "./stage-custom-tip-modal";

const mockUser = {
  id: "1",
  twitterName: "Alice",
  twitterHandle: "alice",
  twitterPicture: "https://randomuser.me/api/portraits/women/1.jpg",
  address: "0x1",
};

const mockReceivedChatMessage = {
  id: "mock-id",
  timestamp: Date.now(),
  message: "mock message",
};

const mockDataChannelsContext = {
  chat: {
    send: async () => mockReceivedChatMessage,
    update: async () => ({
      message: "mock message",
      editTimestamp: Date.now(),
      id: "mock-id",
      timestamp: Date.now(),
    }),
    chatMessages: [mockReceivedChatMessage],
    isSending: false,
  },
  sendTip: () => {},
  sendEmote: () => {},
  sendInvalidateStageInfo: () => {},
  sendMuteMic: () => {},
};

export default {
  title: "Modals/StageCustomTipModal",
  component: CustomTipModal,
  decorators: [
    (Story: any) => (
      <LiveKitRoom
        token="dummy-token"
        serverUrl="wss://dummy-url"
        connect={false}
      >
        <Story />
        <Toaster position="top-center" />
      </LiveKitRoom>
    ),
  ],
};

export const Default = () => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Button onClick={() => setOpen(true)}>Open Stage Custom Tip Modal</Button>
      <CustomTipModal
        open={open}
        setOpen={setOpen}
        userToSend={mockUser as any}
        dataChannelsContext={mockDataChannelsContext}
      />
    </>
  );
};
