import { FC } from "react";

interface EntryRequirementProps {
  ticker: string;
}

export const EntryRequirement: FC<EntryRequirementProps> = ({ ticker }) => {
  return (
    <div className="ml-1 flex gap-1 pb-8 text-xs">
      <span className="font-semibold text-[#f3f3f3]">1M</span>
      <span className="font-semibold text-[#eb540a]">
        ${ticker.toUpperCase()}
      </span>
      <span className="text-[#808080]">
        are required to be in the token chatroom
      </span>
    </div>
  );
};
