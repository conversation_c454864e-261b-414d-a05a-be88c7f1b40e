"use client";

import { FC, useEffect, useMemo, useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { notFound, useRouter } from "next/navigation";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import * as Progress from "@radix-ui/react-progress";
import { useQueryClient } from "@tanstack/react-query";
import { motion, useMotionValue } from "framer-motion";
import DOMPurify from "isomorphic-dompurify";
import CopyToClipboard from "react-copy-to-clipboard";
import { Address, formatEther } from "viem";

import { PageHeader } from "@/app/_components/page-header";
import { CommunityThreads } from "@/app/(main)/community/_components/community-threads";
import {
  ArrowBackOutlineIcon,
  ChatbubbleOutlineIcon,
  CopyOutlineIcon,
  TriangleDownOutlineIcon,
  TriangleUpOutlineIcon,
} from "@/components/icons";
import { GroupIcon, OfficialGroupIcon } from "@/components/icons-v2/group-logo";
import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ERC20_CONTRACT_ABI } from "@/environments/BACKEND_FRIENDS_CONTRACT";
import {
  COMMUNITIES_CONTRACT,
  COMMUNITY_ABI,
} from "@/environments/COMMUNITY_CONTRACT";
import { useCanPost } from "@/hooks/use-can-post";
import { useTokenPriceChange } from "@/hooks/use-token-price-change";
import {
  useCommunityByStrQuery,
  useFollowCommunityMutation,
  useUnfollowCommunityMutation,
} from "@/queries";
import { useAvaxPriceQuery } from "@/queries/currency-queries";
import { useCommunityNameTakenCheckQuery } from "@/queries/groups-queries";
import { TradesGroupsTrendingResponse } from "@/queries/types/new-users-response";
import { useUser } from "@/stores";
import { CommunityExtended } from "@/types/community";
import { abbreviateNumber, checkContent, cn } from "@/utils";
import { formatMarketCap } from "@/utils/format-market-cap";
import { formatAddress, formatPrice } from "@/utils/format-token-price";

import {
  NameChangeAlertModal,
  ToPhaseHandleModal,
  ToPhaseTippingModal,
  ToPhaseTokenModal,
} from "./community-phase-modals";
import { CommunityProfileLoadingSkeleton } from "./community-profile-loading-skeleton";
import { CommunityProfileMenu } from "./community-profile-menu";
import { CommunityQuickBuyModal } from "./community-quickBuy-modal";
import { CommunityStats } from "./community-stats";
import { TokenHoldersTab } from "./community-token-holders-tab";
import {
  HANDLE_PHASE,
  HANDLE_PHASE_MARKET_CAP_THRESHOLD,
  HANDLE_PHASE_NAME,
  START_PHASE,
  START_PHASE_NAME,
  TIPPING_PHASE,
  TIPPING_PHASE_MARKET_CAP_THRESHOLD,
  TOKEN_PHASE,
  TOKEN_PHASE_LIQUIDITY_THRESHOLD,
  TOKEN_PHASE_NAME,
} from "./consts";

interface CommunityProfileProps {
  param: string;
}

export const CommunityProfile: FC<CommunityProfileProps> = ({ param }) => {
  const queryClient = useQueryClient();
  const [groupBalance, setGroupBalance] = useState(0);
  const [userTokenBalance, setUserTokenBalance] = useState(0);
  const [noDuration, setNoDuration] = useState(false);
  const [open, setOpen] = useState(false);
  const [feeWalletAddress, setFeeWalletAddress] = useState<Address | null>(
    null,
  );
  const [isBuyModalOpen, setIsBuyModalOpen] = useState(false);

  const tabRef = useRef<HTMLDivElement>(null);
  const backgroundColor = useMotionValue("rgb(20 20 20 / 0)");
  const [isSticked, setIsSticked] = useState(false);

  const router = useRouter();
  const { user } = useUser();

  if (!user) {
    throw new Error("User not specified");
  }

  const { data, isLoading: isCommunityDataLoading } =
    useCommunityByStrQuery(param);

  const { setHasEnoughTokens, canPost, isUserBannedFromCommunity } =
    useCanPost(data);

  const { data: avaxPrice } = useAvaxPriceQuery();

  const { data: nameCheckData, isLoading: isNameCheckDataLoading } =
    useCommunityNameTakenCheckQuery(data?.community.name, data?.community.id);

  const marketCap =
    formatPrice(data?.community.stats?.marketCap ?? "0") *
    (avaxPrice?.avax || 0);

  const { primaryWallet } = useDynamicContext();

  const { mutateAsync: follow } = useFollowCommunityMutation({
    onMutate: async () => {
      toast.green(`Joined $${data?.community.ticker}!`);
      await queryClient.cancelQueries({
        queryKey: ["community", "param", param],
      });
      await queryClient.cancelQueries({
        queryKey: ["trade", "groups", "trending"],
      });

      const previousCommunity = queryClient.getQueryData([
        "community",
        "param",
        param,
      ]);
      const previousTrendingGroups = queryClient.getQueryData([
        "trade",
        "groups",
        "trending",
      ]);

      queryClient.setQueryData(
        ["community", "param", param],
        (
          old:
            | {
                community: CommunityExtended;
              }
            | undefined,
        ) => {
          if (!old) {
            return old;
          }

          return {
            ...old,
            community: {
              ...old.community,
              following: true,
            },
          };
        },
      );
      queryClient.setQueryData(
        ["trade", "groups", "trending"],
        (old: TradesGroupsTrendingResponse) => {
          if (!old) return old;

          return {
            ...old,
            communities: old.communities.map((c) => {
              if (c.id && c.id === data?.community.id) {
                return {
                  ...c,
                  following: true,
                };
              }
              return c;
            }),
          };
        },
      );

      return { previousCommunity, previousTrendingGroups };
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to join $${data?.community.ticker}.`);
      queryClient.setQueryData(
        ["community", "param", param],
        context.previousCommunity,
      );
      queryClient.setQueryData(
        ["trade", "groups", "trending"],
        context?.previousTrendingGroups,
      );
    },
  });

  const { mutateAsync: unfollow } = useUnfollowCommunityMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["community", "param", param],
      });
      await queryClient.cancelQueries({
        queryKey: ["trade", "groups", "trending"],
      });

      const previousCommunity = queryClient.getQueryData([
        "community",
        "param",
        param,
      ]);
      const previousTrendingGroups = queryClient.getQueryData([
        "trade",
        "groups",
        "trending",
      ]);

      queryClient.setQueryData(
        ["community", "param", param],
        (
          old:
            | {
                community: CommunityExtended;
              }
            | undefined,
        ) => {
          if (!old) {
            return old;
          }

          return {
            ...old,
            community: {
              ...old.community,
              following: false,
            },
          };
        },
      );
      queryClient.setQueryData(
        ["trade", "groups", "trending"],
        (old: TradesGroupsTrendingResponse) => {
          if (!old) return old;

          return {
            ...old,
            communities: old.communities.map((c) => {
              if (c.id && c.id === data?.community.id) {
                return {
                  ...c,
                  following: false,
                };
              }
              return c;
            }),
          };
        },
      );

      return { previousCommunity, previousTrendingGroups };
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to leave ${data?.community.ticker}.`);
      queryClient.setQueryData(
        ["community", "param", param],
        context.previousCommunity,
      );
      queryClient.setQueryData(
        ["trade", "groups", "trending"],
        context?.previousTrendingGroups,
      );
    },
  });

  const [isNegative, percentageIncrease] = useTokenPriceChange(
    data?.community?.stats,
  );

  const handleBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push("/home");
    }
  };

  const handleFollow = () => {
    if (!data?.community) return;

    if (data.community.following) {
      unfollow({ communityId: data.community.id });
    } else {
      follow({ communityId: data.community.id });
    }
  };

  const fetchProgress = async () => {
    try {
      if (!primaryWallet) throw new Error("Dynamic wallet is not initialized");
      if (!isEthereumWallet(primaryWallet))
        throw new Error("Not an Ethereum wallet");

      if (!data || !data?.community) return;

      const publicClient = await primaryWallet.getPublicClient();

      const info = await Promise.all([
        publicClient.readContract({
          address: COMMUNITIES_CONTRACT.addressMainnet as Address,
          abi: COMMUNITY_ABI,
          functionName: "tokenBalanceOf",
          args: [BigInt(data?.community.bcGroupId)],
          account: primaryWallet.address as Address,
        }),
        await publicClient.readContract({
          address: data?.community.contractAddress as Address,
          abi: ERC20_CONTRACT_ABI,
          functionName: "balanceOf",
          args: [primaryWallet.address as Address],
        }),
        await publicClient.readContract({
          address: COMMUNITIES_CONTRACT.addressMainnet as Address,
          abi: COMMUNITY_ABI,
          functionName: "STAKER_REWARD_TOKEN_VAULT",
        }),
      ]);
      setFeeWalletAddress(info[2]);
      setUserTokenBalance(Number(formatEther(info[1])));
      setGroupBalance(parseFloat(formatEther(info[0])));
    } catch {
      toast.red("Failed to fetch progress");
    }
  };

  useEffect(() => {
    if (primaryWallet && data) {
      void fetchProgress();
    }
  }, [primaryWallet, data]);

  useEffect(() => {
    if (data) {
      const paramIsName = data.community.name === param;
      if (!paramIsName && data.community.tokenPhase >= HANDLE_PHASE)
        window.history.replaceState(null, "", data.community.name);
    }
  }, [data]);

  const calculateProgress = (
    phase: number,
    totalSupply: number,
    marketCap: number,
    liquidityThreshold: number,
  ) => {
    let progressValue = 0;
    let title: JSX.Element | null = null;
    let phaseDialog: JSX.Element | null = null;

    if (phase === START_PHASE) {
      progressValue = Math.min(100, (totalSupply / liquidityThreshold) * 100);

      phaseDialog = (
        <>
          <span className="flex">phase</span>
          <span className="flex items-end gap-[1px]">
            <div>{START_PHASE_NAME}</div>
            <img
              src="/icons/phases.svg"
              className="mb-1 h-2 w-2 self-end"
              alt="Phases icon"
            />
          </span>
        </>
      );

      title = (
        <>
          <span className="text-xs font-semibold text-off-white sm:text-sm">
            {`Reach ${abbreviateNumber(liquidityThreshold)} AVAX`}
          </span>
          <span className="text-xs text-[#B4B4B4] sm:text-sm">{` to graduate the token`}</span>
        </>
      );
    } else if (phase === TOKEN_PHASE) {
      progressValue = Math.min(
        100,
        ((marketCap * (avaxPrice?.avax || 0)) /
          HANDLE_PHASE_MARKET_CAP_THRESHOLD) *
          100,
      );

      phaseDialog = (
        <>
          <span className="flex">phase</span>
          <span className="flex items-end gap-[1px]">
            <div>{TOKEN_PHASE_NAME}</div>
            <img
              src="/icons/phases.svg"
              className="mb-1 h-2 w-2 self-end"
              alt="Phases icon"
            />
          </span>
        </>
      );

      title = (
        <>
          <span className="text-xs font-semibold text-off-white sm:text-sm">
            {`Reach $${abbreviateNumber(HANDLE_PHASE_MARKET_CAP_THRESHOLD, 1, true, { padding: false })} MC`}
          </span>
          <span className="text-xs text-[#B4B4B4] sm:text-sm">{` to secure a profile handle`}</span>
        </>
      );
    } else if (phase === HANDLE_PHASE) {
      progressValue = Math.min(
        100,
        ((marketCap * (avaxPrice?.avax || 0)) /
          TIPPING_PHASE_MARKET_CAP_THRESHOLD) *
          100,
      );

      phaseDialog = (
        <>
          <span className="flex">phase</span>
          <span className="flex items-end gap-[1px]">
            <div>{HANDLE_PHASE_NAME}</div>
            <img
              src="/icons/phases.svg"
              className="mb-1 h-2 w-2 self-end"
              alt="Phases icon"
            />
          </span>
        </>
      );

      title = (
        <>
          <span className="text-xs font-semibold text-off-white sm:text-sm">
            {`Reach $${abbreviateNumber(TIPPING_PHASE_MARKET_CAP_THRESHOLD, 1, true, { padding: false })} MC`}
          </span>
          <span className="text-xs text-[#B4B4B4] sm:text-sm">
            {` to become a tipping token`}
          </span>
        </>
      );
    }

    return { progressValue, phaseDialog, title };
  };

  const { progressValue, phaseDialog, title } = useMemo(() => {
    return calculateProgress(
      data?.community.tokenPhase || 1,
      groupBalance,
      formatPrice(data?.community.stats?.marketCap ?? "0"),
      TOKEN_PHASE_LIQUIDITY_THRESHOLD,
    );
  }, [
    data?.community.tokenPhase,
    groupBalance,
    data?.community.stats?.marketCap,
    avaxPrice?.avax,
  ]);

  const handleClick = () => {
    setOpen(true);
  };

  const communityDescription = useMemo(() => {
    const [content] = checkContent({
      content: data?.community?.description ?? "",
      truncate: false,
    });

    return DOMPurify.sanitize(content);
  }, [data?.community?.description]);

  useEffect(() => {
    const handleScroll = () => {
      const topSafeArea = Number(
        getComputedStyle(document.documentElement)
          .getPropertyValue("--sat")
          .replace("px", ""),
      );

      if (
        tabRef.current &&
        tabRef.current.getBoundingClientRect().top <= 0 + topSafeArea
      ) {
        backgroundColor.set("rgb(20 20 20 / 0.88)");
        setIsSticked(true);
      } else {
        backgroundColor.set("rgb(20 20 20 / 0)");
        setIsSticked(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [backgroundColor]);

  const isOwner = data?.community.ownerId === user.id;

  if (isCommunityDataLoading || isNameCheckDataLoading)
    return <CommunityProfileLoadingSkeleton />;

  if (!data?.community) return notFound();

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTitle className="hidden" />
        <DialogContent
          className="flex w-[80%] max-w-[350px] flex-col gap-5 rounded-[20px]
          bg-[#0e0e0e] p-0 focus:outline-none
          sm:top-[50%] sm:rounded-[20px] sm:bg-[#0e0e0e] sm:backdrop-blur-none"
        >
          {data.community.tokenPhase === START_PHASE && <ToPhaseTokenModal />}
          {data.community.tokenPhase === TOKEN_PHASE && <ToPhaseHandleModal />}
          {data.community.tokenPhase === HANDLE_PHASE && (
            <ToPhaseTippingModal />
          )}
        </DialogContent>
      </Dialog>
      {!isNameCheckDataLoading &&
        nameCheckData?.isAvailable === false &&
        isOwner && (
          <NameChangeAlertModal
            contractAddress={data?.community.contractAddress}
          />
        )}
      <div className="pt-pwa relative z-20">
        <div
          className="aspect-[4/1] w-full bg-[#0e0e0e]"
          style={{
            backgroundImage: `url(${data?.community.bannerURL || "/images/default-banner.png"})`,
            backgroundSize: "cover",
          }}
        >
          <PageHeader
            isTransparent
            renderProp={() => (
              <CommunityProfileMenu
                community={data.community}
                isOwner={isOwner}
                path={
                  data?.community?.tokenPhase >= HANDLE_PHASE
                    ? data?.community?.name
                    : data?.community?.contractAddress
                }
              />
            )}
          />
        </div>
        <motion.div
          className="relative -mt-10 px-6"
          animate={{
            y: isSticked ? -81 : 0,
            visibility: isSticked ? "hidden" : "visible",
          }}
          transition={{
            ease: "linear",
            duration: noDuration ? 0 : 0.1,
            visibility: {
              delay: noDuration ? 0 : isSticked ? 0.1 : 0,
            },
          }}
        >
          <div className="mb-4 flex gap-5">
            <Image
              width={92}
              height={92}
              src={data?.community.photoURL}
              alt="Group Logo"
              className={cn(
                "rounded-[8px] border-[1px]",
                data?.community?.isOfficial
                  ? "border-[#6F15C8]"
                  : "border-[#EB540A]",
              )}
            />
            <div className="mt-auto flex-row">
              <div className="text-sm text-[#808080]">Followers</div>
              <span className="text-off-white">
                {abbreviateNumber(data?.community.followerCount)}
              </span>
            </div>
            <div className="mt-auto flex-row">
              <div className="text-sm text-[#808080]">{`Market Cap (MC)`}</div>
              <div className="flex items-center gap-2">
                <span className="text-off-white">
                  ${formatMarketCap(marketCap)}
                </span>
                <span
                  className={cn(
                    "flex items-center gap-[4px] text-xs",
                    isNegative ? "text-danger" : "text-[#40B877]",
                  )}
                >
                  {isNegative ? (
                    <TriangleDownOutlineIcon className="h-3 w-3" />
                  ) : (
                    <TriangleUpOutlineIcon className="h-3 w-3" />
                  )}
                  <span>{percentageIncrease}%</span>
                </span>
              </div>
            </div>
          </div>

          <div className="flex gap-1 pl-1">
            {data?.community.isOfficial ? <OfficialGroupIcon /> : <GroupIcon />}
            <span className="font-semibold text-[#f3f3f3]">{`${data?.community.tokenName} ($${data?.community.ticker})`}</span>
          </div>
          {data?.community.tokenPhase >= HANDLE_PHASE && (
            <div className="flex gap-1 pl-1">
              <span className="text-xs font-normal text-[#808080]">
                {`@${data?.community.name}`}
              </span>
            </div>
          )}
          {data.community.description && (
            <div
              className="post-content mt-2 pl-1 text-sm text-off-white"
              dangerouslySetInnerHTML={{
                __html: communityDescription,
              }}
            />
          )}
          {data.community.tokenPhase >= TIPPING_PHASE ||
          data.community.isOfficial ? (
            <div className="mb-2" />
          ) : (
            <div onClick={handleClick}>
              <button className="mb-8 mt-2 flex w-full flex-row items-start gap-3 rounded-[10px] bg-[#191919] p-2 text-sm text-[#656565]">
                <div className="flex flex-col p-1 text-xs font-semibold text-white">
                  {phaseDialog}
                </div>
                <div className="flex w-full flex-col items-start justify-between gap-2">
                  <div className="w-full rounded bg-[#2a2a2a] p-[2px]">
                    <Progress.Root
                      className="relative h-[10px] w-[100%] overflow-hidden rounded-sm bg-[linear-gradient(135deg,#2a2a2a_28.57%,#1a1a1a_28.57%,#1a1a1a_50%,#2a2a2a_50%,#2a2a2a_78.57%,#1a1a1a_78.57%,#1a1a1a_100%)] bg-[length:9.90px_9.90px]"
                      style={{ transform: "translateZ(0)" }}
                      value={progressValue}
                    >
                      <Progress.Indicator
                        className="duration-[660ms] ease-[cubic-bezier(0.65, 0, 0.35, 1)] h-full w-full rounded-sm bg-[linear-gradient(98deg,#6F15C8_0%,#AC58FF_96.86%)] transition-transform"
                        style={{
                          transform: `translateX(-${100 - progressValue}%)`,
                        }}
                      />
                    </Progress.Root>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>{title}</span>
                  </div>
                </div>
              </button>
            </div>
          )}

          <div className="mb-4 grid grid-cols-2 gap-[10px]">
            <ProgressBarLink
              href={
                data?.community?.tokenPhase >= HANDLE_PHASE
                  ? `/community/${data?.community?.name}/trade`
                  : `/community/${data?.community?.contractAddress}/trade`
              }
              className="flex-1"
            >
              <Button
                variant="default"
                className="w-full gap-1 px-[8px] py-2"
                disabled={false}
              >
                {userTokenBalance > 0
                  ? `Trade $${data?.community.ticker}`
                  : `Buy $${data?.community.ticker}`}
              </Button>
            </ProgressBarLink>

            <div className="flex gap-[10px]">
              <Button
                variant="outline"
                className={cn(
                  "flex-1 gap-1 px-[8px] py-2",
                  !data?.community?.following && "border-brand-orange",
                )}
                disabled={isUserBannedFromCommunity}
                onClick={handleFollow}
              >
                {data?.community?.following ? "Leave" : "Join"}
              </Button>
              {data?.community?.group?.memberLink ? (
                <Link href={`/messages/community/${data.community.id}`}>
                  <Button
                    disabled={isCommunityDataLoading}
                    variant="outline"
                    className="size-[38px] flex-shrink-0 p-0"
                  >
                    <ChatbubbleOutlineIcon className="size-[18px] text-gray-text" />
                  </Button>
                </Link>
              ) : (
                <Button
                  onClick={() => setIsBuyModalOpen(true)}
                  disabled={isCommunityDataLoading}
                  variant="outline"
                  className="size-[38px] flex-shrink-0 p-0"
                >
                  <ChatbubbleOutlineIcon className="size-[18px] text-gray-text" />
                </Button>
              )}
            </div>
          </div>

          <div className="mb-2 flex items-center justify-between rounded-lg border border-[#3a3a3a] p-3 text-sm text-[#656565]">
            <div className="flex items-center gap-1">
              <span className="text-sm font-semibold text-[#808080]">
                CONTRACT ADDRESS:
              </span>
              <span>{`${formatAddress(data?.community.contractAddress)}`}</span>
            </div>
            <CopyToClipboard
              text={data?.community ? data?.community.contractAddress : ""}
              onCopy={() => {
                toast.green("Address copied!");
              }}
            >
              <button className="flex-shrink-0">
                <CopyOutlineIcon className="size-5 text-off-white" />
              </button>
            </CopyToClipboard>
          </div>
        </motion.div>
      </div>

      <Tabs
        onValueChange={() => {
          setNoDuration(true);
          setTimeout(() => {
            setNoDuration(false);
          }, 300);
        }}
        ref={tabRef}
        defaultValue="threads"
        className="flex-grow"
      >
        <motion.div
          className="sticky top-0 z-10 mt-[calc(-40px-env(safe-area-inset-top))] w-full pt-[calc(54px+env(safe-area-inset-top))]  shadow-[0px_-5px_14px_20px_rgba(0,0,0,0.25)] backdrop-blur-[9px]"
          style={{ backgroundColor }}
          transition={{
            ease: "linear",
            duration: noDuration ? 0 : 0.1,
          }}
        >
          <motion.div
            className="absolute inset-x-0 top-[calc(14px+env(safe-area-inset-top))] overflow-hidden px-6 opacity-0"
            animate={{
              opacity: isSticked ? 1 : 0,
            }}
            transition={{
              ease: "easeInOut",
              duration: noDuration ? 0 : 0.1,
            }}
          >
            <div className="flex justify-between">
              <div className="flex items-center">
                <Button variant="ghost" onClick={handleBack}>
                  <ArrowBackOutlineIcon className="size-5 text-white" />
                </Button>
                <Image
                  width={34}
                  height={34}
                  src={data?.community.photoURL}
                  alt="Group Logo"
                  className="ml-3 rounded-[8px] border-[1px] border-[#EB540A]"
                />
              </div>
              <div className="flex items-center gap-3">
                <CommunityProfileMenu
                  community={data.community}
                  isOwner={isOwner}
                  path={
                    data?.community?.tokenPhase >= HANDLE_PHASE
                      ? data?.community?.name
                      : data?.community?.contractAddress
                  }
                />
              </div>
            </div>
          </motion.div>
          <TabsList className="mt-2 w-full">
            <TabsTrigger className="w-full" value="threads">
              Threads
            </TabsTrigger>
            {!data?.community?.isOfficial && (
              <TabsTrigger className="w-full" value="token-holders">
                Token Holders
              </TabsTrigger>
            )}
            <TabsTrigger className="w-full" value="stats">
              {data?.community?.isOfficial ? "Info" : "Stats"}
            </TabsTrigger>
          </TabsList>
        </motion.div>
        <TabsContent value="threads" className="h-full">
          <CommunityThreads communityId={data.community.id} />
        </TabsContent>
        <TabsContent value="token-holders" className="h-full py-2">
          <TokenHoldersTab
            communityId={data.community.id}
            feeWalletAddress={feeWalletAddress}
          />
        </TabsContent>
        <TabsContent value="stats">
          <CommunityStats communityId={data.community.id} />
        </TabsContent>
      </Tabs>
      {data?.community && isBuyModalOpen && (
        <CommunityQuickBuyModal
          open={isBuyModalOpen}
          setOpen={setIsBuyModalOpen}
          community={data.community}
          setHasEnoughTokens={setHasEnoughTokens}
        />
      )}
    </>
  );
};
