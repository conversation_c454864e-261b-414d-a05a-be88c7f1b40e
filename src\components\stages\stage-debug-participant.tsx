"use client";

import { useMemo, useState } from "react";

import {
  useLocalParticipant,
  useParticipantAttributes,
  useParticipants,
} from "@livekit/components-react";
import { RoomEvent } from "livekit-client";
import CopyToClipboard from "react-copy-to-clipboard";

import { ArrowBackOutlineIcon, CopyOutlineIcon } from "../icons";
import { toast } from "../toast";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "../ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";

export function StageDebugParticipant({
  children,
}: {
  children: React.ReactNode;
}) {
  const [open, setOpen] = useState(false);
  const participants = useParticipants({
    updateOnlyOn: [RoomEvent.ParticipantAttributesChanged],
  });
  const local = useLocalParticipant();

  const { attributes: myAttributes } = useParticipantAttributes({
    participant: local.localParticipant,
  });

  const attributes = useMemo(() => {
    return participants.map((participant) => ({
      id: participant.identity,
      name: participant.attributes?.name,
      avatar: participant.attributes?.avatar,
      username: participant.attributes?.username,
      role: participant.attributes?.role,
    }));
  }, [participants]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="flex h-full w-full flex-col gap-0 overflow-y-auto bg-dark-bk p-0 sm:max-h-[650px] sm:bg-[rgba(15,15,15)]">
        <div className="sticky top-0 flex gap-2 bg-dark-bk px-3 pt-[calc(1rem+env(safe-area-inset-top))] sm:bg-[rgba(15,15,15)]">
          <div className="flex w-full items-center justify-between">
            <DialogClose className="flex size-10 items-center justify-center">
              <ArrowBackOutlineIcon className="size-5" />
            </DialogClose>
          </div>
        </div>
        <div className="flex flex-col gap-8 px-6 pb-10">
          <Tabs defaultValue="me" className="flex-grow">
            <TabsList className="mt-3 w-full">
              <TabsTrigger value="me">Me</TabsTrigger>
              <TabsTrigger value="all">All</TabsTrigger>
            </TabsList>
            <TabsContent value="me" className="h-full py-6">
              <CopyToClipboard
                text={JSON.stringify(myAttributes, null, 2)}
                onCopy={() => {
                  toast.green("Copied to clipboard");
                }}
              >
                <button className="flex-shrink-0">
                  <CopyOutlineIcon className="size-5 text-[#9E9E9E]" />
                </button>
              </CopyToClipboard>
              <div className="flex flex-col gap-2 overflow-auto">
                {JSON.stringify(myAttributes, null, 2)}
              </div>
            </TabsContent>
            <TabsContent value="all" className="h-full py-6">
              <CopyToClipboard
                text={JSON.stringify(attributes, null, 2)}
                onCopy={() => {
                  toast.green("Copied to clipboard");
                }}
              >
                <button className="flex-shrink-0">
                  <CopyOutlineIcon className="size-5 text-[#9E9E9E]" />
                </button>
              </CopyToClipboard>
              <div className="flex flex-col gap-2 overflow-auto">
                {JSON.stringify(attributes, null, 2)}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
