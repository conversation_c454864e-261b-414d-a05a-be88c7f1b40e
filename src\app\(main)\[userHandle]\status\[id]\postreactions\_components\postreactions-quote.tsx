import { useParams } from "next/navigation";

import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { PostUI } from "@/components/post";
import {
  useBookmarkThreadMutation,
  useDeleteRepostMutation,
  useLikeThreadMutation,
  useRepostThreadMutation,
  useUnbookmarkThreadMutation,
  useUnlikeThreadMutation,
} from "@/queries";
import { PostReactionsQuotesResponse } from "@/queries/types/postreactions";
import { Thread } from "@/types";

interface PostReactionsQuoteProps {
  thread: Thread;
}

export const PostReactionsQuote = ({ thread }: PostReactionsQuoteProps) => {
  const params = useParams() as { id: string };
  const isRepost = thread.threadType === "repost";
  const activePost = isRepost && thread.repost ? thread.repost : thread;
  const queryClient = useQueryClient();

  const { mutateAsync: repost } = useRepostThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["postreactions", "quotes", params.id],
      });

      const previousQuotes:
        | InfiniteData<PostReactionsQuotesResponse, unknown>
        | undefined = queryClient.getQueryData([
        "postreactions",
        "quotes",
        params.id,
      ]);

      queryClient.setQueryData(
        ["postreactions", "quotes", params.id],
        (old: InfiniteData<PostReactionsQuotesResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                quotes: page.quotes.map((q) => {
                  if (q.id === threadId) {
                    return {
                      ...q,
                      repostCount: q.repostCount + 1,
                      reposted: true,
                    };
                  }

                  return q;
                }),
              };
            }),
          };
        },
      );

      return { previousQuotes };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["postreactions", "quotes", params.id],
        context?.previousQuotes,
      );
    },
  });
  const { mutateAsync: deleteRepost } = useDeleteRepostMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["postreactions", "quotes", params.id],
      });

      const previousQuotes:
        | InfiniteData<PostReactionsQuotesResponse, unknown>
        | undefined = queryClient.getQueryData([
        "postreactions",
        "quotes",
        params.id,
      ]);

      queryClient.setQueryData(
        ["postreactions", "quotes", params.id],
        (old: InfiniteData<PostReactionsQuotesResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                quotes: page.quotes.map((q) => {
                  if (q.id === threadId) {
                    return {
                      ...q,
                      repostCount: q.repostCount - 1,
                      reposted: false,
                    };
                  }

                  return q;
                }),
              };
            }),
          };
        },
      );

      return { previousQuotes };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["postreactions", "quotes", params.id],
        context?.previousQuotes,
      );
    },
  });
  const { mutateAsync: like } = useLikeThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["postreactions", "quotes", params.id],
      });

      const previousQuotes:
        | InfiniteData<PostReactionsQuotesResponse, unknown>
        | undefined = queryClient.getQueryData([
        "postreactions",
        "quotes",
        params.id,
      ]);

      queryClient.setQueryData(
        ["postreactions", "quotes", params.id],
        (old: InfiniteData<PostReactionsQuotesResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                quotes: page.quotes.map((q) => {
                  if (q.id === threadId) {
                    return {
                      ...q,
                      likeCount: q.likeCount + 1,
                      like: true,
                    };
                  }

                  return q;
                }),
              };
            }),
          };
        },
      );

      return { previousQuotes };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["postreactions", "quotes", params.id],
        context?.previousQuotes,
      );
    },
  });
  const { mutateAsync: unlike } = useUnlikeThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["postreactions", "quotes", params.id],
      });

      const previousQuotes:
        | InfiniteData<PostReactionsQuotesResponse, unknown>
        | undefined = queryClient.getQueryData([
        "postreactions",
        "quotes",
        params.id,
      ]);

      queryClient.setQueryData(
        ["postreactions", "quotes", params.id],
        (old: InfiniteData<PostReactionsQuotesResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                quotes: page.quotes.map((q) => {
                  if (q.id === threadId) {
                    return {
                      ...q,
                      likeCount: q.likeCount - 1,
                      like: false,
                    };
                  }

                  return q;
                }),
              };
            }),
          };
        },
      );

      return { previousQuotes };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["postreactions", "quotes", params.id],
        context?.previousQuotes,
      );
    },
  });
  const { mutateAsync: bookmark } = useBookmarkThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["postreactions", "quotes", params.id],
      });

      const previousQuotes:
        | InfiniteData<PostReactionsQuotesResponse, unknown>
        | undefined = queryClient.getQueryData([
        "postreactions",
        "quotes",
        params.id,
      ]);

      queryClient.setQueryData(
        ["postreactions", "quotes", params.id],
        (old: InfiniteData<PostReactionsQuotesResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                quotes: page.quotes.map((q) => {
                  if (q.id === threadId) {
                    return {
                      ...q,
                      bookmarkCount: q.bookmarkCount + 1,
                      bookmark: true,
                    };
                  }

                  return q;
                }),
              };
            }),
          };
        },
      );

      return { previousQuotes };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["postreactions", "quotes", params.id],
        context?.previousQuotes,
      );
    },
  });
  const { mutateAsync: unbookmark } = useUnbookmarkThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["postreactions", "quotes", params.id],
      });

      const previousQuotes:
        | InfiniteData<PostReactionsQuotesResponse, unknown>
        | undefined = queryClient.getQueryData([
        "postreactions",
        "quotes",
        params.id,
      ]);

      queryClient.setQueryData(
        ["postreactions", "quotes", params.id],
        (old: InfiniteData<PostReactionsQuotesResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                quotes: page.quotes.map((q) => {
                  if (q.id === threadId) {
                    return {
                      ...q,
                      bookmarkCount: q.bookmarkCount - 1,
                      bookmark: false,
                    };
                  }

                  return q;
                }),
              };
            }),
          };
        },
      );

      return { previousQuotes };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["postreactions", "quotes", params.id],
        context?.previousQuotes,
      );
    },
  });

  const handleLike = async ({ threadId }: { threadId: string }) => {
    if (activePost.like) {
      await unlike({ threadId });
    } else {
      await like({ threadId });
    }
  };

  const handleBookmark = async ({ threadId }: { threadId: string }) => {
    if (activePost.bookmark) {
      await unbookmark({ threadId });
    } else {
      await bookmark({ threadId });
    }
  };

  const handleRepost = async ({ threadId }: { threadId: string }) => {
    if (activePost.reposted) {
      await deleteRepost({ threadId });
    } else {
      await repost({ threadId });
    }
  };

  return (
    <PostUI
      thread={thread}
      handleLike={handleLike}
      handleBookmark={handleBookmark}
      handleRepost={handleRepost}
    />
  );
};
