"use client";

import { useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";

import {
  useLocalParticipant,
  useParticipantAttribute,
  useRoomInfo,
} from "@livekit/components-react";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useDeletePinPostFromStageMutation } from "@/queries";
import { useStageStore } from "@/stores/stage";
import { checkContent, cn, formatTimeDistance } from "@/utils";

import { ArrowBackOutlineIcon, XCircleOutlineIcon } from "../icons";
import { ProgressBarLink } from "../progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
  useCarousel,
} from "../ui/carousel";
import { Role } from "./constants";

export function StageSharedPosts() {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const isChatOpen = useStageStore((state) => state.chat.isOpen);

  const roomInfo = useRoomInfo();
  const metadata = roomInfo.metadata
    ? JSON.parse(roomInfo.metadata)
    : { raisedHands: [], pinnedPosts: [] };

  const pinnedPosts = useMemo(() => {
    const pinnedPosts = metadata.pinnedPosts ?? [];

    return pinnedPosts as {
      id: string;
      content: string;
      createdOn: string;
      user: {
        id: string;
        twitterHandle: string;
        twitterName: string;
        twitterPicture: string;
      };
    }[];
  }, [metadata.pinnedPosts]);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
    api.on("reInit", () => {
      setCount(api.scrollSnapList().length);
    });
  }, [api, pinnedPosts]);

  if (pinnedPosts.length === 0 || isChatOpen) return null;

  return (
    <div className="relative flex flex-col gap-2 py-2">
      <Carousel
        setApi={setApi}
        opts={{
          startIndex: pinnedPosts.length - 1,
        }}
      >
        <CarouselContent>
          {pinnedPosts.map((post) => (
            <CarouselItem key={post.id}>
              <StageSharedPost post={post} />
            </CarouselItem>
          ))}
        </CarouselContent>
        <PreviousButton />
        <NextButton />
      </Carousel>
      <div className="flex items-center justify-center gap-2">
        {Array.from({ length: count }).map((_, index) => (
          <div
            key={index + "-carousel-dot"}
            className={cn(
              "rounded-full",
              current === index + 1
                ? "size-2 bg-off-white"
                : "size-1 bg-gray-text",
            )}
          />
        ))}
      </div>
    </div>
  );
}

const PreviousButton = () => {
  const { scrollPrev, canScrollPrev } = useCarousel();

  if (!canScrollPrev) return null;

  return (
    <Button
      variant="outline"
      className="absolute left-2.5 top-1/2 z-30  hidden size-7 -translate-y-1/2 rounded-full bg-dark-bk p-1 hover:bg-chat-bubble lg:flex lg:items-center lg:justify-center"
      onClick={scrollPrev}
    >
      <ArrowBackOutlineIcon className="size-4 text-white" />
    </Button>
  );
};

const NextButton = () => {
  const { scrollNext, canScrollNext } = useCarousel();

  if (!canScrollNext) return null;

  return (
    <Button
      variant="outline"
      className="absolute right-2.5 top-1/2 z-30 hidden size-7 -translate-y-1/2 rotate-180 rounded-full bg-dark-bk p-1 hover:bg-chat-bubble lg:flex lg:items-center lg:justify-center"
      onClick={scrollNext}
    >
      <ArrowBackOutlineIcon className="size-4 text-white" />
    </Button>
  );
};

const StageSharedPost = ({
  post,
}: {
  post: {
    id: string;
    content: string;
    createdOn: string;
    user: {
      id: string;
      twitterHandle: string;
      twitterName: string;
      twitterPicture: string;
    };
  };
}) => {
  const router = useRouter();
  const local = useLocalParticipant();
  const role = useParticipantAttribute("role", {
    participant: local.localParticipant,
  }) as Role;
  const stageId = useStageStore((state) => state.id);
  const actions = useStageStore((state) => state.actions);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [sanitizedContent, isTruncated] = checkContent({
    content: post.content,
  });

  const { mutate: deletePinnedPost, isPending } =
    useDeletePinPostFromStageMutation();

  const closeFullScreen = () => {
    if (!isTablet) {
      actions.setFullScreen(false);
    }
  };

  return (
    <div className="h-full px-6">
      <div
        className="flex h-full cursor-pointer flex-col gap-2 rounded-lg border border-dark-gray p-3 hover:bg-dark-gray/[0.08]"
        onClick={() => {
          router.push(`/${post.user.twitterHandle}/status/${post.id}`);
          closeFullScreen();
        }}
      >
        <div className="flex items-center justify-between gap-4 text-sm leading-4 text-gray-text">
          <div
            className="flex min-w-0 items-center"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <ProgressBarLink
              href={`/${post.user.twitterHandle}`}
              onClick={closeFullScreen}
            >
              <Avatar className="mr-2 size-5">
                <AvatarImage src={post.user.twitterPicture} />
                <AvatarFallback />
              </Avatar>
            </ProgressBarLink>
            <ProgressBarLink
              href={`/${post.user.twitterHandle}`}
              className={cn(
                "whitespace-nowrap font-semibold text-off-white",
                post.user.twitterName.length > 17 && "truncate",
              )}
              onClick={closeFullScreen}
            >
              {post.user.twitterName}
            </ProgressBarLink>
            ・
            <ProgressBarLink
              href={`/${post.user.twitterHandle}`}
              className="truncate"
              onClick={closeFullScreen}
            >
              @{post.user.twitterHandle}
            </ProgressBarLink>
            ・
            <div className="flex flex-shrink-0 items-center gap-2">
              <span className="flex-shrink-0">
                {formatTimeDistance(post.createdOn)}
              </span>
              {role === "HOST" || role === "COHOST" ? (
                <button
                  className="flex-shrink-0 disabled:opacity-50"
                  disabled={isPending}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (!stageId) return;

                    deletePinnedPost({
                      postId: post.id,
                      stageId,
                    });
                  }}
                >
                  <XCircleOutlineIcon className="size-6 text-off-white" />
                </button>
              ) : null}
            </div>
          </div>
        </div>
        <div className="flex flex-col text-sm">
          <div
            dangerouslySetInnerHTML={{ __html: sanitizedContent }}
            className="post-content line-clamp-3 select-none text-[#F4F4F4]"
          />
          {isTruncated && <div className="text-brand-orange">Show more</div>}
        </div>
      </div>
    </div>
  );
};
