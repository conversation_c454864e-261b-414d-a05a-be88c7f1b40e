"use client";

import { memo } from "react";

import { Virtuoso } from "react-virtuoso";

import { UserListItemLoadingSkeleton } from "@/components/user-list-item-loading-skeleton";
import { useNewCommunitiesQuery } from "@/queries";

import { CommunityItem } from "./community-item";
import { NoContent } from "./no-content";

export const NewCommunitiesTab = memo(function NewCommunitiesTab() {
  const { data, isLoading } = useNewCommunitiesQuery();

  return (
    <>
      <p className="mt-[8px] px-6 text-sm text-[#808080]">
        Recently created Tokens
      </p>
      <div>
        {isLoading &&
          Array.from({ length: 15 }).map((_, index) => {
            return <UserListItemLoadingSkeleton key={index} />;
          })}
        {!isLoading && data?.communities.length === 0 && (
          <NoContent message={"No Tokens to show!"} />
        )}
        {!isLoading && data && data.communities.length > 0 && (
          <Virtuoso
            useWindowScroll
            data={data?.communities || []}
            increaseViewportBy={500}
            itemContent={(_, community) => {
              return <CommunityItem community={community} />;
            }}
          />
        )}
      </div>
    </>
  );
});
