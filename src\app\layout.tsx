import { Inter } from "next/font/google";
import { cookies } from "next/headers";

import type { Metadata, Viewport } from "next";

import { GoogleAnalytics } from "@next/third-parties/google";

import { axios } from "@/lib/axios";

import { Providers } from "./providers";

import "./globals.css";

import PostHogClient from "@/lib/posthog";

export const viewport: Viewport = {
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover",
};

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "The Arena",
  description:
    "A next gen SocialFi app redefining how creators connect, engage, and monetize their content.",
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "black-translucent",
  },
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const tokenCookie = cookies().get("token");
  const token = tokenCookie ? tokenCookie.value : null;

  const userCookie = cookies().get("user");
  const user = userCookie ? JSON.parse(userCookie.value || "{}") : null;

  const twitterUserCookie = cookies().get("twitterUser");
  const twitterUser = twitterUserCookie
    ? JSON.parse(twitterUserCookie.value || "{}")
    : null;

  let flags = {};
  // if (user && user.twitterHandle) {
  //   const posthog = PostHogClient();
  //   flags = await posthog.getAllFlags(user.twitterHandle);
  //   await posthog.shutdown();
  // }

  if (token) {
    axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  }

  return (
    <html lang="en">
      <body className={`${inter.className} select-none md:select-auto`}>
        <Providers
          token={token}
          user={user}
          twitterUser={twitterUser}
          flags={flags}
        >
          {children}
        </Providers>
      </body>
      <GoogleAnalytics gaId="G-G9PNQ21Q6T" />
    </html>
  );
}
