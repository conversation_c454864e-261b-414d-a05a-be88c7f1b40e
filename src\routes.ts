/**
 * An array of routes that are accessible to the public
 * These routes do not require authentication
 * @type {string[]}
 */
export const publicRoutes = [
  "/",
  "/api/healthcheck",
  "/terms-of-use",
  "/privacy-policy",
  "/suspended",
  "/content-moderation-policy",
];

/**
 * An array of routes that are used for authentication
 * These routes will redirect logged in users to /home
 * @type {string[]}
 */
export const authRoutes = ["/", "/twitter/auth", "/auth/dynamic"];

/**
 * An array of routes that are allowed
 * These routes will redirect logged in users to /home
 * @type {string[]}
 */
export const allowedRoutesForSuspendedUser = ["/home", "/wallet"];

/**
 * The default redirect path after logging in
 * @type {string}
 */
export const DEFAULT_LOGIN_REDIRECT = "/home";

/**
 * The default redirect path after logging in for banned users
 * @type {string}
 */
export const DEFAULT_SUSPENDED_REDIRECT = "/suspended";
