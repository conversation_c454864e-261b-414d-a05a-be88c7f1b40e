"use client";

import { ChangeE<PERSON>, useEffect, useMemo, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";

import Skeleton from "react-loading-skeleton";
import { Virtuoso } from "react-virtuoso";

import {
  ArrowBackOutlineIcon,
  CloseOutlineIcon,
  SearchFilledIcon,
} from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { Input } from "@/components/ui/input";
import useThrottle from "@/hooks/use-throttle";
import {
  useIsBlockedByUserQuery,
  useIsUserBlockedQuery,
  useUserByHandleQuery,
} from "@/queries";
import { useFollowingInfiniteQuery } from "@/queries/follow-queries";
import { Following } from "@/queries/types/follow";
import { useUser } from "@/stores";
import { UserFlaggedEnum } from "@/types";
import { cn } from "@/utils";

import { UserListItem } from "./user-list-item";
import { UserListItemLoadingSkeleton } from "./user-list-item-loading-skeleton";

export const FollowingList = () => {
  const router = useRouter();
  const { user } = useUser();
  const params = useParams() as { userHandle: string };
  const [searchString, setSearchString] = useState("");
  const throttledSearchString = useThrottle(searchString);

  const { data: userData, isLoading: isUserDataLoading } = useUserByHandleQuery(
    params.userHandle,
  );

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } =
    useFollowingInfiniteQuery({
      userId: userData?.user.id,
      searchString: throttledSearchString,
    });

  const following = useMemo(() => {
    if (!data) return [];
    return data?.pages.reduce((prev, current) => {
      return [
        ...prev,
        ...(current?.followingsWithFollowedByloggedInUser || []),
      ];
    }, [] as Following[]);
  }, [data]);

  const handleBack = () => {
    if (typeof window !== "undefined" && window.history.length > 1) {
      router.back();
    } else {
      router.push(`/${params.userHandle}`);
    }
  };

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchString(e.target.value);
  };

  const { data: isUserBlocked, isLoading: isUserBlockedLoading } =
    useIsUserBlockedQuery(userData?.user.id);
  const { data: isBlockedByUser, isLoading: isBlockedByUserLoading } =
    useIsBlockedByUserQuery(userData?.user.id);
  const isSuspended = userData?.user?.flag === UserFlaggedEnum.SUSPENDED;
  const isUserSuspended = user?.flag === UserFlaggedEnum.SUSPENDED;

  const isBlocked =
    (isUserBlocked && !isUserBlockedLoading) ||
    (isBlockedByUser && !isBlockedByUserLoading);

  useEffect(() => {
    if (isBlocked || isSuspended || isUserSuspended) {
      router.push(`/${params.userHandle}`);
    }
  }, [isBlocked, isSuspended, isUserSuspended]);

  return (
    <div>
      <div className="sticky top-0 z-10 bg-dark-bk">
        <div className=" flex items-center px-6 pb-4 pt-[calc(1rem+env(safe-area-inset-top))]">
          <div className="flex-1">
            <button className="flex flex-shrink-0" onClick={handleBack}>
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </button>
          </div>
          <h2 className="text-base font-semibold leading-5 text-white">
            {isUserDataLoading ? (
              <Skeleton className="h-4 w-28" />
            ) : (
              userData?.user.twitterName
            )}
          </h2>
          <div className="flex-1" />
        </div>
        <div className="mt-2 px-6">
          <div className="relative">
            <Input
              value={searchString}
              onChange={handleSearch}
              placeholder="Search"
              className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-10 ring-0  placeholder:text-gray-text"
            />
            <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
            {searchString && (
              <button className="absolute right-[10px] top-1/2 -translate-y-1/2 rounded-full border border-dark-gray bg-[rgba(20,20,20,0.88)] p-1">
                <CloseOutlineIcon
                  className="pointer-events-auto size-[14px] select-none text-off-white"
                  onClick={() => setSearchString("")}
                />
              </button>
            )}
          </div>
        </div>
        <div className="hide-scrollbar mt-4 inline-flex h-10 w-full items-center justify-start overflow-x-auto border-b border-white/10 px-5">
          <ProgressBarLink
            href={`/${params.userHandle}/followers`}
            className={cn(
              "relative -mt-[1px] inline-flex items-center justify-center whitespace-nowrap px-5 pb-3 pt-2 text-sm font-medium text-dark-gray transition-all disabled:pointer-events-none disabled:opacity-50 sm:px-14",
            )}
          >
            Followers
          </ProgressBarLink>
          <ProgressBarLink
            href={`/${params.userHandle}/following`}
            className={cn(
              "relative -mt-[1px] inline-flex items-center justify-center whitespace-nowrap px-5 pb-3 pt-2 text-sm font-medium text-dark-gray transition-all disabled:pointer-events-none disabled:opacity-50 sm:px-14",
              "text-off-white after:absolute after:bottom-0 after:h-px after:w-full after:bg-brand-orange",
            )}
          >
            Following
          </ProgressBarLink>
        </div>
      </div>
      <div className="mt-5">
        {!isUserDataLoading &&
          !isFetchingNextPage &&
          !isLoading &&
          following.length === 0 && (
            <div className="mt-10 flex w-full items-center justify-center">
              <div className="max-w-64 text-center">
                <h4 className="text-sm font-semibold text-[#EDEDED]">
                  No following found!
                </h4>
              </div>
            </div>
          )}
        <Virtuoso
          useWindowScroll
          data={following}
          increaseViewportBy={500}
          itemContent={(index, follower) => {
            return <UserListItem {...follower} />;
          }}
          endReached={() => {
            if (hasNextPage) {
              fetchNextPage();
            }
          }}
          components={{
            Footer: () => {
              if (isUserDataLoading || isLoading || isFetchingNextPage) {
                return (
                  <>
                    {Array.from({ length: 10 }).map((_, i) => (
                      <UserListItemLoadingSkeleton key={i} />
                    ))}
                  </>
                );
              }
              return null;
            },
          }}
        />
      </div>
    </div>
  );
};
