import {
  createContext,
  FC,
  ReactNode,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";

import {
  loadStripeOnramp,
  OnrampSessionResult,
  OnrampUIEventMap,
} from "@stripe/crypto";
import { StripeOnramp } from "@stripe/crypto/types/crypto-js/onramp";
import {
  OnrampSession,
  OnrampUIEvents,
} from "@stripe/crypto/types/crypto-js/onramp-session";

import { env } from "@/env";

// ReactContext to simplify access of StripeOnramp object
const CryptoElementsContext = createContext<{
  onramp: StripeOnramp | null;
}>({
  onramp: null,
});
CryptoElementsContext.displayName = "CryptoElementsContext";

export const CryptoElements = ({
  stripeOnramp,
  children,
}: {
  stripeOnramp: Promise<StripeOnramp | null>;
  children: ReactNode;
}) => {
  const [ctx, setContext] = useState<{ onramp: StripeOnramp | null }>(() => ({
    onramp: null,
  }));

  useEffect(() => {
    let isMounted = true;

    Promise.resolve(stripeOnramp).then((onramp) => {
      if (onramp && isMounted) {
        setContext((ctx) => (ctx.onramp ? ctx : { onramp }));
      }
    });

    return () => {
      isMounted = false;
    };
  }, [stripeOnramp]);

  return (
    <CryptoElementsContext.Provider value={ctx}>
      {children}
    </CryptoElementsContext.Provider>
  );
};

// React hook to get StripeOnramp from context
export const useStripeOnramp = () => {
  const context: any = useContext(CryptoElementsContext);
  return context?.onramp;
};

// React element to render Onramp UI
const useOnrampSessionListener = (
  type: keyof OnrampUIEventMap,
  session?: OnrampSession,
  callback?: (payload?: { session: OnrampSessionResult }) => {},
) => {
  useEffect(() => {
    if (session && callback) {
      const listener = (e: OnrampUIEvents) => callback(e.payload);
      session.addEventListener(type, listener);
      return () => {
        session.removeEventListener(type, listener);
      };
    }
    return () => {};
  }, [session, callback, type]);
};

interface OnrampElementProps {
  clientSecret?: string | null;
  appearance: any;
  onReady?: (payload?: { session: OnrampSessionResult }) => {};
  onChange?: (payload?: { session: OnrampSessionResult }) => {};
}
export const OnrampElement: FC<OnrampElementProps> = ({
  clientSecret,
  appearance,
  onReady,
  onChange,
  ...props
}) => {
  const stripeOnramp = useStripeOnramp();
  const onrampElementRef = useRef(null);
  const [session, setSession] = useState<OnrampSession>();

  const appearanceJSON = JSON.stringify(appearance);
  useEffect(() => {
    const containerRef: any = onrampElementRef.current;
    if (containerRef) {
      // NB: ideally we want to be able to hot swap/update onramp iframe
      // This currently results a flash if one needs to mint a new session when they need to udpate fixed transaction details
      containerRef.innerHTML = "";

      if (clientSecret && stripeOnramp) {
        setSession(
          stripeOnramp
            .createSession({
              clientSecret,
              appearance: appearanceJSON ? JSON.parse(appearanceJSON) : {},
            })
            .mount(containerRef),
        );
      }
    }
  }, [appearanceJSON, clientSecret, stripeOnramp]);

  useOnrampSessionListener("onramp_ui_loaded", session, onReady);
  useOnrampSessionListener("onramp_session_updated", session, onChange);

  return <div {...props} ref={onrampElementRef}></div>;
};
export const StripeCrypto = (props: { clientSecret: string }) => {
  const stripeOnrampPromise = loadStripeOnramp(
    env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
  );

  const [isDark, setIsDark] = useState<boolean>(false);

  const appearance = {
    theme: isDark ? "dark" : "stripe",
  };

  useEffect(() => {
    const prefersDark = window.matchMedia(
      "(prefers-color-scheme: dark)",
    ).matches;

    if (prefersDark) {
      setIsDark(true);
    }
  }, []);

  return (
    <CryptoElements stripeOnramp={stripeOnrampPromise}>
      {props.clientSecret && (
        <OnrampElement
          clientSecret={props.clientSecret}
          appearance={appearance}
        />
      )}
    </CryptoElements>
  );
};
