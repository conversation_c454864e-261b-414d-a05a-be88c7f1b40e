"use client";

import { useCallback, useEffect, useRef, useState } from "react";

import { useQuery } from "@tanstack/react-query";
import EmojiPicker, { Emoji, EmojiClickData, Theme } from "emoji-picker-react";
import { clone } from "remeda";

import { ArrowBackOutlineIcon, CogOutlineIcon } from "@/components/icons";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { useUpdateEmotesMutation } from "@/queries";
import { stageQueries } from "@/queries/stage-queries";
import { cn } from "@/utils";

const defaultEmotes = [
  ["2764-fe0f", "1f525", "1f4af", "1f44f", "1f92f"],
  ["2694-fe0f", "1f923", "1fae1", "1f44d", "1f921"],
];

export function StageSettingsModal() {
  const [open, setOpen] = useState(false);
  const [emojis, setEmojis] = useState<string[][] | null>(null);
  const [selectedEmoji, setSelectedEmoji] = useState<[number, number] | null>(
    null,
  );
  const [renderEmojiPicker, setRenderEmojiPicker] = useState(false);
  const selectedEmojiRef = useRef<[number, number] | null>(null);
  const { data, refetch } = useQuery(stageQueries.emotes());

  const loadedEmojis =
    data && data.emotes && data.emotes.length > 0 ? data.emotes : defaultEmotes;

  const { mutate: updateEmotes, isPending } = useUpdateEmotesMutation({
    onSuccess: async () => {
      toast.green("Successfully updated emojis");
      await refetch();
      reset();
    },
  });

  const handleSaveEmojis = useCallback(() => {
    updateEmotes({ emotes: emojis ?? loadedEmojis });
  }, [emojis, loadedEmojis, updateEmotes]);

  const handleEmojiClick = useCallback(
    (emoji: EmojiClickData) => {
      if (!selectedEmojiRef.current) return;

      const newEmojis = clone(emojis ?? loadedEmojis);
      newEmojis[selectedEmojiRef.current[0]][selectedEmojiRef.current[1]] =
        emoji.unified;
      setEmojis(newEmojis);
    },
    [emojis, loadedEmojis, selectedEmoji],
  );

  const reset = useCallback(() => {
    setSelectedEmoji(null);
    selectedEmojiRef.current = null;
    setEmojis(null);
  }, []);

  useEffect(() => {
    if (open) {
      setTimeout(() => {
        setRenderEmojiPicker(true);
      }, 300);
    } else {
      setRenderEmojiPicker(false);
      reset();
    }
  }, [open]);

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
        if (!open) {
          reset();
        }
      }}
    >
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="outline-none">
          <CogOutlineIcon className="size-6 text-off-white" />
        </Button>
      </DialogTrigger>
      <DialogContent className="pt-pwa z-50 flex h-full w-full flex-col gap-0 bg-dark-bk px-0 pb-0 sm:h-auto sm:max-w-sm sm:bg-dark-bk sm:py-2">
        <div className="pb-pwa flex h-full flex-col overflow-y-auto">
          <div className="sticky top-0 z-20 px-6">
            <div className="flex items-center gap-2 py-4">
              <div className="flex-1">
                <button
                  className="flex flex-shrink-0"
                  onClick={() => {
                    setOpen(false);
                    reset();
                  }}
                >
                  <ArrowBackOutlineIcon className="size-5 text-off-white" />
                </button>
              </div>
              <h2 className="text-base font-semibold leading-[22px] text-off-white">
                Stages Settings
              </h2>
              <div className="flex-1" />
            </div>
          </div>
          <div className="flex flex-col px-6 py-1">
            <h4 className="text-base font-semibold text-off-white">Emoji</h4>
            <p className="mt-1 text-sm text-gray-text">
              Click on an emoji to update it.
            </p>
            <div className="mt-6 flex flex-col items-center justify-center gap-1 rounded-[10px] border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] px-2 py-6">
              {(emojis ? emojis : loadedEmojis).map((row, i) => {
                return (
                  <div className="flex items-center gap-1" key={i}>
                    {row.map((emote, j) => (
                      <button
                        key={emote + i + j}
                        className={cn(
                          "flex size-14 items-center justify-center rounded border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] outline-none",
                          selectedEmoji?.[0] === i &&
                            selectedEmoji?.[1] === j &&
                            "border-brand-orange bg-dark-gray/30",
                        )}
                        onClick={() => {
                          setSelectedEmoji((prev) =>
                            prev?.[0] === i && prev?.[1] === j ? null : [i, j],
                          );
                          selectedEmojiRef.current = [i, j];
                        }}
                      >
                        <Emoji unified={emote} size={32} />
                      </button>
                    ))}
                  </div>
                );
              })}
            </div>
            {renderEmojiPicker && (
              <div className={cn("mt-4", !selectedEmoji && "hidden")}>
                <EmojiPicker
                  reactionsDefaultOpen={false}
                  skinTonesDisabled
                  onEmojiClick={handleEmojiClick}
                  theme={Theme.DARK}
                  height={350}
                  width="100%"
                  className="!bg-chat-bubble [&_h2]:bg-chat-bubble/95"
                  previewConfig={{ showPreview: false }}
                  lazyLoadEmojis
                />
              </div>
            )}
          </div>
          <div className="mt-auto w-full px-6 py-4">
            <Button
              className="w-full"
              onClick={handleSaveEmojis}
              loading={isPending}
            >
              Save Emojis
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
