"use client";

import { FC, useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";

import { useInView } from "react-intersection-observer";

import { Search } from "@/app/(messages)/messages/_components/search";
import useThrottle from "@/hooks/use-throttle";
import { useGroupedTicketsInfiniteQuery } from "@/queries/report-tickets-query";
import { GroupedTicket } from "@/queries/types/report-tickets";
import { cn } from "@/utils";

import { useReportsContext } from "../context/reports-context";
import { GroupItem } from "./group-item";

interface ReportTicketsListProps {
  status: "Active" | "Closed";
}

export const ReportTicketsList: FC<ReportTicketsListProps> = ({ status }) => {
  const params = useParams() as { ticketId?: string };
  const router = useRouter();
  const [search, setSearch] = useState("");
  const throttledSearch = useThrottle(search);
  const [sortBy, setSortBy] = useState<"date" | "count" | "score" | "live">(
    "date",
  );
  const { ref, inView } = useInView();
  const { activeGroups, closedGroups, setActiveGroups, setClosedGroups } =
    useReportsContext();

  const groups = status === "Active" ? activeGroups : closedGroups;

  const { data, fetchNextPage, hasNextPage, isFetching } =
    useGroupedTicketsInfiniteQuery(status, sortBy, throttledSearch);

  const handleGroupClick = (group: GroupedTicket) => {
    const firstTicketId = group.tickets[0].id;
    router.push(`/moderation/reports/${firstTicketId}`);
  };

  useEffect(() => {
    if (inView && hasNextPage && !isFetching) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetching, fetchNextPage]);

  useEffect(() => {
    if (data) {
      const allGroups = data.pages.flatMap((page) => page.groups);
      if (status === "Active") {
        setActiveGroups(allGroups);
      } else {
        setClosedGroups(allGroups);
      }
    }
  }, [data, status, setActiveGroups, setClosedGroups]);

  return (
    <div className="mt-4">
      <Search
        placeholder="Search reports"
        search={search}
        inputOnChange={(e) => setSearch(e.target.value)}
        closeOnClick={() => setSearch("")}
      />
      <div className="flex items-center gap-4 whitespace-nowrap p-6 ">
        <h3 className="text-sm font-semibold">Sort by</h3>
        <div className="flex gap-2">
          {(["date", "count", "score", "live"] as const).map((sort) => (
            <button
              key={sort}
              className={cn(
                "rounded-full border border-dark-gray px-[16px] py-[6px] text-sm capitalize",
                sortBy === sort && "border-brand-orange",
              )}
              onClick={() => setSortBy(sort)}
            >
              {sort === "date" ? (
                "Date"
              ) : sort === "count" ? (
                "Report Count"
              ) : sort === "score" ? (
                "Score"
              ) : (
                <div className="flex items-center gap-2">
                  <div className="h-[6px] w-[6px] rounded-full bg-red-600" />
                  Live
                </div>
              )}
            </button>
          ))}
        </div>
      </div>
      <div className="mt-2">
        {groups.map((group) => (
          <GroupItem
            key={`${group.reportedUser.id}-${group.tickets.some((t) => !t.isSeen)}`}
            group={group}
            isSelected={group.tickets.some(
              (t) => t.id.toString() === params?.ticketId,
            )}
            onClick={() => handleGroupClick(group)}
          />
        ))}
      </div>
      <div ref={ref} className="h-1" />
      {isFetching && <div>Loading...</div>}
    </div>
  );
};
