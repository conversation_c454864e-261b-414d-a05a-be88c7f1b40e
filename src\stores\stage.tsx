"use client";

import { createContext, useContext, useState } from "react";

import * as R from "remeda";
import { createStore, StoreApi, useStore } from "zustand";

import { Role } from "@/components/stages/constants";

export interface StageUser {
  emotes: string[];
  tips: {
    currency: string;
    amount: number;
    from: {
      name: string;
      avatar: string;
      username: string;
    };
    id: string;
  }[];
}

interface Store {
  id: string | null;
  token: string | null;
  isFullScreen: boolean;
  isGuestsModalOpen: boolean;
  isRequestedToSpeak: boolean;
  hasUnseenRequests: boolean;
  users: {
    [id: string]: StageUser | undefined;
  };
  reactions: {
    value: string;
    id: number;
  }[];
  chat: {
    isOpen: boolean;
    isFullScreen: boolean;
  };
  lastConfettiId: {
    current: string | null;
  };
  canShowCurrentlyListening: boolean;
  startedTime: number;
  speakingDuration: number;
  myRole: Role | null;
}

interface Actions {
  actions: {
    reset: () => void;
    setId: (id: string | null) => void;
    setToken: (token: string | null) => void;
    setFullScreen: (isFullScreen: boolean) => void;
    setIsGuestsModalOpen: (isGuestsModalOpen: boolean) => void;
    setIsRequestedToSpeak: (isRequestedToSpeak: boolean) => void;
    setHasUnseenRequests: (hasUnseenRequests: boolean) => void;
    addUser: (id: string, user: StageUser) => void;
    getUser: (id: string) => StageUser | undefined;
    removeUser: (id: string) => void;
    setEmotes: (userId: string, emotes: string[]) => void;
    addEmote: (userId: string, emote: string) => void;
    setTips: (
      userId: string,
      callback: (tips: StageUser["tips"]) => StageUser["tips"],
    ) => void;
    addTip: (userId: string, tip: StageUser["tips"][0]) => void;
    addReaction: (reaction: { value: string; id: number }) => void;
    removeReaction: (reactionId: number) => void;
    setChatOpen: (isOpen: boolean) => void;
    toggleChatOpen: () => void;
    setChatFullScreen: (isFullScreen: boolean) => void;
    setCanShowCurrentlyListening: (canShowCurrentlyListening: boolean) => void;
    setSpeakingDuration: (duration: number) => void;
    addSpeakingDuration: (duration: number) => void;
    getSpeakingDuration: () => number;
    getSpeakingDurationAndReset: () => number;
    setStartedTime: (startedTime: number) => void;
    setMyRole: (role: Role) => void;
  };
}

type StageState = Store & Actions;

const StageStoreContext = createContext<StoreApi<StageState> | null>(null);

export const StageStoreProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [store] = useState(() =>
    createStore<StageState>((set, get) => ({
      id: null,
      token: null,
      isFullScreen: true,
      isGuestsModalOpen: false,
      isRequestedToSpeak: false,
      users: {},
      reactions: [],
      chat: {
        isOpen: false,
        isFullScreen: false,
      },
      hasUnseenRequests: false,
      lastConfettiId: { current: null },
      canShowCurrentlyListening: true,
      speakingDuration: 0,
      startedTime: 0,
      myRole: null,
      actions: {
        reset: () => {
          set({
            token: null,
            users: {},
            reactions: [],
            id: null,
            isFullScreen: true,
            isGuestsModalOpen: false,
            isRequestedToSpeak: false,
            canShowCurrentlyListening: false,
            speakingDuration: 0,
            hasUnseenRequests: false,
            lastConfettiId: { current: null },
            chat: {
              isOpen: false,
              isFullScreen: false,
            },
          });
        },
        setId: (id) => {
          set({ id });
        },
        setToken: (token) => {
          set({ token });
        },
        setFullScreen: (isFullScreen) => {
          set({ isFullScreen });
        },
        setIsGuestsModalOpen: (isGuestsModalOpen) => {
          const hasUnseenRequests = get().hasUnseenRequests;
          set({
            isGuestsModalOpen,
            hasUnseenRequests:
              isGuestsModalOpen === true ? false : hasUnseenRequests,
          });
        },
        setIsRequestedToSpeak: (isRequestedToSpeak) => {
          set({ isRequestedToSpeak });
        },
        setHasUnseenRequests: (hasUnseenRequests) => {
          const isGuestsModalOpen = get().isGuestsModalOpen;
          set({
            hasUnseenRequests:
              isGuestsModalOpen === true ? false : hasUnseenRequests,
          });
        },
        addUser: (id, user) => {
          set({
            users: {
              [id]: user,
            },
          });
        },
        getUser: (id) => {
          return get().users[id];
        },
        removeUser: (id) => {
          const users = get().users;
          delete users[id];
          set({ users });
        },
        setEmotes: (id, emotes) => {
          const users = get().users;
          let user = users[id];
          if (!user) {
            user = {
              emotes: [],
              tips: [],
            };
            users[id] = user;
          }
          user.emotes = emotes;
          set({ users });
        },
        addEmote: (id, emote) => {
          const users = get().users;
          let user = users[id];
          if (!user) {
            user = {
              emotes: [],
              tips: [],
            };
            users[id] = user;
          }
          user.emotes = [...user.emotes, emote];
          set({ users });
        },
        setTips: (id, callback) => {
          const users = get().users;
          let user = R.clone(users[id]);
          if (!user) {
            user = {
              emotes: [],
              tips: [],
            };
          }

          user.tips = callback(user.tips);
          users[id] = user;

          set({ users });
        },
        addTip: (id, tip) => {
          const users = get().users;
          let user = R.clone(users[id]);
          if (!user) {
            user = {
              emotes: [],
              tips: [],
            };
          }

          user.tips = [...user.tips, tip];
          users[id] = user;

          set({
            users,
          });
        },
        addReaction: (reaction) => {
          set({ reactions: [...get().reactions, reaction] });
        },
        removeReaction: (reactionId) => {
          set({
            reactions: get().reactions.filter(
              (reaction) => reaction.id !== reactionId,
            ),
          });
        },
        setChatOpen: (isOpen) => {
          const chat = R.clone(get().chat);
          chat.isOpen = isOpen;

          set({ chat });
        },
        toggleChatOpen: () => {
          const chat = R.clone(get().chat);
          chat.isOpen = !chat.isOpen;
          set({ chat });
        },
        setChatFullScreen: (isFullScreen) => {
          const chat = R.clone(get().chat);
          chat.isFullScreen = isFullScreen;
          set({ chat });
        },
        setCanShowCurrentlyListening: (canShowCurrentlyListening) => {
          set({ canShowCurrentlyListening });
        },
        setSpeakingDuration: (duration) => {
          set({ speakingDuration: duration });
        },
        addSpeakingDuration: (duration) => {
          set({ speakingDuration: get().speakingDuration + duration });
        },
        getSpeakingDuration: () => get().speakingDuration,
        getSpeakingDurationAndReset: () => {
          const store = get();
          let rawDuration = store.speakingDuration;
          if (store.startedTime > 0) {
            rawDuration = rawDuration + (Date.now() - store.startedTime);
          }
          set({ speakingDuration: 0, startedTime: 0 });
          const duration = Math.round(rawDuration / 1000);
          return duration;
        },
        setStartedTime: (startedTime) => {
          set({ startedTime });
        },
        setMyRole: (role) => {
          set({ myRole: role });
        },
      },
    })),
  );

  return (
    <StageStoreContext.Provider value={store}>
      {children}
    </StageStoreContext.Provider>
  );
};

export function useStageStore(): StageState;
export function useStageStore<T>(selector: (state: StageState) => T): T;
export function useStageStore<T>(selector?: (state: StageState) => T) {
  const store = useContext(StageStoreContext);
  if (!store) {
    throw new Error("Missing StageStoreProvider");
  }
  return useStore(store, selector!);
}
