import { ChangeEvent, FC } from "react";

import { CloseOutlineIcon, SearchFilledIcon } from "@/components/icons";
import { Input } from "@/components/ui/input";

interface SearchProps {
  placeholder: string;
  search: string;
  inputOnChange: (e: ChangeEvent<HTMLInputElement>) => void;
  closeOnClick: () => void;
}

export const Search: FC<SearchProps> = ({
  placeholder,
  search,
  inputOnChange,
  closeOnClick,
}) => {
  return (
    <div className="mt-[23px] px-6">
      <div className="relative">
        <Input
          placeholder={placeholder}
          className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-10 placeholder:text-gray-text"
          value={search}
          onChange={inputOnChange}
        />
        <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
        {search && (
          <button className="absolute right-[10px] top-1/2 -translate-y-1/2 rounded-full border border-dark-gray bg-[rgba(20,20,20,0.88)] p-1">
            <CloseOutlineIcon
              className="pointer-events-auto size-[14px] select-none text-off-white"
              onClick={closeOnClick}
            />
          </button>
        )}
      </div>
    </div>
  );
};
