import { cva, VariantProps } from "class-variance-authority";

import { MessageType } from "@/queries/types/chats";
import { cn } from "@/utils";

const replyVariants = cva(
  "flex items-center gap-2 rounded border-l-[3px] border-gray-text p-2 pl-1.5 text-xs leading-none",
  {
    variants: {
      variant: {
        default: "bg-dark-gray",
        input: "bg-dark-gray",
        mine: "bg-dark-brand",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

interface ReplyProps extends VariantProps<typeof replyVariants> {
  message: MessageType;
  className?: string;
  onClick?: () => void;
}

export const Reply = ({ message, variant, className, onClick }: ReplyProps) => {
  const _message = message.message.replace(/<br>/g, " ");
  const parser = new DOMParser();
  const html = parser.parseFromString(_message, "text/html");
  const replyMessage = html.body.textContent;
  const attachment = message.attachments?.[0];
  const isImage = attachment?.messageType === 2;
  const isVideo = attachment?.messageType === 3;

  return (
    <div
      className={cn(
        replyVariants({ variant }),
        "cursor-pointer select-none",
        className,
      )}
      onClick={onClick}
    >
      {attachment && isImage && (
        <img
          src={attachment.url}
          alt="attachment"
          className="size-7 rounded-sm object-cover"
        />
      )}
      {attachment && isVideo && (
        <video
          src={attachment.url}
          className="size-7 rounded-sm object-cover"
        />
      )}
      <div className="flex min-w-0 flex-col gap-1.5">
        <div className="font-semibold text-[#40B877]">{message.userName}</div>
        {attachment && (
          <>
            {replyMessage && (
              <p
                className="truncate whitespace-nowrap"
                dangerouslySetInnerHTML={{ __html: replyMessage }}
              />
            )}
            {!replyMessage && isImage && <p>Photo</p>}
            {!replyMessage && isVideo && <p>Video</p>}
          </>
        )}

        {!attachment && (
          <p
            className="truncate whitespace-nowrap"
            dangerouslySetInnerHTML={{
              __html: replyMessage ?? "",
            }}
          />
        )}
      </div>
    </div>
  );
};
