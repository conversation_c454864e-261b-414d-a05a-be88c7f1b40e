import { HANDLE_PHASE } from "@/app/(main)/community/_components/consts";
import { useTokenPriceChange } from "@/hooks/use-token-price-change";
import { useAvaxPriceQuery } from "@/queries/currency-queries";
import { useIsUserBannedQuery } from "@/queries/groups-queries";
import { useUser } from "@/stores";
import { CommunityExtended } from "@/types/community";
import { cn, formatAvax } from "@/utils";
import { formatAsSubscript } from "@/utils/format-as-subscript";
import { formatMarketCap } from "@/utils/format-market-cap";
import { formatPrice } from "@/utils/format-token-price";

import {
  ArenaLogo,
  TriangleDownOutlineIcon,
  TriangleUpOutlineIcon,
} from "./icons";
import { GroupIcon, OfficialGroupIcon } from "./icons-v2/group-logo";
import { ProgressBarLink } from "./progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Button } from "./ui/button";

interface CommunityCardUIProps {
  community: CommunityExtended;
  handleFollow: () => void;
}

export const CommunityCardUI = ({
  community,
  handleFollow,
}: CommunityCardUIProps) => {
  const { user } = useUser();

  const { data: isUserBannedFromCommunity } = useIsUserBannedQuery(
    community.id || "",
    user?.id || "",
  );

  const [isNegative, percentageIncrease] = useTokenPriceChange(community.stats);

  const tickerPrice = formatPrice(community?.stats?.price || "0");
  const marketCap = formatAvax(community?.stats?.marketCap || "0");
  const { data: avaxPrice } = useAvaxPriceQuery();

  return (
    <div className="relative isolate flex w-[150px] flex-col items-center justify-start gap-[10px] rounded-xl bg-[#2B2B2B] p-4">
      <div className="absolute inset-0 -z-10 overflow-hidden rounded-xl">
        <div className="absolute inset-0 bg-[#141414] backdrop-blur-sm" />
        <ArenaLogo className="absolute -left-6 -top-10 h-[425px] w-[270px] opacity-5" />
      </div>
      <ProgressBarLink
        href={
          community.tokenPhase >= HANDLE_PHASE
            ? `/community/${community.name}`
            : `/community/${community.contractAddress}`
        }
      >
        <Avatar className="size-12">
          <AvatarImage src={community.photoURL} />
          <AvatarFallback />
        </Avatar>
      </ProgressBarLink>
      <div className="flex min-w-0 flex-col items-center justify-start gap-[6px] overflow-hidden text-center">
        <ProgressBarLink
          href={
            community.tokenPhase >= HANDLE_PHASE
              ? `/community/${community.name}`
              : `/community/${community.contractAddress}`
          }
          className="max-w-[118px] truncate text-xs font-medium text-off-white"
        >
          <div className="flex items-center gap-1.5">
            {community.isOfficial ? <OfficialGroupIcon /> : <GroupIcon />}
            <h4 className="truncate text-[#F4F4F4]">
              ${community.ticker.toUpperCase()}
            </h4>
          </div>
        </ProgressBarLink>
        <ProgressBarLink
          href={
            community.tokenPhase >= HANDLE_PHASE
              ? `/community/${community.name}`
              : `/community/${community.contractAddress}`
          }
          className="max-w-[118px] truncate text-[11px] leading-4 text-gray-text"
        >
          ${formatMarketCap(Number(marketCap) * (avaxPrice?.avax || 0))} MKT CAP
        </ProgressBarLink>
        <div className="flex items-center gap-[6px]">
          <span className="text-xs font-medium leading-5 text-off-white">
            {formatAsSubscript(
              (Number(tickerPrice) * (avaxPrice?.avax || 0)).toFixed(18),
            )}
          </span>
          <span
            className={cn(
              "flex items-center gap-[4px] text-sm",
              isNegative ? "text-danger" : "text-[#40B877]",
            )}
          >
            {percentageIncrease !== "0" && (
              <>
                {isNegative ? (
                  <TriangleDownOutlineIcon className="h-4 w-4" />
                ) : (
                  <TriangleUpOutlineIcon className="h-4 w-4" />
                )}
              </>
            )}
            <span>{percentageIncrease}%</span>
          </span>
        </div>
      </div>
      <Button
        variant="outline"
        className={cn(
          "w-full min-w-[118px] py-2",
          community.following ? "border-gray-text" : "border-brand-orange",
        )}
        disabled={isUserBannedFromCommunity}
        onClick={handleFollow}
      >
        {community.following ? "Leave" : "Join"}
      </Button>
    </div>
  );
};
