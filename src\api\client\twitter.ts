import { axios } from "@/lib/axios";
import { dc, gen<PERSON>eyPair } from "@/lib/encryption";

export const getPrivateKey = async () => {
  const pair = genKeyPair();
  const searchParams = new URLSearchParams({
    pk: pair.getPublic("hex"),
  });
  const response = await axios.get(
    `/twitter/export?${searchParams.toString()}`,
  );

  if (!response.data.pk) {
    throw new Error("Not Authorized!");
  }
  const privateKey = dc(response.data.pk, pair.getPrivate("hex"));
  const oldPrivateKey = response.data.oldPk
    ? dc(response.data.oldPk, pair.getPrivate("hex"))
    : undefined;

  return {
    privateKey: privateKey || undefined,
    oldPrivateKey,
    solanaPrivateKey: response.data.solanaPrivateKey.toString(),
  };
};
