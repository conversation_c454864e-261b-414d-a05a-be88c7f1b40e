import Image from "next/image";

import {
  BeakerOutlineIcon,
  PuzzleOutlineIcon,
  ShoppingCartOutlineIcon,
  TemplateOutlineIcon,
} from "@/components/icons";
import { Button } from "@/components/ui/button";

import { AddYourAppFormModal } from "./_components/add-your-app-form-modal";
import { Header } from "./_components/header";
import { PlayPittButton } from "./_components/play-pitt-button";

export default function AppStorePage() {
  return (
    <div className="relative isolate h-full w-full overflow-hidden pb-20">
      <Header>Arena App Store</Header>
      <div className="mt-4 flex flex-col gap-6 sm:mt-8">
        <h2 className="px-6 text-xl font-semibold text-off-white">
          Top Categories
        </h2>
        <div className="hide-scrollbar relative flex flex-grow select-none flex-nowrap items-start justify-start gap-4 overflow-x-auto px-6 opacity-35 sm:grid sm:grid-cols-4">
          <div className="flex min-w-[130px] flex-col items-center gap-2 rounded-[10px] border border-dark-gray bg-chat-bubble p-4 shadow-[2px_2px_4px_0px_rgba(0,0,0,0.3)] sm:w-auto">
            <PuzzleOutlineIcon className="size-5 text-off-white" />
            <div className="text-base font-semibold leading-5 text-off-white">
              Games
            </div>
          </div>
          <div className="flex min-w-[130px] flex-col items-center gap-2 rounded-[10px] border border-dark-gray bg-chat-bubble p-4 shadow-[2px_2px_4px_0px_rgba(0,0,0,0.3)] sm:w-auto">
            <ShoppingCartOutlineIcon className="size-5 text-off-white" />
            <div className="text-base font-semibold leading-5 text-off-white">
              Shopping
            </div>
          </div>
          <div className="flex min-w-[130px] flex-col items-center gap-2 rounded-[10px] border border-dark-gray bg-chat-bubble p-4 shadow-[2px_2px_4px_0px_rgba(0,0,0,0.3)] sm:w-auto">
            <TemplateOutlineIcon className="size-5 text-off-white" />
            <div className="text-base font-semibold leading-5 text-off-white">
              Tools
            </div>
          </div>
          <div className="flex min-w-[130px] flex-col items-center gap-2 rounded-[10px] border border-dark-gray bg-chat-bubble p-4 shadow-[2px_2px_4px_0px_rgba(0,0,0,0.3)] sm:w-auto">
            <BeakerOutlineIcon className="size-5 text-off-white" />
            <div className="text-base font-semibold leading-5 text-off-white">
              Other
            </div>
          </div>
        </div>
      </div>
      <div className="mt-8 flex flex-col gap-8 px-6">
        <div className="flex flex-col gap-8">
          <h2 className="text-xl font-semibold text-off-white">
            Featured Applications
          </h2>
          <div className="flex flex-col gap-4 rounded-[16px] border border-dark-gray bg-chat-bubble p-4">
            <div className="relative aspect-video w-full">
              <Image
                src="/images/the-pit.png"
                alt="The Pit"
                width={975}
                height={506}
                className="rounded-[10px] object-cover"
              />
            </div>
            <div className="flex flex-col gap-6">
              <div className="flex flex-col gap-4">
                <h3 className="text-lg font-semibold leading-5 text-off-white">
                  The Pit
                </h3>
                <p className="text-base text-off-white">
                  Fight for glory and loot against other AVAX communities to
                  find out which is the Arena&apos;s strongest. Play now!
                </p>
              </div>
              <PlayPittButton />
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-4 rounded-[16px] border border-dark-gray bg-chat-bubble p-4">
          <div className="relative mx-auto aspect-square w-full max-w-[300px]">
            <Image
              src="/images/cast3_logo.png"
              alt="CAST3"
              width={834}
              height={836}
              className="rounded-[10px] object-contain"
            />
          </div>
          <div className="flex flex-col gap-6">
            <div className="flex flex-col gap-4">
              <h3 className="text-lg font-semibold leading-5 text-off-white">
                CAST3
              </h3>
              <p className="text-base text-off-white">
                Promoted Posts For The Future Of Social Media. Stop grinding.
                Start going viral.
              </p>
            </div>
            <Button asChild variant="default" className="w-full">
              <a
                href="https://app.cast3.io/?ref=arena-app-store"
                target="_blank"
                rel="noopener noreferrer"
              >
                Visit CAST3
              </a>
            </Button>
          </div>
        </div>
        <div className="flex flex-col gap-6">
          <h2 className="text-xl font-semibold text-off-white">Developers</h2>
          <AddYourAppFormModal>
            <div className="flex cursor-pointer select-none flex-col items-center gap-2 rounded-[10px] border border-dashed border-dark-gray bg-chat-bubble px-4 py-6 shadow-[2px_2px_4px_0px_rgba(0,0,0,0.3)] hover:border-off-white/30">
              <PuzzleOutlineIcon className="size-5 text-off-white" />
              <div className="text-base font-semibold leading-5 text-off-white">
                Add your App
              </div>
            </div>
          </AddYourAppFormModal>
        </div>
      </div>
      <div className="absolute bottom-0 -z-20 aspect-square w-full rounded-full bg-purple-gradient blur-[200px]" />
      <div className="absolute inset-0 -z-10 bg-[rgba(28,22,20,0.75)] backdrop-blur-[9px]" />
    </div>
  );
}
