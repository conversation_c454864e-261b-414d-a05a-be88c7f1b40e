"use server";

import axios from "axios";

export async function proxyFile(url: string) {
  try {
    const response = await axios.get(url, {
      responseType: "arraybuffer",
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    });

    return {
      data: Buffer.from(response.data).toString("base64"),
      contentType: response.headers["content-type"],
    };
  } catch (error) {
    console.error("Error proxying file:", error);
    throw new Error("Failed to proxy file");
  }
}
