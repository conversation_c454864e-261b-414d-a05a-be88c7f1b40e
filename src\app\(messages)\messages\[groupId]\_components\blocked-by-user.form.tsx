import { <PERSON><PERSON><PERSON>, FC, SetStateAction } from "react";

import { ProgressBarLink } from "@/components/progress-bar";
import { But<PERSON> } from "@/components/ui/button";

export const BlockedUserForm = () => {
  const hint = `You were blocked by this user.`;
  return (
    <div className="flex w-full flex-col gap-4 border-r-2 border-t-2 border-dark-gray bg-gray-bg p-6">
      <span className="text-sm">{hint}</span>
      <div className="flex w-full flex-col gap-2.5">
        <ProgressBarLink href={"/messages"}>
          <Button variant="outline" className="w-full">
            Back
          </Button>
        </ProgressBarLink>
      </div>
    </div>
  );
};
