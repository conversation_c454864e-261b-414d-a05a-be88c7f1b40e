"use client";

import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import { usePathname } from "next/navigation";

import { SystemCurrency } from "@/api/client/admin-currency";

interface RewardCurrency {
  id: string | number;
  name: string;
  description?: string;
  image?: string;
  rewardAmount?: number;
}

interface AdminContextType {
  selectedProject: SystemCurrency | null;
  setSelectedProject: (project: SystemCurrency | null) => void;
  selectedReward: RewardCurrency | null;
  setSelectedReward: (reward: RewardCurrency | null) => void;
  resetState: () => void;
}

const AdminContext = createContext<AdminContextType | null>(null);

export function AdminProvider({ children }: { children: React.ReactNode }) {
  const [selectedProject, setSelectedProject] = useState<SystemCurrency | null>(
    null,
  );
  const [selectedReward, setSelectedReward] = useState<RewardCurrency | null>(
    null,
  );
  const pathname = usePathname();

  const resetState = useCallback(() => {
    setSelectedProject(null);
    setSelectedReward(null);
  }, []);

  useEffect(() => {
    if (!pathname?.startsWith("/moderation/admin")) {
      resetState();
    }
  }, [pathname, resetState]);

  return (
    <AdminContext.Provider
      value={{
        selectedProject,
        setSelectedProject,
        selectedReward,
        setSelectedReward,
        resetState,
      }}
    >
      {children}
    </AdminContext.Provider>
  );
}

export function useAdmin() {
  const context = useContext(AdminContext);
  if (!context) {
    throw new Error("useAdmin must be used within an AdminProvider");
  }
  return context;
}
