import { create } from "zustand";

import { Thread } from "@/types";
import { CommunityExtended } from "@/types/community";

interface Store {
  type: "reply" | "quote" | null;
  thread: Thread | null;
  community: CommunityExtended | null;
  referrer: string | null;
}

interface Actions {
  setReply: (thread: Thread) => void;
  setQuote: (thread: Thread) => void;
  setCommunity: (community: CommunityExtended | null) => void;
  setReferrer: (referrer: string | null) => void;
  reset: () => void;
}

export const usePostStore = create<Store & Actions>((set) => ({
  type: null,
  thread: null,
  community: null,
  referrer: null,
  setReply: (thread) => set({ type: "reply", thread }),
  setQuote: (thread) => set({ type: "quote", thread }),
  setCommunity: (community) => set({ community }),
  setReferrer: (referrer) => set({ referrer }),
  reset: () =>
    set({ type: null, thread: null, community: null, referrer: null }),
}));
