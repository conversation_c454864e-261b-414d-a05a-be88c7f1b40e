import Skeleton from "react-loading-skeleton";

import { CommunityTradeTabRows } from "./community-trade-rows";

export const CommunityTradeTabSkeleton = ({
  toggleTab,
}: {
  toggleTab: () => void;
}) => {
  return (
    <div className="mx-[10px] flex flex-col justify-center border-t border-[#3B3B3B] pt-[10px]">
      <div className="mb-2 flex gap-1 pt-6">
        <Skeleton width={100} height={18} />
        <Skeleton width={30} height={18} />
      </div>
      <div className="relative">
        <div className="flex h-[52px] items-center rounded-lg border border-[#3B3B3B] transition-colors focus-within:border-off-white">
          <div className="flex items-center pl-4">
            <Skeleton circle={true} height={16} width={16} />
          </div>
          <Skeleton height={16} width={50} className="ml-2" />
        </div>
      </div>

      <div className="flex gap-2 pt-3">
        {[...Array(5)].map((_, index) => (
          <Skeleton
            key={index}
            width={56}
            height={34}
            className="flex items-center justify-center"
            style={{ borderRadius: "16px" }}
          />
        ))}
      </div>
      <div className="flex flex-col gap-1 py-8">
        <Skeleton width={200} height={16} />
        <CommunityTradeTabRows onClick={toggleTab} />
      </div>

      <div className="mb-2 flex gap-1">
        <Skeleton width={100} height={16} />
        <Skeleton width={30} height={16} />
      </div>
      <div className="relative pb-10">
        <div className="flex h-[52px] items-center rounded-lg border border-[#3B3B3B]">
          <div className="flex items-center pl-4">
            <Skeleton circle={true} height={16} width={16} />
          </div>
          <Skeleton height={16} width={50} className="ml-2" />
        </div>
      </div>

      <Skeleton width={"100%"} height={44} style={{ borderRadius: "22px" }} />
      <div className="mt-6 flex items-center gap-4">
        <div className="flex flex-grow items-center justify-between rounded-xl border border-[#3a3a3a] p-4 text-sm text-[#656565]">
          <div className="flex flex-col items-start">
            <Skeleton height={16} width={150} />
            <Skeleton height={16} width={100} />
          </div>
          <Skeleton height={20} width={20} />
        </div>
        <div className="flex flex-col rounded-xl border border-[#3a3a3a] p-3">
          <Skeleton height={16} width={60} />
          <Skeleton height={16} width={40} />
        </div>
      </div>
    </div>
  );
};
