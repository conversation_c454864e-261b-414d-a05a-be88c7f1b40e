"use client";

import {
  Dispatch,
  SetStateAction,
  startTransition,
  useEffect,
  useRef,
  useState,
} from "react";
import { useParams } from "next/navigation";

import { InfiniteData, useQueryClient } from "@tanstack/react-query";
import <PERSON>Handler from "@tiptap-pro/extension-file-handler";
import CharacterCount from "@tiptap/extension-character-count";
import Document from "@tiptap/extension-document";
import HardBreak from "@tiptap/extension-hard-break";
import History from "@tiptap/extension-history";
import Mention from "@tiptap/extension-mention";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, useEditor } from "@tiptap/react";
import { AxiosError } from "axios";
import EmojiPicker, { Theme } from "emoji-picker-react";
import { AnimatePresence, motion, useMotionTemplate } from "framer-motion";
import DOMPurify from "isomorphic-dompurify";
import { flushSync } from "react-dom";
import { v4 as uuidv4 } from "uuid";

import { proxyFile } from "@/actions/proxy-file";
import {
  AddCircleOutlineIcon,
  CloseOutlineIcon,
  GIFOutlineIcon,
  ImageOutlineIcon,
  ReplyOutlineIcon,
  SendOutlineIcon,
  SmileOutlineIcon,
  TipOutlineIcon,
  TrashOutlineIcon,
} from "@/components/icons";
import { suggestion } from "@/components/mention/suggestion";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Drawer, DrawerClose, DrawerContent } from "@/components/ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SOCKET_MESSAGE } from "@/environments/socket-messages";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useSendMessageMutation } from "@/queries";
import { FileType, MessageType } from "@/queries/types/chats";
import { useUser } from "@/stores";
import { useSocket } from "@/stores/socket";
import { cn, upload } from "@/utils";
import { compressImageToJpeg } from "@/utils/compress-jpeg";
import { removeMentionSpans } from "@/utils/mention";
import { IS_ANDROID, IS_IOS } from "@/utils/window-environment";

import { BadgeChatsTippingPartyModal } from "../../_components/badges-tipping-party-modal";
import { GIFsModal } from "../../_components/gifs-modal";
import { Reply } from "../../_components/reply";
import { TippingPartyModal } from "../../_components/tipping-party-modal";
import { useGroup, useGroupStore } from "../context/group-context";

const MAX_CHARACTERS = 1000;

interface ChatInputProps {
  reply: MessageType | null;
  setReply?: Dispatch<SetStateAction<MessageType | null>>;
  scrollToBottom?: () => void;
  canOptimisticUpdate?: boolean;
}

export const ChatInput = ({
  reply,
  setReply,
  scrollToBottom,
  canOptimisticUpdate,
}: ChatInputProps) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const savedReply = useRef<MessageType | null>(null);
  const savedFiles = useRef<FileType[]>([]);
  const queryClient = useQueryClient();
  const params = useParams() as { groupId: string };
  const { user } = useUser();
  const { socket } = useSocket();
  const containerRef = useRef<HTMLDivElement>(null);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [isEmojiPickerOpen, setEmojiPickerOpen] = useState(false);
  const emojiPickerRef = useRef<HTMLDivElement | null>(null);
  const emojiPickerButtonRef = useRef<HTMLDivElement | null>(null);
  const editorRef = useRef<HTMLDivElement>(null);

  const [isUploading, setIsUploading] = useState(false);
  const [open, setOpen] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const [gifOpen, setGifOpen] = useState(false);
  const [isTippingPartyOpen, setIsTippingPartyOpen] = useState(false);
  const [preview, setPreview] = useState<{
    url: string;
    type: "video" | "image";
  } | null>(null);
  const [files, setFiles] = useState<FileType[]>([]);
  const [progress, setProgress] = useState(0);
  const width = useMotionTemplate`${progress}%`;
  const [isDragging, setIsDragging] = useState(false);

  const [throttledValue, setThrottledValue] = useState("");
  const lastUpdated = useRef<number | null>(null);
  const timeout = useRef<number | null>(null);
  const { data: group, updateIsRequest, isLoading } = useGroup();
  const messageId = useGroupStore((state) => state.messageId);
  const hasTippingParty =
    !isLoading && group?.group?.ownerUserId && !group?.group?.isDirect;

  const replaceMentions = (text: string): string => {
    return text.replace(
      /(^|\s)@([\w.-]+)/g,
      '$1<a href="/$2" class="message-tag">@$2</a>',
    );
  };

  const { mutate } = useSendMessageMutation({
    onMutate: (variables) => {
      const previousMessages = queryClient.getQueryData([
        "chat",
        "group-infinite-messages",
        {
          groupId: params.groupId,
          messageId,
        },
      ]);
      if (canOptimisticUpdate) {
        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages",
            {
              groupId: params.groupId,
              messageId,
            },
          ],
          (
            old: InfiniteData<{
              messages: MessageType[];
            }>,
          ) => {
            if (!old) return old;
            const messageId = uuidv4();
            return {
              ...old,
              pages: old.pages.map((page, index) => {
                if (index === old.pages.length - 1) {
                  return {
                    ...page,
                    messages: [
                      ...page.messages,
                      {
                        attachments:
                          savedFiles.current.length > 0
                            ? [
                                {
                                  id: savedFiles.current[0].id,
                                  messageId,
                                  messageType:
                                    savedFiles.current[0].fileType === "image"
                                      ? 2
                                      : 3,
                                  url: savedFiles.current[0].url,
                                },
                              ]
                            : [],
                        createdOn: Date.now().toString(),
                        id: `remove-${messageId}`,
                        message: replaceMentions(
                          DOMPurify.sanitize(variables.text),
                        ),
                        reply: savedReply.current
                          ? {
                              ...savedReply.current,
                              message: DOMPurify.sanitize(
                                savedReply.current.message,
                              ),
                            }
                          : undefined,
                        groupId: params.groupId,
                        messageType: 0,
                        picture: user?.twitterPicture || "",
                        userId: user?.id || "",
                        userName: user?.twitterName || "",
                      },
                    ],
                  };
                }
                return page;
              }),
            };
          },
        );
      }
      scrollToBottom?.();
      setOpen(false);
      setReply?.(null);
      resetFilePreview();
      return { previousMessages };
    },
    onError: (err, variables, context) => {
      console.log(err);
      if (err instanceof AxiosError) {
        if (
          err.response?.data.message === "You are not a member of this group"
        ) {
          if (
            group?.group?.isDirect &&
            group?.group?.ownerUserId !== user?.id
          ) {
            toast.red("Your DM request is deleted by this user.");
          } else if (
            group?.group?.isDirect &&
            group?.group?.ownerUserId === user?.id
          ) {
            toast.red("You have deleted this user's DM request.");
          }
        }
      }
      if (canOptimisticUpdate || !messageId) {
        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages",
            {
              groupId: params.groupId,
              messageId,
            },
          ],
          context?.previousMessages,
        );
      }
    },
    onSuccess: () => {
      localStorage.removeItem(MESSAGE_DRAFT_KEY);
      savedReply.current = null;
      savedFiles.current = [];
      queryClient.invalidateQueries({
        queryKey: ["chat", "conversations"],
      });
      queryClient.invalidateQueries({
        queryKey: ["chat", "direct-messages"],
      });
    },
  });

  const extensions = [
    Document.extend({
      addKeyboardShortcuts() {
        return {
          Enter: () => {
            if (IS_IOS || IS_ANDROID) {
              // If on mobile, insert a hard break instead of sending the message
              this.editor.commands.insertContent("<br/>");
              return true;
            }

            if (
              savedFiles.current.length === 0 &&
              this.editor.getText().trim() === ""
            ) {
              return true;
            }
            const text = this.editor.getHTML();
            let message = text.replace(/<p>/g, "").replace(/<\/p>/g, "");

            if (message.length > MAX_CHARACTERS) {
              toast.red(
                `Message too long. Please limit to ${MAX_CHARACTERS} characters.`,
              );
              return true;
            }

            this.editor.chain().focus().clearContent().run();

            message = DOMPurify.sanitize(text);
            message = removeMentionSpans(message);

            mutate({
              groupId: params.groupId,
              files: savedFiles.current,
              text: message,
              replyId: savedReply.current?.id,
            });
            return true;
          },
        };
      },
    }),
    Text,
    Paragraph,
    HardBreak,
    History,
    Placeholder.configure({
      placeholder: ({ editor }) => {
        return (
          editor.options.element.getAttribute("data-placeholder") ?? "Message"
        );
      },
    }),
    Mention.configure({
      HTMLAttributes: {
        class: "text-brand-orange",
      },
      suggestion,
    }),
    CharacterCount.configure({
      limit: MAX_CHARACTERS,
    }),
  ];

  const captionEditor = useEditor({
    extensions,
    editorProps: {
      attributes: {
        class:
          "focus:outline-none h-auto max-h-20 w-full overflow-y-auto py-4 pl-5 pr-11 text-sm",
      },
    },
  });

  const MESSAGE_DRAFT_KEY = `message-draft_${params.groupId}`;

  const editor = useEditor(
    {
      extensions: [
        ...extensions,
        FileHandler.configure({
          allowedMimeTypes: [
            "image/png",
            "image/jpeg",
            "image/gif",
            "image/webp",
            "video/mp4",
            "video/quicktime",
          ],

          onDrop: (currentEditor, files, pos) => {
            setIsDragging(false);
            files.forEach((file) => {
              handleUpload(file);
            });
            // Move text from editor to captionEditor
            const editorText = currentEditor.getHTML() ?? "";
            captionEditor?.commands.setContent(editorText);
            currentEditor.chain().focus().clearContent().run();
          },
          onPaste: (currentEditor, files, htmlContent) => {
            if (htmlContent) {
              const parser = new DOMParser();
              const doc = parser.parseFromString(htmlContent, "text/html");
              const imgElement = doc.querySelector("img");

              if (imgElement && imgElement.src) {
                handleUpload(imgElement.src);
                // Move text from editor to captionEditor
                const editorText = currentEditor.getHTML() ?? "";
                captionEditor?.commands.setContent(editorText);
                currentEditor.chain().focus().clearContent().run();
                return false; // Prevent default paste behavior
              }
            }

            files.forEach((file) => handleUpload(file));
            // Move text from editor to captionEditor
            const editorText = currentEditor.getHTML() ?? "";
            captionEditor?.commands.setContent(editorText);
            currentEditor.chain().focus().clearContent().run();
          },
        }),
      ],
      editorProps: {
        attributes: {
          class:
            "focus:outline-none h-auto max-h-36 w-full overflow-y-auto py-4 pl-5 pr-11 text-sm",
        },
      },
      content: localStorage.getItem(MESSAGE_DRAFT_KEY) || "",
      onUpdate: ({ editor }) => {
        const content = editor.getHTML();
        localStorage.setItem(MESSAGE_DRAFT_KEY, content);

        const interval = 700;
        const text = editor.getText();
        const now = Date.now();

        if (lastUpdated.current && now >= lastUpdated.current + interval) {
          lastUpdated.current = now;
          setThrottledValue(text);
        } else {
          timeout.current = window.setTimeout(() => {
            lastUpdated.current = now;
            setThrottledValue(text);
          }, interval);
        }
      },
    },
    [captionEditor],
  );

  const resetFilePreview = () => {
    setTimeout(() => {
      setPreview(null);
      setFiles([]);
      setProgress(0);
    }, 200);

    inputRef.current?.value && (inputRef.current.value = "");

    savedFiles.current = [];
    const editorText = captionEditor?.getHTML() ?? "";
    editor?.commands.setContent(editorText);
    captionEditor?.chain().focus().clearContent().run();
  };

  const handleSend = (type: "normal" | "modal" = "normal") => {
    if (
      !editor ||
      (savedFiles.current.length === 0 && editor?.getText().trim() === "")
    )
      return true;

    let message = "";
    const text = editor.getHTML();
    message = text.replace(/<p>/g, "").replace(/<\/p>/g, "");
    if (message.length > MAX_CHARACTERS) {
      toast.red(
        `Message too long. Please limit to ${MAX_CHARACTERS} characters.`,
      );
      return true;
    }
    if (type === "normal") {
      editor.chain().focus().clearContent().run();
    } else {
      if (captionEditor) {
        captionEditor.chain().focus().clearContent().run();
      }
    }

    message = DOMPurify.sanitize(text);
    message = removeMentionSpans(message);
    mutate({
      groupId: params.groupId,
      files: savedFiles.current,
      text: DOMPurify.sanitize(message),
      replyId: savedReply.current?.id,
    });
  };

  const resetUploading = () => {
    setIsUploading(false);
    setProgress(0);
    setPreview(null);
  };

  const handleUpload = async (file: File | string) => {
    let fileToUpload: File;
    let previewURL: string;

    if (typeof file === "string") {
      try {
        const { data, contentType } = await proxyFile(file);
        const blob = new Blob([Buffer.from(data, "base64")], {
          type: contentType,
        });
        const fileName = file.split("/").pop() || "";
        fileToUpload = new File([blob], fileName, { type: contentType });
        previewURL = URL.createObjectURL(blob);
      } catch (error) {
        console.error("Error fetching external image:", error);
        resetUploading();
        return;
      }
    } else {
      fileToUpload = file;
      previewURL = URL.createObjectURL(file);
    }

    if (fileToUpload.type.includes("video/ogg")) {
      toast.danger("Please upload video in either mp4 or webm format");
      return;
    }
    if (
      fileToUpload.type.includes("image") &&
      !(
        fileToUpload.type.includes("image/jpeg") ||
        fileToUpload.type.includes("image/gif") ||
        fileToUpload.type.includes("image/png")
      )
    ) {
      toast.danger("Please upload image in JPEG, JPG, PNG or GIF format");
      return;
    }
    if (
      fileToUpload.type.includes("image") &&
      fileToUpload.size > 10 * 1024 * 1024
    ) {
      toast.danger("Uploaded image file cannot exceed 10 MB");
      return;
    }
    if (
      fileToUpload.type.includes("video") &&
      fileToUpload.size > 100 * 1024 * 1024
    ) {
      toast.danger("Uploaded video file cannot exceed 100 MB");
      return;
    }

    if (fileToUpload.type.includes("video")) {
      previewURL += "#t=0.001";
    }
    setPreview({
      url: previewURL,
      type: fileToUpload.type.includes("image") ? "image" : "video",
    });
    setIsUploading(true);
    setOpen(true);
    setProgress(0);

    if (
      fileToUpload.type.includes("image") &&
      (fileToUpload.type.includes("image/jpeg") ||
        fileToUpload.type.includes("image/png")) &&
      fileToUpload.size > 500 * 1024
    ) {
      const compressed = await compressImageToJpeg(fileToUpload, (progress) => {
        setProgress(progress);
      });
      if (compressed) {
        fileToUpload = compressed;
      }
    }

    const editorText = editor?.getHTML() ?? "";
    captionEditor?.commands.setContent(editorText);
    editor?.chain().focus().clearContent().run();

    try {
      const res = await upload({
        file: fileToUpload,
        onProgressChange: (progress) => {
          startTransition(() => {
            setProgress(progress);
          });
        },
      });
      setFiles([
        {
          id: res.id,
          fileType: fileToUpload.type.includes("image") ? "image" : "video",
          mimetype: fileToUpload.type,
          url: res.url,
        },
      ]);
      savedFiles.current = [
        {
          id: res.id,
          fileType: fileToUpload.type.includes("image") ? "image" : "video",
          mimetype: fileToUpload.type,
          url: res.url,
        },
      ];
    } catch (error) {
      console.error(error);
      toast.danger("File upload failed");
    } finally {
      setIsUploading(false);
    }
  };

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) return;

    await handleUpload(file);
  };

  useEffect(() => {
    if (socket && params.groupId && user) {
      socket.emit(SOCKET_MESSAGE.CHAT_TYPING, {
        groupId: params.groupId,
        user: { id: user.id, name: user.twitterName },
      });
    }

    return () => {
      if (timeout.current) {
        window.clearTimeout(timeout.current);
      }
    };
  }, [socket, params.groupId, user, throttledValue]);

  useEffect(() => {
    if (reply) {
      savedReply.current = reply;
    }
  }, [reply]);

  useEffect(() => {
    const handleClickOutside = async (event: MouseEvent | TouchEvent) => {
      const target = event.target as Node;
      if (IS_ANDROID || IS_IOS) {
        return;
      }
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(target) &&
        emojiPickerButtonRef.current &&
        !emojiPickerButtonRef.current.contains(target)
      ) {
        setEmojiPickerOpen(false);
      }
      if (editorRef.current && editorRef.current.contains(target)) {
        setEmojiPickerOpen(false);
        editor?.chain().focus().run();
      }
    };
    document.addEventListener("pointerdown", handleClickOutside);

    return () => {
      document.removeEventListener("pointerdown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if ((!IS_IOS && !IS_ANDROID) || typeof window === "undefined") {
      return undefined;
    }

    const { visualViewport } = window;
    if (!visualViewport) {
      return undefined;
    }

    const handleResize = () => {
      const isFixNeeded =
        visualViewport.height < document.documentElement.clientHeight;

      containerRef.current?.parentElement?.parentElement?.classList.toggle(
        "tbottom-pwa",
        isFixNeeded,
      );
    };
    visualViewport.addEventListener("resize", handleResize);

    return () => {
      visualViewport.removeEventListener("resize", handleResize);
    };
  }, []);

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  return (
    <>
      <div
        className="flex flex-col gap-4 bg-[#141414] bg-opacity-[0.88] px-3 py-3 backdrop-blur-sm"
        ref={containerRef}
      >
        {reply && (
          <div className="flex items-center gap-2">
            <div>
              <ReplyOutlineIcon className="size-6 text-off-white" />
            </div>
            <div className="flex-1 overflow-hidden">
              <Reply message={reply} variant="input" />
            </div>
            <button
              onClick={() => {
                setReply?.(null);
              }}
            >
              <CloseOutlineIcon className="size-6 text-off-white" />
            </button>
          </div>
        )}
        <div className="flex items-end gap-1">
          <div className="relative flex min-w-0 flex-grow items-end bg-transparent">
            {!(IS_ANDROID || IS_IOS) && (
              <div
                ref={emojiPickerButtonRef}
                className="bg-slate-500 text-black-700 left-0 mb-[15px]
                  flex
                  cursor-pointer items-center rounded-full outline-none
                  "
                onClick={() => {
                  flushSync(() => {
                    setEmojiPickerOpen(!isEmojiPickerOpen);
                  });
                  if (isEmojiPickerOpen) {
                    editor?.chain().focus().run();
                  }
                }}
              >
                <SmileOutlineIcon
                  strokeWidth={1.5}
                  className="size-6 text-[#E0E0E0]"
                />
              </div>
            )}
            <div
              ref={editorRef}
              className={cn(
                "relative w-full rounded-[10px] border border-dashed",
                isDragging ? "border-brand-orange" : "border-transparent",
              )}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={() => {
                setIsDragging(false);
              }}
            >
              <EditorContent editor={editor} className="w-full" />
            </div>
            {!(IS_ANDROID || IS_IOS) && isEmojiPickerOpen && (
              <div
                ref={emojiPickerRef}
                className={cn(
                  "left-0.5px absolute bottom-full z-10 mb-[15px] flex w-full min-w-[250px] max-w-[60%]",
                )}
              >
                <EmojiPicker
                  reactionsDefaultOpen={false}
                  theme={Theme.DARK}
                  height={300}
                  width="100%"
                  onEmojiClick={(emoji) => {
                    editor?.chain().insertContent(emoji.emoji).run();
                  }}
                  previewConfig={{ showPreview: false }}
                  className="!bg-chat-bubble [&_h2]:bg-chat-bubble/95"
                />
              </div>
            )}
            <div className="absolute bottom-0 right-0 mb-[5px]">
              <DropdownMenu open={menuOpen} onOpenChange={setMenuOpen}>
                <DropdownMenuTrigger className="flex size-[42px] items-center justify-center outline-none">
                  <AddCircleOutlineIcon className="size-7 text-[#E0E0E0]" />
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  sideOffset={24}
                  alignOffset={-54}
                  collisionPadding={{
                    right: 8,
                  }}
                  className="w-[220px] bg-[#121212]"
                >
                  {hasTippingParty && (
                    <DropdownMenuItem
                      className="gap-4"
                      onSelect={() => {
                        setIsTippingPartyOpen(true);
                      }}
                    >
                      <TipOutlineIcon className="size-[22px] flex-shrink-0" />
                      <span>Tipping Party</span>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    className="gap-4"
                    onSelect={() => {
                      setGifOpen(true);
                    }}
                  >
                    <GIFOutlineIcon className="size-[22px] flex-shrink-0" />
                    <span>GIFs</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="gap-4"
                    onSelect={() => {
                      !isUploading &&
                        inputRef.current &&
                        inputRef.current.click();
                    }}
                  >
                    <ImageOutlineIcon className="size-[22px] flex-shrink-0" />
                    <span>Images & Video</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <input
                ref={inputRef}
                style={{ display: "none" }}
                type="file"
                accept="image/*,video/*"
                onChange={handleChange}
              />
            </div>
          </div>
          <button
            className="mb-1 flex size-[45px] flex-shrink-0 items-center justify-center"
            onClick={() => {
              handleSend();
            }}
          >
            <SendOutlineIcon className="size-6 text-off-white" />
          </button>
        </div>
      </div>
      {isTablet && (
        <Dialog
          open={open}
          onOpenChange={(open) => {
            setOpen(open);
            if (!open) {
              resetFilePreview();
            }
          }}
        >
          <DialogContent className="max-w-md gap-0 p-0">
            <div className="flex items-center px-2 py-1">
              <DrawerClose className="flex size-[40px] items-center justify-center">
                <CloseOutlineIcon className="size-6 text-off-white" />
              </DrawerClose>
              <h2 className="mx-6 text-lg font-semibold leading-[22px] text-off-white">
                Send {preview?.type === "video" ? "Video" : "Image"}
              </h2>
            </div>
            <div className="px-2">
              {preview && preview.type === "video" && (
                <video
                  src={
                    isUploading && !files[0]?.url ? preview.url : files[0]?.url
                  }
                  className="max-h-[24rem] w-full rounded-xl object-cover"
                  autoPlay
                  loop
                  disablePictureInPicture
                  muted
                  playsInline
                  webkit-playsinline="true"
                />
              )}
              {preview && preview.type === "image" && (
                <div className="relative aspect-square w-full overflow-hidden rounded-xl">
                  <img
                    src={
                      isUploading && !files[0]?.url
                        ? preview.url
                        : files[0]?.url
                    }
                    alt="preview"
                    className="absolute inset-0 h-full w-full object-cover"
                    draggable={false}
                  />
                  <button
                    className="absolute bottom-2 right-2 flex size-[30px] items-center justify-center rounded-lg bg-dark-bk/25 backdrop-blur-md"
                    onClick={() => {
                      setOpen(false);
                      resetFilePreview();
                    }}
                  >
                    <TrashOutlineIcon className="size-5 text-off-white" />
                  </button>
                  <AnimatePresence>
                    {isUploading && (
                      <motion.div
                        style={{ width }}
                        exit={{ opacity: 0 }}
                        className="absolute bottom-0 left-0 h-1 min-w-4 bg-brand-orange"
                      />
                    )}
                  </AnimatePresence>
                </div>
              )}
            </div>
            <div className="flex min-w-0 items-end gap-2 px-2">
              <div className="relative flex min-w-0 flex-grow items-end">
                <EditorContent
                  editor={captionEditor}
                  className="w-full"
                  data-placeholder="Add a caption..."
                />
              </div>
              <Button
                className="mb-2 h-[40px] flex-shrink-0 rounded-xl uppercase"
                onClick={() => {
                  handleSend("modal");
                }}
              >
                Send
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
      {!isTablet && (
        <Drawer
          open={open}
          onOpenChange={(open) => {
            setOpen(open);
            if (!open) {
              resetFilePreview();
            }
          }}
          fixed
          dismissible={false}
        >
          <DrawerContent className="pb-pwa p-0">
            <div className="flex items-center px-5 py-1.5">
              <DrawerClose className="flex size-[40px] items-center justify-center">
                <CloseOutlineIcon className="size-6 text-off-white" />
              </DrawerClose>
              <h2 className="mx-6 text-lg font-semibold leading-[22px] text-off-white">
                Send {preview?.type === "video" ? "Video" : "Image"}
              </h2>
            </div>
            <div className="px-2">
              {preview && preview.type === "video" && (
                <video
                  src={
                    isUploading && !files[0]?.url ? preview.url : files[0]?.url
                  }
                  className="max-h-[24rem] w-full rounded-xl object-cover"
                  autoPlay
                  loop
                  disablePictureInPicture
                  muted
                  playsInline
                  webkit-playsinline="true"
                />
              )}
              {preview && preview.type === "image" && (
                <div className="relative aspect-square w-full overflow-hidden rounded-xl">
                  <img
                    src={
                      isUploading && !files[0]?.url
                        ? preview.url
                        : files[0]?.url
                    }
                    alt="preview"
                    className="absolute inset-0 h-full w-full object-cover"
                    draggable={false}
                  />
                  <button
                    className="absolute bottom-2 right-2 flex size-[30px] items-center justify-center rounded-lg bg-dark-bk/25 backdrop-blur-md"
                    onClick={() => {
                      setOpen(false);
                      resetFilePreview();
                    }}
                  >
                    <TrashOutlineIcon className="size-5 text-off-white" />
                  </button>
                  <AnimatePresence>
                    {isUploading && (
                      <motion.div
                        style={{ width }}
                        exit={{ opacity: 0 }}
                        className="absolute bottom-0 left-0 h-1 min-w-4 bg-brand-orange"
                      />
                    )}
                  </AnimatePresence>
                </div>
              )}
            </div>
            <div className="flex items-end gap-2 px-2 pb-2 pt-1">
              <div className="relative flex h-20 min-w-0 flex-grow items-end">
                <EditorContent
                  editor={captionEditor}
                  className="w-full"
                  data-placeholder="Add a caption..."
                />
              </div>
              <Button
                className="mb-2 h-[40px] flex-shrink-0 rounded-xl uppercase"
                onClick={() => {
                  handleSend("modal");
                }}
              >
                Send
              </Button>
            </div>
          </DrawerContent>
        </Drawer>
      )}
      <GIFsModal
        onSelect={(gif) => {
          const id = uuidv4();
          const files: FileType[] = [
            {
              id,
              fileType: "image",
              mimetype: "image/gif",
              url: gif.media_formats.gif.url,
            },
          ];
          setFiles(files);
          savedFiles.current = files;
          setPreview({
            url: gif.media_formats.gifpreview.url,
            type: "image",
          });
          setOpen(true);

          const editorText = editor?.getHTML() ?? "";
          captionEditor?.commands.setContent(editorText);
          editor?.chain().focus().clearContent().run();
        }}
        open={gifOpen}
        setOpen={setGifOpen}
      />
      {hasTippingParty &&
        (group?.group?.isBadge ? (
          <BadgeChatsTippingPartyModal
            open={isTippingPartyOpen}
            setOpen={setIsTippingPartyOpen}
            groupId={params.groupId}
          />
        ) : (
          <TippingPartyModal
            open={isTippingPartyOpen}
            setOpen={setIsTippingPartyOpen}
            ownerUserId={group?.group?.ownerUserId ?? ""}
          />
        ))}
    </>
  );
};
