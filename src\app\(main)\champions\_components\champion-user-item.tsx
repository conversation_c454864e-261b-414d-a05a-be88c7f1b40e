import { useMemo } from "react";

import { TriangleDownOutlineIcon } from "@/components/icons/triangle-down-outline";
import { TriangleUpOutlineIcon } from "@/components/icons/triangle-up-outline";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { abbreviateNumber, cn, divideBigInt } from "@/utils";
import { formatMarketCap } from "@/utils/format-market-cap";
import { formatPrice } from "@/utils/format-token-price";

interface ChampionUserItemProps {
  rank: number;
  twitterHandle: string;
  twitterPicture: string;
  twitterName: string;
  ewmaScore: string;
  lastEwmaScore: string;
}

export const ChampionUserItem = ({
  rank,
  twitterHandle,
  twitterPicture,
  twitterName,
  ewmaScore,
  lastEwmaScore,
}: ChampionUserItemProps) => {
  const [isNegative, percentageIncrease] = useMemo(() => {
    const stakedAmount = formatPrice(ewmaScore || "0");
    const lastStakedAmount = formatPrice(lastEwmaScore || "0");

    const percentage = Number(
      (lastStakedAmount
        ? 100 * ((24 * 60 * 60) / 100) * (stakedAmount / lastStakedAmount - 1)
        : stakedAmount
          ? 100
          : 0
      )
        .toFixed(2)
        .replace(/\.?0+$/, ""),
    );

    return [percentage < 0, abbreviateNumber(percentage, 2, false)];
  }, [ewmaScore, lastEwmaScore]);

  return (
    <div className="flex flex-row gap-[8px] px-6 py-4">
      <div className="flex items-center">{rank === 0 ? "N/A" : rank}</div>
      <ProgressBarLink
        href={`/${twitterHandle}`}
        className="flex w-full justify-between gap-4"
      >
        <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
          <Avatar className="size-[42px]">
            <AvatarImage src={twitterPicture} />
            <AvatarFallback />
          </Avatar>
          <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
            <div className="flex gap-1.5">
              <h4 className="max-w-[150px] truncate text-[#F4F4F4] md:max-w-full">
                {twitterName}
              </h4>
              {rank <= 500 && (
                <img
                  src="/icons/arena-champion-user.svg"
                  className="h-4 w-4 rounded-full"
                  alt="Arena logo"
                />
              )}
            </div>
            <div className="max-w-[150px] truncate text-[#808080] md:max-w-full">
              @{twitterHandle}
            </div>
          </div>
        </div>
        <div className="flex flex-shrink-0 flex-col items-end justify-center gap-[2px]">
          <div className="flex items-center gap-[6px]">
            <img
              src="/assets/coins/arena.png"
              className="h-4 w-4 rounded-full"
              alt="Arena logo"
            />
            <span className="text-sm font-medium text-[#F4F4F4]">
              {formatMarketCap(formatPrice(ewmaScore))}
            </span>
          </div>
          <span
            className={cn(
              "flex items-center gap-[4px] text-sm",
              isNegative ? "text-danger" : "text-[#40B877]",
            )}
          >
            {percentageIncrease !== "0" && (
              <>
                {isNegative ? (
                  <TriangleDownOutlineIcon className="h-4 w-4" />
                ) : (
                  <TriangleUpOutlineIcon className="h-4 w-4" />
                )}
              </>
            )}
            <span>{percentageIncrease}%</span>
          </span>
        </div>
      </ProgressBarLink>
    </div>
  );
};
