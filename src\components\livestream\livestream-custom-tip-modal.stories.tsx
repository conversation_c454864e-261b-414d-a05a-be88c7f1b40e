import React, { useState } from "react";

import { LiveKitRoom } from "@livekit/components-react";
import { Toaster } from "sonner";

import { Button } from "@/components/ui/button";

import { CustomTipModal } from "./livestream-custom-tip-modal";

const mockUserToSend = {
  threadCount: 0,
  followerCount: 0,
  followingsCount: 0,
  twitterFollowers: 0,
  id: "user-1",
  createdOn: new Date().toISOString(),
  twitterId: "tw-1",
  twitterHandle: "alice",
  twitterName: "Alice",
  twitterPicture: "https://randomuser.me/api/portraits/women/1.jpg",
  bannerUrl: "",
  address: "0x1",
  ethereumAddress: null,
  prevAddress: null,
  addressConfirmed: true,
  twitterDescription: "",
  signedUp: true,
  subscriptionCurrency: "ARENA",
  subscriptionCurrencyAddress: null,
  subscriptionPrice: "0",
  keyPrice: "0",
  lastKeyPrice: "0",
  subscriptionsEnabled: false,
  userConfirmed: true,
  twitterConfirmed: true,
  flag: 0,
  ixHandle: "alice",
  handle: null,
  following: null,
  dynamicAddress: null,
  addressBeforeDynamicMigration: null,
};

const mockCurrencies = [
  { symbol: "ARENA", balance: "1000", isToken: false, systemRate: "1" },
  { symbol: "AVAX", balance: "5", isToken: true, systemRate: "15" },
];

const mockToken = {
  symbol: "ARENA",
  balance: "1000",
  isToken: false,
  systemRate: "1",
};

const mockLivestreamStoreSnapshot = {
  id: "mock-livestream-id",
  sortedCurrencies: mockCurrencies,
  token: mockToken,
};

const mockReceivedChatMessage = {
  id: "msg-1",
  timestamp: Date.now(),
  message: JSON.stringify({ type: "text", message: "Hello!" }),
  severity: undefined,
};

const mockDataChannelsContext = {
  chat: {
    send: async () => mockReceivedChatMessage,
    update: async () => ({
      message: "Hello!",
      editTimestamp: Date.now(),
      id: "msg-1",
      timestamp: Date.now(),
    }),
    chatMessages: [mockReceivedChatMessage],
    isSending: false,
  },
  sendInvalidateTippingStats: () => {},
};

export default {
  title: "Modals/CustomTipModal",
  component: CustomTipModal,
  decorators: [
    (Story: React.FC) => (
      <LiveKitRoom
        token="dummy-token"
        serverUrl="wss://dummy-url"
        connect={false}
      >
        <Story />
        <Toaster position="top-center" />
      </LiveKitRoom>
    ),
  ],
};

export const Default = () => {
  const [open, setOpen] = useState(true);
  return (
    <>
      <CustomTipModal
        open={open}
        setOpen={setOpen}
        userToSend={mockUserToSend}
        dataChannelsContext={mockDataChannelsContext}
        livestreamStoreSnapshot={mockLivestreamStoreSnapshot}
      />
      <Button onClick={() => setOpen(true)}>Open Custom Tip Modal</Button>
    </>
  );
};
