"use client";

import { useCallback } from "react";

import { useStageStore } from "@/stores/stage";

export const useEmotesQueue = (userId: string) => {
  const emotes = useStageStore((state) => state.users[userId]?.emotes ?? []);

  const actions = useStageStore((state) => state.actions);

  const add = useCallback(
    (emote: string) => {
      actions.setEmotes(userId, [...emotes, emote]);
    },
    [emotes, actions, userId],
  );

  const remove = useCallback(() => {
    const [removedEmote, ...restEmotes] = emotes;
    actions.setEmotes(userId, restEmotes);
    return removedEmote;
  }, [emotes, actions, userId]);

  const clear = useCallback(() => {
    actions.setEmotes(userId, []);
  }, [actions, userId]);

  return {
    add,
    remove,
    clear,
    first: emotes[0],
    last: emotes[emotes.length - 1],
    size: emotes.length,
    emotes,
  };
};
