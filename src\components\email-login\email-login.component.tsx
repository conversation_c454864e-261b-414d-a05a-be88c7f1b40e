import { useCallback, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useSearchPara<PERSON> } from "next/navigation";

import { getAuthToken, useConnectWithOtp } from "@dynamic-labs/sdk-react-core";

import { loginEmail } from "@/actions/login-email";
import { isEmailRegistered } from "@/api/client/userEmail";
import { EmailInputModal } from "@/components/email-login/_components/email-input";
import { VerifyEmailModal } from "@/components/email-login/_components/verify-email";
import { toast } from "@/components/toast";
import { DEFAULT_LOGIN_REDIRECT } from "@/routes";
import { UserFlaggedEnum } from "@/types";

interface EmailLoginProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
}

const handleEmailLogin = async (
  token: string,
  router: any,
  ref: string | null,
  callbackUrl: string | null,
) => {
  try {
    const result = await loginEmail({ token, ref });
    if (result.data) {
      if (result.data.user.flag === UserFlaggedEnum.SUSPENDED) {
        window.location.href = DEFAULT_LOGIN_REDIRECT;
      } else {
        if (callbackUrl) {
          window.location.href = callbackUrl;
        } else {
          window.location.href = DEFAULT_LOGIN_REDIRECT;
        }
      }
    } else if (result.error) {
      console.error("error", result.error);
      router.push("/?error=Something went wrong!");
    }
  } catch (error) {
    console.error("error", error);
    router.push("/?error=Something went wrong!");
  }
};

export const EmailLogin = ({ visible, setVisible }: EmailLoginProps) => {
  const [emailInputMode, setEmailInputMode] = useState(true);
  const [verifyEmailMode, setVerifyEmailMode] = useState(false);
  const [emailToLogin, setEmailToLogin] = useState("");

  const router = useRouter();
  const searchParams = useSearchParams();
  const ref = searchParams.get("ref");
  const callbackUrl = searchParams.get("callbackUrl");

  const { connectWithEmail, verifyOneTimePassword, retryOneTimePassword } =
    useConnectWithOtp();
  const initiateEmailLogin = async (email: string) => {
    const user = await isEmailRegistered(email);
    if (!user.isExists) {
      toast.red("Email is not registered.");
      return;
    }

    setEmailToLogin(email);

    try {
      await connectWithEmail(email);
      setEmailInputMode(false);
      setVerifyEmailMode(true);
      toast.green("Verification code sent successfully.");
    } catch (error) {
      toast.red(
        "Failed to resend another verification code, please try again later!",
      );
    }
  };

  const verifyEmail = useCallback(
    async (verificationToken: string) => {
      try {
        await verifyOneTimePassword(verificationToken);
        const authToken = await getAuthToken();
        if (authToken) {
          await handleEmailLogin(authToken, router, ref, callbackUrl);
          setVerifyEmailMode(false);
        }
      } catch (error) {
        console.error("Email verification failed!", error);
      }
    },
    [emailToLogin, verifyOneTimePassword, router, ref, callbackUrl],
  );

  const closeModals = useCallback(() => {
    setEmailInputMode(false);
    setVerifyEmailMode(false);
    setVisible(false);
  }, [setVisible]);

  return (
    <>
      <div>
        {emailInputMode ? (
          <EmailInputModal
            handleEmail={initiateEmailLogin}
            handleClose={closeModals}
          />
        ) : (
          <></>
        )}
        {verifyEmailMode ? (
          <VerifyEmailModal
            email={emailToLogin}
            verifyAction={verifyEmail}
            resendOTP={retryOneTimePassword}
            closeAction={closeModals}
          />
        ) : (
          <></>
        )}
      </div>
    </>
  );
};
