import Skeleton from "react-loading-skeleton";

export const NotificationItemLoadingSkeleton = () => {
  return (
    <div className="relative flex items-center gap-[10px] px-6 py-4 leading-none">
      <Skeleton circle className="size-6 flex-shrink-0" />
      <div className="flex-grow overflow-hidden ">
        <Skeleton className="h-[14px] w-28" />
        <Skeleton className="mt-1 h-[14px] w-40" />
      </div>
      <div className="ml-2 flex w-12 flex-shrink-0 justify-end">
        <Skeleton className="h-[12px] w-12" />
      </div>
    </div>
  );
};
