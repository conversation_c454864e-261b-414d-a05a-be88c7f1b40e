import { HANDLE_PHASE } from "@/app/(main)/community/_components/consts";
import {
  TriangleDownOutlineIcon,
  TriangleUpOutlineIcon,
} from "@/components/icons";
import { GroupIcon, OfficialGroupIcon } from "@/components/icons-v2/group-logo";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useTokenPriceChange } from "@/hooks/use-token-price-change";
import { useAvaxPriceQuery } from "@/queries/currency-queries";
import { CommunityExtended } from "@/types/community";
import { cn } from "@/utils";
import { formatAsSubscript } from "@/utils/format-as-subscript";
import { formatMarketCap } from "@/utils/format-market-cap";
import { formatPrice } from "@/utils/format-token-price";

interface CommunityItemProps {
  community: CommunityExtended;
}

export const CommunityItem = ({ community }: CommunityItemProps) => {
  const [isNegative, percentageIncrease] = useTokenPriceChange(community.stats);

  const tickerPrice = formatPrice(community?.stats?.price || "0");
  const marketCap = formatPrice(community?.stats?.marketCap || "0");
  const { data: avaxPrice } = useAvaxPriceQuery();

  return (
    <ProgressBarLink
      href={
        community.tokenPhase >= HANDLE_PHASE
          ? `/community/${community.name}`
          : `/community/${community.contractAddress}`
      }
      className="flex w-full justify-between gap-4 px-6 py-4"
    >
      <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
        <Avatar className="size-[42px]">
          <AvatarImage src={community.photoURL} />
          <AvatarFallback />
        </Avatar>
        <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
          <div className="flex items-center gap-1.5">
            {community.isOfficial ? <OfficialGroupIcon /> : <GroupIcon />}
            <h4 className="truncate text-[#F4F4F4]">
              ${community.ticker.toUpperCase()}
            </h4>
          </div>
          <div className="truncate text-[#808080]">
            {`$${formatMarketCap(Number(marketCap) * (avaxPrice?.avax || 0))} MKT CAP`}
          </div>
        </div>
      </div>
      <div className="flex flex-shrink-0 flex-col items-end justify-center gap-[2px]">
        <div className="flex items-center gap-[6px]">
          <span className="text-sm font-medium text-[#F4F4F4]">
            {formatAsSubscript(
              (Number(tickerPrice) * (avaxPrice?.avax || 0)).toFixed(18),
            )}
          </span>
        </div>
        <span
          className={cn(
            "flex items-center gap-[4px] text-sm",
            isNegative ? "text-danger" : "text-[#40B877]",
          )}
        >
          {isNegative ? (
            <TriangleDownOutlineIcon className="h-4 w-4" />
          ) : (
            <TriangleUpOutlineIcon className="h-4 w-4" />
          )}
          <span>{percentageIncrease}%</span>
        </span>
      </div>
    </ProgressBarLink>
  );
};
