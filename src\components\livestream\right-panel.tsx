"use client";

import { useLivestreamStore } from "@/stores/livestream";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "../ui/tabs";
import { LivestreamChatContainer } from "./livestream-chat";
import { SupportersContainer } from "./supporters-container";
import { TipCards } from "./tip-cards";

export function RightPanel() {
  const isOpenNewWindow = useLivestreamStore(
    (state) => state.chat.isOpenInNewWindow,
  );

  if (isOpenNewWindow) {
    return (
      <div className="flex h-full items-center justify-center p-4 text-center">
        <div className="text-base text-off-white">
          Opened in a separate window
        </div>
      </div>
    );
  }

  return (
    <>
      <TipCards />
      <Tabs defaultValue="chat" className="flex flex-grow flex-col">
        <TabsList className="w-full">
          <TabsTrigger
            value="chat"
            className="flex-grow data-[state=active]:after:bg-purple"
          >
            Chat
          </TabsTrigger>
          <TabsTrigger
            value="supporters"
            className="flex-grow data-[state=active]:after:bg-purple"
          >
            Supporters
          </TabsTrigger>
        </TabsList>
        <TabsContent value="chat" className="flex-grow">
          <LivestreamChatContainer />
        </TabsContent>
        <TabsContent value="supporters" className="flex-grow">
          <SupportersContainer />
        </TabsContent>
      </Tabs>
    </>
  );
}
