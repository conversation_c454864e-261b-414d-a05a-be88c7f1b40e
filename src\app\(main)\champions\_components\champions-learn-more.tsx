"use client";

import { Dispatch, SetStateAction } from "react";

import { cn } from "@/utils";

// import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

interface ChampionsLearnMoreModalProps {
  isTablet: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
}

export const ChampionsLearnMoreModal = ({
  isTablet,
  setOpen,
}: ChampionsLearnMoreModalProps) => {
  const handleClickOutside = (event: React.MouseEvent<HTMLDivElement>) => {
    const target = event.currentTarget.querySelector(".target");
    if (target && !target.contains(event.target as Node)) {
      setOpen(false);
    }
  };

  const content = (
    <>
      <div className="mt-6 flex flex-col pb-5 text-off-white md:pb-2">
        <h3 className="mb-2 flex text-base font-semibold leading-6">
          How do I climb the Arena Champion rank?
        </h3>
        <p className="mb-2 flex text-sm leading-5 text-light-gray-text">
          Your points are calculated based on the moving average of staked ARENA
          tokens and are used to determine your rank on the leaderboard.
        </p>
        <p className="mb-2 flex text-sm leading-5 text-light-gray-text">
          The more ARENA you stake and the longer you stake it, the higher your
          points will be.
        </p>
        <p className="flex text-sm leading-5 text-light-gray-text">
          Please note that the current leaderboard is a beta version and should
          be used for reference only. The final leaderboard will use an
          algorithm that weights current staking activity more heavily than past
          staking activity.
        </p>
      </div>
    </>
  );

  return (
    <div
      className={cn(
        "pointer-events-auto absolute inset-0 z-50 flex justify-center bg-[#000] bg-opacity-60",
        isTablet && "items-center",
      )}
      onClick={handleClickOutside}
    >
      <div className="sticky inset-0 flex h-screen items-center">
        <div
          className={cn(
            "target flex flex-col gap-2 rounded-[10px] bg-[#000] bg-opacity-80 backdrop-blur-sm",
            isTablet ? "w-[390px]" : "fixed bottom-0 left-0 right-0 w-full",
          )}
        >
          <div className="overflow-y-auto px-6 pb-6">{content}</div>
        </div>
      </div>
    </div>
  );
};
