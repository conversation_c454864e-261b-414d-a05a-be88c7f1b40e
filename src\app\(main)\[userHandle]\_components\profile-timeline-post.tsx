"use client";

import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { PostUI } from "@/components/post";
import { toast } from "@/components/toast";
import {
  useBookmarkThreadMutation,
  useDeleteRepostMutation,
  useDeleteThreadMutation,
  useLikeThreadMutation,
  useModeratePinThreadMutation,
  usePinThreadMutation,
  useRepostThreadMutation,
  useUnbookmarkThreadMutation,
  useUnlikeThreadMutation,
} from "@/queries";
import { ThreadsResponse } from "@/queries/types";
import { Thread } from "@/types";

interface ProfileTimelinePostProps {
  thread: Thread;
  userId: string;
}

export const ProfileTimelinePost = ({
  thread,
  userId,
}: ProfileTimelinePostProps) => {
  const isRepost = thread.threadType === "repost";
  const activePost = isRepost && thread.repost ? thread.repost : thread;
  const isCommunityPost = !!(activePost.communityId && activePost.community);
  const queryClient = useQueryClient();

  const { mutateAsync: repost } = useRepostThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "user", userId],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "user", userId]);

      queryClient.setQueryData(
        ["threads", "user", userId],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.reposted) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount + 1,
                          reposted: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount + 1,
                      reposted: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "user", userId],
        context?.previousMyFeed,
      );
    },
    onSuccess(_, variables) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.reposted) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount + 1,
                          reposted: true,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount + 1,
                      reposted: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.reposted) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount + 1,
                          reposted: true,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount + 1,
                      reposted: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );
      if (isCommunityPost) {
        queryClient.setQueryData(
          ["home", "threads", "trenches-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.map((t) => {
                    const isRepost = t.threadType === "repost";

                    if (isRepost && t.repost && !t.repost.reposted) {
                      if (t.repost.id === variables.threadId) {
                        return {
                          ...t,
                          repost: {
                            ...t.repost,
                            repostCount: t.repost.repostCount + 1,
                            reposted: true,
                          },
                        };
                      }
                    }

                    if (t.id === variables.threadId) {
                      return {
                        ...t,
                        repostCount: t.repostCount + 1,
                        reposted: true,
                      };
                    }

                    return t;
                  }),
                };
              }),
            };
          },
        );
        queryClient.refetchQueries({
          queryKey: ["threads", "community", activePost.communityId],
        });
      }
    },
  });
  const { mutateAsync: deleteRepost } = useDeleteRepostMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "user", userId],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "user", userId]);

      queryClient.setQueryData(
        ["threads", "user", userId],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.reposted) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount - 1,
                          reposted: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount - 1,
                      reposted: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "user", userId],
        context?.previousMyFeed,
      );
    },
    onSuccess(_, variables) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.reposted) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount - 1,
                          reposted: false,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount - 1,
                      reposted: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.reposted) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount - 1,
                          reposted: false,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount - 1,
                      reposted: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );
      if (isCommunityPost) {
        queryClient.setQueryData(
          ["home", "threads", "trenches-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.map((t) => {
                    const isRepost = t.threadType === "repost";

                    if (isRepost && t.repost && t.repost.reposted) {
                      if (t.repost.id === variables.threadId) {
                        return {
                          ...t,
                          repost: {
                            ...t.repost,
                            repostCount: t.repost.repostCount - 1,
                            reposted: false,
                          },
                        };
                      }
                    }

                    if (t.id === variables.threadId) {
                      return {
                        ...t,
                        repostCount: t.repostCount - 1,
                        reposted: false,
                      };
                    }

                    return t;
                  }),
                };
              }),
            };
          },
        );
        queryClient.refetchQueries({
          queryKey: ["threads", "community", activePost.communityId],
        });
      }
    },
  });
  const { mutateAsync: like } = useLikeThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "user", userId],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "user", userId]);

      queryClient.setQueryData(
        ["threads", "user", userId],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.like) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount + 1,
                          like: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && !t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount + 1,
                      like: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "user", userId],
        context?.previousMyFeed,
      );
    },
    onSuccess(_, variables) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.like) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount + 1,
                          like: true,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && !t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount + 1,
                      like: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.like) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount + 1,
                          like: true,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && !t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount + 1,
                      like: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );
      if (isCommunityPost) {
        queryClient.setQueryData(
          ["home", "threads", "trenches-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.map((t) => {
                    const isRepost = t.threadType === "repost";

                    if (isRepost && t.repost && !t.repost.like) {
                      if (t.repost.id === variables.threadId) {
                        return {
                          ...t,
                          repost: {
                            ...t.repost,
                            likeCount: t.repost.likeCount + 1,
                            like: true,
                          },
                        };
                      }
                    }

                    if (t.id === variables.threadId && !t.like) {
                      return {
                        ...t,
                        likeCount: t.likeCount + 1,
                        like: true,
                      };
                    }

                    return t;
                  }),
                };
              }),
            };
          },
        );
        queryClient.refetchQueries({
          queryKey: ["threads", "community", activePost.communityId],
        });
      }
    },
  });
  const { mutateAsync: unlike } = useUnlikeThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "user", userId],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "user", userId]);

      queryClient.setQueryData(
        ["threads", "user", userId],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.like) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount - 1,
                          like: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount - 1,
                      like: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "user", userId],
        context?.previousMyFeed,
      );
    },
    onSuccess(_, variables) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.like) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount - 1,
                          like: false,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount - 1,
                      like: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.like) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount - 1,
                          like: false,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount - 1,
                      like: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );
      if (isCommunityPost) {
        queryClient.setQueryData(
          ["home", "threads", "trenches-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.map((t) => {
                    const isRepost = t.threadType === "repost";

                    if (isRepost && t.repost && t.repost.like) {
                      if (t.repost.id === variables.threadId) {
                        return {
                          ...t,
                          repost: {
                            ...t.repost,
                            likeCount: t.repost.likeCount - 1,
                            like: false,
                          },
                        };
                      }
                    }

                    if (t.id === variables.threadId && t.like) {
                      return {
                        ...t,
                        likeCount: t.likeCount - 1,
                        like: false,
                      };
                    }

                    return t;
                  }),
                };
              }),
            };
          },
        );
        queryClient.refetchQueries({
          queryKey: ["threads", "community", activePost.communityId],
        });
      }
    },
  });
  const { mutateAsync: bookmark } = useBookmarkThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "user", userId],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "user", userId]);

      queryClient.setQueryData(
        ["threads", "user", userId],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.bookmark) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount + 1,
                          bookmark: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && !t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount + 1,
                      bookmark: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "user", userId],
        context?.previousMyFeed,
      );
    },
    onSuccess(_, variables) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.bookmark) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount + 1,
                          bookmark: true,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && !t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount + 1,
                      bookmark: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.bookmark) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount + 1,
                          bookmark: true,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && !t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount + 1,
                      bookmark: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );
      if (isCommunityPost) {
        queryClient.setQueryData(
          ["home", "threads", "trenches-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.map((t) => {
                    const isRepost = t.threadType === "repost";

                    if (isRepost && t.repost && !t.repost.bookmark) {
                      if (t.repost.id === variables.threadId) {
                        return {
                          ...t,
                          repost: {
                            ...t.repost,
                            bookmarkCount: t.repost.bookmarkCount + 1,
                            bookmark: true,
                          },
                        };
                      }
                    }

                    if (t.id === variables.threadId && !t.bookmark) {
                      return {
                        ...t,
                        bookmarkCount: t.bookmarkCount + 1,
                        bookmark: true,
                      };
                    }

                    return t;
                  }),
                };
              }),
            };
          },
        );
        queryClient.refetchQueries({
          queryKey: ["threads", "community", activePost.communityId],
        });
      }
    },
  });
  const { mutateAsync: unbookmark } = useUnbookmarkThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "user", userId],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "user", userId]);

      queryClient.setQueryData(
        ["threads", "user", userId],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.bookmark) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount - 1,
                          bookmark: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount - 1,
                      bookmark: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "user", userId],
        context?.previousMyFeed,
      );
    },
    onSuccess(_, variables) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.bookmark) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount - 1,
                          bookmark: false,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount - 1,
                      bookmark: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.bookmark) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount - 1,
                          bookmark: false,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount - 1,
                      bookmark: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );
      if (isCommunityPost) {
        queryClient.setQueryData(
          ["home", "threads", "trenches-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.map((t) => {
                    const isRepost = t.threadType === "repost";

                    if (isRepost && t.repost && t.repost.bookmark) {
                      if (t.repost.id === variables.threadId) {
                        return {
                          ...t,
                          repost: {
                            ...t.repost,
                            bookmarkCount: t.repost.bookmarkCount - 1,
                            bookmark: false,
                          },
                        };
                      }
                    }

                    if (t.id === variables.threadId && t.bookmark) {
                      return {
                        ...t,
                        bookmarkCount: t.bookmarkCount - 1,
                        bookmark: false,
                      };
                    }

                    return t;
                  }),
                };
              }),
            };
          },
        );
        queryClient.refetchQueries({
          queryKey: ["threads", "community", activePost.communityId],
        });
      }
    },
  });
  const { mutateAsync: pin } = usePinThreadMutation({
    onMutate: async ({ threadId, isPinned }) => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "user", userId],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "user", userId]);

      queryClient.setQueryData(
        ["threads", "user", userId],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  if (t.id === threadId) {
                    return {
                      ...t,
                      isPinned,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "user", userId],
        context?.previousMyFeed,
      );
    },
  });
  const { mutateAsync: deleteThread } = useDeleteThreadMutation({
    onMutate: async ({ threadId }) => {
      toast.red("Post deleted");
      await queryClient.cancelQueries({
        queryKey: ["threads", "user", userId],
      });
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "trending-feed"],
      });
      if (isCommunityPost) {
        await queryClient.cancelQueries({
          queryKey: ["threads", "community", activePost.communityId],
        });
      }

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "user", userId]);

      queryClient.setQueryData(
        ["threads", "user", userId],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.filter((t) => t.id !== threadId),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "user", userId],
        context?.previousMyFeed,
      );
    },
    onSuccess(_, variables) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.filter(
                  (t) => t.id !== variables.threadId,
                ),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.filter(
                  (t) => t.id !== variables.threadId,
                ),
              };
            }),
          };
        },
      );
      if (isCommunityPost) {
        queryClient.setQueryData(
          ["home", "threads", "trenches-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.filter(
                    (t) => t.id !== variables.threadId,
                  ),
                };
              }),
            };
          },
        );
        queryClient.refetchQueries({
          queryKey: ["threads", "community", activePost.communityId],
        });
      }
    },
  });

  const { mutateAsync: moderateCommunityPinThread } =
    useModeratePinThreadMutation({
      onMutate: async ({ communityId, threadId, pinnedInCommunity }) => {
        await queryClient.cancelQueries({
          queryKey: ["threads", "community", communityId],
        });

        const previousCommunityFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = queryClient.getQueryData([
          "threads",
          "community",
          communityId,
        ]);

        queryClient.setQueryData(
          ["threads", "community", communityId],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.map((t) => {
                    if (t.id === threadId) {
                      return {
                        ...t,
                        pinnedInCommunity,
                      };
                    }

                    return t;
                  }),
                };
              }),
            };
          },
        );

        return { previousCommunityFeed };
      },
      onError(err, variables, context) {
        queryClient.setQueryData(
          ["threads", "community", variables.communityId],
          context?.previousCommunityFeed,
        );
      },
    });

  const handleLike = async ({ threadId }: { threadId: string }) => {
    if (activePost.like) {
      await unlike({ threadId });
    } else {
      await like({ threadId });
    }
  };

  const handleBookmark = async ({ threadId }: { threadId: string }) => {
    if (activePost.bookmark) {
      await unbookmark({ threadId });
    } else {
      await bookmark({ threadId });
    }
  };

  const handleRepost = async ({ threadId }: { threadId: string }) => {
    if (activePost.reposted) {
      await deleteRepost({ threadId });
    } else {
      await repost({ threadId });
    }
  };

  const handlePin = async ({
    threadId,
    isPinned,
  }: {
    threadId: string;
    isPinned: boolean;
  }) => {
    await pin({ threadId, isPinned });
  };

  const handleDelete = async ({ threadId }: { threadId: string }) => {
    await deleteThread({ threadId });
  };

  const handleModerateCommmunityPin = async ({
    communityId,
    threadId,
    pinnedInCommunity,
  }: {
    communityId: string;
    threadId: string;
    pinnedInCommunity: boolean;
  }) => {
    await moderateCommunityPinThread({
      communityId,
      threadId,
      pinnedInCommunity,
    });
  };

  return (
    <PostUI
      thread={thread}
      handleLike={handleLike}
      handleBookmark={handleBookmark}
      handleRepost={handleRepost}
      handlePin={handlePin}
      handleDelete={handleDelete}
      handleModerateCommunityPin={handleModerateCommmunityPin}
      pinnable
      feedContext="profile"
    />
  );
};
