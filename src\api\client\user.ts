import { axios } from "@/lib/axios";
import { UsersWithBadgesByTypeResponse } from "@/queries/types";
import { UprisingTopUsersResponse } from "@/queries/types/uprising-user-leaderboard-response";
import {
  ReferralStatsResponse,
  UpdateUserPreferencesVariables,
  UserPreferencesResponse,
  UserSearchResponse,
} from "@/queries/types/user";
import { Me, User } from "@/types";

export const getMe = async () => {
  const response = await axios.get<{
    user: Me;
  }>("/user/me");
  return response.data;
};

export const getUserByHandle = async (params: { handle: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<{ user: User }>(
    `/user/handle?${searchParams.toString()}`,
  );
  return response.data;
};

export const getUserById = async (params: { userId: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<{ user: User }>(
    `/user/id?${searchParams.toString()}`,
  );
  return response.data;
};

const MAX_ADDRESSES_PER_REQUEST = 100;
export const getUserAddressesForMultiSendBatched = async (
  userIds: string[],
): Promise<string[]> => {
  const allAddresses: string[] = [];
  const batches = [];
  for (let i = 0; i < userIds.length; i += MAX_ADDRESSES_PER_REQUEST) {
    batches.push(userIds.slice(i, i + MAX_ADDRESSES_PER_REQUEST));
  }

  try {
    for (const batch of batches) {
      const addresses = await getUserAddressesForMultiSend(batch);
      allAddresses.push(...addresses);
    }
  } catch (error) {
    console.error("Error fetching user addresses:", error);
    throw error;
  }

  return allAddresses;
};

export const getUserAddressesForMultiSend = async (
  userIds: string[],
): Promise<string[]> => {
  const searchParams = new URLSearchParams({
    userIds: userIds.join(","),
  });

  const response = await axios.get<string[]>(
    `/user/addresses-for-multi-send?${searchParams.toString()}`,
  );
  return response.data;
};

interface UserSearchData {
  searchString: string;
}

export const getUsersSearch = async ({ searchString }: UserSearchData) => {
  const searchParams = new URLSearchParams({
    searchString,
  });

  const response = await axios.get<UserSearchResponse>(
    `/user/search?${searchParams.toString()}`,
  );
  return response.data;
};

export const getUprisingTopBadges = async () => {
  const response = await axios.get("/uprising/badge-leaderboard");
  return response.data;
};

export const getUprisingCurrentUser = async () => {
  const response = await axios.get("/uprising/user");
  return response.data;
};

export const getUprisingQuestCards = async () => {
  const response = await axios.get("/uprising/milestone");
  return response.data;
};

export const getUprisingAnyUser = async (userUuid: string) => {
  const searchParams = new URLSearchParams({
    id: userUuid,
  });
  const response = await axios.get(
    `/uprising/user-info?${searchParams.toString()}`,
  );
  return response.data;
};

export const getTopUsers = async () => {
  const response = await axios.get("/user/top");
  return response.data;
};

export const getUprisingTopUsers = async ({
  page,
  pageSize,
}: {
  page: number;
  pageSize: number;
}) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<UprisingTopUsersResponse>(
    `/uprising/user-leaderboard?${searchParams.toString()}`,
  );
  return response.data;
};

export const getNewUsers = async () => {
  const response = await axios.get("/user/page");
  return response.data;
};

export const getIsUserBlocked = async (params: { userId: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<boolean>(
    `/user/isUserBlocked?${searchParams.toString()}`,
  );
  return response.data;
};

export const getIsBlockedByUser = async (params: { userId: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<boolean>(
    `/user/isBlockedByUser?${searchParams.toString()}`,
  );
  return response.data;
};

export const blockUser = async (params: { userId: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get(`/user/block?${searchParams.toString()}`);
  return response.data;
};

export const unblockUser = async (params: { userId: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get(`/user/unblock?${searchParams.toString()}`);
  return response.data;
};

export const getIsUserBanned = async (params: { userId: string }) => {
  const response = await axios.get<boolean>(`/user/${params.userId}/isBanned`);
  return response.data;
};

export const getIsUserAcceptedAppStore = async (params: { userId: string }) => {
  const response = await axios.get<boolean>(`/user/isAcceptedAppStore`);
  return response.data;
};

export const acceptAppStore = async (params: { userId: string }) => {
  const response = await axios.post(`/user/acceptAppStore`);
  return response.data;
};

export const banUser = async (params: { userId: string }) => {
  const response = await axios.post(`/user/${params.userId}/ban`);
  return response.data;
};

export const unbanUser = async (params: { userId: string }) => {
  const response = await axios.post(`/user/${params.userId}/unban`);
  return response.data;
};

export const postNotifiedClick = async () => {
  const response = await axios.post<boolean>("/user/notifiedClick");
  return response.data;
};

export const getNotifiedClick = async () => {
  const response = await axios.get<boolean>("/user/notifiedClick");
  return response.data;
};

interface Referrer {
  id: string;
  referrerId: string;
  isYourReferrerAnswer: boolean | null;
  twitterHandle: string;
}

export const getReferrers = async () => {
  const response = await axios.get<{ referrers: Referrer[] }>(
    "/user/referrers",
  );
  return response.data;
};

export const getReferralStats = async () => {
  const response = await axios.get<ReferralStatsResponse>(
    "/user/stats/referral",
  );
  return response.data;
};

export const postConfirmReferrer = async (data: {
  referralId: string;
  referrerId: string;
  answer: boolean;
}) => {
  const response = await axios.post(`/user/confirmReferrer`, data);
  return response.data;
};

export const getUserPreferences = async () => {
  const response =
    await axios.get<UserPreferencesResponse>(`/user/preferences`);
  return response.data;
};

export const updateUserPreferences = async ({
  userPreferences,
}: UpdateUserPreferencesVariables) => {
  const response = await axios.post(`/user/preferences`, { userPreferences });
  return response.data;
};

interface PageParams {
  page: number;
  pageSize: number;
}

export const getUsersWithBadgesByType = async (
  type: number,
  pageParam: PageParams,
): Promise<UsersWithBadgesByTypeResponse> => {
  const searchParams = new URLSearchParams({
    badgeType: String(type),
    page: pageParam.page.toString(),
    pageSize: pageParam.pageSize.toString(),
  });

  const response = await axios.get(`/user/badges?${searchParams.toString()}`);
  return response.data;
};

export const connectExternalWallet = async (data: {
  connectedWalletAddress: string;
}) => {
  const response = await axios.post(`/user/connectExternalWallet`, data);
  return response.data;
};

export const getTopCommunities = async () => {
  const response = await axios.get("/communities/top");
  return response.data;
};

export const getOfficialTopCommunities = async () => {
  const response = await axios.get("/communities/top-official");
  return response.data;
};

export const getNewCommunities = async () => {
  const response = await axios.get("/communities/new");
  return response.data;
};

export const getCommunityActivities = async () => {
  const response = await axios.get("/communities/activity");
  return response.data;
};

export const setTutorialShown = async (tutorialType: string): Promise<void> => {
  const response = await axios.post("/user/tutorial-shown", {
    tutorialType,
  });
  return response.data;
};
