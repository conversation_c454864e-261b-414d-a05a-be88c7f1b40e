import { z } from "zod";

import { getCommunityNameCheck } from "@/api/client/group";

export type CreateCommunityFormInputType = z.infer<
  typeof CreateCommunityFormInput
>;

export const CreateCommunityFormInput = z.object({
  name: z.string({ required_error: "Community name is required" }).max(22),
  description: z
    .string()
    .min(1, {
      message: "Community description is required",
    })
    .max(140),
  type: z.enum(["open", "exclusive"]),
  test: z.string({ required_error: "Test name is required" }),
});

export type Step1FormInputType = z.infer<typeof Step1FormInput>;

export const Step1FormInput = z.object({
  name: z
    .string()
    .trim()
    .min(1, { message: "Profile page handle is required" })
    .max(22)
    .regex(
      /^[a-zA-Z0-9_]+$/,
      "Only alphanumeric characters and underscores are allowed",
    ),
  ticker: z
    .string()
    .trim()
    .min(1, { message: "Ticker is required" })
    .max(5, { message: "Ticker must contain atmost 5 character(s)" })
    .regex(/^[a-zA-Z0-9]+$/, "Only alphanumeric characters are allowed")
    .transform((val) => val.toUpperCase()),
  photoURL: z
    .string()
    .trim()
    .min(1, { message: "Please upload token picture" }),
  tokenName: z
    .string()
    .trim()
    .min(1, {
      message: "Token name is required",
    })
    .max(22)
    .regex(
      /^[a-zA-Z0-9 ]+$/,
      "Only alphanumeric characters and spaces are allowed",
    ),
});

export type CreateCommunityFormState = {
  name: string;
  photoURL: string;
  ticker: string;
  tokenName: string;
};

export type EditCommunityFormState = {
  communityId: string;
  name?: string;
  bannerUrl?: string;
  description: string | null;
};

export const EditCommunityFormInput = (existingName: string) =>
  z
    .object({
      name: z
        .string()
        .trim()
        .min(1, {
          message: "Profile page handle is required",
        })
        .max(22)
        .regex(
          /^[a-zA-Z0-9_]+$/,
          "Only alphanumeric characters and underscores are allowed",
        )
        .optional(),
      description: z
        .string()
        .trim()
        .max(110)
        .optional()
        .nullable()
        .default(null),
      bannerUrl: z.string().optional(),
    })
    .superRefine(async (data, ctx) => {
      const name = data.name ?? "";
      if (name !== existingName) {
        const nameAvailable = await getCommunityNameCheck({ name });
        if (!nameAvailable.isAvailable) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ["name"],
            message: "Profile page handle taken. Please choose another one.",
          });
        }
      }
    });

export type EditCommunityFormInputType = z.infer<
  ReturnType<typeof EditCommunityFormInput>
>;
