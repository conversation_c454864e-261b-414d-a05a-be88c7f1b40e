"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useBlockUserLivestreamMutation } from "@/queries";

interface User {
  id: string;
  name: string;
  avatar: string;
  username: string;
  role: string;
}

interface BlockLivestreamUserModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: User;
  livestreamId: string;
}

export const BlockLivestreamUserModal = ({
  open,
  setOpen,
  user,
  livestreamId,
}: BlockLivestreamUserModalProps) => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  // const { sendInvalidateStageInfo } = useDataChannelsContext();
  const { mutateAsync: blockUser, isPending } = useBlockUserLivestreamMutation({
    onSuccess: () => {
      // sendInvalidateStageInfo();
    },
  });

  const handleBlock = async () => {
    setOpen(false);
    await blockUser({ livestreamId, userId: user.id });
  };

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-sm">
          <BlockLivestreamUserModalContent
            user={user}
            setOpen={setOpen}
            isPending={isPending}
            handleBlock={handleBlock}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="justify-start gap-6 text-left">
        <BlockLivestreamUserModalContent
          user={user}
          setOpen={setOpen}
          isPending={isPending}
          handleBlock={handleBlock}
        />
      </DrawerContent>
    </Drawer>
  );
};

interface BlockLivestreamUserModalContentProps {
  user: User;
  setOpen: (open: boolean) => void;
  isPending: boolean;
  handleBlock: () => void;
}

const BlockLivestreamUserModalContent = ({
  user,
  setOpen,
  isPending,
  handleBlock,
}: BlockLivestreamUserModalContentProps) => {
  return (
    <>
      <DialogHeader className="space-y-4">
        <DialogTitle>Remove @{user.username}?</DialogTitle>
        <DialogDescription className="text-gray-text">
          @{user.username} will not be able to join this Live Stream again. Are
          you sure you want to proceed?
        </DialogDescription>
      </DialogHeader>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => {
            setOpen(false);
          }}
        >
          Cancel
        </Button>
        <Button
          variant="destructive"
          className="flex-1"
          onClick={handleBlock}
          disabled={isPending}
        >
          Remove
        </Button>
      </div>
    </>
  );
};
