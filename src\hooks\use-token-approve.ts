import { useCallback, useState } from "react";

import { MaxUint256 } from "ethers";
import { Address } from "viem";

import { ERC20_CONTRACT_ABI } from "@/environments/BACKEND_FRIENDS_CONTRACT";

export const useTokenApprove = (
  wallet: any,
  tokenAddress: string | undefined,
  spenderAddress: string | undefined,
) => {
  const [isApproving, setIsApproving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const approveMax = useCallback(async () => {
    if (!wallet || !tokenAddress || !spenderAddress) return;
    setIsApproving(true);
    try {
      const walletClient = await wallet.getWalletClient();
      const tx = await walletClient.writeContract({
        address: tokenAddress as Address,
        abi: ERC20_CONTRACT_ABI,
        functionName: "approve",
        args: [spenderAddress, MaxUint256],
        account: wallet.address as Address,
      });
      setError(null);
      return tx;
    } catch (e: any) {
      setError(e.message || "Approve error");
      throw e;
    } finally {
      setIsApproving(false);
    }
  }, [wallet, tokenAddress, spenderAddress]);

  return { approveMax, isApproving, error };
};
