"use client";

import { useEffect } from "react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";

import { setTutorialShown } from "@/api/client/user";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useUser } from "@/stores";
import { useTutorialStore } from "@/stores/tutorial";
import { cn } from "@/utils";

import { TicketOutlineIcon, UserGroupOutlineIcon } from "../icons";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
} from "../ui/alert-dialog";
import { Button } from "../ui/button";

export const TutorialDialog = () => {
  const pathname = usePathname();
  const router = useRouter();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const { user, updateUser } = useUser();
  const isTutorialOpen = useTutorialStore((state) => state.isTutorialOpen);
  const actions = useTutorialStore((state) => state.actions);
  const isStep1 = pathname === "/" + user?.twitterHandle;
  const isStep2 = pathname === "/wallet";
  const isStep3 = pathname === "/compose/post";
  const isOpen = isTutorialOpen && (isStep1 || isStep2 || isStep3);

  const step = isStep1 ? 1 : isStep2 ? 2 : 3;

  useEffect(() => {
    if (user?.tutorial?.profileTutorialShown) {
      actions.setIsTutorialOpen(false);
    }
  }, [user, actions]);

  const handleUpdate = () => {
    if (!user) return;
    updateUser({
      ...user,
      tutorial: {
        ...user.tutorial,
        profileTutorialShown: true,
      },
    });
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={actions.setIsTutorialOpen}>
      <AlertDialogContent
        overlayNoBG
        className={cn(
          "w-11/12 max-w-md translate-y-0 rounded-[10px] border border-off-white/20 p-6 text-off-white shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)]",
          isTablet
            ? "items-center justify-center"
            : "bottom-[calc(70px+env(safe-area-inset-bottom))] top-auto",
        )}
      >
        {isStep1 && <Step1 />}
        {isStep2 && <Step2 />}
        {isStep3 && <Step3 />}
        <AlertDialogFooter className="mt-2 flex-row items-baseline gap-2 pb-2">
          {step < 3 && (
            <Button
              onClick={() => {
                actions.setIsTutorialOpen(false);
                setTutorialShown("profile");
                handleUpdate();
              }}
              variant="outline"
              className="h-9 w-1/3 text-off-white"
            >
              Skip
            </Button>
          )}
          <Button
            onClick={() => {
              if (step === 1) {
                router.push("/wallet");
              }
              if (step === 2) {
                router.push("/compose/post");
              }
              if (step === 3) {
                actions.setIsTutorialOpen(false);
                setTutorialShown("profile");
                handleUpdate();
              }
            }}
            className={`${step === 3 ? "w-full" : "w-2/3"} h-9 bg-orange-gradient`}
          >
            {step === 3 ? "Done" : `Next ${step}/3`}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

const Step1 = () => {
  return (
    <div className="flex flex-col items-center text-center">
      <TicketOutlineIcon className="h-6 w-6 text-off-white" />
      <h2 className="mt-2 text-base font-semibold text-off-white">
        See that over there? That&apos;s a ticket.
      </h2>
      <p className="mt-4 text-sm text-gray-text ">
        They give you exclusive access to:
      </p>
      <ul className="mt-2 flex list-inside list-disc flex-col items-start text-sm text-gray-text">
        <li>Private Chatroom with other holders.</li>
        <li>Private Stages that a user host.</li>
        <li>Any Stickers created by this user.</li>
        <li>And much more coming soon!</li>
      </ul>
    </div>
  );
};

const Step2 = () => {
  return (
    <div className="flex flex-col items-center text-center">
      <Image
        src="/assets/coins/avax.png"
        alt="AVAX icon"
        width={30}
        height={30}
        className="size-6 rounded-full"
      />
      <h2 className="mt-2 text-base font-semibold text-off-white">
        Earn AVAX to use across the app!
      </h2>
      <p className="mt-4 text-sm text-gray-text ">
        Each time other users buy or sell your ticket, trading fees are sent to
        your way.
      </p>
      <p className="mt-2 text-sm text-gray-text ">
        You can use them to buy tickets, tip users, or withdraw them to another
        wallet!
      </p>
    </div>
  );
};

const Step3 = () => {
  return (
    <div className="flex flex-col items-center text-center">
      <UserGroupOutlineIcon className="size-6 text-off-white" />
      <h2 className="mt-2 text-base font-semibold text-off-white">
        Start building your network
      </h2>
      <p className="mt-4 text-sm text-gray-text ">
        We take pride in being one of the nicest communities of all social media
        platforms!
      </p>
      <p className="mt-2 text-sm text-gray-text ">
        Tell us a bit about yourself and see how quickly you&apos;ll start
        connecting with others.
      </p>
    </div>
  );
};
