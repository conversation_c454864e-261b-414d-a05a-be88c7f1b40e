import { AVAX } from "@/environments/tokens";
import { SharesHolder } from "@/queries/types";
import { formatAvax, numberFormatter } from "@/utils";

import { ProgressBarLink } from "./progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";

export const ShareHoldingItem = ({ holder }: { holder: SharesHolder }) => {
  return (
    <ProgressBarLink
      href={`/${holder.subjectUser?.twitterHandle}`}
      className="flex items-center justify-between gap-[10px] px-6 py-4"
    >
      <Avatar className="size-[42px]">
        <AvatarImage src={holder.subjectUser?.twitterPicture} />
        <AvatarFallback />
      </Avatar>
      <div className="flex w-full flex-col items-center gap-[4px]">
        <div className="flex w-full flex-row justify-between text-[14px] leading-4">
          <h4 className="flex text-[#F4F4F4]">
            {holder.subjectUser?.twitterName}
          </h4>
          <h4 className="flex text-[#F4F4F4]">{holder.amount}</h4>
        </div>
        <div className="flex w-full flex-row justify-between text-[14px] leading-4">
          <p className="flex text-[#808080]">
            @{holder.subjectUser?.twitterHandle}
          </p>
          <div className="flex items-center gap-[4px] text-[#808080]">
            <img
              src={AVAX.icon}
              className="size-3 rounded-full"
              alt="AVAX logo"
            />
            <span>
              {numberFormatter.format(
                Number(holder.amount) *
                  Number(formatAvax(holder?.subjectUser?.keyPrice || "0")),
              )}
            </span>
          </div>
        </div>
      </div>
    </ProgressBarLink>
  );
};
