"use client";

import { useEffect, useRef, useState } from "react";

import { ShieldIcon } from "@/components/icons-v2/account-security";
import { Button } from "@/components/ui/button";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

interface IDeleteMfaModalProps {
  onSubmit: (code: string) => void;
  onClose: () => void;
}

export const DeleteMfaModal = (props: IDeleteMfaModalProps) => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const VERIFICATION_LETTER_COUNT = 6;
  const [verificationParts, setVerificationParts] = useState(
    Array(VERIFICATION_LETTER_COUNT).fill(""),
  );
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const handleInputChange = (
    index: number,
    event: React.FormEvent<HTMLInputElement>,
  ) => {
    const value = (event.target as HTMLInputElement).value.toUpperCase();
    if (value.length === 1) {
      inputRefs.current[index]!.value = value;
      if (index < inputRefs.current.length - 1) {
        inputRefs.current[index + 1]?.focus();
        inputRefs.current[index + 1]?.select();
      }
      const newVerificationParts = [...verificationParts];
      newVerificationParts[index] = value;
      setVerificationParts(newVerificationParts);
    } else {
      inputRefs.current[index]!.value = value.slice(-1);
      const newVerificationParts = [...verificationParts];
      newVerificationParts[index] = value.slice(-1);
      setVerificationParts(newVerificationParts);
    }
  };

  const onKeyDown = (
    index: number,
    event: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (event.key === "Backspace" && !event.currentTarget.value) {
      if (index > 0) {
        inputRefs.current[index - 1]?.focus();
        inputRefs.current[index - 1]?.select();
      }
    }
  };

  const submit = () => {
    props.onSubmit(verificationParts.join(""));
  };

  return (
    <div
      className={
        isTablet
          ? "fixed inset-0 z-50 flex content-center items-center justify-center"
          : ""
      }
    >
      <div
        className="bg-black fixed inset-0 z-40 bg-[#020202CC]"
        onClick={props.onClose}
      />
      <div
        className={`fixed z-50 border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] px-[24px] pb-[48px] pt-[24px] shadow-[0px_4px_16px_0px_rgba(0,0,0,0.25)] ${isTablet ? "rounded-[20px]" : "bottom-[0px] w-full rounded-t-[20px]"}`}
        style={{ backgroundColor: "rgba(15, 15, 15, 0.90)" }}
      >
        <div className="justify-left flex flex-col items-center gap-[32px]">
          <div className="font-inter flex flex-col items-center gap-[8px] self-stretch text-[16px] font-semibold leading-[22px] text-[#F4F4F4]">
            <ShieldIcon />
            <p>Confirm verification code</p>
          </div>
          <div className="flex flex-col items-start gap-[24px] self-stretch">
            <div className="font-inter self-stretch text-[14px] font-normal leading-[21px] text-[var(--LIGHT-GRAY-TEXT,#B5B5B5)]">
              Enter the verification code generated by your authenticator app
            </div>
            <div className="flex items-center justify-between self-stretch px-[8px]">
              {Array.from({ length: VERIFICATION_LETTER_COUNT }).map(
                (_, index) => (
                  <input
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]"
                    key={index}
                    ref={(el) => {
                      inputRefs.current[index] = el;
                    }}
                    maxLength={1}
                    className="selection-transparent flex h-[45px] w-[45px] select-none items-center justify-between rounded-[10px] border border-[var(--GRAY-TEXT,#808080)] bg-[var(--LIGHT-BACKGROUND,#1A1A1A)] p-[16px] text-center caret-transparent opacity-85"
                    placeholder="-"
                    onInput={(event) => handleInputChange(index, event)}
                    onClick={(event) => event.currentTarget.select()}
                    onKeyDown={(event) => onKeyDown(index, event)}
                  />
                ),
              )}
            </div>
          </div>
          <div className="flex-space-around flex w-full items-center gap-[8px]">
            <Button
              className="flex h-[44px] w-full items-center justify-center rounded-[40px] bg-[var(--ORANGE-GRADIENT,#D64C05)] px-[60px] py-[8px]"
              onClick={submit}
              disabled={verificationParts.join("").length !== 6}
            >
              <p className="font-inter text-center text-[14px] font-semibold leading-[20px] text-[var(--OFF-WHITE,#F4F4F4)]">
                Continue
              </p>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
