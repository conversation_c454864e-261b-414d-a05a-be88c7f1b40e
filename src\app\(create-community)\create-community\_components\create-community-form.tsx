"use client";

import { useState } from "react";

import { PageHeader } from "@/app/_components/page-header";

import { CreateCommunity } from "./create-community";
import { CreateCommunityFormState } from "./create-community-form-input";
import { CreateCommunityStep1 } from "./step-1";

const headerMessages: Record<number, string> = {
  [1]: "Create your Token",
  [2]: "Back to Step 1",
};

export const CreateCommunityForm = () => {
  const [step, setStep] = useState<number>(1);
  const [formState, setFormState] = useState<CreateCommunityFormState>({
    name: "",
    photoURL: "",
    ticker: "",
    tokenName: "",
  });

  const handleNext = () => {
    if (step <= 1) {
      setStep(step + 1);
    }
  };

  const handleUpdateState = (params: Partial<CreateCommunityFormState>) => {
    setFormState({
      ...formState,
      ...params,
    });
  };

  return (
    <>
      <PageHeader hint={headerMessages[step]} isSticky isBorder />
      <div className="mt-4 flex flex-1 flex-col gap-3 px-6">
        {step === 1 && (
          <CreateCommunityStep1
            onNext={handleNext}
            onUpdateState={handleUpdateState}
            formState={formState}
          />
        )}
        {step === 2 && <CreateCommunity formState={formState} />}
      </div>
    </>
  );
};
