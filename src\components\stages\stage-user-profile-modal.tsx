"use client";

import { memo, useMemo, useState } from "react";
import Image from "next/image";

import {
  useLocalParticipant,
  useParticipantAttribute,
} from "@livekit/components-react";
import { useQuery } from "@tanstack/react-query";
import DOMPurify from "isomorphic-dompurify";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { userQueries, useSharesStatsQuery } from "@/queries";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";
import { User } from "@/types";
import { checkContent, cn, formatAvax } from "@/utils";

import { BanOutlineIcon } from "../icons";
import { ProgressBarLink } from "../progress-bar";
import { TradeTicketsModal } from "../trade-tickets-modal";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { But<PERSON> } from "../ui/button";
import { Drawer, DrawerContent, DrawerTrigger } from "../ui/drawer";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { BlockStageUserModal } from "./block-stage-user-modal";
import { ROLES } from "./constants";
import { CustomTipModal } from "./stage-custom-tip-modal";
import { StageTipModal } from "./stage-tip-modal";
import { useDataChannelsContext } from "./stores/data-channels-context";

// ["⚔️", "🔥", "💯", "👏", "🤯"],
// ["🤣", "🫡", "👍", "👎", "🤡"],
const emotes = [
  ["2694-fe0f", "1f525", "1f4af", "1f44f", "1f92f"],
  ["1f923", "1fae1", "1f44d", "1f44e", "1f921"],
];

interface StageUserProfileModalProps {
  user: {
    id: string;
    name: string;
    avatar: string;
    username: string;
    role: string;
  };
  isMuted: boolean;
  children?: React.ReactNode;
  open?: boolean;
  setOpen?: (open: boolean) => void;
}

const StageUserProfileModalComponent = ({
  children,
  user,
  isMuted,
  open: externalOpen,
  setOpen: setExternalOpen,
}: StageUserProfileModalProps) => {
  const isLaptop = useMediaQuery(BREAKPOINTS.lg);
  const [internalOpen, setInternalOpen] = useState(false);
  const [customTipOpen, setCustomTipOpen] = useState(false);
  const [isBlockOpen, setIsBlockOpen] = useState(false);
  const stageId = useStageStore((state) => state.id!);

  const [open, setOpen] = useMemo(() => {
    if (externalOpen !== undefined && setExternalOpen !== undefined) {
      return [externalOpen, setExternalOpen];
    }
    return [internalOpen, setInternalOpen];
  }, [externalOpen, setExternalOpen, internalOpen, setInternalOpen]);

  const memoizedUser = useMemo(() => user, [user]);
  const { data: userData, isLoading: isUserDataLoading } = useQuery({
    ...userQueries.byHandle(memoizedUser.username),
    enabled: !!memoizedUser.username && open,
  });
  const { data: statsData, isLoading: isStatsDataLoading } =
    useSharesStatsQuery({
      userId: open ? memoizedUser?.id : undefined,
    });

  const profileDescription = useMemo(() => {
    if (!userData) return "";

    const [content] = checkContent({
      content: userData?.user.twitterDescription ?? "",
      truncate: false,
    });

    return DOMPurify.sanitize(content);
  }, [userData]);

  const ticketPrice = useMemo(() => {
    if (isUserDataLoading || isStatsDataLoading) return "";

    const formattedEther = formatAvax(
      statsData?.stats?.keyPrice ?? userData?.user?.keyPrice ?? "",
    );

    if (parseFloat(formattedEther) >= 1) {
      return parseFloat(formattedEther).toFixed(2);
    }

    return formattedEther;
  }, [
    statsData?.stats?.keyPrice,
    userData?.user?.keyPrice,
    isUserDataLoading,
    isStatsDataLoading,
  ]);

  const handleBlockUser = async () => {
    setOpen(false);
    setIsBlockOpen(true);
  };

  if (isLaptop) {
    return (
      <>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>{children}</PopoverTrigger>
          <PopoverContent className="w-[350px] overflow-hidden p-0">
            {userData && userData.user && (
              <StageUserProfileModalContent
                user={userData.user}
                isUserDataLoading={isUserDataLoading}
                profileDescription={profileDescription}
                ticketPrice={ticketPrice}
                setOpen={setOpen}
                setCustomTipOpen={setCustomTipOpen}
                customTipOpen={customTipOpen}
                stageId={stageId}
                isMuted={isMuted}
                role={memoizedUser.role}
                handleBlockUser={handleBlockUser}
              />
            )}
          </PopoverContent>
        </Popover>
        <BlockStageUserModal
          open={isBlockOpen}
          setOpen={setIsBlockOpen}
          user={memoizedUser}
          stageId={stageId}
        />
      </>
    );
  }

  return (
    <>
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerTrigger asChild>{children}</DrawerTrigger>
        <DrawerContent className="pb-pwa overflow-hidden rounded-t-xl p-0">
          {userData && userData.user && (
            <StageUserProfileModalContent
              user={userData.user}
              isUserDataLoading={isUserDataLoading}
              profileDescription={profileDescription}
              ticketPrice={ticketPrice}
              setOpen={setOpen}
              setCustomTipOpen={setCustomTipOpen}
              customTipOpen={customTipOpen}
              stageId={stageId}
              isMuted={isMuted}
              role={memoizedUser.role}
              handleBlockUser={handleBlockUser}
            />
          )}
        </DrawerContent>
      </Drawer>
      <BlockStageUserModal
        open={isBlockOpen}
        setOpen={setIsBlockOpen}
        user={memoizedUser}
        stageId={stageId}
      />
    </>
  );
};

export const StageUserProfileModal = memo(StageUserProfileModalComponent);

const StageUserProfileModalContent = ({
  user,
  profileDescription,
  ticketPrice,
  setOpen,
  setCustomTipOpen,
  customTipOpen,
  stageId,
  isUserDataLoading,
  isMuted,
  role,
  handleBlockUser,
}: {
  stageId: string;
  user: User;
  profileDescription: string;
  ticketPrice: string;
  setOpen: (open: boolean) => void;
  setCustomTipOpen: (open: boolean) => void;
  customTipOpen: boolean;
  isUserDataLoading: boolean;
  isMuted: boolean;
  role: string;
  handleBlockUser: () => void;
}) => {
  const local = useLocalParticipant();
  const myRole = useParticipantAttribute("role", {
    participant: local.localParticipant,
  });
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const { user: me } = useUser();
  const { sendMuteMic } = useDataChannelsContext();
  const actions = useStageStore((state) => state.actions);

  const onProfileClick = () => {
    setOpen(false);
    if (!isTablet) {
      actions.setFullScreen(false);
    }
  };

  return (
    <>
      <div
        className="aspect-[4/1] w-full bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${user.bannerUrl})`,
        }}
      ></div>
      <div className={cn("px-5 ", me?.id === user.id ? "pb-2" : "pb-8")}>
        <div className="-mt-5 flex items-end gap-5">
          <ProgressBarLink
            href={`/${user?.twitterHandle}`}
            onClick={onProfileClick}
          >
            <Avatar className="size-[40px] border border-off-white">
              <AvatarImage src={user.twitterPicture} />
              <AvatarFallback />
            </Avatar>
          </ProgressBarLink>
        </div>
        <div className="mt-2 flex flex-col gap-0.5">
          <ProgressBarLink
            href={`/${user?.twitterHandle}`}
            onClick={onProfileClick}
            className="flex items-center gap-1.5 text-base font-medium leading-5 text-off-white"
          >
            {user.twitterName}
          </ProgressBarLink>
          <ProgressBarLink
            href={`/${user?.twitterHandle}`}
            onClick={onProfileClick}
            className="text-xs text-gray-text"
          >
            @{user.twitterHandle}
          </ProgressBarLink>
        </div>
        <div
          className="post-content mt-3 text-sm text-off-white"
          dangerouslySetInnerHTML={{
            __html: profileDescription,
          }}
        />
        {me?.id !== user.id && (
          <>
            <div className="mt-4 flex items-center gap-2">
              {(myRole === ROLES.HOST || myRole === ROLES.COHOST) &&
              (role === ROLES.LISTENER || role === ROLES.SPEAKER) ? (
                <Button
                  variant="outline"
                  className="size-9"
                  onClick={handleBlockUser}
                >
                  <BanOutlineIcon
                    strokeWidth={1.5}
                    className="size-5 flex-shrink-0 text-gray-text"
                  />
                </Button>
              ) : null}
              {(myRole === ROLES.HOST || myRole === ROLES.COHOST) &&
              (role === ROLES.COHOST || role === ROLES.SPEAKER) ? (
                <Button
                  variant="outline"
                  className="flex-1 gap-1 py-2"
                  disabled={isMuted}
                  onClick={() => {
                    sendMuteMic({
                      identity: user.id,
                    });
                  }}
                >
                  Mute
                </Button>
              ) : (
                <TradeTicketsModal userHandle={user.twitterHandle}>
                  <Button className="flex-1 gap-1 py-2">
                    Buy{" "}
                    <Image
                      src="/assets/coins/avax.png"
                      className="ml-0.5 size-3 rounded-full brightness-110 grayscale"
                      alt={`AVAX logo`}
                      width={12}
                      height={12}
                    />
                    <span className="text-xs font-medium leading-5 text-off-white">
                      {ticketPrice}
                    </span>
                  </Button>
                </TradeTicketsModal>
              )}
              <StageTipModal user={user} setCustomTipOpen={setCustomTipOpen}>
                <Button
                  variant="outline"
                  className="flex-1 gap-1 border-brand-orange py-2"
                >
                  Tip
                </Button>
              </StageTipModal>
            </div>
            {/* <div className="mt-5 text-center text-xs text-gray-text">
              Send them an emoji
            </div>
            <div className="mt-5 flex flex-col items-center justify-center gap-3">
              {emotes.map((row, i) => {
                return (
                  <div className="flex items-center gap-3" key={i + "emoji"}>
                    {row.map((emoji, j) => (
                      <button
                        key={i + j}
                        className="flex size-8 items-center justify-center"
                        onClick={() => {
                          if (!me) return;

                          sendEmote({
                            emote: emoji,
                            id: Date.now(),
                            from: {
                              id: me?.id,
                              name: me?.twitterName,
                              avatar: me?.twitterPicture,
                              username: me?.twitterHandle,
                            },
                            to: user.id,
                          });
                          setOpen(false);
                        }}
                      >
                        <Emoji unified={emoji} size={32} />
                      </button>
                    ))}
                  </div>
                );
              })}
            </div> */}
          </>
        )}
      </div>
      <CustomTipModal
        userToSend={user}
        open={customTipOpen}
        setOpen={setCustomTipOpen}
      />
    </>
  );
};
