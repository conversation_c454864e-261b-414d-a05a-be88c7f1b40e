"use client";

import { ComponentProps, useEffect, useRef } from "react";

interface ContentEditableProps extends Omit<ComponentProps<"div">, "onChange"> {
  value: string;
  setHtmlContent: (value: string) => void;
  setTextContent: (value: string) => void;
}

export const ContentEditable = ({
  value,
  setHtmlContent,
  setTextContent,
  ...props
}: ContentEditableProps) => {
  const contentEditableRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (contentEditableRef.current === null) return;
    if (contentEditableRef.current.innerHTML !== value) {
      contentEditableRef.current.innerHTML = value;
    }
  }, [value]);

  return (
    <div
      contentEditable="true"
      content={value}
      ref={contentEditableRef}
      onInput={(event) => {
        setHtmlContent((event.target as HTMLDivElement).innerHTML || "");
        setTextContent((event.target as HTMLDivElement).textContent || "");
      }}
      {...props}
    />
  );
};
