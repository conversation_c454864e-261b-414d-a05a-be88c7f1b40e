import Skeleton from "react-loading-skeleton";

import { RewardCoin } from "@/app/(main)/champions/_components/reward-coin";
import { IReward<PERSON>oin } from "@/queries/types/token-portal";

interface RewardSectionParams {
  title?: string;
  rewards: IRewardCoin[] | undefined;
  isLoading: boolean;
}

const LoadingSkeleton = () => {
  return (
    <div className="flex w-full justify-between gap-6">
      <div className="flex items-center gap-[10px]">
        <Skeleton circle className="size-[30px]" />
        <div className="flex w-full flex-col leading-3">
          <Skeleton className="h-[14px] w-20" />
          <Skeleton className="mt-[6px] h-[12px] w-28" />
        </div>
      </div>
      <div className="flex w-full flex-col items-end leading-3">
        <Skeleton className="h-[14px] w-28" />
        <Skeleton className="mt-[6px] h-[14px] w-20" />
      </div>
    </div>
  );
};

export const RewardSection = ({
  title,
  rewards,
  isLoading,
}: RewardSectionParams) => (
  <div className="flex flex-col">
    {title && (
      <div className="mb-6 text-xs font-semibold text-gray-text">{title}</div>
    )}
    <div className="flex flex-col gap-6">
      {isLoading &&
        Array.from({ length: 2 }).map((_, index) => {
          return <LoadingSkeleton key={index} />;
        })}
      {!isLoading &&
        rewards &&
        rewards.map((coin, index) => (
          <RewardCoin
            key={index}
            amount={coin.amount}
            name={coin.name}
            symbol={coin.symbol}
            systemRate={coin.systemRate}
            image={coin.image}
          />
        ))}
    </div>
  </div>
);
