import { useCallback, useEffect, useState } from "react";

import { Address } from "viem";

import { ERC20_CONTRACT_ABI } from "@/environments/BACKEND_FRIENDS_CONTRACT";

export const useTokenAllowance = (
  wallet: any,
  tokenAddress: string | undefined,
  spenderAddress: string | undefined,
) => {
  const [allowance, setAllowance] = useState<bigint>(0n);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAllowance = useCallback(async () => {
    if (!wallet || !tokenAddress || !spenderAddress) return;
    setIsLoading(true);
    try {
      const publicClient = await wallet.getPublicClient();
      const _allowance = await publicClient.readContract({
        address: tokenAddress as Address,
        abi: ERC20_CONTRACT_ABI,
        functionName: "allowance",
        args: [wallet.address, spenderAddress],
      });
      setAllowance(_allowance as bigint);
      setError(null);
    } catch (e: any) {
      setError(e.message || "Allowance error");
    }
    setIsLoading(false);
  }, [wallet, tokenAddress, spenderAddress]);

  useEffect(() => {
    fetchAllowance();
  }, [fetchAllowance]);

  return { allowance, isLoading, error, fetchAllowance };
};
