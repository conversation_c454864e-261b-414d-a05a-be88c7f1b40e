"use client";

import { useEffect } from "react";

import { isSupported } from "firebase/messaging";
import { useCookies } from "react-cookie";
import { v4 as uuid } from "uuid";

import { isIOS } from "@/components/ui/vaul/browser";
import { generateToken } from "@/lib/firebase";
import { useCloudMessagingSettings } from "@/queries";

export default function CloudMessaging() {
  const [cookies, setCookie] = useCookies(["__uv"]);
  const { data: cloudData } = useCloudMessagingSettings();

  useEffect(() => {
    const uv = uuid() + "-" + Date.now().toString(16);
    cookies.__uv ||
      setCookie("__uv", uv, {
        expires: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      });
  }, [cookies, setCookie]);

  useEffect(() => {
    async function refreshFCMToken() {
      const supported = await isSupported();
      if (!supported) {
        return;
      }

      const IOS = isIOS();
      const isPWA =
        (window.matchMedia &&
          window.matchMedia("(display-mode: standalone)").matches) ||
        (window.navigator && (window.navigator as any).standalone === true);

      // iOS must be in PWA mode
      if (IOS && !isPWA) return;

      if (
        Notification.permission === "granted" &&
        cloudData?.settings?.notificationsEnabled &&
        cookies.__uv
      ) {
        await generateToken(cookies.__uv);
      }
    }

    if (
      typeof window !== "undefined" &&
      "Notification" in window &&
      "serviceWorker" in navigator
    ) {
      if (Notification.permission === "granted") {
        refreshFCMToken();
      }
    } else {
      console.error("This browser does not support push notifications");
    }
  }, [cloudData?.settings?.notificationsEnabled, cookies.__uv]);

  return null;
}
