import { useState } from "react";

import { BanOutlineIcon } from "@/components/icons";
import { ChatbubbleOutlineIcon } from "@/components/icons-v2";
import { ChatIcon } from "@/components/icons-v2/chat-icon";
import { GroupIcon } from "@/components/icons-v2/group-logo";
import { HandleOkOutlineIcon } from "@/components/icons-v2/handle-ok";
import { TipPlusOutlineIcon } from "@/components/icons-v2/tip-outline-groups";
import { TokenPhase1OutlineIcon } from "@/components/icons-v2/token-phase-1-outline";
import { ProgressBarLink } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerContent,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { abbreviateNumber } from "@/utils";

import {
  HANDLE_PHASE_MARKET_CAP_THRESHOLD,
  TIPPING_PHASE_MARKET_CAP_THRESHOLD,
  TOKEN_PHASE_LIQUIDITY_THRESHOLD,
} from "./consts";

const ModalContainer = ({ children }: { children: React.ReactNode }) => (
  <div className="px-6">
    <div
      className="mb-8 mt-10 flex flex-col items-center justify-start gap-6
      rounded-[10px] border border-[#2a2a2a] bg-[#191919] px-4 py-6"
    >
      {children}
    </div>
  </div>
);

const ModalContent = ({
  icon,
  title,
  description,
}: {
  icon: JSX.Element;
  title: string[];
  description: string;
}) => (
  <div className="flex w-full flex-col items-center justify-start gap-2">
    <div className="relative h-6 w-6">{icon}</div>
    <div className="mb-4 text-center text-base font-semibold leading-tight text-[#ececec]">
      {title.map((line, index) => (
        <span key={index}>
          {line}
          {index !== title.length - 1 && <br />}
        </span>
      ))}
    </div>
    <div className="flex w-full flex-col items-start justify-start gap-2">
      <div className="text-center text-sm font-normal text-[#B4B4B4]">
        <span>{description}</span>
      </div>
    </div>
  </div>
);

export const FirstPostModal = ({
  buttonRender,
}: {
  buttonRender: () => JSX.Element;
}) => {
  return (
    <ModalContainer>
      <div className="flex w-full flex-col items-center justify-start gap-2">
        <div className="relative h-6 w-6">
          <ChatIcon />
        </div>
        <div className="mb-4 text-center text-base font-semibold leading-tight text-[#ececec]">
          This looks kinda empty...
        </div>
        <div className="flex w-full flex-col items-start justify-start gap-2">
          <div className="text-center text-sm font-normal text-[#B4B4B4]">
            <span>Make your first community post!</span>
            <br />
            <span className="mt-3 flex">
              Anything you post in here will also be posted in your profile and
              the home feed.
            </span>
          </div>
          <div className="mt-6 flex w-full justify-center">
            {buttonRender()}
          </div>
        </div>
      </div>
    </ModalContainer>
  );
};

export const ToPhaseTokenModal = () => {
  return (
    <div
      className="flex flex-col items-center justify-start gap-6 
      rounded-[10px] border border-[#2a2a2a] bg-[#191919] px-8 py-6"
    >
      <div className="flex w-full flex-col items-center justify-start gap-2">
        <div className="relative h-6 w-6">
          <TokenPhase1OutlineIcon />
        </div>
        <div className="mb-4 text-center text-base font-semibold leading-tight text-[#ececec]">
          <span>
            {`Reach ${TOKEN_PHASE_LIQUIDITY_THRESHOLD} AVAX to graduate your token!`}
          </span>
        </div>
        <div className="flex w-full flex-col items-start justify-start gap-2">
          <div className="flex flex-col gap-2 text-center text-sm font-normal text-[#B4B4B4]">
            <h3>
              {`Once a token has graduated, it will be migrated to ArenaDex where it will be freely tradable outside of The Arena.`}
            </h3>
          </div>
        </div>
      </div>
    </div>
  );
};

export const ProjectChatEmptyModal = () => {
  return (
    <ModalContainer>
      <ModalContent
        icon={<ChatbubbleOutlineIcon />}
        title={[`Community Chats`]}
        description="Join the exclusive community chats for all new projects that come out of The Arena by holding tokens and earning badges."
      />
    </ModalContainer>
  );
};

export const ToPhaseHandleModal = () => {
  return (
    <div
      className="flex flex-col items-center justify-start gap-6 
      rounded-[10px] border border-[#2a2a2a] bg-[#191919] px-8 py-6"
    >
      <div className="flex w-full flex-col items-center justify-start gap-2">
        <div className="relative h-6 w-6">
          <HandleOkOutlineIcon />
        </div>
        <div className="mb-4 text-center text-base font-semibold leading-tight text-[#ececec]">
          <span>
            {`Reach a $${abbreviateNumber(HANDLE_PHASE_MARKET_CAP_THRESHOLD, 1, true, { padding: false })} market cap to secure a handle!`}
          </span>
        </div>
        <div className="flex w-full flex-col items-start justify-start gap-2">
          <div className="flex flex-col gap-2 text-center text-sm font-normal text-[#B4B4B4]">
            <>
              <h3>
                {`Say goodbye to those ugly looking URLs that have a contract address.`}
              </h3>
              <h3>
                {`Users will now be able to find you on arena.social/community/your_handle.`}
              </h3>
            </>
          </div>
        </div>
      </div>
    </div>
  );
};

export const ToPhaseTippingModal = () => {
  return (
    <div
      className="flex flex-col items-center justify-start gap-6 
      rounded-[10px] border border-[#2a2a2a] bg-[#191919] px-8 py-6"
    >
      <div className="flex w-full flex-col items-center justify-start gap-2">
        <div className="relative h-6 w-6">
          <TipPlusOutlineIcon />
        </div>
        <div className="mb-4 text-center text-base font-semibold leading-tight text-[#ececec]">
          <span>
            {`Reach a $${abbreviateNumber(TIPPING_PHASE_MARKET_CAP_THRESHOLD, 1, true, { padding: false })} market cap to be added as a tipping token!`}
          </span>
        </div>
        <div className="flex w-full flex-col items-start justify-start gap-2">
          <div className="flex flex-col gap-2 text-center text-sm font-normal text-[#B4B4B4]">
            <>
              <h3>
                {`Your token will be automatically added to the list of tipping tokens in The Arena.`}
              </h3>
              <h3>
                {`You won’t even need to ask everyone in your community to tag Jason!`}
              </h3>
            </>
          </div>
        </div>
      </div>
    </div>
  );
};

export const NameChangeAlertModal = ({
  contractAddress,
}: {
  contractAddress: string;
}) => {
  const [open, setOpen] = useState(true);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTitle className="hidden" />
      <DialogContent
        className="top-[70%] flex w-[90%] max-w-[470px] flex-col gap-5 
        rounded-[20px] bg-[#0e0e0e] shadow-[0_0_10px_5px_rgba(58,58,58,0.5)] focus:outline-none 
        sm:top-[50%] sm:rounded-[20px] sm:bg-[#0e0e0e] sm:backdrop-blur-none"
      >
        <div className="flex flex-col items-center justify-center gap-4">
          <GroupIcon stroke="#FFFFFF" />
          <p className="text-center text-base font-semibold leading-tight text-[#ececec]">
            Your profile page handle was taken!
          </p>
        </div>
        <div className="flex flex-col items-center justify-center gap-2 py-6">
          <span className="text-center text-sm font-normal leading-[21px] text-[#808080]">
            Another Arena Launch token reached Phase 4 before yours and took the
            handle you picked.
          </span>
          <span className="text-center text-sm font-normal leading-[21px] text-[#808080]">
            Please change your handle to another one as soon as possible.
          </span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            className="w-full border border-[#808080] px-4 py-2"
            onClick={() => setOpen(false)}
          >
            Close
          </Button>
          <ProgressBarLink
            href={`/community/${contractAddress}/edit`}
            className="w-full"
          >
            <Button className="w-full px-4 py-2">Change Handle</Button>
          </ProgressBarLink>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const ArenaLaunchTnCs = () => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [open, setOpen] = useState(false);

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <div className="flex w-full justify-center">
            <div className="mt-2 flex w-full cursor-pointer flex-col items-center gap-1 text-center text-[12px] leading-[12px] text-gray-text">
              {'By clicking on "Create" you are agreeing to'}
              <div className="flex gap-1">
                {"Arena Launch "}
                <div className="font-semibold text-off-white underline">
                  Terms & Conditions
                </div>
              </div>
            </div>
          </div>
        </DialogTrigger>
        <DialogTitle className="hidden" />
        <DialogContent>
          <ArenaLaunchTnCsModalContent />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <div className="flex w-full justify-center">
          <div className="mt-2 flex w-full cursor-pointer flex-col items-center gap-1 text-center text-[12px] leading-[12px] text-gray-text">
            {'By clicking on "Create" you are agreeing to'}
            <div className="flex gap-1">
              {"Arena Launch "}
              <div className="font-semibold text-off-white underline">
                Terms & Conditions
              </div>
            </div>
          </div>
        </div>
      </DrawerTrigger>
      <DrawerTitle className="hidden" />
      <DrawerContent>
        <ArenaLaunchTnCsModalContent />
      </DrawerContent>
    </Drawer>
  );
};

const ArenaLaunchTnCsModalContent = () => (
  <div>
    <h3 className="text-base font-semibold leading-[22px] text-off-white">
      Arena Launch Terms &amp; Conditions
    </h3>
    <div className="mt-4 text-[12px] text-gray-text">
      By using the Arena, you agree to comply with all applicable laws,
      regulations, and guidelines related to the creation, distribution,
      trading, and use of all of your digital assets on the Arena. This
      includes, but is not limited to, compliance with U.S. federal securities
      laws, the Commodity Exchange Act, and any other relevant laws and
      regulations. You hereby represent and affirm that any tokens that you
      create are not securities under U.S. federal or state securities laws.
    </div>
    <div className="mt-2 text-[12px] font-semibold text-gray-text">
      THE COMPANY ALSO RESERVES THE RIGHT TO, IN ITS SOLE DISCRETION, REMOVE
      DIGITAL ASSETS YOU CREATE, OR RESTRICT YOUR ACCESS TO THE SITE OR
      SERVICES. SUCH REMOVAL OR RESTRICTION MAY OCCUR IN INSTANCES INCLUDING,
      BUT NOT LIMITED TO, POTENTIAL VIOLATION OF LEGAL OR REGULATORY
      REQUIREMENTS (AS DETERMINED IN THE COMPANY’S SOLE DISCRETION), SECURITY
      BREACHES, LOSS OF COMMUNITY SUPPORT, OR INACCURATE OR MISLEADING
      STATEMENTS MADE BY YOU OR YOUR AFFILIATES.
    </div>
    <div className="mt-2 text-[12px] text-gray-text">
      In addition to the terms above, you fully agree to the terms and
      conditions at{" "}
      <a className="text-off-white" target="_blank">
        https://arena.social/terms-of-use
      </a>
    </div>
  </div>
);

interface BannedUserModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export const BannedUserModal = ({ open, setOpen }: BannedUserModalProps) => {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTitle className="hidden" />
      <DialogContent className="w-[90%] max-w-sm gap-5 rounded-[10px] bg-[#18181B] focus:outline-none sm:rounded-[10px] sm:bg-[#18181B]">
        <div className="flex w-full flex-col items-center justify-start gap-2">
          <div className="relative h-6 w-6">
            <BanOutlineIcon className="flex-shrink-0 text-brand-orange" />
          </div>
          <div className="mb-4 items-center text-base font-semibold text-off-white">
            <span>You are banned from posting</span>
          </div>
          <div className="flex w-full flex-col items-start justify-start gap-2">
            <div className="flex flex-col gap-2 text-center text-sm font-normal text-gray-text">
              <h3>
                {`The group admin has banned you because of your posting activity inside this feed.`}
              </h3>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
