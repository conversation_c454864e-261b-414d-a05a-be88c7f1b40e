"use client";

import { MutableRefObject, useEffect, useMemo, useRef, useState } from "react";

import {
  AudioTrack,
  isTrackReference,
  TrackReference,
  useRoomContext,
  useStartAudio,
  useTrackMutedIndicator,
  useTracks,
  VideoTrack,
} from "@livekit/components-react";
import * as SliderPrimitive from "@radix-ui/react-slider";
import { useQuery } from "@tanstack/react-query";
import { Track } from "livekit-client";
import { useHotkeys } from "react-hotkeys-hook";

import { livestreamQueries } from "@/queries";
import { useLivestreamStore } from "@/stores/livestream";
import { cn } from "@/utils/cn";

import {
  ArrowsExpandOutlineIcon,
  VolumeOffOutlineIcon,
  VolumeUpOutlineIcon,
} from "../icons";

export function VideoPlayer() {
  const room = useRoomContext();
  const { mergedProps, canPlayAudio } = useStartAudio({
    room,
    props: {},
  });

  const videoRef = useRef<HTMLVideoElement>(null);
  const id = useLivestreamStore((state) => state.id!);
  const { data } = useQuery(livestreamQueries.livestreamSimpleInfo(id));
  const containerRef = useRef<HTMLDivElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [volume, setVolume] = useState(100);
  const [muted, setMuted] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const screenVideoRef = useRef<HTMLVideoElement>(null);
  const cameraVideoRef = useRef<HTMLVideoElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const animationFrameRef = useRef<number>();
  const isIOS = useRef(
    typeof navigator !== "undefined" &&
      /iPad|iPhone|iPod/.test(navigator.userAgent),
  );

  useHotkeys(["f", "F"], () => {
    if (isFullscreen) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  });

  const resetTimer = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setShowControls(true);
    timeoutRef.current = setTimeout(() => {
      setShowControls(false);
    }, 5000);
  };

  const handleMouseMove = () => {
    resetTimer();
  };

  const handleMouseLeave = () => {
    setShowControls(false);
  };

  const handleTouchStart = () => {
    resetTimer();
  };

  useEffect(() => {
    resetTimer();
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const screenShareTracks = useTracks(
    [{ source: Track.Source.ScreenShare, withPlaceholder: false }],
    {
      onlySubscribed: false,
    },
  )
    .filter(isTrackReference)
    .filter((track) => !track.publication.isMuted);

  const cameraTracks = useTracks(
    [{ source: Track.Source.Camera, withPlaceholder: false }],
    {
      onlySubscribed: false,
    },
  )
    .filter(isTrackReference)
    .filter((track) => !track.publication.isMuted);

  const micTracks = useTracks(
    [{ source: Track.Source.Microphone, withPlaceholder: false }],
    {
      onlySubscribed: false,
    },
  ).filter(isTrackReference);

  const enterFullscreen = async () => {
    try {
      if (containerRef.current) {
        await containerRef.current.requestFullscreen();
      }
    } catch (error) {
      console.error("Failed to enter fullscreen:", error);
    }
  };

  const exitFullscreen = async () => {
    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      }
    } catch (error) {
      console.error("Failed to exit fullscreen:", error);
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () =>
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, []);

  const renderVideo = useMemo(() => {
    if (screenShareTracks.length === 0 && cameraTracks.length === 0) {
      return (
        <div
          className={cn(
            "flex w-full items-center justify-center bg-dark-bk",
            isFullscreen ? "h-full" : "aspect-video lg:rounded-xl",
          )}
        >
          <span className="text-sm text-off-white">
            Waiting for screen share or camera
          </span>
        </div>
      );
    }

    return (
      <>
        <canvas
          ref={canvasRef}
          className="hidden"
          style={{ width: "1920px", height: "1080px" }}
        />
        {screenShareTracks.map((track, index) => (
          <div key={"screen-source-" + index} className="hidden">
            <VideoTrack
              trackRef={track}
              ref={(el: HTMLVideoElement | null) => {
                if (el) {
                  console.log("Got screen video element");
                  (
                    screenVideoRef as MutableRefObject<HTMLVideoElement | null>
                  ).current = el;
                  el.play().catch(console.error);
                }
              }}
            />
          </div>
        ))}
        {cameraTracks.map((track, index) => (
          <div key={"camera-source-" + index} className="hidden">
            <VideoTrack
              trackRef={track}
              ref={(el: HTMLVideoElement | null) => {
                if (el) {
                  console.log("Got camera video element");
                  (
                    cameraVideoRef as MutableRefObject<HTMLVideoElement | null>
                  ).current = el;
                  el.play().catch(console.error);
                }
              }}
            />
          </div>
        ))}
        {stream && (
          <video
            className={cn(
              "w-full bg-dark-bk object-contain",
              isFullscreen ? "h-full" : "max-h-[700px] lg:rounded-xl",
            )}
            autoPlay
            playsInline
            muted={false}
            ref={(el) => {
              if (el) {
                el.srcObject = stream;
                el.play().catch(console.error);
              }
            }}
          />
        )}
      </>
    );
  }, [cameraTracks, screenShareTracks, isFullscreen, stream]);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d", { alpha: false });
    if (!ctx) return;

    // Adjust canvas size based on device
    const scale = isIOS.current ? window.devicePixelRatio || 1 : 1;
    const maxWidth = isIOS.current ? 1280 : 1920;
    const maxHeight = isIOS.current ? 720 : 1080;

    canvas.width = maxWidth;
    canvas.height = maxHeight;

    // Create stream immediately
    const newStream = canvas.captureStream(isIOS.current ? 24 : 30);
    setStream(newStream);

    const fps = isIOS.current ? 24 : 30;
    const interval = 1000 / fps;
    let then = Date.now();

    const drawCanvas = () => {
      const now = Date.now();
      const delta = now - then;

      if (delta > interval) {
        then = now - (delta % interval);

        if (!ctx) return;

        // Clear with black background
        ctx.fillStyle = "#000000";
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        try {
          // Draw screen share if available
          if (screenShareTracks.length > 0 && screenVideoRef.current) {
            console.log("Screen video state:", {
              readyState: screenVideoRef.current.readyState,
              paused: screenVideoRef.current.paused,
              videoWidth: screenVideoRef.current.videoWidth,
              videoHeight: screenVideoRef.current.videoHeight,
              currentTime: screenVideoRef.current.currentTime,
            });

            if (screenVideoRef.current.videoWidth > 0) {
              ctx.drawImage(
                screenVideoRef.current,
                0,
                0,
                canvas.width,
                canvas.height,
              );
            }
          }
          // Draw camera if no screen share or as PiP
          if (cameraTracks.length > 0 && cameraVideoRef.current) {
            console.log("Camera video state:", {
              readyState: cameraVideoRef.current.readyState,
              paused: cameraVideoRef.current.paused,
              videoWidth: cameraVideoRef.current.videoWidth,
              videoHeight: cameraVideoRef.current.videoHeight,
              currentTime: cameraVideoRef.current.currentTime,
            });

            if (cameraVideoRef.current.videoWidth > 0) {
              if (screenShareTracks.length === 0) {
                // Full screen camera
                ctx.drawImage(
                  cameraVideoRef.current,
                  0,
                  0,
                  canvas.width,
                  canvas.height,
                );
              } else {
                // PiP camera
                const pipWidth = Math.min(canvas.width * 0.2, 320);
                const pipHeight = (pipWidth * 9) / 16;
                // Save current context state
                ctx.save();

                // Create rounded rectangle path
                ctx.beginPath();
                ctx.roundRect(
                  canvas.width - pipWidth - 20,
                  canvas.height - pipHeight - 20,
                  pipWidth,
                  pipHeight,
                  10,
                );
                ctx.clip();

                // Draw the camera video within the clipped region
                ctx.drawImage(
                  cameraVideoRef.current,
                  canvas.width - pipWidth - 20,
                  canvas.height - pipHeight - 20,
                  pipWidth,
                  pipHeight,
                );

                // Restore context state
                ctx.restore();
              }
            }
          }
        } catch (error) {
          console.error("Canvas drawing error:", error);
        }
      }

      animationFrameRef.current = requestAnimationFrame(drawCanvas);
    };

    drawCanvas();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (newStream) {
        newStream.getTracks().forEach((track) => track.stop());
      }
    };
  }, [screenShareTracks.length, cameraTracks.length]);

  console.log(stream);

  return (
    <div
      className={cn(
        "relative overflow-hidden lg:rounded-xl",
        screenShareTracks.length === 0 &&
          cameraTracks.length === 0 &&
          !isFullscreen &&
          "border-off-white/30 lg:border",
      )}
      ref={containerRef}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onTouchStart={handleTouchStart}
    >
      {renderVideo}

      <div
        className={cn(
          "pointer-events-none absolute inset-x-0 bottom-0 transition-opacity duration-150",
          showControls ? "opacity-100" : "opacity-0",
        )}
        style={{
          height: "99px",
          backgroundImage:
            "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAADGCAYAAAAT+OqFAAAAdklEQVQoz42QQQ7AIAgEF/T/D+kbq/RWAlnQyyazA4aoAB4FsBSA/bFjuF1EOL7VbrIrBuusmrt4ZZORfb6ehbWdnRHEIiITaEUKa5EJqUakRSaEYBJSCY2dEstQY7AuxahwXFrvZmWl2rh4JZ07z9dLtesfNj5q0FU3A5ObbwAAAABJRU5ErkJggg==)",
          backgroundRepeat: "repeat-x",
          backgroundPosition: "bottom",
        }}
      />
      <div
        className={cn(
          "absolute inset-x-0 bottom-0 z-10 flex items-center justify-end p-1 transition-opacity duration-150",
          showControls ? "opacity-100" : "pointer-events-none opacity-0",
        )}
      >
        {micTracks.length > 0 && (
          <>
            {canPlayAudio ? (
              <>
                {muted ? null : (
                  <SliderPrimitive.Root
                    className={cn(
                      "relative flex w-16 touch-none select-none items-center",
                    )}
                    defaultValue={[volume]}
                    max={100}
                    step={1}
                    onValueChange={(value) => setVolume(value[0])}
                  >
                    <SliderPrimitive.Track className="relative h-1 w-full grow overflow-hidden rounded-full bg-off-white/30">
                      <SliderPrimitive.Range className="absolute h-full bg-off-white" />
                    </SliderPrimitive.Track>
                    <SliderPrimitive.Thumb className="block size-3 rounded-full bg-off-white transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50" />
                  </SliderPrimitive.Root>
                )}
                <button
                  className="ml-1 flex items-center gap-2 p-2"
                  onClick={() => setMuted(!muted)}
                >
                  {muted ? (
                    <VolumeOffOutlineIcon className="size-6 text-off-white" />
                  ) : (
                    <VolumeUpOutlineIcon className="size-6 text-off-white" />
                  )}
                </button>
              </>
            ) : (
              <button
                {...mergedProps}
                className="ml-1 flex items-center gap-2 p-2"
              >
                <VolumeOffOutlineIcon className="size-6 text-off-white" />
              </button>
            )}
          </>
        )}
        <button
          className="mr-1 rounded-full p-2"
          onClick={isFullscreen ? exitFullscreen : enterFullscreen}
        >
          <ArrowsExpandOutlineIcon className="size-6 text-off-white" />
        </button>
      </div>
      {/* data?.livestream.type !== "PRO" && */}
      {micTracks.map((track, index) => (
        <AudioTrack
          trackRef={track}
          key={"mic-" + index}
          volume={volume / 100}
          muted={muted}
        />
      ))}
    </div>
  );
}

function CustomVideoTrack({
  trackRef,
  className,
}: {
  trackRef: TrackReference;
  className?: string;
}) {
  const { isMuted } = useTrackMutedIndicator(trackRef);

  if (isMuted) return null;

  return <VideoTrack trackRef={trackRef} className={className} />;
}
