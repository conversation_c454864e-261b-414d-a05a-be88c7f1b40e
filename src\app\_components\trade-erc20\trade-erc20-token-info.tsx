import { FC } from "react";

import CopyToClipboard from "react-copy-to-clipboard";
import { formatEther } from "viem";

import { CopyOutlineIcon } from "@/components/icons";
import { toast } from "@/components/toast";
import { formatMarketCap } from "@/utils/format-market-cap";
import { formatAddress } from "@/utils/format-token-price";

import { TradeERC20Context } from "./trade-erc20";

interface TradeERC20TokenInfoProps {
  ctx: TradeERC20Context;
}

export const TradeERC20TokenInfo: FC<TradeERC20TokenInfoProps> = ({ ctx }) => {
  return (
    <div className="mt-6 flex items-center gap-4">
      <div className="flex h-[68px] flex-grow items-center justify-between rounded-xl border border-[#3a3a3a] p-4">
        <div className="flex flex-col items-start justify-center">
          <span className="text-[11px] font-semibold text-[#808080]">
            CONTRACT ADDRESS:
          </span>
          <span className="text-xs text-[#808080]">
            {formatAddress(ctx.token.address).toUpperCase()}
          </span>
        </div>

        <CopyToClipboard
          text={ctx.token.address}
          onCopy={() => toast.green("Address copied!")}
        >
          <button className="flex-shrink-0">
            <CopyOutlineIcon className="size-5 text-off-white" />
          </button>
        </CopyToClipboard>
      </div>
      {ctx.community?.stats?.marketCap && (
        <div className="flex h-[68px] w-[100px] flex-col rounded-xl border border-[#3a3a3a] p-4">
          <span className="text-[11px] font-semibold text-[#808080]">
            MKT CAP
          </span>
          <span className="text-xs text-[#808080]">
            $
            {formatMarketCap(
              Number(formatEther(BigInt(ctx.community.stats.marketCap))) *
                ctx.avaxPrice,
            )}
          </span>
        </div>
      )}
    </div>
  );
};
