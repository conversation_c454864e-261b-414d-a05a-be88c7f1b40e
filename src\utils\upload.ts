import { v4 as uuidv4 } from "uuid";

import { axios } from "@/lib/axios";

import { getOAuthAccessToken } from "./get-x-token";

type OnProgressChangeHandler = (progress: number) => void;

interface Params {
  file: File;
  onProgressChange?: OnProgressChangeHandler;
}

export async function upload(params: Params) {
  try {
    params.onProgressChange?.(0);

    const res = await uploadFile(params);
    return res;
  } catch (error) {
    throw error;
  }
}

interface UploadPolicyType {
  url: string;
  enctype: string;
  key: string;
  "x-goog-date": string;
  "x-goog-credential": string;
  "x-goog-algorithm": string;
  policy: string;
  "x-goog-signature": string;
}

async function uploadFile({ file, onProgressChange }: Params) {
  try {
    onProgressChange?.(0);
    const searchParams = new URLSearchParams({
      fileType: file.type,
      fileName: file.name,
    });

    const res = await axios.get<{ uploadPolicy: UploadPolicyType }>(
      `/uploads/getUploadPolicy?${searchParams.toString()}`,
    );

    if (res.status !== 200) {
      throw new Error("Failed to get upload policy");
    }
    const uploadPolicy = res.data.uploadPolicy;
    await uploadFileInner(file, uploadPolicy, onProgressChange);

    return {
      id: uuidv4(),
      name: uploadPolicy.key,
      url: `https://static.starsarena.com/${uploadPolicy.key}`,
      createdOn: new Date(),
      path: `/${uploadPolicy.key}`,
      previewUrl: `https://static.starsarena.com/${uploadPolicy.key}`,
      size: file.size,
    };
  } catch (e) {
    onProgressChange?.(0);
    throw e;
  }
}

const uploadFileInner = async (
  file: File | Blob,
  uploadPolicy: UploadPolicyType,
  onProgressChange?: OnProgressChangeHandler,
) => {
  const promise = new Promise<boolean | null>((resolve, reject) => {
    const formData = new FormData();

    formData.append("key", uploadPolicy.key);
    formData.append("x-goog-date", uploadPolicy["x-goog-date"]);
    formData.append("x-goog-credential", uploadPolicy["x-goog-credential"]);
    formData.append("x-goog-algorithm", uploadPolicy["x-goog-algorithm"]);
    formData.append("policy", uploadPolicy["policy"]);
    formData.append("x-goog-signature", uploadPolicy["x-goog-signature"]);

    formData.append("Content-Type", file.type);
    formData.append("file", file);
    const request = new XMLHttpRequest();
    request.open("POST", uploadPolicy.url, true);
    request.addEventListener("loadstart", () => {
      onProgressChange?.(0);
    });
    request.upload.addEventListener("progress", (e) => {
      if (e.lengthComputable) {
        // 2 decimal progress
        const progress = Math.round((e.loaded / e.total) * 10000) / 100;
        onProgressChange?.(progress);
      }
    });
    request.addEventListener("error", () => {
      reject(new Error("Error uploading file"));
    });
    request.addEventListener("abort", () => {
      reject(new Error("File upload aborted"));
    });

    request.addEventListener("loadend", () => {
      resolve(true);
    });

    request.send(formData);
  });
  return promise;
};

export async function uploadFileToXAPI(
  file: File,
  onProgressChange?: OnProgressChangeHandler,
) {
  onProgressChange?.(0);

  const accessToken = await getOAuthAccessToken();

  const formData = new FormData();

  formData.append("file", file);
  formData.append("accessToken", accessToken);

  const res = await axios.post(`/uploads/x-api-upload`, formData);
  if (res.data.suceess) {
    onProgressChange?.(100);
  }

  return res.data;
}
