"use client";

import { useMemo } from "react";

import { Virtuoso } from "react-virtuoso";

import { PostLoadingSkeleton } from "@/components/post-loading-skeleton";
import { useBookmarksInfiniteQuery } from "@/queries";
import { Thread } from "@/types";

import { BookmarkPost } from "./bookmark-post";

export const Bookmarks = () => {
  const { data, fetchNextPage, hasNextPage, isLoading, isFetchingNextPage } =
    useBookmarksInfiniteQuery();

  const threads = useMemo(() => {
    if (!data) return [];

    return data.pages.reduce((prev, current) => {
      return [...prev, ...current.threads];
    }, [] as Thread[]);
  }, [data]);

  return threads.length === 0 && !isLoading ? (
    <div className="mt-10 flex flex-col items-center justify-center">
      <p className="px-2 text-xs font-normal md:text-sm">
        You have not bookmarked any posts yet.
      </p>
    </div>
  ) : (
    <Virtuoso
      useWindowScroll
      data={threads}
      endReached={() => {
        if (hasNextPage) {
          fetchNextPage();
        }
      }}
      overscan={200}
      itemContent={(index, thread) => {
        return <BookmarkPost thread={thread} />;
      }}
      components={{
        Footer: () => {
          if (isLoading || isFetchingNextPage) {
            return (
              <>
                {Array.from({ length: 5 }).map((_, i) => (
                  <PostLoadingSkeleton key={i} />
                ))}
              </>
            );
          }
          return null;
        },
      }}
    />
  );
};
