import { axios } from "@/lib/axios";
import {
  BlockedViewersResponse,
  CreateLivestreamParams,
  CreateLivestreamResponse,
  DeletePinPostParams,
  EditLivestreamParams,
  EditLivestreamResponse,
  EndLivestreamParams,
  GenerateLivestreamIngressParams,
  JoinLivestreamParams,
  JoinLivestreamResponse,
  LiveLivestreamsResponse,
  LivestreamIdParams,
  LivestreamIngressResponse,
  LivestreamOkResponse,
  LivestreamSimpleInfoResponse,
  PostBlockUserParams,
  PostPinPostParams,
  TippingStatsResponse,
  TopTippersResponse,
} from "@/queries/types/livestream";

export const postCreateLivestream = async (data: CreateLivestreamParams) => {
  const response = await axios.post<CreateLivestreamResponse>(
    `/livestreams`,
    data,
  );

  return response.data;
};

export const postEndLivestream = async (data: EndLivestreamParams) => {
  const response = await axios.post<LivestreamOkResponse>(
    `/livestreams/end`,
    data,
  );

  return response.data;
};

export const postJoinLivestream = async (data: JoinLivestreamParams) => {
  const response = await axios.post<JoinLivestreamResponse>(
    `/livestreams/join`,
    data,
  );

  return response.data;
};

export const getLiveLivestreams = async () => {
  const response = await axios.get<LiveLivestreamsResponse>(
    "/livestreams/live-livestreams",
  );

  return response.data;
};

export const getLivestreamSimpleInfo = async (livestreamId: string) => {
  const response = await axios.get<LivestreamSimpleInfoResponse>(
    `/livestreams/get-simple-info`,
    {
      params: { livestreamId },
    },
  );

  return response.data;
};

export const getLivestreamIngress = async () => {
  const response =
    await axios.get<LivestreamIngressResponse>(`/livestreams/ingress`);

  return response.data;
};

export const postGenerateLivestreamIngress = async (
  data: GenerateLivestreamIngressParams,
) => {
  const response = await axios.post<LivestreamIngressResponse>(
    `/livestreams/generate-ingress`,
    data,
  );

  return response.data;
};

export const postStartLivestream = async (data: LivestreamIdParams) => {
  const response = await axios.post<JoinLivestreamResponse>(
    `/livestreams/start`,
    data,
  );

  return response.data;
};

export const postRemindLivestream = async (data: LivestreamIdParams) => {
  const response = await axios.post<LivestreamOkResponse>(
    `/livestreams/remind`,
    data,
  );

  return response.data;
};

export const postEditLivestream = async (data: EditLivestreamParams) => {
  const response = await axios.post<EditLivestreamResponse>(
    `/livestreams/edit`,
    data,
  );

  return response.data;
};

export const postPinPost = async (data: PostPinPostParams) => {
  const response = await axios.post<LivestreamOkResponse>(
    `/livestreams/pin-post`,
    data,
  );

  return response.data;
};

export const deletePinPost = async (data: DeletePinPostParams) => {
  const response = await axios.delete<LivestreamOkResponse>(
    `/livestreams/unpin-post`,
    { data },
  );

  return response.data;
};

export const postBlockUser = async (data: PostBlockUserParams) => {
  const response = await axios.post<LivestreamOkResponse>(
    `/livestreams/block`,
    data,
  );

  return response.data;
};

export const postUnblockUser = async (data: PostBlockUserParams) => {
  const response = await axios.post<LivestreamOkResponse>(
    `/livestreams/unblock`,
    data,
  );

  return response.data;
};

export const postUpdateAttributes = async (data: LivestreamIdParams) => {
  const response = await axios.post<boolean>(
    `/livestreams/update-participant-attributes`,
    data,
  );

  return response.data;
};

export const getBlockedViewers = async (livestreamId: string) => {
  const response = await axios.get<BlockedViewersResponse>(
    `/livestreams/blocked-viewers`,
    {
      params: { livestreamId },
    },
  );

  return response.data;
};

export const postTipLivestream = async (data: {
  livestreamId: string;
  tipAmount: string;
  userId: string;
  currency: string;
}) => {
  const response = await axios.post<any>("/livestreams/tip", data);
  return response.data;
};

export const postTipNotifyLivestream = async (data: {
  livestreamId: string;
  tipAmount: string;
  userId: string;
  currency: string;
  txHash: string;
  txData: string;
  isToken?: boolean;
  tokenContractAddress?: string;
}) => {
  const response = await axios.post<any>("/livestreams/notify-tip", data);
  return response.data;
};

export const getTippingStats = async (livestreamId: string) => {
  const response = await axios.get<TippingStatsResponse>(
    `/livestreams/tipping-stats`,
    {
      params: { livestreamId },
    },
  );
  return response.data;
};

export const getTopLivestreamTippers = async (livestreamId: string) => {
  const response = await axios.get<TopTippersResponse>(
    `/livestreams/top-livestream-tippers`,
    {
      params: { livestreamId },
    },
  );
  return response.data;
};

export const getTopCreatorTippers = async (userId: string) => {
  const response = await axios.get<TopTippersResponse>(
    `/livestreams/top-creator-tippers`,
    {
      params: { userId },
    },
  );
  return response.data;
};
