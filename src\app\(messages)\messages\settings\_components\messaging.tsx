"use client";

import { FC, useEffect, useState } from "react";

import Skeleton from "react-loading-skeleton";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/utils";

import { useSettings } from "../../context/settings-context";

const MessagingSkeleton: FC<{ isDesktop: boolean }> = ({ isDesktop }) => {
  return (
    <div
      className={cn(
        "flex flex-col justify-between px-6 py-9",
        !isDesktop && "flex-grow",
      )}
    >
      <div>
        <div>
          <Skeleton className="h-[20px] w-[150px]" />
          <div className="flex w-full justify-between">
            <Skeleton className="h-[20px] w-[200px]" />
            <Skeleton className="h-[20px] w-[40px]" />
          </div>
        </div>
        <div className="mt-6">
          <Skeleton className="h-[20px] w-[150px]" />
          <div className="flex w-full justify-between">
            <Skeleton className="h-[20px] w-[200px]" />
            <div className="px-2.5">
              <Skeleton className="h-[20px] w-[20px]" />
            </div>
          </div>
        </div>
        <div className="mt-6">
          <Skeleton className="h-[20px] w-[150px]" />
          <div className="flex w-full justify-between">
            <Skeleton className="h-[20px] w-[200px]" />
            <div className="px-2.5">
              <Skeleton className="h-[20px] w-[20px]" />
            </div>
          </div>
        </div>
      </div>
      <div className={cn(isDesktop && "flex justify-end")}>
        <Skeleton
          style={{ borderRadius: "20px" }}
          className={cn("h-[40px]", isDesktop ? "mt-8 w-[100px]" : "w-full")}
        />
      </div>
    </div>
  );
};

export const Messaging: FC = () => {
  const isDesktop = useMediaQuery(BREAKPOINTS.lg);

  const {
    isHolders,
    setIsHolders,
    isFollowers,
    setIsFollowers,
    isLoading: isFetchSettings,
    isPending,
    handleSetSettings,
  } = useSettings();

  const [isDirectMessagesEnabled, setIsDirectMessagesEnabled] = useState(
    isHolders || isFollowers,
  );

  useEffect(() => {
    if (!isDirectMessagesEnabled) {
      setIsHolders(false);
      setIsFollowers(false);
    } else if (!isHolders && !isFollowers) {
      setIsHolders(true);
      setIsFollowers(false);
    }
  }, [isDirectMessagesEnabled]);

  useEffect(() => {
    if (!isHolders && !isFollowers) {
      setIsDirectMessagesEnabled(false);
    }
  }, [isHolders, isFollowers]);

  if (isFetchSettings) {
    return <MessagingSkeleton isDesktop={isDesktop} />;
  }

  return (
    <div
      className={cn(
        "flex flex-col justify-between px-6 py-6",
        !isDesktop && "flex-grow",
      )}
    >
      <div>
        <div className="mb-6">
          <Label className="mt-2 flex justify-between gap-2">
            <div className="text-sm font-semibold normal-case text-off-white">
              Enable Direct Messages
            </div>
            <div className="px-2.5">
              <Switch
                checked={isDirectMessagesEnabled}
                onCheckedChange={(checked) =>
                  setIsDirectMessagesEnabled(checked)
                }
                disabled={isPending}
              />
            </div>
          </Label>
        </div>
        <h3 className="text-sm text-off-white">Customize who can DM you:</h3>
        <div className="mt-8">
          <h4 className="text-xs font-semibold uppercase text-off-white">
            ONLY TICKET HOLDERS
          </h4>
          <Label className="mt-2 flex justify-between gap-2">
            <div className="text-sm font-medium normal-case leading-5 text-gray-text">
              Ticket holders can send you DM requests
            </div>
            <div className="px-2.5">
              <Checkbox
                className="h-[20px] w-[20px]"
                checked={isHolders}
                onCheckedChange={(checked: boolean) => setIsHolders(checked)}
                disabled={isPending || !isDirectMessagesEnabled}
              />
            </div>
          </Label>
        </div>
        <div className="mt-6">
          <h4 className="text-xs font-semibold uppercase text-off-white">
            USERS I FOLLOW
          </h4>
          <Label className="mt-2 flex justify-between gap-2">
            <div className="text-sm font-medium normal-case leading-5 text-gray-text">
              Followed users can send you DM requests
            </div>
            <div className="px-2.5">
              <Checkbox
                className="h-[20px] w-[20px]"
                checked={isFollowers}
                onCheckedChange={(checked: boolean) => setIsFollowers(checked)}
                disabled={isPending || !isDirectMessagesEnabled}
              />
            </div>
          </Label>
        </div>
      </div>
      <Button
        className={cn(
          "p-2.5",
          isDesktop ? "mt-8 w-[100px] self-end" : "w-full",
        )}
        onClick={handleSetSettings}
        disabled={isPending}
      >
        Save
      </Button>
    </div>
  );
};
