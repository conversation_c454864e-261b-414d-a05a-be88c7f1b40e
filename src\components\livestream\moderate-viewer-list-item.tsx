"use client";

import { useQueryClient } from "@tanstack/react-query";

import {
  livestreamQueries,
  useBlockUserLivestreamMutation,
  useUnblockUserLivestreamMutation,
} from "@/queries";
import { LiveParticipant } from "@/queries/types/livestream";
import { useLivestreamStore } from "@/stores/livestream";

import { Button } from "../ui/button";
import { ParticipantListItem } from "./user-list-item";

export const ModerateViewerListItem = ({
  user,
  blocked,
}: {
  user: LiveParticipant;
  blocked: boolean;
}) => {
  const queryClient = useQueryClient();
  const id = useLivestreamStore((state) => state.id!);

  const { mutateAsync: blockViewer, isPending: isBlockViewerPending } =
    useBlockUserLivestreamMutation({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: livestreamQueries.blockedViewersKey(id),
        });
      },
    });
  const { mutateAsync: unblockViewer, isPending: isUnblockViewerPending } =
    useUnblockUserLivestreamMutation({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: livestreamQueries.blockedViewersKey(id),
        });
      },
    });

  const handleBlockViewer = async () => {
    await blockViewer({
      livestreamId: id,
      userId: user.id,
    });
  };

  const handleUnblockViewer = async () => {
    await unblockViewer({
      livestreamId: id,
      userId: user.id,
    });
  };

  return (
    <ParticipantListItem user={user}>
      {blocked ? (
        <Button
          variant="secondary"
          className="h-[34px] w-24"
          onClick={handleUnblockViewer}
          loading={isUnblockViewerPending}
        >
          Undo
        </Button>
      ) : (
        <Button
          variant="outline"
          className="h-[34px] w-24"
          onClick={handleBlockViewer}
          loading={isBlockViewerPending}
        >
          Remove
        </Button>
      )}
    </ParticipantListItem>
  );
};
