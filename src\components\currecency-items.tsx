import { FC } from "react";

import { SystemCurrency } from "@/api/client/currency";
import { formatPrice } from "@/utils/format-token-price";

export const filterCurrencies = (currencies: SystemCurrency[]) => {
  return currencies.filter((currency) => {
    if (currency.symbol === "AVAX") return true;
    const balance = numberFormatter.format(
      currency.isToken
        ? parseFloat(
            formatPrice(currency.balance).toFixed(
              formatPrice(currency.balance) > 1 ? 2 : 4,
            ),
          )
        : Number(currency.balance),
    );
    return Number(balance.replace(/,/g, "")) > 0;
  });
};

interface ICurrencyItemProps {
  currency: SystemCurrency;
}

export const CurrencyItem: FC<ICurrencyItemProps> = ({ currency }) => {
  const balance = numberFormatter.format(
    currency.isToken
      ? parseFloat(
          formatPrice(currency.balance).toFixed(
            formatPrice(currency.balance) > 1 ? 2 : 4,
          ),
        )
      : Number(currency.balance),
  );

  return (
    <div className="flex w-full items-center justify-between gap-4">
      <div className="flex items-center gap-4">
        <img
          src={currency.photoURL}
          className="size-8 rounded-full"
          alt={`${currency.symbol} logo`}
        />
        {currency.isToken ? `$${currency.symbol}` : currency.symbol}
      </div>
      <div className="group-data-[state=checked]:hidden">{balance}</div>
    </div>
  );
};

const numberFormatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 20,
});
