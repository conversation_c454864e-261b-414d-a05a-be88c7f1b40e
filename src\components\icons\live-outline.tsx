import { ComponentProps } from "react";

export const LiveOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 25 24"
    fill="none"
    {...props}
  >
    <rect
      x="5.5"
      y="1.75"
      width="13.5"
      height="16.5"
      rx="3.25"
      stroke="currentColor"
      strokeWidth="1.5"
    />
    <path
      d="M8.75 6.2627H15.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M8.75 9.4209H15.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M8.75 12.5791H15.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <rect
      x="11"
      y="18.25"
      width="2.5"
      height="3.5"
      fill="currentColor"
      stroke="currentColor"
      strokeWidth="1.5"
    />
    <path
      d="M17.75 22L6.75 22"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);
