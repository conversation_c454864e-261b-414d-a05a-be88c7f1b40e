import { getAxios } from "@/lib/axios-server";
import { ChainWithdrawData } from "@/queries/types";

export const getChainDegodsBadgeAvailable = async () => {
  const axios = getAxios();

  const response = await axios.get(`/chain/degods_badge_available`);
  return response.data;
};

export const getChainDokyoBadgeAvailable = async () => {
  const axios = getAxios();

  const response = await axios.get(`/chain/dokyo_badge_available`);
  return response.data;
};

export const getChainSappySealsBadgeAvailable = async () => {
  const axios = getAxios();

  const response = await axios.get(`/chain/sappy_seals_badge_available`);
  return response.data;
};

export const postChainWithdraw = async (data: ChainWithdrawData) => {
  const axios = getAxios();

  const response = await axios.post(`/chain/withdraw`, data);
  return response.data;
};
