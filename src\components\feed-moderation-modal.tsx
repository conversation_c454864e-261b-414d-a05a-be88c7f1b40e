"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import {
  useModerateDeleteThreadMutation,
  useSharesStatsQuery,
} from "@/queries";
import { useIsUserBannedQuery } from "@/queries/groups-queries";
import { ThreadsResponse } from "@/queries/types";
import { Thread } from "@/types/thread";

import { ArrowBackOutlineIcon } from "./icons/arrow-back-outline";
import { UserListItem } from "./user-list-item";

interface CommunityFeedModerationModalProps {
  thread: Thread;
  open: boolean;
  setOpen: (open: boolean) => void;
  triggeredFromMainPostUI?: boolean;
}

export const CommunityFeedModerationModal = ({
  thread,
  open,
  setOpen,
  triggeredFromMainPostUI,
}: CommunityFeedModerationModalProps) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const [showBanConfirm, setShowBanConfirm] = useState(false);

  const { data: isUserBannedFromCommunity } = useIsUserBannedQuery(
    thread.communityId || "",
    thread.userId,
  );
  const { data: userStats } = useSharesStatsQuery({
    userId: thread.user.id,
  });

  const { mutateAsync: moderateDeleteThread, isPending } =
    useModerateDeleteThreadMutation({
      onMutate: async ({ threadId, communityId, banUser }) => {
        if (banUser) {
          toast.red("Post deleted and user banned successfully");
        } else {
          toast.red("Post deleted");
        }

        await queryClient.cancelQueries({
          queryKey: ["threads", "community", communityId],
        });
        await queryClient.cancelQueries({
          queryKey: ["home", "threads", "my-feed"],
        });
        await queryClient.cancelQueries({
          queryKey: ["home", "threads", "trending-feed"],
        });
        await queryClient.cancelQueries({
          queryKey: ["threads", "user", thread.userId],
        });
        if (banUser) {
          await queryClient.cancelQueries({
            queryKey: ["community", "isUserBanned", communityId, thread.userId],
          });
        }

        const previousMyFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = queryClient.getQueryData([
          "home",
          "threads",
          "my-feed",
        ]);
        const previousTrendingFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = queryClient.getQueryData([
          "home",
          "threads",
          "trending-feed",
        ]);
        const previousCommunityFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = queryClient.getQueryData([
          "threads",
          "community",
          communityId,
        ]);
        const previousUserProfileFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = queryClient.getQueryData([
          "threads",
          "user",
          thread.userId,
        ]);
        let previousBanData: boolean | undefined = undefined;
        if (banUser) {
          previousBanData = queryClient.getQueryData([
            "community",
            "isUserBanned",
            communityId,
            thread.userId,
          ]);
        }

        queryClient.setQueryData(
          ["home", "threads", "my-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.filter((t) => t.id !== threadId),
                };
              }),
            };
          },
        );
        queryClient.setQueryData(
          ["home", "threads", "trending-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.filter((t) => t.id !== threadId),
                };
              }),
            };
          },
        );
        queryClient.setQueryData(
          ["threads", "community", communityId],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.filter((t) => t.id !== threadId),
                };
              }),
            };
          },
        );
        queryClient.setQueryData(
          ["threads", "user", thread.userId],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.filter((t) => t.id !== threadId),
                };
              }),
            };
          },
        );
        if (banUser) {
          queryClient.setQueryData(
            ["community", "isUserBanned", communityId, thread.userId],
            true,
          );
        }

        return {
          previousMyFeed,
          previousTrendingFeed,
          previousCommunityFeed,
          previousUserProfileFeed,
          previousBanData,
        };
      },
      onError(err, variables, context) {
        queryClient.setQueryData(
          ["threads", "community", thread.communityId],
          context?.previousCommunityFeed,
        );
        queryClient.setQueryData(
          ["home", "threads", "my-feed"],
          context?.previousMyFeed,
        );
        queryClient.setQueryData(
          ["home", "threads", "trending-feed"],
          context?.previousTrendingFeed,
        );
        queryClient.setQueryData(
          ["threads", "user", thread.userId],
          context?.previousUserProfileFeed,
        );
        if (variables.banUser) {
          queryClient.setQueryData(
            ["community", "isUserBanned", thread.communityId, thread.userId],
            context?.previousBanData ?? false,
          );
        }
      },
    });

  const handleDeleteOnly = async () => {
    await moderateDeleteThread({
      threadId: thread.id,
      communityId: thread.communityId || "",
      banUser: false,
    });
    setOpen(false);
    setShowBanConfirm(false);
    if (triggeredFromMainPostUI) {
      router.back();
    }
  };

  const handleDeleteAndBan = async () => {
    await moderateDeleteThread({
      threadId: thread.id,
      communityId: thread.communityId || "",
      banUser: true,
    });
    setOpen(false);
    setShowBanConfirm(false);
    if (triggeredFromMainPostUI) {
      router.back();
    }
  };

  const ModerationContent = (
    <div>
      <div className="flex flex-row items-center gap-2">
        <button onClick={() => setOpen(false)}>
          <ArrowBackOutlineIcon className="size-5" />
        </button>
        <span>Feed Moderation</span>
      </div>
      <div className="mt-8 flex items-center gap-2">
        <Button
          variant="outline"
          className="flex-1 items-center"
          onClick={() => {
            setShowBanConfirm(true);
          }}
          disabled={isUserBannedFromCommunity}
        >
          Delete & Ban User
        </Button>
        <Button
          variant="default"
          className="flex-1 items-center"
          onClick={handleDeleteOnly}
          disabled={isPending}
        >
          Delete Post
        </Button>
      </div>
    </div>
  );

  const BanConfirmContent = (
    <div className="">
      <div className="mb-6 flex flex-col">
        <p className="text-lg font-semibold">Delete Post & Ban User</p>
        <p className="text-xs text-[#D14848]">
          (This action can&apos;t be undone)
        </p>
      </div>
      <div className="pointer-events-none mb-8">
        <UserListItem
          user={{ ...thread.user, stats: userStats?.stats, lastKeyPrice: "" }}
          className="rounded-lg border border-gray-border"
        />
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => {
            setShowBanConfirm(false);
          }}
          disabled={isPending}
        >
          Cancel
        </Button>
        <Button
          variant="destructive"
          className="flex-grow"
          onClick={handleDeleteAndBan}
          disabled={isPending}
        >
          Confirm
        </Button>
      </div>
    </div>
  );

  const content = showBanConfirm ? BanConfirmContent : ModerationContent;

  return isTablet ? (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-sm sm:bg-[#191919] ">
        {content}
      </DialogContent>
    </Dialog>
  ) : (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="justify-start p-6 text-left">
        {content}
      </DrawerContent>
    </Drawer>
  );
};
