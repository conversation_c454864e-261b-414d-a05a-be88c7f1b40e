import { ComponentProps } from "react";

export const UnionIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 40 32"
    fill="none"
    {...props}
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M10.1556 2.94029L9.07128 4.02459L8.69178 2.56079L8.53977 1.97448L8.16763 0.539062L9.60303 0.911208L10.1893 1.06321L11.6531 1.44272L10.5688 2.52702L10.1556 2.94029ZM19.6887 9.47015L11.661 1.44244L9.07945 4.02406L17.1071 12.0518L7.96354 21.1954L5.38047 18.6123C5.02403 18.2559 4.44612 18.2559 4.08968 18.6123C3.73323 18.9688 3.73323 19.5467 4.08968 19.9031L5.3429 21.1564C5.16382 21.016 4.90401 21.0282 4.73906 21.1932C4.56083 21.3714 4.56083 21.6604 4.73906 21.8386L6.0297 23.1293L2.1553 27.0037C1.68205 27.0704 1.22573 27.2857 0.861757 27.6497C-0.0294399 28.5409 -0.0294399 29.9858 0.861757 30.877C1.75295 31.7682 3.19787 31.7682 4.08907 30.877C4.45193 30.5142 4.66704 30.0595 4.73442 29.5878L8.61129 25.7109L9.90275 27.0023C10.081 27.1806 10.3699 27.1806 10.5481 27.0023C10.7131 26.8374 10.7254 26.5776 10.585 26.3985L11.8352 27.6487C12.1917 28.0052 12.7696 28.0052 13.126 27.6487C13.4825 27.2923 13.4825 26.7144 13.126 26.3579L10.5451 23.777L19.6887 14.6334L28.8322 23.777L26.2513 26.3579C25.8949 26.7144 25.8949 27.2923 26.2513 27.6487C26.6078 28.0052 27.1857 28.0052 27.5421 27.6487L28.7924 26.3985C28.652 26.5776 28.6642 26.8374 28.8292 27.0023C29.0074 27.1806 29.2964 27.1806 29.4746 27.0023L30.7661 25.7109L34.6429 29.5878C34.7103 30.0595 34.9254 30.5142 35.2883 30.877C36.1795 31.7682 37.6244 31.7682 38.5156 30.877C39.4068 29.9858 39.4068 28.5409 38.5156 27.6497C38.1516 27.2857 37.6953 27.0704 37.222 27.0037L33.3476 23.1293L34.6383 21.8386C34.8165 21.6604 34.8165 21.3714 34.6383 21.1932C34.4733 21.0282 34.2135 21.016 34.0344 21.1564L35.2877 19.9031C35.6441 19.5467 35.6441 18.9688 35.2877 18.6123C34.9312 18.2559 34.3533 18.2559 33.9969 18.6123L31.4138 21.1954L22.2703 12.0518L30.2979 4.02406L27.7163 1.44244L19.6887 9.47015ZM30.3061 4.02459L29.2218 2.94029L28.8085 2.52702L27.7242 1.44272L29.188 1.06321L29.7743 0.911208L31.2097 0.539062L30.8376 1.97448L30.6856 2.56079L30.3061 4.02459Z"
      fill="currentColor"
    />
  </svg>
);
