import { axios } from "@/lib/axios";
import {
  UserEmailResponse,
  UserExistsResponse,
} from "@/queries/types/user-email";

export const registerEmail = async (email: String) => {
  const response = await axios.post<UserEmailResponse>(
    `/userEmail/registerToCurrentUser`,
    {
      email: email,
    },
  );
  return response.data;
};

export const getEmail = async () => {
  const response = await axios.get(`/userEmail/email`);
  return response.data;
};

export const isEmailRegistered = async (email: String) => {
  const response = await axios.get<UserExistsResponse>(
    `/userEmail/registered/${email}`,
  );
  return response.data;
};
