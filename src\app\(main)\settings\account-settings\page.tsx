"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";

import { MFADevice } from "@dynamic-labs/sdk-api-core";
import {
  useIsLoggedIn,
  useMfa,
  useOtpVerificationRequest,
  useUserUpdateRequest,
} from "@dynamic-labs/sdk-react-core";

import {
  getEmail,
  isEmailRegistered,
  registerEmail,
} from "@/api/client/userEmail";
import { Header } from "@/components/header";
import { EllipsisHorizontalFilledIcon } from "@/components/icons";
import {
  MailIcon,
  PlusCircleIcon,
  ShieldIcon,
  UnlinkIcon,
} from "@/components/icons-v2/account-security";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { env } from "@/env";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { UserEmailResponse } from "@/queries/types/user-email";

import { AddMfaModal } from "./_components/add-mfa-device";
import { DeleteMfaModal } from "./_components/delete-mfa-device";
import { RegisterEmailModal } from "./_components/register-email";
import { MfaBackupCodesModal } from "./_components/show-mfa-backup-codes";
import { VerifyEmailModal } from "./_components/verify-email";
import { MfaOneTimePasswordModal } from "./_components/verify-mfa-one-time-password";

type MfaRegisterData = {
  uri: string;
  secret: string;
};

type MfaModalStates =
  | "CLOSED"
  | "ADD_MFA"
  | "ONE_TIME_PASSWORD"
  | "BACKUP_CODES"
  | "DELETE_MFA";

function calculateTimeDiff(createdAt: Date) {
  const diff = Date.now() - createdAt.getTime();

  const daysSince = Math.floor(diff / 86_400_000);
  if (daysSince == 1) {
    return "1 day";
  } else if (daysSince > 1) {
    return `${daysSince} days`;
  }

  const hoursSince = Math.floor(diff / 3_600_000);
  if (hoursSince == 1) {
    return "1 hour";
  } else if (hoursSince > 1) {
    return `${hoursSince} hours`;
  }

  const minutesSince = Math.floor(diff / 60_000);
  if (minutesSince <= 1) {
    return "1 minute";
  } else if (minutesSince > 1) {
    return `${minutesSince} minutes`;
  }
}

function AccountSettingsPage() {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [email, setEmail] = useState<String | null>(null);
  const [registerEmailMode, setRegisterEmailMode] = useState(false);
  const [verifyEmailMode, setVerifyEmailMode] = useState(false);
  const [emailToBeRegistered, setEmailToBeRegistered] = useState("");
  const [userDevices, setUserDevices] = useState<MFADevice[]>([]);
  const [mfaViewMode, setMfaViewMode] = useState<MfaModalStates>("CLOSED");
  const [mfaRegisterData, setMfaRegisterData] = useState<MfaRegisterData>();
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [mfaMenuOpen, setMfaMenuOpen] = useState(false);
  const isLoggedIn = useIsLoggedIn();

  useEffect(() => {
    getEmail()
      .then((response) => {
        setEmail(response.email);
      })
      .catch((_) => {});
  }, []);

  useEffect(() => {
    if (isLoggedIn) {
      refreshUserDevices();
    }
  }, [isLoggedIn]);

  const { updateUser } = useUserUpdateRequest();
  const initiateEmailRegistration = async (email: string) => {
    const user = await isEmailRegistered(email);
    if (user.isExists) {
      toast.red("This email has already been registered!");
      return;
    }

    setEmailToBeRegistered(email);

    try {
      const { isEmailVerificationRequired } = await updateUser({ email });
      if (isEmailVerificationRequired) {
        setRegisterEmailMode(false);
        setVerifyEmailMode(true);
        toast.green("Verification code sent successfully.");
      }
    } catch (error) {
      if (error instanceof Error) {
        if (error.message && error.message.includes("Email already exists")) {
          toast.red("This email has already been registered!");
        } else if (
          error.message &&
          error.message.includes("Too many email verification attempts.")
        ) {
          toast.red(
            "Too many email verification attempts, please try again later!",
          );
        } else {
          toast.red("Email registration failed, please try again later!");
        }
      }
    }
  };

  const { verifyOtp } = useOtpVerificationRequest();
  const verifyEmailRegistration = async (verificationToken: string) => {
    try {
      const verifyOtpResponse = await verifyOtp(verificationToken, "email");
      if (verifyOtpResponse) {
        toast.green("Email verified successfully");
        registerEmail(emailToBeRegistered)
          .then((response: UserEmailResponse) => {
            toast.green("Email updated successfully");
            setEmail(response.email);
            setRegisterEmailMode(false);
            setVerifyEmailMode(false);
          })
          .catch((errorMessage) => {
            toast.red("Error updating email");
          });
      } else {
        toast.red("Email verification failed");
      }
    } catch (error) {
      toast.red("Email verification failed");
    }
  };

  const {
    addDevice,
    authDevice,
    getUserDevices,
    getRecoveryCodes,
    completeAcknowledgement,
    deleteUserDevice,
  } = useMfa();

  const refreshUserDevices = async () => {
    const devices = await getUserDevices();
    const verifiedDevices = devices.filter((device) => {
      return device.verified;
    });
    setUserDevices(verifiedDevices);
  };

  const initiateMultiFactorAuthRegistration = async () => {
    const devices = await getUserDevices();
    const verifiedDevices = devices.filter((device) => {
      return device.verified;
    });

    if (verifiedDevices.length === 0) {
      const { uri, secret } = await addDevice();
      setMfaRegisterData({ uri, secret });
    }
  };

  useEffect(() => {
    if (mfaRegisterData?.uri) {
      setMfaViewMode("ADD_MFA");
    }
  }, [mfaRegisterData]);

  const onQRCodeContinue = async () => {
    setMfaRegisterData({
      uri: "",
      secret: "",
    });
    setMfaViewMode("ONE_TIME_PASSWORD");
  };

  const onOtpSubmit = async (code: string) => {
    const result = authDevice(code)
      .then(async (response) => {
        if (response) {
          getRecoveryCodes().then((codes) => {
            setBackupCodes((prevCodes) => {
              setMfaViewMode("BACKUP_CODES");
              return codes;
            });
          });
          await refreshUserDevices();
        }
      })
      .catch((e) => {
        if (e.message === "Invalid code") {
          toast.red("Invalid code!");
        }
      });
  };

  const onAcceptBackupCodes = async () => {
    completeAcknowledgement().then(() => {
      closeModals();
    });
  };

  const fetchDynamicMFAToken = async (deviceId: string, code: string) => {
    const rawDynamicAuthenticationToken = localStorage.getItem(
      "dynamic_authentication_token",
    );
    const dynamicAuthenticationToken = rawDynamicAuthenticationToken
      ? JSON.parse(rawDynamicAuthenticationToken || "")
      : null;

    try {
      const response = await fetch(
        `https://app.dynamicauth.com/api/v0/sdk/${env.NEXT_PUBLIC_DYNAMIC_ENVIRONMENT_ID}/users/mfa/auth/totp`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${dynamicAuthenticationToken}`,
            "Content-Type": "application/json",
          },
          body: `{"id":"${deviceId}","createMfaToken":{"singleUse":true},"code":"${code}"}`,
        },
      );

      const body = await response.json();
      return body.mfaToken;
    } catch (e) {
      throw e;
    }
  };

  const onOtpDelete = async (code: string) => {
    const deviceId = userDevices[0].id;
    if (userDevices.length > 0 && deviceId) {
      let mfaToken;
      try {
        mfaToken = await fetchDynamicMFAToken(deviceId, code);
      } catch (e: any) {
        if (e.message === "Invalid code") {
          toast.red("Invalid code!");
        }
        return;
      }

      if (!mfaToken) {
        toast.red("Something went wrong!");
        return;
      }

      try {
        await deleteUserDevice(deviceId, mfaToken);
      } catch (e: any) {
        if (e.message === "Invalid code") {
          toast.red("Invalid code!");
        }
        return;
      }

      refreshUserDevices();
      closeModals();
    }
  };

  const closeModals = useCallback(() => {
    setMfaViewMode("CLOSED");
    setRegisterEmailMode(false);
    setVerifyEmailMode(false);
  }, []);

  const renderMFAView = useCallback(
    (mfaViewMode: MfaModalStates) => {
      switch (mfaViewMode) {
        case "CLOSED":
          return;
        case "ADD_MFA":
          return (
            <AddMfaModal
              mfaRegisterData={mfaRegisterData!}
              onContinue={onQRCodeContinue}
              onClose={closeModals}
            />
          );
        case "ONE_TIME_PASSWORD":
          return (
            <MfaOneTimePasswordModal
              onSubmit={onOtpSubmit}
              onClose={closeModals}
            />
          );
        case "BACKUP_CODES":
          return (
            <MfaBackupCodesModal
              backupCodes={backupCodes}
              onAccept={onAcceptBackupCodes}
              onClose={closeModals}
            />
          );
        case "DELETE_MFA":
          return (
            <DeleteMfaModal onSubmit={onOtpDelete} onClose={closeModals} />
          );
      }
    },
    [
      backupCodes,
      closeModals,
      completeAcknowledgement,
      mfaRegisterData,
      onOtpSubmit,
    ],
  );

  const copyBackupCodes = async () => {
    getRecoveryCodes(true)
      .then((codes) => {
        setBackupCodes(codes);
        setMfaViewMode("BACKUP_CODES");
        setMfaMenuOpen(false);
      })
      .catch((err) => {
        toast.red("Something went wrong when generating backup codes!");
      });
  };

  return (
    <>
      <div>
        <Header defaultBackUrl="/settings">Account Security</Header>
        <div className=" flex w-full flex-col items-center justify-center gap-[18px] px-[24px] py-[32px]">
          <div className="flex w-full cursor-pointer flex-col items-start justify-center gap-0.5 rounded-[10px] border border-[#78716C] p-4">
            {!!email
              ? updateEmailComponent(email, () => {
                  setRegisterEmailMode(true);
                })
              : registerEmailComponent(() => {
                  setRegisterEmailMode(true);
                })}
          </div>
          <div className="flex w-full cursor-pointer flex-col items-start justify-center gap-0.5 rounded-[10px] border border-[#78716C] p-4">
            <div className="inline-flex items-center justify-between gap-4 self-stretch">
              <div
                className="inline-flex items-center justify-start gap-4 self-stretch"
                onClick={initiateMultiFactorAuthRegistration}
              >
                <div className="relative h-6 w-6">
                  {userDevices.length === 0 && <PlusCircleIcon />}
                  {userDevices.length > 0 && <ShieldIcon />}
                </div>
                <div className="inline-flex shrink grow basis-0 flex-col items-start justify-start gap-0.5">
                  <h4 className="text-sm font-medium normal-case leading-5 text-off-white">
                    {userDevices.length === 0 && "Add Authenticator"}
                    {userDevices.length > 0 && "Authenticator App"}
                  </h4>
                  <p className="text-sm font-normal normal-case text-light-gray-text">
                    {!isLoggedIn && "Log out and log in to continue."}
                    {isLoggedIn &&
                      userDevices.length === 0 &&
                      "No authenticator apps configured."}
                    {isLoggedIn &&
                      userDevices.length !== 0 &&
                      `Added ${calculateTimeDiff(userDevices[0].verifiedAt!)} ago`}
                  </p>
                </div>
              </div>
              {userDevices.length > 0 && isTablet && (
                <DropdownMenu open={mfaMenuOpen} onOpenChange={setMfaMenuOpen}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="ml-1 flex size-[26px] flex-shrink-0 items-center justify-center border-none p-0 outline-none"
                    >
                      <EllipsisHorizontalFilledIcon className="size-5 fill-off-white" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      className="w-full gap-4"
                      asChild
                      onClick={() => setMfaViewMode("DELETE_MFA")}
                    >
                      <div>
                        <UnlinkIcon className="size-5 flex-shrink-0 text-gray-text" />
                        <span>Disconnect authenticator</span>
                      </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="w-full gap-4"
                      asChild
                      onClick={copyBackupCodes}
                    >
                      <div>
                        <PlusCircleIcon className="size-5 flex-shrink-0 text-gray-text" />
                        <span>Get new backup codes</span>
                      </div>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
              {userDevices.length > 0 && !isTablet && (
                <Drawer open={mfaMenuOpen} onOpenChange={setMfaMenuOpen}>
                  <DrawerTrigger>
                    <EllipsisHorizontalFilledIcon className="size-5 fill-off-white" />
                  </DrawerTrigger>
                  <DrawerContent className="px-4 pt-4">
                    <div
                      className="flex items-center gap-2 p-2 text-base leading-5"
                      onClick={() => {
                        setMfaViewMode("DELETE_MFA");
                        setMfaMenuOpen(false);
                      }}
                    >
                      <UnlinkIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Disconnect Authenticator</span>
                    </div>
                    <div
                      className="flex items-center gap-2 p-2 text-base leading-5"
                      onClick={copyBackupCodes}
                    >
                      <PlusCircleIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Get new backup codes</span>
                    </div>
                  </DrawerContent>
                </Drawer>
              )}
            </div>
          </div>
        </div>
      </div>
      {registerEmailMode ? (
        <RegisterEmailModal
          handleRegister={initiateEmailRegistration}
          handleClose={closeModals}
        />
      ) : (
        <></>
      )}
      {verifyEmailMode ? (
        <VerifyEmailModal
          email={emailToBeRegistered}
          verifyAction={verifyEmailRegistration}
          resendOTP={(email: string) => {
            updateUser({ email });
          }}
          closeAction={closeModals}
        />
      ) : (
        <></>
      )}
      {renderMFAView(mfaViewMode)}
    </>
  );
}

const registerEmailComponent = (
  initiateEmailRegistrationHandle: () => void,
) => {
  return (
    <div
      className="inline-flex items-center justify-start gap-4 self-stretch"
      onClick={initiateEmailRegistrationHandle}
    >
      <div className="relative h-6 w-6">
        <PlusCircleIcon />
      </div>
      <div className="inline-flex shrink grow basis-0 flex-col items-start justify-start gap-0.5">
        <h4 className="text-sm font-medium normal-case leading-5 text-off-white">
          Recovery Email
        </h4>
        <p className="text-sm font-normal normal-case text-light-gray-text">
          No recovery email added.
        </p>
      </div>
    </div>
  );
};

const updateEmailComponent = (
  email: String,
  initiateEmailRegistrationHandle: () => void,
) => {
  return (
    <div
      className="inline-flex items-center justify-start gap-4 self-stretch"
      onClick={initiateEmailRegistrationHandle}
    >
      <div className="relative h-6 w-6">
        <MailIcon />
      </div>
      <div className="inline-flex shrink grow basis-0 flex-col items-start justify-start gap-0.5">
        <h4 className="text-sm font-medium normal-case leading-5 text-off-white">
          Update Recovery Email
        </h4>
        <p className="text-sm font-normal normal-case text-light-gray-text">
          {email}
        </p>
      </div>
    </div>
  );
};

export default AccountSettingsPage;
