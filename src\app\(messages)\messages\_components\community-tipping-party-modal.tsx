import { useEffect, useMemo, useState } from "react";

import * as RadioGroup from "@radix-ui/react-radio-group";
import { useQueryClient } from "@tanstack/react-query";

import { postTippingPartyNotifyBundle } from "@/api/client/chat";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { TippingInfoModal } from "@/components/tipping-info";
import { TipFormContent } from "@/components/tipping/tip-form-content";
import { ArenaDialogHeader } from "@/components/ui/arena-dialog-header";
import { Button } from "@/components/ui/button";
import { Dialog, DialogClose, DialogContent } from "@/components/ui/dialog";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import { useTippableCurrenciesQuery } from "@/queries/currency-queries";
import { useTokenHoldersQuery } from "@/queries/groups-queries";
import { useUser } from "@/stores";
import { cn } from "@/utils/cn";

import {
  CommunityTipReceiver,
  EditCommunityTipReceiversModal,
} from "./edit-community-tip-receivers-modal";
import { TipPartyContent } from "./TipPartyContent";

enum TipOption {
  EveryChatroomMember = "EveryChatroomMember",
  Top10Holders = "Top10Holders",
}

interface CommunityTippingPartyModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  contractAddress: string;
}

export const CommunityTippingPartyModal = ({
  open,
  setOpen,
  contractAddress,
}: CommunityTippingPartyModalProps) => {
  const queryClient = useQueryClient();
  const [isEditTipReceiversOpen, setIsEditTipReceiversOpen] = useState(false);
  const [option, setOption] = useState<TipOption>(
    TipOption.EveryChatroomMember,
  );
  const [tipReceivers, setTipReceivers] = useState<CommunityTipReceiver[]>([]);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const { user } = useUser();
  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useTippableCurrenciesQuery();
  const { sortedCurrencies } = useWalletCurrencies({
    user,
    currenciesData,
    isCurrenciesLoading,
  });
  const {
    data: holdersData,
    isLoading: isLoadingHolders,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useTokenHoldersQuery(contractAddress);
  const [step, setStep] = useState<1 | 2>(1);
  useEffect(() => {
    const fetchAllPages = async () => {
      while (hasNextPage) {
        await fetchNextPage();
      }
    };
    if (!isLoadingHolders && !isFetchingNextPage) {
      fetchAllPages();
    }
  }, [hasNextPage, fetchNextPage, isLoadingHolders, isFetchingNextPage]);
  const holders = useMemo(() => {
    if (!holdersData) return [];
    return holdersData.pages.flatMap((page) => page.holders);
  }, [holdersData]);
  useEffect(() => {
    if (!holders) return;
    setTipReceivers(
      holders
        .filter((holder) => holder.user && holder.user.id !== user?.id)
        .map((holder) => ({ ...holder, isChecked: true, amount: "1" })),
    );
  }, [holders, user?.id]);
  useEffect(() => {
    const handleResize = () => {
      const viewportHeight =
        window.visualViewport?.height || window.innerHeight;
      const windowHeight = window.innerHeight;
      if (viewportHeight < windowHeight) {
        setIsKeyboardVisible(true);
      } else {
        setIsKeyboardVisible(false);
      }
    };
    if (typeof visualViewport != "undefined") {
      window.visualViewport?.addEventListener("resize", handleResize);
    }
    return () => {
      if (typeof visualViewport != "undefined") {
        window.visualViewport?.removeEventListener("resize", handleResize);
      }
    };
  }, []);
  return (
    <>
      <Dialog
        open={open}
        onOpenChange={(open) => {
          setOpen(open);
        }}
      >
        <DialogContent
          className={cn(
            "flex h-full w-full flex-grow flex-col bg-dark-bk  px-6 pt-0 backdrop-blur-sm sm:h-fit sm:w-[420px] sm:gap-0 sm:bg-[#1A1A1A] sm:p-6",
            isKeyboardVisible
              ? "flex-grow-0 justify-end pb-[calc(142px+env(safe-area-inset-bottom))]"
              : "flex-grow justify-between",
          )}
        >
          {" "}
          <ArenaDialogHeader
            title="Tipping Party"
            showBack={true}
            onBack={step === 2 ? () => setStep(1) : () => setOpen(false)}
            className="mb-12 sm:mb-8"
          />
          {step === 1 && (
            <TipPartyContent
              options={[
                {
                  value: TipOption.EveryChatroomMember,
                  label: (
                    <>
                      Tip Every
                      <br />
                      Chatroom Member
                    </>
                  ),
                  emoji: "🙋",
                },
                {
                  value: TipOption.Top10Holders,
                  label: (
                    <>
                      Tip Top 10
                      <br />
                      Token Holders
                    </>
                  ),
                  emoji: <span className="rotate-[15deg]">👑</span>,
                },
              ]}
              selected={option as string}
              setSelected={(v) => setOption(v as TipOption)}
              infoText={
                <>
                  <h4 className="text-sm text-gray-text">
                    {option === TipOption.EveryChatroomMember
                      ? "Every member of this chatroom will receive the same tip."
                      : "The Top 10 Token holders will receive the same tip."}
                  </h4>
                  <p className="text-sm leading-[18px] ">
                    {option === TipOption.EveryChatroomMember
                      ? "You will be excluded"
                      : "If you are a top 10 token holder, you will be excluded"}
                  </p>
                </>
              }
              onContinue={() => setStep(2)}
            />
          )}
          {step === 2 && (
            <TipFormContent
              recepients={
                option === TipOption.Top10Holders
                  ? tipReceivers
                      .filter((r) => r.isChecked)
                      .sort(
                        (a, b) => (b as any).tokenRatio - (a as any).tokenRatio,
                      )
                      .slice(0, 10)
                      .map((r) => r.user)
                  : tipReceivers.filter((r) => r.isChecked).map((r) => r.user)
              }
              setOpen={setOpen}
              sortedCurrencies={sortedCurrencies}
              setResetForm={undefined}
              distributionMode="equal"
              buttonLabel="Send tips"
              customRecipientsSelector={null}
              onPartyNotify={async ({
                currency,
                txHash,
                txData,
                recipient,
              }) => {
                await postTippingPartyNotifyBundle({
                  currency,
                  txHash: Array.isArray(txHash) ? txHash : [txHash],
                  txData: Array.isArray(txData) ? txData : [txData],
                });
              }}
            />
          )}
          <TippingInfoModal />
        </DialogContent>
        <EditCommunityTipReceiversModal
          open={isEditTipReceiversOpen}
          setOpen={setIsEditTipReceiversOpen}
          tipReceivers={tipReceivers}
          setTipReceivers={setTipReceivers}
          showTips={true}
        />
      </Dialog>
    </>
  );
};
