import { format } from "date-fns";

import { UserLink } from "@/app/(moderation)/moderation/reports/_components/ticket-details/user-link";
import { ReportTicket } from "@/queries/types/report-tickets";

const HEADERS = [
  "Report Number",
  "Type",
  "Timestamp",
  "Reported User",
  "Report Reason",
  "Reporter",
];

export const TicketInfoTable = ({ ticket }: { ticket: ReportTicket }) => (
  <div className="flex gap-6">
    <div className="flex-col space-y-6">
      {HEADERS.map((header) => (
        <h3 key={header} className="font-semibold">
          {header}
        </h3>
      ))}
    </div>
    <div className="flex-col space-y-6 text-light-gray-text">
      <p>#{ticket.id}</p>
      <p>{ticket.type}</p>
      <p>{format(new Date(ticket.timestamp), "MM/dd/yyyy - hh:mm a")}</p>
      <UserLink user={ticket.reportedUser} />
      <p>{ticket.reportType}</p>
      <UserLink user={ticket.reportingUser} />
    </div>
  </div>
);
