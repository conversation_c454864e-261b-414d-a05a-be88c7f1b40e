"use client";

import React, { useEffect, useState } from "react";
import {
  usePathname,
  useRouter,
  useSelectedLayoutSegments,
} from "next/navigation";

import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useReportTicketQuery } from "@/queries/report-tickets-query";

import { AdminProvider } from "./admin/context/admin-context";
import { CurrencyCreate } from "./admin/projects/[projectId]/_components/currency-create";
import { CurrencyEdit } from "./admin/projects/[projectId]/_components/currency-edit";
import { RewardCreate } from "./admin/rewards/[rewardId]/_components/reward-create";
import { RewardEdit } from "./admin/rewards/[rewardId]/_components/reward-edit";
import { ReportTicketDetails } from "./reports/_components/report-ticket-details";
import {
  ReportsProvider,
  useReportsContext,
} from "./reports/context/reports-context";

interface TicketDetailsContainerProps {
  ticketId: number;
}

interface ProjectDetailsContainerProps {
  projectId: string;
}

interface RewardDetailsContainerProps {
  rewardId: string;
}

interface ModerationLayoutProps {
  children: React.ReactNode;
}

interface ModerationLayoutWrapperProps {
  children: React.ReactNode;
}

const TicketDetailsContainer = ({ ticketId }: TicketDetailsContainerProps) => {
  const { activeGroups, closedGroups } = useReportsContext();
  const { data: ticket, isLoading } = useReportTicketQuery(ticketId);

  const allGroups = [...activeGroups, ...closedGroups];
  const group = allGroups.find((g) => g.tickets.some((t) => t.id === ticketId));

  if (isLoading) {
    return <div className="p-4">Loading...</div>;
  }

  if (!ticket && !group) {
    return <div className="p-4">Ticket not found</div>;
  }

  return (
    <ReportTicketDetails
      ticketId={ticketId}
      group={group ? { tickets: group.tickets } : undefined}
    />
  );
};

const ProjectDetailsContainer = ({
  projectId,
}: ProjectDetailsContainerProps) => {
  return <CurrencyEdit projectId={projectId} />;
};

const RewardDetailsContainer = ({ rewardId }: RewardDetailsContainerProps) => {
  return <RewardEdit rewardId={rewardId} />;
};

const NewProjectContainer = () => {
  return <CurrencyCreate />;
};

const NewRewardContainer = () => {
  return <RewardCreate />;
};

const ModerationLayout = ({ children }: ModerationLayoutProps) => {
  const [mounted, setMounted] = useState(false);
  const isLargeTablet = useMediaQuery(BREAKPOINTS.lg);
  const pathname = usePathname();
  const router = useRouter();
  const segments = useSelectedLayoutSegments();

  const isAdminRoute = pathname.startsWith("/moderation/admin");
  const isReportsRoute = pathname.startsWith("/moderation/reports");
  const currentTab = isAdminRoute ? "admin" : "reports";

  const hasTicketId = segments.length > 1 && segments[0] === "reports";
  const ticketId = hasTicketId ? Number(segments[1]) : null;

  const hasProjectId =
    segments.length > 2 &&
    segments[0] === "admin" &&
    segments[1] === "projects";
  const projectId = hasProjectId ? segments[2] : null;

  const hasRewardId =
    segments.length > 2 && segments[0] === "admin" && segments[1] === "rewards";
  const rewardId = hasRewardId ? segments[2] : null;

  const isNewProjectPage =
    segments.length === 3 &&
    segments[0] === "admin" &&
    segments[1] === "projects" &&
    segments[2] === "new";

  const isNewRewardPage =
    segments.length === 3 &&
    segments[0] === "admin" &&
    segments[1] === "rewards" &&
    segments[2] === "new";

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleTabChange = (value: string) => {
    if (value === "reports") {
      router.push("/moderation/reports");
    } else if (value === "admin") {
      router.push("/moderation/admin");
    }
  };

  if (!mounted) {
    return (
      <div className="flex flex-shrink flex-grow-[2] justify-start">
        <div className="relative w-full sm:w-[520px] sm:border-x sm:border-dark-gray" />
        <div className="hidden lg:flex lg:w-[405px] lg:flex-col lg:border-r lg:border-dark-gray" />
      </div>
    );
  }

  if (!isLargeTablet) {
    return (
      <div className="flex flex-shrink flex-grow-[2] justify-start">
        <div className="pt-pwa pb-pwa relative flex max-h-screen w-full flex-grow flex-col sm:w-[520px] sm:border-x sm:border-dark-gray lg:w-[380px]">
          {!hasTicketId && !hasProjectId && !hasRewardId && (
            <div className="container mx-auto px-0 pt-4 sm:p-4">
              <Tabs
                value={currentTab}
                onValueChange={handleTabChange}
                className="flex flex-grow flex-col"
              >
                <TabsList className="inline-flex justify-start border-none px-0">
                  <TabsTrigger
                    value="reports"
                    className="relative mb-3 text-2xl font-semibold data-[state=active]:after:h-[2px]"
                  >
                    Mod
                  </TabsTrigger>
                  <TabsTrigger
                    value="admin"
                    className="relative mb-3 text-2xl font-semibold data-[state=active]:after:h-[2px]"
                  >
                    Admin
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          )}
          {children}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-shrink flex-grow-[2] justify-start">
      <div className="relative h-full max-h-screen w-full overflow-y-auto sm:w-[520px] sm:border-x sm:border-dark-gray">
        <div className="container mx-auto px-0 py-4 sm:px-4 sm:pb-0">
          <Tabs
            value={currentTab}
            onValueChange={handleTabChange}
            className="flex flex-grow flex-col"
          >
            <TabsList className="inline-flex justify-start border-none px-0">
              <div className="flex">
                <TabsTrigger
                  value="reports"
                  className="relative mb-2 px-0 text-2xl font-semibold after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px] after:bg-dark-gray/50 data-[state=active]:font-semibold data-[state=active]:after:h-[4px] data-[state=active]:after:bg-brand-orange sm:!px-7"
                >
                  Mod
                </TabsTrigger>
                <TabsTrigger
                  value="admin"
                  className="relative mb-2 px-0 text-2xl font-semibold after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px] after:bg-dark-gray/50 data-[state=active]:font-semibold data-[state=active]:after:h-[4px] data-[state=active]:after:bg-brand-orange sm:!px-7"
                >
                  Admin
                </TabsTrigger>
              </div>
            </TabsList>
          </Tabs>
        </div>
        {children}
      </div>
      <div className="hidden lg:flex lg:w-[405px] lg:flex-col lg:border-r lg:border-dark-gray">
        <div className="fixed top-0 flex h-full max-h-screen w-full flex-col lg:w-[405px]">
          {isReportsRoute && hasTicketId && ticketId && (
            <TicketDetailsContainer ticketId={ticketId} />
          )}
          {isAdminRoute && hasProjectId && projectId && !isNewProjectPage && (
            <ProjectDetailsContainer projectId={projectId} />
          )}
          {isAdminRoute && hasRewardId && rewardId && !isNewRewardPage && (
            <RewardDetailsContainer rewardId={rewardId} />
          )}
          {isAdminRoute && isNewProjectPage && <NewProjectContainer />}
          {isAdminRoute && isNewRewardPage && <NewRewardContainer />}
        </div>
      </div>
    </div>
  );
};

const ModerationLayoutWrapper = ({
  children,
}: ModerationLayoutWrapperProps) => {
  const pathname = usePathname();
  const isAdminRoute = pathname.startsWith("/moderation/admin");
  const isReportsRoute = pathname.startsWith("/moderation/reports");

  if (isReportsRoute) {
    return (
      <ReportsProvider>
        <ModerationLayout>{children}</ModerationLayout>
      </ReportsProvider>
    );
  }

  if (isAdminRoute) {
    return (
      <AdminProvider>
        <ModerationLayout>{children}</ModerationLayout>
      </AdminProvider>
    );
  }

  return <ModerationLayout>{children}</ModerationLayout>;
};

export default ModerationLayoutWrapper;
