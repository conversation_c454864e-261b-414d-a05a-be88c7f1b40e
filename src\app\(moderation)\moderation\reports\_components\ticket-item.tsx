"use client";

import { FC } from "react";

import { format } from "date-fns";

import { ReportTicket } from "@/queries/types/report-tickets";
import { cn } from "@/utils";

interface TicketItemProps {
  ticket: ReportTicket;
  isSelected?: boolean;
  onClick?: () => void;
}

export const TicketItem: FC<TicketItemProps> = ({
  ticket,
  isSelected = false,
  onClick,
}) => {
  return (
    <div
      className={cn(
        "group relative flex cursor-pointer items-start rounded-[10px] p-6",
        "hover:bg-accent/50 transition-colors",
        isSelected && "bg-[#111] bg-opacity-[0.88]",
      )}
      onClick={onClick}
    >
      <div className="flex-1">
        <div className="flex items-center justify-between gap-2">
          <div className="text-sm ">
            Reported User: @{ticket.reportedUser.twitterHandle}
          </div>
          <div className="text-xs text-gray-text">
            {format(new Date(ticket.timestamp), "MM/dd/yyyy - hh:mm a")}
          </div>
        </div>

        <div className="text-sm text-gray-text">
          {ticket.type} #{ticket.id} - {ticket.reportType}
        </div>
      </div>
      {isSelected && (
        <div className="absolute inset-y-0 right-0 w-1.5 rounded-r-[10px] bg-brand-orange" />
      )}
    </div>
  );
};
