export const LocatedNumber = (
  number: string | number,
  minimumFractionDigits: number = 2,
) => {
  const checkedNumber = isNaN(Number(number)) ? 0 : Number(number);
  return checkedNumber.toLocaleString("en-US", {
    minimumFractionDigits,
    maximumFractionDigits: 2,
  });
};

export const LocatedBigNumber = (number: string, decimalPlaces: number = 2) => {
  const parts = number.split(".");
  const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  const decimalPart = parts[1]
    ? parts[1].padEnd(decimalPlaces, "0").slice(0, decimalPlaces)
    : "";
  return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
};
