"use client";

import { useMemo, useState } from "react";

import { Virtuoso } from "react-virtuoso";

import { CommunityListItem } from "@/app/(main)/community/_components/community-list-item";
import { CommunityListItemLoadingSkeleton } from "@/app/(main)/community/_components/community-list-item-loading-skeleton";
import {
  ArrowBackOutlineIcon,
  CloseOutlineIcon,
  SearchFilledIcon,
} from "@/components/icons";
import { Input } from "@/components/ui/input";
import useThrottle from "@/hooks/use-throttle";
import { useFeedEligibleCommunitiesQuery } from "@/queries/groups-queries";
import { CommunityExtended } from "@/types/community";

interface CommunitySelectionPageProps {
  onSelect: (community: CommunityExtended) => void;
  onClose: () => void;
}

export const CommunitySelectionPage: React.FC<CommunitySelectionPageProps> = ({
  onSelect,
  onClose,
}) => {
  const [search, setSearch] = useState("");
  const throttledSearch = useThrottle(search);

  const {
    data: communitiesData,
    isLoading: isLoadingCommunities,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useFeedEligibleCommunitiesQuery(throttledSearch);

  const communities = useMemo(() => {
    return communitiesData?.pages.map((page) => page.communities).flat() || [];
  }, [communitiesData]);

  return (
    <div className="flex flex-col bg-dark-bk text-off-white">
      <div className="flex items-center border-b border-dark-gray px-6 py-4">
        <div className="flex flex-1 justify-start">
          <button onClick={onClose}>
            <ArrowBackOutlineIcon className="size-5 text-off-white" />
          </button>
        </div>
        <h3 className="text-base font-semibold leading-5 text-white">
          Choose Token Feed
        </h3>
        <div className="flex-1" />
      </div>
      <div className="mb-4 mt-6 px-6">
        <div className="relative">
          <Input
            placeholder="Search Token Feeds"
            className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-10 placeholder:text-gray-text"
            value={search}
            onChange={(e) => {
              setSearch(e.target.value);
            }}
          />
          <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
          {search && (
            <button className="absolute right-[10px] top-1/2 -translate-y-1/2 rounded-full border border-dark-gray bg-[rgba(20,20,20,0.88)] p-1">
              <CloseOutlineIcon
                className="pointer-events-auto size-[14px] select-none text-off-white"
                onClick={() => {
                  setSearch("");
                }}
              />
            </button>
          )}
        </div>
      </div>
      {!isLoadingCommunities && communities.length === 0 && (
        <div className="mt-10 flex w-full items-center justify-center">
          <div className="max-w-64 text-center">
            <h4 className="text-sm font-semibold text-[#EDEDED]">
              No token feeds found!
            </h4>
          </div>
        </div>
      )}
      <Virtuoso
        useWindowScroll
        data={communities}
        increaseViewportBy={500}
        itemContent={(index, community) => (
          <button
            onClick={() => {
              onSelect(community);
            }}
            className="w-full hover:bg-gray-bg"
          >
            <CommunityListItem community={community} />
          </button>
        )}
        endReached={() => {
          if (hasNextPage) {
            fetchNextPage();
          }
        }}
        components={{
          Footer: () => {
            if (isLoadingCommunities || isFetchingNextPage) {
              return (
                <>
                  {Array.from({ length: 10 }).map((_, i) => (
                    <CommunityListItemLoadingSkeleton key={i} />
                  ))}
                </>
              );
            }
            return null;
          },
        }}
      />
    </div>
  );
};
