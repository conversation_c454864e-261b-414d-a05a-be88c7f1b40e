"use client";

import { ComponentProps } from "react";

export const SmileOutlineIcon = (props: ComponentProps<"svg">) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 23 23"
      fill="none"
      {...props}
    >
      <path
        d="M11.5 22.5C5.43429 22.5 0.5 17.5657 0.5 11.5C0.5 5.43429 5.43507 0.5 11.5 0.5C17.5649 0.5 22.5 5.43429 22.5 11.5C22.5 17.5657 17.5657 22.5 11.5 22.5ZM11.5 1.63793C6.30093 1.63793 1.63793 6.30093 1.63793 11.5C1.63793 16.6991 6.30093 21.3621 11.5 21.3621C16.6991 21.3621 21.3621 16.6991 21.3621 11.5C21.3621 6.30093 16.6991 1.63793 11.5 1.63793Z"
        fill="currentColor"
      />
      <path
        d="M11.5251 17.9173C9.33528 17.9173 7.30421 16.9273 5.95357 15.2011C5.68642 14.8593 5.74614 14.3659 6.08792 14.0987C6.42892 13.8308 6.67885 14.1917 6.94678 14.5343C7.99728 15.8771 9.79507 16.8101 11.4985 16.8101C13.22 16.8101 14.9997 15.9006 16.0502 14.5343C16.315 14.1901 16.6448 13.7868 16.9889 14.0516C17.3331 14.3164 17.3983 14.8098 17.1335 15.1532C15.7829 16.91 13.7384 17.9173 11.5251 17.9173ZM7.98935 11.579C7.338 11.579 6.81078 11.0517 6.81078 10.4004V8.82896C6.81078 8.1776 7.338 7.65039 7.98935 7.65039C8.64071 7.65039 9.16792 8.1776 9.16792 8.82896V10.4004C9.16792 11.051 8.63992 11.579 7.98935 11.579ZM15.0608 11.579C14.4102 11.579 13.8822 11.0517 13.8822 10.4004V8.82896C13.8822 8.1776 14.4102 7.65039 15.0608 7.65039C15.7114 7.65039 16.2394 8.1776 16.2394 8.82896V10.4004C16.2394 11.051 15.7114 11.579 15.0608 11.579Z"
        fill="currentColor"
      />
    </svg>
  );
};
