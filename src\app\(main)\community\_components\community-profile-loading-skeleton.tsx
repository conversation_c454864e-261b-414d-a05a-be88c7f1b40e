"use client";

import Skeleton from "react-loading-skeleton";

import { PageHeader } from "@/app/_components/page-header";
import { PostLoadingSkeleton } from "@/components/post-loading-skeleton";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export const CommunityProfileLoadingSkeleton = () => {
  return (
    <>
      <div className="pt-pwa relative z-20">
        <div className="aspect-[4/1] w-full bg-cover bg-center bg-no-repeat">
          <PageHeader />
        </div>
        <div className="px-6">
          <div className="-mt-10 mb-4 flex items-end gap-5 leading-none">
            <Skeleton className="h-[92px] w-[92px] !rounded-lg border border-[#E8E8E8]" />
            <div className="flex flex-col gap-0.5 font-medium text-off-white">
              <span className="text-sm text-gray-text">Followers</span>
              <span className="text-base leading-5">
                <Skeleton className="h-4 w-16" />
              </span>
            </div>
            <div className="flex flex-col gap-0.5 font-medium text-off-white">
              <span className="text-sm text-gray-text">Market Cap (MC)</span>
              <span className="text-base leading-5">
                <Skeleton className="h-4 w-16" />
              </span>
            </div>
          </div>
          <Skeleton className="h-5 w-32" />
          <div className="mb-8 mt-2">
            <Skeleton className="h-[55px] w-full" />
          </div>
          <div className="mb-4 flex gap-[10px]">
            <div className="flex-1 overflow-hidden rounded-full leading-none">
              <Skeleton className="h-9 w-full" />
            </div>
            <div className="flex flex-1 justify-between gap-[10px] leading-none">
              <div className="flex-1 overflow-hidden rounded-full leading-none">
                <Skeleton className="h-9 w-full" />
              </div>
              <Skeleton circle className="h-9 w-9" />
            </div>
          </div>
          <Skeleton className="mb-[7px] h-[44px] w-full" />
        </div>
      </div>
      <Tabs value="threads" className="flex-grow">
        <div className="sticky top-0 z-10 w-full pt-[calc(14px+env(safe-area-inset-top))] shadow-[0px_-5px_14px_20px_rgba(0,0,0,0.25)] backdrop-blur-[9px]">
          <TabsList className="mt-2 w-full">
            <TabsTrigger className="w-full" value="threads">
              Threads
            </TabsTrigger>
            <TabsTrigger className="w-full" value="ticket-holders">
              Token Holders
            </TabsTrigger>
            <TabsTrigger className="w-full" value="stats">
              Stats
            </TabsTrigger>
          </TabsList>
        </div>
        <TabsContent value="threads" className="h-full">
          {Array.from({ length: 10 }).map((_, i) => (
            <PostLoadingSkeleton key={i} />
          ))}
        </TabsContent>
      </Tabs>
    </>
  );
};
