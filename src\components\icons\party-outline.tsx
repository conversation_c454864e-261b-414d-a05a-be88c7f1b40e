import { ComponentProps } from "react";

export const PartyOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <g clip-path="url(#clip0_36891_73277)">
      <path
        d="M7.27002 7.71157L2.75333 21.3141L16.3454 16.7659M7.27002 7.71157C8.78337 11.6761 12.6632 15.7928 16.3454 16.7659M7.27002 7.71157C10.8713 8.73667 15.7507 12.883 16.3454 16.7659"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.1797 6.6228L18.7865 7.50884"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.7881 1.55566L15.8588 4.9373"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.9248 13.3323L17.5192 10.937"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.7266 6.12595L10.3207 3.73047"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_36891_73277">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
