"use client";

import { useMemo, useState } from "react";

import { useDynamicContext } from "@dynamic-labs/sdk-react-core";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { useTokenBalancesQuery } from "@/queries";
import { useUser } from "@/stores";
import { UserFlaggedEnum } from "@/types";
import { cn, formatEther } from "@/utils";

export const MenuModal = () => {
  const { user } = useUser();
  const [isOpen, setIsOpen] = useState(false);

  const { balances } = useTokenBalancesQuery({
    address: user?.address,
    currencies: [{ symbol: "AVAX" }],
  });

  const balance = useMemo(() => {
    if (!balances || !balances.AVAX) return "0.00";

    const formattedEther = formatEther(balances.AVAX.balance.toString());

    if (parseFloat(formattedEther) >= 1) {
      return parseFloat(formattedEther).toFixed(2);
    }

    return formattedEther;
  }, [balances]);

  const isBanned = user?.flag === UserFlaggedEnum.SUSPENDED;

  const { handleLogOut } = useDynamicContext();

  return (
    <Drawer
      direction="left"
      disablePreventScroll
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <DrawerTrigger className="outline-none">
        <Avatar className="size-[34px]">
          <AvatarImage src={user?.twitterPicture} />
          <AvatarFallback />
        </Avatar>
      </DrawerTrigger>
      <DrawerContent className="pt-pwa pb-pwa flex h-full w-[70%] touch-auto flex-col gap-4 rounded-none border-y-0 border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.95)] bg-none p-0 shadow-[0px_4px_16px_0px_rgba(0,0,0,0.25)] backdrop-blur-sm">
        <div className="flex flex-col gap-4 overflow-y-auto">
          <DrawerClose asChild>
            <button className="ml-6 mt-8">
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </button>
          </DrawerClose>
          <div
            className={cn(
              "mt-4 flex flex-col items-start px-6",
              isBanned && "pointer-events-none opacity-20",
            )}
            tabIndex={isBanned ? -1 : 0}
          >
            <ProgressBarLink href={`/${user?.twitterHandle}`}>
              <Avatar className="size-[53px]">
                <AvatarImage src={user?.twitterPicture} />
                <AvatarFallback />
              </Avatar>
            </ProgressBarLink>
            <ProgressBarLink
              href={`/${user?.twitterHandle}`}
              className="mt-3 text-base font-medium leading-5 text-off-white"
            >
              {user?.twitterName}
            </ProgressBarLink>
            <ProgressBarLink
              href={`/${user?.twitterHandle}`}
              className="mt-1 text-xs text-gray-text"
            >
              {user?.twitterHandle}
            </ProgressBarLink>
            <Button
              className="mt-5 flex h-auto gap-[10px] border-brand-orange px-3 py-2"
              variant="outline"
            >
              <img
                src="/images/avax.png"
                className="size-4 rounded-full"
                alt="AVAX logo"
              />
              <span className="text-base font-medium leading-5 text-off-white">
                {balance}
              </span>
            </Button>
          </div>
          <div
            className={cn(
              "mt-8 flex flex-col",
              isBanned && "pointer-events-none opacity-20",
            )}
            tabIndex={isBanned ? -1 : 0}
          >
            <Button
              className="flex h-auto w-full items-center justify-between rounded-none border-x-0 border-y border-dark-gray px-6 py-4 text-sm font-normal text-gray-text"
              variant="outline"
              asChild
            >
              <ProgressBarLink href={`/${user?.twitterHandle}`}>
                Profile
              </ProgressBarLink>
            </Button>
            <Button
              className="flex h-auto w-full items-center justify-between rounded-none border-0 border-b border-dark-gray px-6 py-4 text-sm font-normal text-gray-text"
              variant="outline"
              asChild
            >
              <ProgressBarLink href="/create-community">
                Arena Launch
              </ProgressBarLink>
            </Button>
            <Button
              className="flex h-auto w-full items-center justify-between rounded-none border-0 border-b border-dark-gray px-6 py-4 text-sm font-normal text-gray-text"
              variant="outline"
              asChild
            >
              <ProgressBarLink href="/referral">Refer & Earn</ProgressBarLink>
            </Button>
            <Button
              className="flex h-auto w-full items-center justify-between rounded-none border-0 border-b border-dark-gray px-6 py-4 text-sm font-normal text-gray-text"
              variant="outline"
              asChild
            >
              <ProgressBarLink href="/bookmarks">Bookmarks</ProgressBarLink>
            </Button>
            <Button
              className="flex h-auto w-full items-center justify-between rounded-none border-0 border-b border-dark-gray px-6 py-4 text-sm font-normal text-gray-text"
              variant="outline"
              asChild
            >
              <ProgressBarLink href="/app-store">
                Arena App Store
              </ProgressBarLink>
            </Button>
            <Button
              className="flex h-auto w-full items-center justify-between rounded-none border-0 border-b border-dark-gray px-6 py-4 text-sm font-normal text-gray-text"
              variant="outline"
              asChild
            >
              <ProgressBarLink href="/tokenomics">
                $ARENA Tokenomics
              </ProgressBarLink>
            </Button>
            <Button
              className="flex h-auto w-full items-center justify-between rounded-none border-0 border-b border-dark-gray px-6 py-4 text-sm font-normal text-gray-text"
              variant="outline"
              asChild
            >
              <a href="https://arena.trade/" target="_blank" rel="noreferrer">
                ArenaBook
              </a>
            </Button>
            {user?.isMod && (
              <Button
                className="flex h-auto w-full items-center justify-between rounded-none border-0 border-b border-dark-gray px-6 py-4 text-sm font-normal text-gray-text"
                variant="outline"
                asChild
              >
                <ProgressBarLink href="/moderation/reports">
                  Moderation
                </ProgressBarLink>
              </Button>
            )}
            <Button
              className="flex h-auto w-full items-center justify-between rounded-none border-0 border-b border-dark-gray px-6 py-4 text-sm font-normal text-gray-text"
              variant="outline"
              asChild
            >
              <ProgressBarLink href="/settings">
                Setting & Support
              </ProgressBarLink>
            </Button>
          </div>
          <div className="mt-auto px-6 pb-8 pt-6">
            <Button
              variant="outline"
              className="w-full"
              onClick={async () => {
                handleLogOut();
              }}
            >
              Log out
            </Button>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
};
