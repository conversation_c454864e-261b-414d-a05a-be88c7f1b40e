"use client";

import { useEffect, useRef, useState } from "react";

import { PauseIcon, PlayIcon, RotateCcwIcon, RotateCwIcon } from "lucide-react";

import { ArrowBackOutlineIcon } from "../icons";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "../ui/dialog";
import { Slider } from "../ui/slider";
import { ROLE_NAMES, ROLES } from "./constants";

export const PlayStageModal = ({
  children,
  recordingURL,
  avatar,
  name,
}: {
  children?: React.ReactNode;
  recordingURL: string;
  avatar: string;
  name: string;
}) => {
  const [open, setOpen] = useState(false);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    const audio = new Audio(recordingURL);
    audioRef.current = audio;

    const setAudioData = () => {
      setDuration(audio.duration);
      setCurrentTime(audio.currentTime);
      setIsLoaded(true);
    };

    const setAudioTime = () => {
      const newProgress = (audio.currentTime / audio.duration) * 100;
      setProgress(newProgress);
      setCurrentTime(audio.currentTime);
    };

    audio.addEventListener("loadedmetadata", setAudioData);
    audio.addEventListener("timeupdate", setAudioTime);
    audio.addEventListener("ended", () => setIsPlaying(false));

    return () => {
      audio.removeEventListener("loadedmetadata", setAudioData);
      audio.removeEventListener("timeupdate", setAudioTime);
      audio.removeEventListener("ended", () => setIsPlaying(false));
      audio.pause();
      audio.currentTime = 0;
    };
  }, [recordingURL]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.pause();
    setIsPlaying(false);
  }, [open]);

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleProgressChange = (newValue: number[]) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = (newValue[0] / 100) * audio.duration;
    audio.currentTime = newTime;
    setProgress(newValue[0]);
    setCurrentTime(newTime);
  };

  const skipTime = (seconds: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.currentTime += seconds;
  };

  const formatTime = (time: number): string => {
    if (isNaN(time)) return "0:00";
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  if (!isLoaded) {
    return <div>Loading audio...</div>;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {children ? <DialogTrigger>{children}</DialogTrigger> : null}
      <DialogContent
        // className="pt-pwa z-50 flex h-full w-full flex-col gap-0 bg-dark-bk px-0 pb-0 sm:h-auto sm:py-6"
        className="w-[90%] rounded-[20px] border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] backdrop-blur-sm"
      >
        <div className="mb-6 flex items-center gap-2">
          <DialogClose>
            <ArrowBackOutlineIcon className="size-5" />
          </DialogClose>
          <h2 className="text-base font-semibold text-off-white">Play stage</h2>
        </div>
        <div className="my-6 flex flex-col items-center">
          <Avatar className="size-[60px]">
            <AvatarImage src={avatar} />
            <AvatarFallback />
          </Avatar>
          <div className="mt-2 text-center">
            <h3 className="mt-3 text-base font-semibold leading-[22px] text-off-white">
              {name}
            </h3>
            <span className="whitespace-nowrap text-sm">
              {ROLE_NAMES[ROLES.HOST]}
            </span>
          </div>
        </div>
        <div>
          <Slider
            value={[progress]}
            onValueChange={handleProgressChange}
            max={100}
            step={0.1}
            className="w-full"
          />
          <div className="text-gray-400 mt-1 flex justify-between text-xs">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>
        <div className="flex items-center justify-center">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => skipTime(-15)}
              aria-label="Rewind 15 seconds"
            >
              <RotateCcwIcon className="size-6" />
            </Button>
            <Button
              className="size-12 p-0"
              onClick={togglePlayPause}
              aria-label={isPlaying ? "Pause" : "Play"}
            >
              {isPlaying ? <PauseIcon size={24} /> : <PlayIcon size={24} />}
            </Button>
            <Button
              variant="ghost"
              onClick={() => skipTime(15)}
              aria-label="Forward 15 seconds"
            >
              <RotateCwIcon className="size-6" />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
