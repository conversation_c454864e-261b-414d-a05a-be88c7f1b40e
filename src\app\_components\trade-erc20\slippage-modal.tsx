"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  <PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  <PERSON>er<PERSON>eader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/utils";
import { isNumber } from "@/utils/is-number";

const amounts = [0.1, 0.5, 1];

interface SlippageProps {
  slippage: number;
  setSlippage: Dispatch<SetStateAction<number>>;
}

const Content: FC<SlippageProps> = ({ slippage, setSlippage }) => {
  const [value, setValue] = useState<string>("0.5");
  const inputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let input = e.target.value;
    if (input === "" || !isNumber(input)) input = "0";
    if (!/^\d+(\.\d{0,2})?$/.test(input)) return;
    if (Number(input) >= 100) input = "100";
    if (input.startsWith(".")) input = "0" + input;
    if (input.startsWith("0") && input.length > 1 && input[1] !== ".")
      input = input.substring(1);

    setValue(input);
    if (!input.endsWith(".")) setSlippage(Number(input));
  };

  const handleClick = (amount: number) => {
    setValue(String(amount));
    setSlippage(amount);
  };

  useEffect(() => {
    setValue(String(slippage));
    if (!amounts.includes(slippage) && inputRef.current)
      inputRef.current.focus();
  }, []);

  return (
    <div>
      <span className="block text-[16px] font-semibold text-off-white">
        Slippage Tolerance
      </span>
      <span className="block pt-2 text-[14px] text-gray-text">
        Choose how much you’ll allow the price to deviate from the quote to
        execute the trade.
      </span>
      <div className="flex items-center justify-between py-6">
        <div className="flex gap-4">
          {amounts.map((amount) => (
            <Button
              key={amount}
              variant="outline-ghost"
              onClick={() => handleClick(amount)}
              className={cn(
                "h-[34px] p-0 px-3 text-[14px] font-normal data-[state=active]:border-brand-orange",
                {
                  "border-brand-orange font-semibold": slippage === amount,
                },
              )}
            >
              {amount} %
            </Button>
          ))}
        </div>
        <div className="flex w-[80px] flex-row items-center justify-between rounded-lg border border-[#3B3B3B] px-2 transition-colors focus-within:border-off-white">
          <input
            ref={inputRef}
            className="h-[32px] w-[50px] border-0 bg-transparent text-[14px] text-gray-text focus:text-off-white focus:outline-none"
            value={value}
            onChange={handleInputChange}
          />
          <div>%</div>
        </div>
      </div>
    </div>
  );
};

interface SlippageModalProps extends SlippageProps {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
}

export const SlippageModal: FC<SlippageModalProps> = ({
  isOpen,
  setIsOpen,
  slippage,
  setSlippage,
}) => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  if (isTablet) {
    return (
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogHeader className="p-0">
          <DialogTitle />
        </DialogHeader>
        <DialogContent className="bg-[rgba(15,15,15,0.10)] backdrop-blur-[8px] sm:bg-[rgba(15,15,15,0.10)]">
          <Content slippage={slippage} setSlippage={setSlippage} />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerHeader className="p-0">
        <DrawerTitle />
      </DrawerHeader>
      <DrawerContent className="bg-[rgba(15,15,15,0.10)] backdrop-blur-[8px]">
        <Content slippage={slippage} setSlippage={setSlippage} />
      </DrawerContent>
    </Drawer>
  );
};
