// import { assets } from "@/data/threads";

import { Asset } from "@/types";

import { ImagePreviewModal } from "./image-preview-modal";

interface PhotoPageProps {
  params: { id: string };
}

function PhotoPage({ params: { id } }: PhotoPageProps) {
  const assets: Asset[] = [];
  const asset = assets.find((asset) => asset.id === parseInt(id, 10));

  if (!asset) return <div>Asset not found</div>;

  return <ImagePreviewModal url={asset.url} />;
}

export default PhotoPage;
