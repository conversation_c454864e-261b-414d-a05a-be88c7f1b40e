import { axios } from "@/lib/axios";
import {
  CloudMessagingSettingsResponse,
  SaveCloudMessagingSettingsVariables,
} from "@/queries/types/cloud-messaging";

export const getCloudMessagingSettings = async () => {
  const response = await axios.get<CloudMessagingSettingsResponse>(
    `/cloud-messaging/settings`,
  );
  return response.data;
};

export const postCloudMessagingPushToken = async ({
  notificationToken,
  deviceType,
}: {
  notificationToken: string;
  deviceType: string;
}) => {
  const response = await axios.post(`/cloud-messaging/push/token`, {
    notificationToken,
    deviceType,
  });
  return response.data;
};

export const postSaveCloudMessagingSettings = async ({
  settings,
}: SaveCloudMessagingSettingsVariables) => {
  const response = await axios.post(`/cloud-messaging/settings`, {
    settings,
  });
  return response.data;
};
