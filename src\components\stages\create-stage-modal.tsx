"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";

import { DeleteStageModal } from "@/app/compose/stage/_components/delete-stage-modal";
import { StageScheduleModal } from "@/app/compose/stage/_components/stage-schedule-modal";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useCreateStageMutation, useEditStageMutation } from "@/queries";
import { stageQueries } from "@/queries/stage-queries";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";
import { ThreadPrivacyTypeEnum } from "@/types";
import { cn } from "@/utils";

import {
  ArrowBackOutlineIcon,
  CalendarOutlineIcon,
  PencilAltOutlineIcon,
  TrashOutlineIcon,
} from "../icons";
import { toast } from "../toast";
import { AlertDialog, AlertDialogContent } from "../ui/alert-dialog";
import { Button } from "../ui/button";
import { Drawer, DrawerContent } from "../ui/drawer";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Switch } from "../ui/switch";
import { useStageEditor } from "./hooks/use-stage-editor";

const MAX_LENGTH = 60;

export function CreateStageModal() {
  const [{ composeStage: open, stageId }, setStageEditor] = useStageEditor();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { user } = useUser();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const { data: stageData, isLoading } = useQuery({
    ...stageQueries.stageInfoSimple(stageId ?? ""),
    enabled: !!stageId,
  });

  const [spaceTopic, setSpaceTopic] = useState(
    () => stageData?.stage.name ?? "",
  );
  const [record, setRecord] = useState(
    () => stageData?.stage.isRecorded ?? false,
  );
  const [isGated, setIsGated] = useState(() => {
    if (!stageData?.stage) return false;
    return (
      stageData.stage.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS ||
      stageData.stage.privacyType === ThreadPrivacyTypeEnum.BADGEHOLDERS
    );
  });
  const [gateType, setGateType] = useState<"ticket" | "badge">(() => {
    if (!stageData?.stage) return "ticket";
    return stageData.stage.privacyType === ThreadPrivacyTypeEnum.BADGEHOLDERS
      ? "badge"
      : "ticket";
  });
  const [selectedBadges, setSelectedBadges] = useState<
    {
      name: string;
      type: number;
      image: string;
    }[]
  >(
    () =>
      stageData?.stage.badgeTypes?.map((badge) => {
        const badgeInfo = badges.find((b) => b.type === badge.badgeType);
        return {
          name: badgeInfo?.name ?? "",
          type: badge.badgeType,
          image: badgeInfo?.image ?? "",
        };
      }) ?? [],
  );
  const [scheduledDate, setScheduledDate] = useState<string | null>(
    () => stageData?.stage.scheduledStartTime ?? null,
  );
  const [isEditingBadges, setIsEditingBadges] = useState(false);

  const actions = useStageStore((state) => state.actions);

  const { mutateAsync: createStage, isPending } = useCreateStageMutation({
    onSuccess: (data) => {
      toast.green("You created a new stage!");
      setSpaceTopic("");
      setScheduledDate(null);
      setIsGated(false);
      setRecord(false);
      setGateType("ticket");
      setSelectedBadges([]);
      queryClient.resetQueries({
        queryKey: ["home", "threads", "stages-feed"],
      });
      if (data.token && !data.stage.scheduledStartTime) {
        router.replace(`/live?streamTab=stages`);
        actions.setToken(data.token ?? null);
        actions.setId(data.stage.id);
      } else {
        const url = new URL(window.location.href);
        url.search = "";
        window.history.replaceState({}, "", url);
        router.push(`/${user?.twitterHandle}/status/${data.stage.threadId}`);
      }
    },
  });

  const { mutateAsync: editStage, isPending: isEditingPending } =
    useEditStageMutation({
      onSuccess: (data) => {
        toast.green("Stage updated successfully!");
        setSpaceTopic("");
        setScheduledDate(null);
        setIsGated(false);
        setRecord(false);
        setGateType("ticket");
        setSelectedBadges([]);
        queryClient.resetQueries({
          queryKey: ["home", "threads", "stages-feed"],
        });
        const url = new URL(window.location.href);
        url.search = "";
        window.history.replaceState({}, "", url);
        router.push(`/${user?.twitterHandle}/status/${data.stage.threadId}`);
      },
    });

  const isButtonDisabled =
    spaceTopic.trim().length === 0 ||
    (gateType === "badge" && selectedBadges.length === 0);

  const handleStartStage = async () => {
    if (spaceTopic.trim() === "") {
      toast.danger("Please enter a name before proceeding");
      return;
    }

    if (stageId) {
      editStage({
        stageId: stageId,
        name: spaceTopic,
        record: record,
        privacyType:
          isGated && gateType === "ticket"
            ? ThreadPrivacyTypeEnum.SHAREHOLDERS
            : isGated && gateType === "badge"
              ? ThreadPrivacyTypeEnum.BADGEHOLDERS
              : ThreadPrivacyTypeEnum.PUBLIC,
        badgeTypes:
          isGated && gateType === "badge" && selectedBadges.length > 0
            ? selectedBadges.map((badge) => badge.type)
            : undefined,
        scheduledStartTime: scheduledDate ?? undefined,
      });
    } else {
      createStage({
        name: spaceTopic,
        record: record,
        privacyType:
          isGated && gateType === "ticket"
            ? ThreadPrivacyTypeEnum.SHAREHOLDERS
            : isGated && gateType === "badge"
              ? ThreadPrivacyTypeEnum.BADGEHOLDERS
              : ThreadPrivacyTypeEnum.PUBLIC,
        badgeTypes:
          isGated && gateType === "badge" && selectedBadges.length > 0
            ? selectedBadges.map((badge) => badge.type)
            : undefined,
        scheduledStartTime: scheduledDate ?? undefined,
      });
    }
  };

  useEffect(() => {
    if (stageId && !isLoading) {
      if (stageData) {
        if (stageData.stage.hostId !== user?.id) {
          toast.danger("You are not the host of this stage");
          router.replace("/compose/stage");
          return;
        }

        setSpaceTopic(stageData.stage.name);
        setRecord(stageData.stage.isRecorded);
        setIsGated(
          stageData.stage.privacyType !== ThreadPrivacyTypeEnum.PUBLIC,
        );
        setGateType(
          stageData.stage.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS
            ? "ticket"
            : stageData.stage.privacyType === ThreadPrivacyTypeEnum.BADGEHOLDERS
              ? "badge"
              : "ticket",
        );
        setSelectedBadges(
          stageData.stage.badgeTypes?.map((badge) => {
            const badgeInfo = badges.find((b) => b.type === badge.badgeType);
            return {
              name: badgeInfo?.name ?? "",
              type: badge.badgeType,
              image: badgeInfo?.image ?? "",
            };
          }) ?? [],
        );
        setScheduledDate(stageData.stage.scheduledStartTime ?? null);
      } else {
        router.replace("/compose/stage");
      }
    }
  }, [stageId, stageData?.stage.scheduledStartTime, isLoading]);

  useEffect(() => {
    if (!open) {
      setSpaceTopic("");
      setRecord(false);
      setIsGated(false);
      setGateType("ticket");
      setSelectedBadges([]);
      setScheduledDate(null);
      setIsEditingBadges(false);
      setStageEditor({ stageId: null });
    }
  }, [open]);

  return (
    <AlertDialog
      open={open}
      onOpenChange={(open) => {
        setStageEditor({ composeStage: open });
      }}
    >
      <AlertDialogContent
        className={cn(
          "pt-pwa flex h-full w-full flex-col overflow-y-auto bg-dark-bk/75 px-0 py-2 backdrop-blur-sm transition-colors sm:h-auto sm:bg-dark-bk/75",
          isEditingBadges &&
            isTablet &&
            "max-w-sm bg-[#0F0F0F]/90 sm:bg-[#0F0F0F]/90",
        )}
      >
        {isEditingBadges && isTablet ? (
          <EditBadges
            onClose={() => setIsEditingBadges(false)}
            initialBadges={selectedBadges}
            onConfirm={(badges) => {
              setSelectedBadges(badges);
              setIsEditingBadges(false);
            }}
          />
        ) : (
          <div className="flex h-full flex-col">
            <div className="flex items-center p-6">
              <div className="flex flex-1 justify-start">
                <button
                  onClick={() => {
                    setStageEditor({ composeStage: false });
                  }}
                >
                  <ArrowBackOutlineIcon className="size-5 text-off-white" />
                </button>
              </div>
              <h3 className="text-base font-semibold leading-5 text-white">
                Create your Stage
              </h3>
              {stageId && stageData && stageData.stage.hostId === user?.id ? (
                <div className="flex flex-1 justify-end">
                  <DeleteStageModal>
                    <button>
                      <TrashOutlineIcon className="size-5 text-brand-orange" />
                    </button>
                  </DeleteStageModal>
                </div>
              ) : (
                <div className="flex-1" />
              )}
            </div>
            <div className="flex flex-grow flex-col sm:flex-grow-0">
              <div className="p-6">
                <div className="flex flex-col gap-2">
                  <div className="flex items-center justify-between gap-2">
                    <Label htmlFor="stage-editor" className="text-off-white">
                      Name
                    </Label>
                  </div>
                  <Input
                    id="stage-editor"
                    type="text"
                    placeholder="Name your Stage!"
                    className="min-h-[50px] w-full bg-transparent p-4 pr-10 text-sm text-off-white placeholder:text-gray-text focus:outline-none"
                    value={spaceTopic}
                    onChange={(e) => setSpaceTopic(e.target.value)}
                    maxLength={MAX_LENGTH}
                  >
                    <span className="pointer-events-none absolute right-4 top-4 text-sm text-gray-text">
                      {MAX_LENGTH - spaceTopic.length}
                    </span>
                  </Input>
                </div>
              </div>
              <div className="flex w-full flex-col gap-6 px-6 sm:justify-start sm:gap-4">
                <Label className="flex w-full items-center justify-between gap-2">
                  <div className="text-sm font-medium normal-case text-off-white">
                    Record my Stage
                  </div>
                  <Switch checked={record} onCheckedChange={setRecord} />
                </Label>
                <Label className="flex w-full items-center justify-between gap-2">
                  <div className="flex flex-col gap-1.5">
                    <div className="text-sm font-medium normal-case text-off-white">
                      Gate my Stage
                    </div>
                    <p className="max-w-48 text-xs font-normal normal-case text-gray-text">
                      Select who can join your Stage
                    </p>
                  </div>
                  <Switch checked={isGated} onCheckedChange={setIsGated} />
                </Label>
                {isGated ? (
                  <div className="mt-6 flex flex-col gap-4">
                    <div className="flex items-center justify-between gap-4">
                      <Button
                        className="flex-1 justify-start rounded-[10px] p-4 text-left text-sm"
                        onClick={() => setGateType("ticket")}
                        variant={gateType === "ticket" ? "default" : "outline"}
                      >
                        Ticket Gated <br /> Stage
                      </Button>
                      <Button
                        className="flex-1 justify-start rounded-[10px] p-4 text-left text-sm"
                        onClick={() => setGateType("badge")}
                        variant={gateType === "badge" ? "default" : "outline"}
                      >
                        Badge Gated <br /> Stage
                      </Button>
                    </div>
                    {gateType === "ticket" ? (
                      <p className="text-xs text-off-white">
                        Only your ticket holders will be allowed to join your
                        stage.
                      </p>
                    ) : null}
                    {gateType === "badge" ? (
                      <>
                        <p className="text-xs text-off-white">
                          Pick up to 3 badges to allow them to join your stage.
                        </p>
                        <div className="flex flex-wrap items-center justify-center gap-[14px] rounded-[10px] px-4 py-6">
                          {selectedBadges.map(({ name, image, type }) => {
                            return (
                              <Button
                                key={type}
                                variant="outline"
                                className={cn(
                                  "flex-shrink-0 cursor-default gap-1.5 border-brand-orange py-1.5 text-base font-medium leading-5 hover:bg-transparent",
                                )}
                              >
                                <div className="w-max">
                                  <img
                                    src={image}
                                    className="h-[18px] w-auto"
                                    alt={`${name} logo`}
                                  />
                                </div>
                                <span>{name}</span>
                              </Button>
                            );
                          })}
                          <Button
                            variant="outline"
                            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5"
                            onClick={() => {
                              setIsEditingBadges(true);
                            }}
                          >
                            <PencilAltOutlineIcon className="size-5 text-off-white" />
                            <span>Edit</span>
                          </Button>
                        </div>
                      </>
                    ) : null}
                  </div>
                ) : null}
              </div>
              <div className="mt-auto flex flex-shrink-0 flex-col gap-4 px-6 py-4 sm:mt-0 sm:items-end sm:justify-between sm:gap-4">
                <div className="flex w-full gap-2">
                  <Button
                    className="w-full flex-grow sm:w-auto sm:px-8 sm:py-2"
                    onClick={handleStartStage}
                    loading={isPending || isEditingPending}
                    disabled={isButtonDisabled}
                  >
                    {scheduledDate ? "Save Scheduled Stage" : "Start Now"}
                  </Button>
                  <StageScheduleModal
                    scheduledDate={scheduledDate}
                    setScheduledDate={setScheduledDate}
                  >
                    <Button variant="outline" className="size-11">
                      <CalendarOutlineIcon className="size-6 text-off-white" />
                    </Button>
                  </StageScheduleModal>
                </div>
                {scheduledDate ? (
                  <div className="w-full rounded-[10px] bg-chat-bubble p-2 text-center text-xs text-gray-text">
                    Starting on{" "}
                    {format(new Date(scheduledDate), "EEEE, MMM d 'at' h:mm a")}
                  </div>
                ) : null}
              </div>
            </div>
          </div>
        )}
        <Drawer
          open={isEditingBadges && !isTablet}
          onOpenChange={setIsEditingBadges}
        >
          <DrawerContent className="p-0">
            <EditBadges
              onClose={() => setIsEditingBadges(false)}
              initialBadges={selectedBadges}
              onConfirm={(badges) => {
                setSelectedBadges(badges);
                setIsEditingBadges(false);
              }}
            />
          </DrawerContent>
        </Drawer>
      </AlertDialogContent>
    </AlertDialog>
  );
}

interface EditBadgesProps {
  onClose: () => void;
  initialBadges: { name: string; type: number; image: string }[];
  onConfirm: (
    badges: {
      name: string;
      type: number;
      image: string;
    }[],
  ) => void;
}

const EditBadges = ({ onClose, initialBadges, onConfirm }: EditBadgesProps) => {
  const [selectedBadges, setSelectedBadges] = useState<
    {
      name: string;
      type: number;
      image: string;
    }[]
  >(initialBadges);
  return (
    <div className="flex h-full flex-col gap-8 p-6">
      <div className="flex h-full flex-col gap-2">
        <div className="flex items-center gap-2">
          <div className="flex justify-start">
            <button onClick={onClose}>
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </button>
          </div>
          <h3 className="text-xl font-semibold leading-6 text-white">
            Select Badges
          </h3>
        </div>
        <p className="text-xs text-off-white">
          Pick up to 3 badges to allow them to join your stage.
        </p>
      </div>
      <div className="flex flex-wrap items-center justify-center gap-[14px] rounded-[10px]">
        {badges.map(({ name, image, type }) => {
          const isSelected = selectedBadges.some(
            (badge) => badge.type === type,
          );
          return (
            <Button
              key={type}
              variant="outline"
              className={cn(
                "flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5",
                isSelected && "border-brand-orange",
              )}
              onClick={() => {
                if (isSelected) {
                  setSelectedBadges((prev) =>
                    prev.filter((badge) => badge.type !== type),
                  );
                } else {
                  if (selectedBadges.length >= 3) {
                    toast.danger("You can only select up to 3 badges");
                  } else {
                    setSelectedBadges((prev) => [
                      ...prev,
                      { name, type, image },
                    ]);
                  }
                }
              }}
            >
              <div className="w-max">
                <img
                  src={image}
                  className={cn("h-[18px] w-auto", !isSelected && "grayscale")}
                  alt={`${name} logo`}
                />
              </div>
              <span>{name}</span>
            </Button>
          );
        })}
      </div>
      <Button
        className="w-full flex-grow sm:w-auto sm:px-8 sm:py-2"
        onClick={() => {
          onConfirm(selectedBadges);
        }}
      >
        Confirm
      </Button>
    </div>
  );
};

const badges = [
  {
    name: "Arena OGs",
    type: 1,
    image: "/assets/badges/badge-type-1.png",
  },
  {
    name: "DeGods",
    type: 2,
    image: "/assets/badges/badge-type-2.png",
  },
  {
    name: "Dokyo",
    type: 3,
    image: "/assets/badges/badge-type-3.png",
  },
  {
    name: "Sappy Seals",
    type: 4,
    image: "/assets/badges/badge-type-4.png",
  },
  {
    name: "GURS",
    type: 5,
    image: "/assets/badges/badge-type-5.png",
  },
  {
    name: "Nochill",
    type: 6,
    image: "/assets/badges/badge-type-6.png",
  },
  {
    name: "Steady",
    type: 8,
    image: "/assets/badges/badge-type-8.png",
  },
  {
    name: "Smol Joe",
    type: 9,
    image: "/assets/badges/badge-type-9.png",
  },
  {
    name: "Gogonauts",
    type: 11,
    image: "/assets/badges/badge-type-11.png",
  },
  {
    name: "Bodoggos",
    type: 12,
    image: "/assets/badges/badge-type-12.png",
  },
  {
    name: "Pudgy Penguins",
    type: 13,
    image: "/assets/badges/badge-type-13.png",
  },
  {
    name: "COQ",
    type: 14,
    image: "/assets/badges/badge-type-14.png",
  },
  {
    name: "MOG",
    type: 15,
    image: "/assets/badges/badge-type-15.png",
  },
  {
    name: "Mad Lads",
    type: 16,
    image: "/assets/badges/badge-type-16.png",
  },
  {
    name: "Nochillio",
    type: 18,
    image: "/assets/badges/badge-type-18.png",
  },
  {
    name: "Arena Champion",
    type: 19,
    image: "/assets/badges/badge-type-19.png",
  },
  {
    name: "AuTistiC BoYs CLub",
    type: 20,
    image: "/assets/badges/badge-type-20.png",
  },
  {
    name: "MU Doggy",
    type: 21,
    image: "/assets/badges/badge-type-21.png",
  },
  {
    name: "MU",
    type: 22,
    image: "/assets/badges/badge-type-22.png",
  },
  {
    name: "Bad Bois",
    type: 23,
    image: "/assets/badges/badge-type-23.png",
  },
  {
    name: "Boi",
    type: 24,
    image: "/assets/badges/badge-type-24.png",
  },
  {
    name: "LOK",
    type: 25,
    image: "/assets/badges/badge-type-25.png",
  },
  {
    name: "EROL",
    type: 26,
    image: "/assets/badges/badge-type-26.png",
  },
  {
    name: "Doomercorp",
    type: 27,
    image: "/assets/badges/badge-type-27.png",
  },
];
