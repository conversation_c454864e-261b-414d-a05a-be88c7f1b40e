import { useState } from "react";

import { DialogTitle } from "@radix-ui/react-dialog";

import { ExclaimationCircleIcon } from "@/components/icons-v2/exclaimation-circle";
import { Dialog, DialogContent } from "@/components/ui/dialog";

export const AlertModal = () => {
  const [open, setOpen] = useState(true);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTitle className="hidden" />
      <DialogContent className="flex w-[90%] max-w-[400px] gap-5 overflow-hidden rounded-[10px] border border-[rgba(284,84,10,0.30)] bg-dark-bk p-0 focus:outline-none sm:rounded-[10px] sm:border-[rgba(284,84,10,0.30)] sm:bg-dark-bk">
        <div className="w-2 bg-brand-orange"></div>
        <div className="flex items-center justify-center">
          <ExclaimationCircleIcon />
        </div>
        <div className="flex flex-col items-start justify-center gap-2 py-6 pr-4">
          <div className="text-sm font-semibold text-[#f3f3f3]">
            Before you start!
          </div>
          <p className="text-xs font-normal text-[#b4b4b4]">
            Upon graduation, 2.5% of your token’s supply will be airdropped to
            Arena Champions, the most loyal, dedicated, and diamond handed users
            of The Arena.
          </p>
          <p className="text-xs font-normal text-[#b4b4b4]">
            Also, it will cost a small amount of AVAX to create a token.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};
