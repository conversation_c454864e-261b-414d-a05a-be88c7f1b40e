import { axios } from "@/lib/axios";

export const reportUser = async (data: {
  userId: string;
  reason: string;
  details: string;
  blockUser?: boolean;
}) => {
  const response = await axios.post(`/report/user`, data);
  return response.data;
};

export const reportThread = async (data: {
  threadId: string;
  reason: string;
  details: string;
  blockUser?: boolean;
}) => {
  const response = await axios.post(`/report/thread`, data);
  return response.data;
};
