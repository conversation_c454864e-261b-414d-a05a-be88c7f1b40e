"use client";

import { FC } from "react";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

import { ReportTicketDetails } from "../_components/report-ticket-details";
import { useReportsContext } from "../context/reports-context";

interface TicketPageProps {
  params: { ticketId: string };
}

const TicketPage: FC<TicketPageProps> = ({ params }) => {
  const ticketId = Number(params.ticketId);
  const { activeGroups, closedGroups } = useReportsContext();
  const isLargeTablet = useMediaQuery(BREAKPOINTS.lg);

  if (isNaN(ticketId)) return <div>Ticket not found</div>;

  if (isLargeTablet) {
    return null;
  }

  const group = [...activeGroups, ...closedGroups].find((group) =>
    group.tickets.some((t) => t.id === ticketId),
  );

  return (
    <ReportTicketDetails
      ticketId={ticketId}
      group={group ? { tickets: group.tickets } : undefined}
    />
  );
};

export default TicketPage;
