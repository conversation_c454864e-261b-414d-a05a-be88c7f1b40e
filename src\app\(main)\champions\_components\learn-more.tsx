"use client";

import { FC, useState } from "react";

import { ChampionsLearnMoreModal } from "./champions-learn-more";

interface LearnMoreItemProps {
  isTablet: boolean;
}

export const LearnMore: FC<LearnMoreItemProps> = ({ isTablet }) => {
  const [open, setOpen] = useState(false);

  return !open ? (
    <div className="sticky bottom-[48px] z-20 items-center justify-start gap-3 bg-[#000000] py-3 text-center md:bottom-[0px]">
      <div className="flex items-center justify-center text-xs">
        <span className="cursor-pointer text-gray-text">
          Learn More about&nbsp;
        </span>
        <span
          className="cursor-pointer whitespace-normal text-brand-orange"
          onClick={() => setOpen(true)}
        >
          Arena Champions
        </span>
      </div>
    </div>
  ) : (
    <ChampionsLearnMoreModal isTablet={isTablet} setOpen={setOpen} />
  );
};
