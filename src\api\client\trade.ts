import { axios } from "@/lib/axios";

export const getTrades = async () => {
  const response = await axios.get("/trade/trades");
  return response.data;
};

export const getRecentTrades = async () => {
  const response = await axios.get("/trade/recent");
  return response.data;
};

export const getTrendingUsers = async () => {
  const response = await axios.get("/trade/users/trending");
  return response.data;
};

export const searchTicketTipping = async (search: string) => {
  const searchParams = new URLSearchParams({
    search,
  });

  const response = await axios.get(
    `/trade/tipping_search?${searchParams.toString()}`,
  );
  return response.data;
};

export const postBuyShare = async ({
  address,
  amount,
}: {
  address: string;
  amount: string;
}) => {
  const response = await axios.post("/trade/buy", {
    address,
    amount,
  });
  return response.data;
};

export const postSellShare = async ({
  address,
  amount,
}: {
  address: string;
  amount: string;
}) => {
  const response = await axios.post("/trade/sell", {
    address,
    amount,
  });
  return response.data;
};

export const postTradeNotification = async ({
  subjectAddress,
  isBuy,
  amount,
}: {
  subjectAddress: string;
  isBuy: boolean;
  amount: string;
}) => {
  const response = await axios.post("/trade/postTradeNotification", {
    subjectAddress,
    isBuy,
    amount,
  });
  return response.data;
};

export const postTicketTransferNotification = async ({
  subjectAddress,
  to,
  amount,
  txHash,
}: {
  subjectAddress: string;
  to: string;
  amount: string;
  txHash: string;
}) => {
  const response = await axios.post("/trade/ticketTransferNotification", {
    subjectAddress,
    to,
    amount,
    txHash,
  });
  return response.data;
};

export const postBuyTicker = async ({
  srcToken,
  destToken,
  srcAmount,
  threadId,
}: {
  srcToken: string;
  destToken: string;
  srcAmount: string;
  threadId?: string;
}) => {
  const response = await axios.post("/trade/ticker/buy", {
    srcToken,
    destToken,
    srcAmount,
    ...(threadId && { threadId }),
  });
  return response.data;
};
