"use client";

import { useMemo } from "react";
import { useParams } from "next/navigation";

import { Virtuoso } from "react-virtuoso";

import { ShareHolderItem } from "@/components/share-holder-item";
import { ShareHolderItemLoadingSkeleton } from "@/components/share-holder-item-loading-skeleton";
import { useSharesHoldersInfiniteQuery, useUserByHandleQuery } from "@/queries";

export const TicketHoldersTab = () => {
  const params = useParams() as { userHandle: string };
  const { data, isLoading } = useUserByHandleQuery(params.userHandle);
  const {
    data: holdersDataInfinite,
    isLoading: isHoldersLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useSharesHoldersInfiniteQuery({
    userId: data?.user.id,
    enabled: !isLoading,
  });

  const holdersData = useMemo(() => {
    return {
      holders:
        holdersDataInfinite?.pages.flatMap((page) => page?.holders || []) || [],
    };
  }, [holdersDataInfinite]);

  return (
    <Virtuoso
      useWindowScroll
      data={holdersData.holders}
      endReached={() => {
        if (hasNextPage) {
          fetchNextPage();
        }
      }}
      overscan={200}
      itemContent={(index, holder) => {
        return <ShareHolderItem holder={holder} />;
      }}
      components={{
        Footer: () => {
          if (isHoldersLoading || isFetchingNextPage) {
            return (
              <>
                {Array.from({ length: 5 }).map((_, i) => (
                  <ShareHolderItemLoadingSkeleton key={i} />
                ))}
              </>
            );
          }
          return null;
        },
      }}
    />
  );
};
