import Skeleton from "react-loading-skeleton";

import { ExternalLinkOutlineIcon } from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { env } from "@/env";
import { useCommunityByIdQuery } from "@/queries";
import { useAvaxPriceQuery } from "@/queries/currency-queries";
import { useCommunityStatsQuery } from "@/queries/groups-queries";
import { formatAsSubscript } from "@/utils/format-as-subscript";
import { formatMarketCap } from "@/utils/format-market-cap";
import { formatPrice } from "@/utils/format-token-price";

import { START_PHASE } from "./consts";

interface CommunityStatsProps {
  communityId: string;
}

export const CommunityStats = ({ communityId }: CommunityStatsProps) => {
  const stageDomain = "https://satest-dev.com";

  const { data } = useCommunityByIdQuery(communityId);
  const { data: statsData, isLoading: isStatsDataLoading } =
    useCommunityStatsQuery(data?.community.contractAddress || "");
  const { data: avaxPrice, isLoading: isFetchingAvaxPrice } =
    useAvaxPriceQuery();

  const communityOwner = data?.community?.owner;
  const price = formatPrice(statsData?.stats?.price ?? "0");
  const usdPriceOfToken = avaxPrice?.avax ? price * avaxPrice?.avax : 0;
  const liquidity =
    formatPrice(statsData?.stats?.liquidity ?? "0") * (avaxPrice?.avax || 0);
  const marketCap =
    formatPrice(statsData?.stats?.marketCap ?? "0") * (avaxPrice?.avax || 0);
  const buyVolumeUsd =
    Number(statsData?.stats?.buyVolume ?? "0") * usdPriceOfToken;
  const sellVolumeUsd =
    Number(statsData?.stats?.sellVolume ?? "0") * usdPriceOfToken;

  return (
    <div className="grid h-full grid-cols-2 gap-3 px-6 pb-[calc(1.5rem+env(safe-area-inset-bottom))] pt-6">
      <div className="col-span-2 flex justify-between rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
        <div className="flex flex-col text-sm font-semibold text-light-gray-text">
          <p>Token</p>
          <p>Created by</p>
        </div>
        {data?.community?.isOwnerExternal ? (
          <div className="flex items-center gap-[10px] overflow-hidden">
            <Avatar className="size-[42px]">
              <AvatarImage
                src={`https://effigy.im/a/${communityOwner?.address}.png`}
              />
              <AvatarFallback />
            </Avatar>
            <div className="flex w-full min-w-0 flex-col gap-1 text-sm leading-4">
              <div className="truncate text-[#808080]">External</div>
              <div className="truncate text-[#808080]">Account</div>
            </div>
          </div>
        ) : (
          <ProgressBarLink
            href={`/${communityOwner?.twitterHandle}`}
            className="flex items-center gap-[10px] overflow-hidden"
          >
            <Avatar className="size-[42px]">
              <AvatarImage src={communityOwner?.twitterPicture} />
              <AvatarFallback />
            </Avatar>
            <div className="flex w-full min-w-0 flex-col gap-1 text-sm leading-4">
              <div className="flex gap-1.5">
                <h4 className="truncate text-[#F4F4F4]">
                  @{communityOwner?.twitterHandle}
                </h4>
              </div>
              <div className="truncate text-[#808080]">
                {communityOwner?.followerCount} Followers
              </div>
            </div>
          </ProgressBarLink>
        )}
      </div>
      <div className="flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
        <h4 className="text-light-gray-text">Price USD</h4>
        <span className="flex flex-row text-base text-off-white">
          {isFetchingAvaxPrice || isStatsDataLoading ? (
            <Skeleton className="h-4 w-16" />
          ) : (
            <>{formatAsSubscript(usdPriceOfToken.toFixed(18))}</>
          )}
        </span>
      </div>
      <div className="flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
        <h4 className="align-sub text-sm text-light-gray-text">Price AVAX</h4>
        <div className="flex items-center gap-2 text-base text-off-white">
          <img
            src="/assets/coins/avax.png"
            className="size-4 rounded-full"
            alt="AVAX logo"
          />
          <span>
            {isFetchingAvaxPrice || isStatsDataLoading ? (
              <Skeleton className="h-4 w-16" />
            ) : (
              formatAsSubscript(price.toFixed(18), false)
            )}
          </span>
        </div>
      </div>
      <div className="flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
        <h4 className="text-light-gray-text">Liquidity</h4>
        <p className="text-base text-off-white">
          {isFetchingAvaxPrice || isStatsDataLoading ? (
            <Skeleton className="h-4 w-16" />
          ) : liquidity && liquidity < 1 ? (
            "< $1"
          ) : (
            `$${formatMarketCap(liquidity)}`
          )}
        </p>
      </div>
      <div className="flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
        <h4 className="text-light-gray-text">MKT CAP</h4>
        <p className="text-base text-off-white">
          {isFetchingAvaxPrice || isStatsDataLoading ? (
            <Skeleton className="h-4 w-16" />
          ) : marketCap < 1 && marketCap ? (
            "< $1"
          ) : (
            `$${formatMarketCap(marketCap)}`
          )}
        </p>
      </div>
      <div className="flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
        <h4 className="text-light-gray-text">Buys/Sells</h4>
        <p className="text-base text-off-white">
          {isFetchingAvaxPrice || isStatsDataLoading ? (
            <Skeleton className="h-4 w-16" />
          ) : (
            <>
              <span className="text-green">+{statsData?.stats?.buys || 0}</span>{" "}
              /
              <span className="text-[#F00]">
                -{statsData?.stats?.sells || 0}
              </span>
            </>
          )}
        </p>
      </div>
      <div className="relative flex flex-col gap-1 rounded-xl border border-dark-gray px-5 py-4 text-sm font-semibold">
        <h4 className="text-light-gray-text">Buy/Sell Volume</h4>
        <p className="text-base text-off-white">
          {isFetchingAvaxPrice || isStatsDataLoading ? (
            <Skeleton className="h-4 w-16" />
          ) : (
            <>
              <span className="text-green">
                +
                {buyVolumeUsd && buyVolumeUsd < 1
                  ? " < $1"
                  : `$${formatMarketCap(buyVolumeUsd)}`}
              </span>{" "}
              /
              <span className="text-[#F00]">
                -
                {sellVolumeUsd && sellVolumeUsd < 1
                  ? " < $1"
                  : `$${formatMarketCap(sellVolumeUsd)}`}
              </span>
            </>
          )}
        </p>
      </div>
      <Button className="col-span-2" asChild>
        {data?.community?.tokenPhase === START_PHASE ? (
          env.NEXT_PUBLIC_APP_DOMAIN === stageDomain ? (
            <a
              href={`https://arenabook.satest-dev.com/token/${data?.community?.contractAddress}`}
              target="_blank"
              rel="noreferrer"
            >
              See in ArenaBook{" "}
              <ExternalLinkOutlineIcon className="ml-1 size-5" />
            </a>
          ) : (
            <a
              href={`https://arena.trade/token/${data?.community?.contractAddress}`}
              target="_blank"
              rel="noreferrer"
            >
              See in ArenaBook{" "}
              <ExternalLinkOutlineIcon className="ml-1 size-5" />
            </a>
          )
        ) : (
          <a
            href={`https://dexscreener.com/avalanche/${data?.community?.contractAddress}`}
            target="_blank"
            rel="noreferrer"
          >
            See on Dexscreener{" "}
            <ExternalLinkOutlineIcon className="ml-1 size-5" />
          </a>
        )}
      </Button>
    </div>
  );
};
