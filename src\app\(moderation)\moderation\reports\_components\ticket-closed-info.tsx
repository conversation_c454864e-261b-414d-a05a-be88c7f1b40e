"use client";

import { format } from "date-fns";

import { ModeratorAction } from "@/queries/report-tickets-mutations";
import { ModeratorActionLog } from "@/queries/types/report-tickets";

import { UserLink } from "../_components/ticket-details/user-link";

interface TicketClosedInfoProps {
  logs: ModeratorActionLog[];
}

export const TicketClosedInfo = ({ logs }: TicketClosedInfoProps) => {
  if (!logs || logs.length === 0) return null;

  const closeLog = logs.find(
    (log) => log.action === ModeratorAction.CLOSE_REPORT,
  );
  const otherActions = logs.filter(
    (log) =>
      log.action !== ModeratorAction.CLOSE_REPORT &&
      log.action !== ModeratorAction.REOPEN_TICKET,
  );

  const uniqueActions = otherActions.reduce(
    (acc, log) => {
      if (!acc[log.action as ModeratorAction]) {
        acc[log.action as ModeratorAction] = log;
      }
      return acc;
    },
    {} as Record<ModeratorAction, ModeratorActionLog>,
  );

  const uniqueActionList = Object.values(uniqueActions);

  return (
    <div className="flex gap-6 rounded-lg bg-[#111] p-4 text-sm">
      {closeLog && (
        <>
          <div className="flex flex-col space-y-4 font-semibold">
            <h3>Closed by</h3>
            <h3>Closed on</h3>
            <h3>Action taken</h3>
          </div>

          <div className="flex flex-col space-y-4">
            <UserLink user={closeLog.moderator} />
            <p className="text-light-gray-text">
              {format(new Date(closeLog.timestamp), "MM/dd/yyyy - hh:mm a")}
            </p>
            <p className="text-light-gray-text">
              {uniqueActionList.length === 0
                ? "None"
                : uniqueActionList.map((log) => log.action).join(", ")}
            </p>
          </div>
        </>
      )}
    </div>
  );
};
