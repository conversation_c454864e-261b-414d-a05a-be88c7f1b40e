import { useState } from "react";

import {
  InformationCircleOutlineIcon,
  TipOutlineIcon,
} from "@/components/icons";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export const TippingInfoModal = () => {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <div className="flex w-full justify-center">
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="mt-auto flex items-center gap-[10px] border-none text-off-white"
          >
            <InformationCircleOutlineIcon className="size-6" />
            <span className="text-sm font-semibold underline">
              Tipping Information
            </span>
          </Button>
        </DialogTrigger>
      </div>
      <DialogContent className="top-[70%] max-w-[87%] gap-6 rounded-[10px] border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)] backdrop-blur-sm sm:top-[50%] sm:max-w-[524px]">
        <TippingInfoModalContent />
        <div className="flex items-center gap-2">
          <Button
            className="grow-1 flex-auto px-2"
            onClick={() => {
              setOpen(false);
            }}
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const TippingInfoModalContent = () => (
  <>
    <DialogHeader className="flex-col items-center space-y-4">
      <TipOutlineIcon className="size-6 text-off-white" />
      <DialogTitle className="text-center leading-[22px]">
        Tipping Information
      </DialogTitle>
    </DialogHeader>
    <p className="text-center text-sm text-light-gray-text">
      A 2% fee is deducted from every tip sent on the platform. This fee is
      withdrawn from the total tipped amount, ensuring that senders do not incur
      any extra charges.
    </p>
  </>
);
