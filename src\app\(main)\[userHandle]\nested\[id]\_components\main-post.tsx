"use client";

import { useParams } from "next/navigation";

import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { MainPostUI } from "@/components/post";
import { toast } from "@/components/toast";
import {
  useBookmarkThreadMutation,
  useDeleteRepostMutation,
  useDeleteThreadMutation,
  useLikeThreadMutation,
  useRepostThreadMutation,
  useUnbookmarkThreadMutation,
  useUnlikeThreadMutation,
} from "@/queries";
import { ThreadsResponse } from "@/queries/types";
import { Thread } from "@/types";

export const MainPost = ({
  thread,
  linkType,
}: {
  thread: Thread;
  linkType?: "main" | "nested";
}) => {
  const params = useParams() as { id: string };
  const queryClient = useQueryClient();

  const { mutateAsync: repost } = useRepostThreadMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "nested", params.id],
      });

      const previousThread = queryClient.getQueryData([
        "threads",
        "nested",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "nested", params.id],
        (old: InfiniteData<ThreadsResponse>) => {
          return {
            ...old,
            pages: old.pages.map((page) => ({
              ...page,
              threads: page.threads.map((t) => {
                if (t.id === thread.id) {
                  return {
                    ...t,
                    repostCount: t.repostCount + 1,
                    reposted: true,
                  };
                }

                return t;
              }),
            })),
          };
        },
      );

      return { previousThread };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(
        ["threads", "nested", params.id],
        context.previousThread,
      );
    },
  });
  const { mutateAsync: deleteRepost } = useDeleteRepostMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "nested", params.id],
      });

      const previousThread = queryClient.getQueryData([
        "threads",
        "nested",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "nested", params.id],
        (old: InfiniteData<ThreadsResponse>) => {
          return {
            ...old,
            pages: old.pages.map((page) => ({
              ...page,
              threads: page.threads.map((t) => {
                if (t.id === thread.id) {
                  return {
                    ...t,
                    repostCount: t.repostCount - 1,
                    reposted: false,
                  };
                }

                return t;
              }),
            })),
          };
        },
      );

      return { previousThread };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(
        ["threads", "nested", params.id],
        context.previousThread,
      );
    },
  });

  const { mutateAsync: deleteThread } = useDeleteThreadMutation({
    onMutate: async ({ threadId }) => {
      toast.red("Post deleted");
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "trending-feed"],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["home", "threads", "my-feed"]);
      const previousTrendingFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "trending-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.filter((t) => t.id !== threadId),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.filter((t) => t.id !== threadId),
              };
            }),
          };
        },
      );

      return { previousMyFeed, previousTrendingFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        context?.previousMyFeed,
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        context?.previousTrendingFeed,
      );
    },
  });

  const { mutateAsync: like } = useLikeThreadMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "nested", params.id],
      });

      const previousThread = queryClient.getQueryData([
        "threads",
        "nested",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "nested", params.id],
        (old: InfiniteData<ThreadsResponse>) => {
          return {
            ...old,
            pages: old.pages.map((page) => ({
              ...page,
              threads: page.threads.map((t) => {
                if (t.id === thread.id) {
                  return {
                    ...t,
                    likeCount: t.likeCount + 1,
                    like: true,
                  };
                }

                return t;
              }),
            })),
          };
        },
      );

      return { previousThread };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(
        ["threads", "nested", params.id],
        context.previousThread,
      );
    },
  });
  const { mutateAsync: unlike } = useUnlikeThreadMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "nested", params.id],
      });

      const previousThread = queryClient.getQueryData([
        "threads",
        "nested",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "nested", params.id],
        (old: InfiniteData<ThreadsResponse>) => {
          return {
            ...old,
            pages: old.pages.map((page) => ({
              ...page,
              threads: page.threads.map((t) => {
                if (t.id === thread.id) {
                  return {
                    ...t,
                    likeCount: t.likeCount - 1,
                    like: false,
                  };
                }

                return t;
              }),
            })),
          };
        },
      );

      return { previousThread };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(
        ["threads", "nested", params.id],
        context.previousThread,
      );
    },
  });

  const { mutateAsync: bookmark } = useBookmarkThreadMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "nested", params.id],
      });

      const previousThread = queryClient.getQueryData([
        "threads",
        "nested",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "nested", params.id],
        (old: InfiniteData<ThreadsResponse>) => {
          return {
            ...old,
            pages: old.pages.map((page) => ({
              ...page,
              threads: page.threads.map((t) => {
                if (t.id === thread.id) {
                  return {
                    ...t,
                    bookmarkCount: t.bookmarkCount + 1,
                    bookmark: true,
                  };
                }

                return t;
              }),
            })),
          };
        },
      );

      return { previousThread };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(
        ["threads", "nested", params.id],
        context.previousThread,
      );
    },
  });
  const { mutateAsync: unbookmark } = useUnbookmarkThreadMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "nested", params.id],
      });

      const previousThread = queryClient.getQueryData([
        "threads",
        "nested",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "nested", params.id],
        (old: InfiniteData<ThreadsResponse>) => {
          return {
            ...old,
            pages: old.pages.map((page) => ({
              ...page,
              threads: page.threads.map((t) => {
                if (t.id === thread.id) {
                  return {
                    ...t,
                    bookmarkCount: t.bookmarkCount - 1,
                    bookmark: false,
                  };
                }

                return t;
              }),
            })),
          };
        },
      );

      return { previousThread };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(
        ["threads", "nested", params.id],
        context.previousThread,
      );
    },
  });

  const handleLike = async ({ threadId }: { threadId: string }) => {
    if (thread.like) {
      await unlike({ threadId });
    } else {
      await like({ threadId });
    }
  };

  const handleBookmark = async ({ threadId }: { threadId: string }) => {
    if (thread.bookmark) {
      await unbookmark({ threadId });
    } else {
      await bookmark({ threadId });
    }
  };

  const handleRepost = async ({ threadId }: { threadId: string }) => {
    if (thread.reposted) {
      await deleteRepost({ threadId });
    } else {
      await repost({ threadId });
    }
  };

  const handleDelete = async ({ threadId }: { threadId: string }) => {
    await deleteThread({ threadId });
  };

  return (
    <MainPostUI
      thread={thread}
      handleLike={handleLike}
      handleBookmark={handleBookmark}
      handleRepost={handleRepost}
      handleDelete={handleDelete}
      linkType={linkType}
    />
  );
};
