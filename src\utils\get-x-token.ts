import { env } from "@/env";

export async function getOAuthAccessToken(): Promise<string> {
  const rawToken = localStorage.getItem("dynamic_authentication_token");
  const token = rawToken ? JSON.parse(rawToken || "") : null;

  const dynamicStore = localStorage.getItem("dynamic_store");
  if (!dynamicStore) {
    throw new Error("No dynamic_store found in localStorage");
  }

  const storeData = JSON.parse(dynamicStore);
  const oauthAccountId = storeData?.state.user?.verifiedCredentials?.find(
    (cred: any) => cred.oauthProvider === "twitter",
  )?.id;

  if (!token || !oauthAccountId) {
    throw new Error(
      "Missing required authentication details in localStorage or environment",
    );
  }

  const apiUrl = `https://app.dynamicauth.com/api/v0/sdk/${env.NEXT_PUBLIC_DYNAMIC_ENVIRONMENT_ID}/oauthAccounts/${oauthAccountId}/accessToken`;

  try {
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    if (data?.accessToken) {
      return data.accessToken;
    } else {
      throw new Error("Failed to retrieve access token");
    }
  } catch (error) {
    console.error("Error fetching OAuth access token:", error);
    throw new Error("Could not retrieve OAuth access token");
  }
}
