import { queryOptions } from "@tanstack/react-query";

import {
  getBlockedViewers,
  getLiveLivestreams,
  getLivestreamIngress,
  getLivestreamSimpleInfo,
  getTippingStats,
  getTopCreatorTippers,
  getTopLivestreamTippers,
} from "@/api/client/livestream";

export const livestreamQueries = {
  allKey: () => ["livestreams"],
  liveLivestreamsKey: () => [...livestreamQueries.allKey(), "live"],
  liveLivestreams: () =>
    queryOptions({
      queryKey: [...livestreamQueries.liveLivestreamsKey()],
      queryFn: () => getLiveLivestreams(),
    }),
  livestreamSimpleInfoKey: (livestreamId: string) => [
    ...livestreamQueries.allKey(),
    "simple-info",
    livestreamId,
  ],
  livestreamSimpleInfo: (livestreamId: string) =>
    queryOptions({
      queryKey: [...livestreamQueries.livestreamSimpleInfoKey(livestreamId)],
      queryFn: () => getLivestreamSimpleInfo(livestreamId),
    }),
  livestreamIngressKey: () => [...livestreamQueries.allKey(), "ingress"],
  livestreamIngress: () =>
    queryOptions({
      queryKey: [...livestreamQueries.livestreamIngressKey()],
      queryFn: () => getLivestreamIngress(),
    }),
  blockedViewersKey: (livestreamId: string) => [
    ...livestreamQueries.allKey(),
    "blocked-viewers",
    livestreamId,
  ],
  blockedViewers: (livestreamId: string) =>
    queryOptions({
      queryKey: [...livestreamQueries.blockedViewersKey(livestreamId)],
      queryFn: () => getBlockedViewers(livestreamId),
    }),
  tippingStatsKey: (livestreamId: string) => [
    ...livestreamQueries.allKey(),
    "tipping-stats",
    livestreamId,
  ],
  tippingStats: (livestreamId: string) =>
    queryOptions({
      queryKey: [...livestreamQueries.tippingStatsKey(livestreamId)],
      queryFn: () => getTippingStats(livestreamId),
    }),
  topLivestreamTippersKey: (livestreamId: string) => [
    ...livestreamQueries.allKey(),
    "top-livestream-tippers",
    livestreamId,
  ],
  topLivestreamTippers: (livestreamId: string) =>
    queryOptions({
      queryKey: [...livestreamQueries.topLivestreamTippersKey(livestreamId)],
      queryFn: () => getTopLivestreamTippers(livestreamId),
    }),
  topCreatorTippersKey: (userId: string) => [
    ...livestreamQueries.allKey(),
    "top-creator-tippers",
    userId,
  ],
  topCreatorTippers: (userId: string) =>
    queryOptions({
      queryKey: [...livestreamQueries.topCreatorTippersKey(userId)],
      queryFn: () => getTopCreatorTippers(userId),
    }),
};
