"use client";

import { useState } from "react";

import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

interface IMfaBackupCodesModalProps {
  backupCodes: string[];
  onAccept: () => void;
  onClose: () => void;
}

export const MfaBackupCodesModal = (props: IMfaBackupCodesModalProps) => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [isAcknowledge, setAcknowledge] = useState<boolean>();

  const copyAll = () => {
    const textToCopy = props.backupCodes.join("\n");
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        toast.green("Backup codes copied!");
      })
      .catch((err) => {
        toast.red("Failed to copy backup codes!");
      });
  };

  return (
    <div
      className={
        isTablet
          ? "fixed inset-0 z-40 flex content-center items-center justify-center"
          : ""
      }
    >
      <div
        className="bg-black fixed inset-0 z-30 bg-[#020202CC]"
        onClick={props.onClose}
      />
      <div
        className={`fixed z-50 border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] px-[24px] pb-[48px] pt-[24px] shadow-[0px_4px_16px_0px_rgba(0,0,0,0.25)] ${isTablet ? "rounded-[20px]" : "bottom-[0px] left-0 right-0 rounded-t-[20px]"}`}
        style={{ backgroundColor: "rgba(15, 15, 15, 0.90)" }}
      >
        <div
          className={`inline-flex w-full flex-col items-start gap-[32px] ${isTablet ? "mx-auto max-w-[402px]" : ""}`}
        >
          <div className="font-inter flex flex-col items-center gap-[8px] self-stretch text-[16px] font-semibold leading-[22px] text-[#F4F4F4]">
            <p>Back up your codes</p>
          </div>
          <div className="flex flex-col items-start gap-[24px] self-stretch">
            <div className="font-inter text-[14px] font-normal leading-normal text-[var(--LIGHT-GRAY-TEXT,#B5B5B5)]">
              Backup codes help recover your account if you lose access to your
              device. Each code can be used only 1 time.
            </div>
            <div className="flex w-full flex-wrap gap-[12px]">
              {props.backupCodes.map((code, i) => {
                return (
                  <div
                    key={`backup-code-${i}`}
                    className={
                      isTablet
                        ? "font-inter flex h-[45px] w-[195px] flex-[1_0_0_195px] flex-row items-center justify-center rounded-[10px] border border-[var(--GRAY-TEXT,#808080)] bg-[var(--LIGHT-BACKGROUND,#1A1A1A)] p-[16px] text-[14px] font-normal font-semibold leading-[20px] text-[var(--OFF-WHITE,#F4F4F4)] opacity-[0.85] [leading-trim:both] [text-edge:cap]"
                        : "font-inter flex h-[45px] flex-[1_0_0] flex-row items-center justify-center rounded-[10px] border border-[var(--GRAY-TEXT,#808080)] bg-[var(--LIGHT-BACKGROUND,#1A1A1A)] p-[16px] text-[14px] font-normal font-semibold leading-[20px] text-[var(--OFF-WHITE,#F4F4F4)] opacity-[0.85] [leading-trim:both] [text-edge:cap]"
                    }
                  >
                    {code}
                  </div>
                );
              })}
            </div>
          </div>
          <div className="flex w-full items-center gap-[8px]">
            <Button
              variant="outline"
              className="flex-grow basis-[34%] cursor-pointer select-none sm:w-[120px]"
              onClick={copyAll}
            >
              Copy All
            </Button>
          </div>
          <div className="flex flex-col items-center justify-center gap-[8px] self-stretch rounded-[10px] bg-[var(--LIGHT-BACKGROUND,#1A1A1A)] p-[12px_16px]">
            <div className="flex flex-row items-center justify-center gap-[4px]">
              <Checkbox
                className="flex h-[16px] w-[16px] flex-shrink-0 items-center justify-center"
                checked={isAcknowledge}
                onCheckedChange={(checked: boolean) => setAcknowledge(checked)}
              />
              <span className="font-inter text-[12px] font-light font-normal leading-[20px] text-[var(--LIGHT-GRAY-TEXT,#B5B5B5)]">{`I've safely stored a copy of my backup codes`}</span>
            </div>
            <div className="font-inter text-[12px] font-normal font-semibold leading-[20px] text-[var(--BRAND-ORANGE,#EB540A)]">
              You won’t be able to see these codes again.
            </div>
          </div>
          <div className="flex w-full items-center gap-[8px]">
            <Button
              className="flex-grow basis-[66%] sm:w-[234px]"
              onClick={props.onAccept}
              disabled={!isAcknowledge}
            >
              Continue
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
