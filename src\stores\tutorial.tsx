import { create } from "zustand";

interface Store {
  isTutorialOpen: boolean;
  isFollowTutorialOpen: boolean;
  isFirstMessageTutorialOpen: boolean;
}

interface Actions {
  actions: {
    setIsTutorialOpen: (isOpen: boolean) => void;
    setIsFollowTutorialOpen: (isFollowTutorialOpen: boolean) => void;
    setIsFirstMessageTutorialOpen: (
      isFirstMessageTutorialOpen: boolean,
    ) => void;
  };
}

export const useTutorialStore = create<Store & Actions>((set, get) => ({
  isTutorialOpen: false,
  isFollowTutorialOpen: false,
  isFirstMessageTutorialOpen: false,
  actions: {
    setIsTutorialOpen: (isTutorialOpen) => set({ isTutorialOpen }),
    setIsFollowTutorialOpen: (isFollowTutorialOpen) => {
      set({ isFollowTutorialOpen });
    },
    setIsFirstMessageTutorialOpen: (isFirstMessageTutorialOpen) => {
      set({ isFirstMessageTutorialOpen });
    },
  },
}));
