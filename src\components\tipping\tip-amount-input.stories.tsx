import React, { useState } from "react";

import { TipAmountInput } from "./tip-amount-input";

export default {
  title: "Tipping/TipAmountInput",
  component: TipAmountInput,
};

export const Default = () => {
  const [value, setValue] = useState("");
  return (
    <div style={{ maxWidth: 320 }}>
      <TipAmountInput
        name="tipAmount"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        totalTipUSD={value}
      />
    </div>
  );
};

export const WithError = () => {
  const [value, setValue] = useState("");
  return (
    <div style={{ maxWidth: 320 }}>
      <TipAmountInput
        name="tipAmount"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        errorMessage="Tip amount is required"
        totalTipUSD={value}
      />
    </div>
  );
};

export const WithMaxButton = () => {
  const [value, setValue] = useState("");
  return (
    <div style={{ maxWidth: 320 }}>
      <TipAmountInput
        name="tipAmount"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        onMaxClick={() => setValue("100")}
        totalTipUSD={value}
      />
    </div>
  );
};
