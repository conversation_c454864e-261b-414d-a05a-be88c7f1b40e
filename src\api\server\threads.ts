import { getAxios } from "@/lib/axios-server";
import { ThreadsResponse } from "@/queries/types";
import { ThreadsGIFResponse } from "@/queries/types/threads";
import {
  PostThreadData,
  PostThreadResponse,
  ThreadActionData,
  ThreadResponse,
} from "@/types";

export const getThreads = async ({
  page,
  pageSize,
}: {
  page: number;
  pageSize: number;
}) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/feed/my?${searchParams.toString()}`,
  );
  return response.data;
};

export const getBookmarks = async ({
  page,
  pageSize,
}: {
  page: number;
  pageSize: number;
}) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/bookmarks?${searchParams.toString()}`,
  );
  return response.data;
};

export const getThreadsByUser = async ({
  userId,
  page,
  pageSize,
}: {
  userId: string;
  page: number;
  pageSize: number;
}) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    userId,
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/feed/user?${searchParams.toString()}`,
  );
  return response.data;
};

export const getThreadById = async (threadId: string) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    threadId,
  });

  const response = await axios.get<ThreadResponse>(
    `/threads?${searchParams.toString()}`,
  );
  return response.data;
};

export const getAnswersByThread = async ({
  threadId,
  page,
  pageSize,
}: {
  threadId: string;
  page: number;
  pageSize: number;
}) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    threadId,
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/answers?${searchParams.toString()}`,
  );
  return response.data;
};

export const getNestedAnswersByThread = async ({
  threadId,
  page,
  pageSize,
}: {
  threadId: string;
  page: number;
  pageSize: number;
}) => {
  const axios = getAxios();

  const searchParams = new URLSearchParams({
    threadId,
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<ThreadsResponse>(
    `/threads/nested?${searchParams.toString()}`,
  );
  return response.data;
};

export const getThreadsGIFs = async (searchString?: string) => {
  const axios = getAxios();

  const searchParams = searchString
    ? new URLSearchParams({
        searchString,
      })
    : null;

  const response = await axios.get<ThreadsGIFResponse>(
    `/threads/gif${searchParams ? "?" + searchParams.toString() : ""}`,
  );
  return response.data;
};

export const postThread = async (data: PostThreadData) => {
  const axios = getAxios();

  const response = await axios.post<PostThreadResponse>("/threads", data);
  return response.data;
};

export const postLikeThread = async ({ threadId }: ThreadActionData) => {
  const axios = getAxios();

  const response = await axios.post<ThreadResponse>("/threads/like", {
    threadId,
  });
  return response.data;
};

export const postUnlikeThread = async ({ threadId }: ThreadActionData) => {
  const axios = getAxios();

  const response = await axios.post<ThreadResponse>("/threads/unlike", {
    threadId,
  });
  return response.data;
};

export const postBookmarkThread = async ({ threadId }: ThreadActionData) => {
  const axios = getAxios();

  const response = await axios.post<ThreadResponse>("/threads/bookmark", {
    threadId,
  });
  return response.data;
};

export const postUnbookmarkThread = async ({ threadId }: ThreadActionData) => {
  const axios = getAxios();

  const response = await axios.post<ThreadResponse>("/threads/unbookmark", {
    threadId,
  });
  return response.data;
};
