"use client";

import { useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useBanCommunityMutation } from "@/queries/groups-queries";
import { CommunityExtended } from "@/types/community";

interface BanCommunityModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  community: CommunityExtended;
}

export const BanCommunityModal = ({
  open,
  setOpen,
  community,
}: BanCommunityModalProps) => {
  const queryClient = useQueryClient();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const { mutateAsync: ban, isPending } = useBanCommunityMutation({
    onError: (err, variables, context) => {
      toast.red(`Failed to ban ${community.name}.`);
      queryClient.setQueryData(
        ["community", "isBanned", community.id],
        context?.previousIsBanned,
      );
    },
    onSuccess: () => {
      toast.red(`${community.name} is now banned!`);
    },
  });

  const handleBan = async () => {
    setOpen(false);
    await ban(community.id);
  };

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-sm">
          <BanCommunityModalContent
            community={community}
            setOpen={setOpen}
            isPending={isPending}
            handleBan={handleBan}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="justify-start gap-6 text-left">
        <BanCommunityModalContent
          community={community}
          setOpen={setOpen}
          isPending={isPending}
          handleBan={handleBan}
        />
      </DrawerContent>
    </Drawer>
  );
};

interface BanCommunityModalContentProps {
  community: CommunityExtended;
  setOpen: (open: boolean) => void;
  isPending: boolean;
  handleBan: () => void;
}

const BanCommunityModalContent = ({
  community,
  setOpen,
  isPending,
  handleBan,
}: BanCommunityModalContentProps) => {
  return (
    <>
      <DialogHeader className="space-y-4">
        <DialogTitle>Ban @{community.name}?</DialogTitle>
        <DialogDescription className="text-gray-text">
          When you ban a token @{community.name}, all the chat messages and
          associated threads from @{community.name} are removed permanently.
          <br />
          Are you sure you want to proceed?
        </DialogDescription>
      </DialogHeader>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => {
            setOpen(false);
          }}
        >
          Cancel
        </Button>
        <Button
          variant="destructive"
          className="flex-1"
          onClick={handleBan}
          disabled={isPending}
        >
          Ban
        </Button>
      </div>
    </>
  );
};
