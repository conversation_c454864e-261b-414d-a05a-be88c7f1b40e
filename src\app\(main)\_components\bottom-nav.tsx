"use client";

import { FC, useEffect, useRef, useState } from "react";
import { useSelectedLayoutSegments } from "next/navigation";

import { motion, useTransform } from "framer-motion";

import { ReplyEditor } from "@/components/editor/reply-editor";
import { LiveOutlineIcon } from "@/components/icons";
import {
  ChatbubbleOutlineIcon,
  HomeOutlineIcon,
  NotificationsOutlineIcon,
  SearchOutlineIcon,
  WalletOutlineIcon,
} from "@/components/icons-v2";
import { MinifiedLivestream } from "@/components/livestream/mini-livestream-mobile";
import { ProgressBarLink } from "@/components/progress-bar";
import { MinifiedStage } from "@/components/stages/stage-mobile";
import { MinifiedStagePlayer } from "@/components/stages/stage-player-mobile";
import { useBoundedScroll } from "@/hooks";
import { useUnseenNotificationsQuery } from "@/queries";
import { useHomeStore, useHomeTabStore, useUser } from "@/stores";
import { useFeatureFlagsStore } from "@/stores/flags";
import { useLivestreamStore } from "@/stores/livestream";
import { UserFlaggedEnum } from "@/types";
import { cn } from "@/utils";

interface BottomNavProps {
  fixedHeight?: boolean;
}

export const BottomNav: FC<BottomNavProps> = ({ fixedHeight = false }) => {
  const bottomNavRef = useRef<HTMLDivElement>(null);
  const [bottomNavHeight, setBottomNavHeight] = useState(0);
  const segments = useSelectedLayoutSegments();
  const [isPageTop, setIsPageTop] = useState(true);
  const { scrollYBoundedProgress } = useBoundedScroll(300);
  const { user } = useUser();
  const isBanned = user?.flag === UserFlaggedEnum.SUSPENDED;
  const scrollYBoundedProgressDelayed = useTransform(
    scrollYBoundedProgress,
    [0, 0.5, 1],
    [0, 1, 1],
  );
  const y = useTransform(scrollYBoundedProgressDelayed, [0, 1], ["0%", "100%"]);

  const isLivestreamOn = useLivestreamStore((state) => Boolean(state.token));

  const tab = useHomeTabStore((state) => state.tab);
  const followingTimelineRef = useHomeStore(
    (state) => state.followingTimelineRef,
  );
  const trendingTimelineRef = useHomeStore(
    (state) => state.trendingTimelineRef,
  );
  const showStages = useFeatureFlagsStore((state) => state.stages);

  const { data, isLoading } = useUnseenNotificationsQuery();

  const isPostPage =
    segments.length === 3 &&
    (segments[1] === "status" || segments[1] === "nested");

  const links = [
    {
      name: "Home",
      href: `/home`,
      isActive: segments.length === 1 && segments[0] === "home",
      icon: HomeOutlineIcon,
      disabled: false,
    },
    {
      name: "Explore",
      href: `/explore`,
      isActive: segments.length === 1 && segments[0] === "explore",
      icon: SearchOutlineIcon,
      disabled: isBanned,
    },
    {
      name: "Notifications",
      href: `/notifications`,
      isActive: segments.length === 1 && segments[0] === "notifications",
      icon: NotificationsOutlineIcon,
      disabled: isBanned,
    },
    ...(showStages
      ? [
          {
            name: "Live",
            href: `/live`,
            isActive: segments.length === 1 && segments[0] === "live",
            icon: LiveOutlineIcon,
            disabled: isBanned,
          },
        ]
      : []),
    {
      name: "Messages",
      href: `/messages`,
      isActive: segments[0] === "messages",
      icon: ChatbubbleOutlineIcon,
      disabled: isBanned,
    },
    {
      name: "Wallet",
      href: `/wallet`,
      isActive: segments.length === 1 && segments[0] === "wallet",
      icon: WalletOutlineIcon,
      disabled: false,
    },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsPageTop(window.scrollY <= 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    const updateHeight = () => {
      if (bottomNavRef.current) {
        setBottomNavHeight(bottomNavRef.current.getBoundingClientRect().height);
      }
    };

    const resizeObserver = new ResizeObserver(updateHeight);
    if (bottomNavRef.current) {
      resizeObserver.observe(bottomNavRef.current);
    }

    updateHeight();
    window.addEventListener("resize", updateHeight);

    return () => {
      window.removeEventListener("resize", updateHeight);
      resizeObserver.disconnect();
    };
  }, [isPostPage]);

  return (
    <>
      <div
        className="flex-shrink-0 sm:hidden"
        style={{ height: bottomNavHeight }}
      />
      <div
        className="fixed inset-x-0 bottom-0 z-10 flex-shrink-0 sm:hidden"
        ref={bottomNavRef}
      >
        <motion.div
          className="flex w-full flex-col"
          style={
            fixedHeight || isPostPage || isLivestreamOn
              ? undefined
              : {
                  y: isPageTop ? 0 : y,
                }
          }
        >
          <MinifiedLivestream />
          <MinifiedStage />
          <MinifiedStagePlayer />
          {isPostPage && <ReplyEditor />}
          <nav
            className={cn(
              "pb-pwa flex h-[calc(3.5rem+env(safe-area-inset-bottom))] items-center justify-between bg-dark-bk px-2 shadow-[0px_-1px_0px_0px_#202020]",
            )}
          >
            {links.map(({ href, icon: Icon, isActive, name, disabled }) => {
              if (isActive) {
                return (
                  <button
                    key={name + "-nav-link"}
                    className={cn(
                      "relative flex size-14 items-center justify-center",
                      disabled
                        ? "pointer-events-none opacity-20"
                        : "opacity-100",
                    )}
                    onClick={() => {
                      if (segments.length === 1 && segments[0] === "home") {
                        if (tab === "following") {
                          followingTimelineRef.current?.scrollToIndex({
                            index: 0,
                            behavior: "smooth",
                            offset: -300,
                          });
                        } else if (tab === "trending") {
                          trendingTimelineRef.current?.scrollToIndex({
                            index: 0,
                            behavior: "smooth",
                            offset: -300,
                          });
                        }
                      } else {
                        window.scrollTo({
                          top: 0,
                          behavior: "smooth",
                        });
                      }
                    }}
                    tabIndex={disabled ? -1 : 0}
                  >
                    <Icon
                      className={cn(
                        "h-6 w-6 flex-shrink-0 text-white",
                        isActive ? "" : "opacity-50",
                      )}
                    />
                    {isActive && (
                      <div className="absolute bottom-2 left-1/2 h-1 w-1 -translate-x-1/2 rounded-full bg-[#EB540A]" />
                    )}
                    {name === "Notifications" &&
                      !isLoading &&
                      data &&
                      data?.count > 0 && (
                        <div className="absolute right-2 top-2 flex min-w-5 items-center justify-center rounded bg-dark-gray px-1 py-0.5 text-xs text-off-white">
                          {data.count}
                        </div>
                      )}
                  </button>
                );
              }

              return (
                <ProgressBarLink
                  href={href}
                  key={name + "-nav-link"}
                  className={cn(
                    "relative flex size-14 items-center justify-center",
                    disabled ? "pointer-events-none opacity-20" : "opacity-100",
                  )}
                  tabIndex={disabled ? -1 : 0}
                >
                  <Icon
                    className={cn(
                      "h-6 w-6 flex-shrink-0 text-white",
                      isActive ? "" : "opacity-50",
                    )}
                  />
                  {isActive && (
                    <div className="absolute bottom-2 left-1/2 h-1 w-1 -translate-x-1/2 rounded-full bg-[#EB540A]" />
                  )}
                  {name === "Notifications" &&
                    !isLoading &&
                    data &&
                    data?.count > 0 && (
                      <div className="absolute right-2 top-2 flex min-w-5 items-center justify-center rounded bg-dark-gray px-1 py-0.5 text-xs text-off-white">
                        {data.count}
                      </div>
                    )}
                </ProgressBarLink>
              );
            })}
          </nav>
        </motion.div>
      </div>
    </>
  );
};
