"use client";

import { useEffect, useRef, useState } from "react";

import { ArenaDialogHeader } from "@/components/ui/arena-dialog-header";
import { Thread } from "@/types";
import { cn } from "@/utils";

import { TippingInfoModal } from "./tipping-info";
import { TipFormContent } from "./tipping/tip-form-content";
import { Dialog, DialogContent, DialogTrigger } from "./ui/dialog";

interface TipModalProps {
  thread: Thread;
  children: React.ReactNode;
  sortedCurrencies?: any[];
}

export const TipModal = ({
  thread,
  children,
  sortedCurrencies,
}: TipModalProps) => {
  const [open, setOpen] = useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const resetFormRef = useRef<() => void>();

  const handleOpenChange = (open: boolean) => {
    setOpen(open);
    if (!open && resetFormRef.current) {
      resetFormRef.current();
    }
  };

  useEffect(() => {
    const handleResize = () => {
      const viewportHeight =
        window.visualViewport?.height || window.innerHeight;
      const windowHeight = window.innerHeight;
      if (viewportHeight < windowHeight) {
        setIsKeyboardVisible(true);
      } else {
        setIsKeyboardVisible(false);
      }
    };
    if (typeof visualViewport != "undefined") {
      window.visualViewport?.addEventListener("resize", handleResize);
    }
    return () => {
      if (typeof visualViewport != "undefined") {
        window.visualViewport?.removeEventListener("resize", handleResize);
      }
    };
  }, []);

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        className={cn(
          "flex h-full w-full flex-grow flex-col bg-dark-bk  px-6 pt-0 backdrop-blur-sm sm:h-fit sm:w-[420px] sm:gap-0 sm:bg-[#1A1A1A] sm:p-6",
          isKeyboardVisible
            ? "flex-grow-0 justify-end pb-[calc(142px+env(safe-area-inset-bottom))]"
            : "flex-grow justify-between",
        )}
      >
        <ArenaDialogHeader
          title="Tip Thread"
          showBack={true}
          className="mb-12 sm:mb-8"
        />
        <TipFormContent
          recepients={[thread.user]}
          threadId={thread.id}
          setOpen={setOpen}
          sortedCurrencies={sortedCurrencies}
          setResetForm={(fn) => (resetFormRef.current = fn)}
          className="sm:mb-6"
        />
        <TippingInfoModal />
      </DialogContent>
    </Dialog>
  );
};

TipModal.displayName = "TipModal";
