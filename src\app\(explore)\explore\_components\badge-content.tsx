"use client";

import { memo, useEffect, useMemo } from "react";

import { useInView } from "react-intersection-observer";
import { Virtuoso } from "react-virtuoso";

import { UserListItem } from "@/components/user-list-item";
import { UserListItemLoadingSkeleton } from "@/components/user-list-item-loading-skeleton";
import { useUsersWithBadgesByTypeQuery } from "@/queries";

export const BadgeContent = memo(function BadgeContent({
  type,
}: {
  type: number;
}) {
  const { ref, inView } = useInView();
  const { data, isLoading, fetchNextPage } =
    useUsersWithBadgesByTypeQuery(type);

  const users = useMemo(() => {
    return data?.pages.map((page) => page.usersWithBadges).flat();
  }, [data]);

  useEffect(() => {
    if (inView) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage]);

  return (
    <>
      <div className="mt-[10px]">
        {isLoading &&
          Array.from({ length: 25 }).map((_, index) => {
            return <UserListItemLoadingSkeleton key={index} />;
          })}
        {!isLoading && users?.length !== 0 && (
          <Virtuoso
            useWindowScroll
            data={users || []}
            increaseViewportBy={500}
            itemContent={(index, user) => {
              return <UserListItem user={user} />;
            }}
          />
        )}
      </div>
      <div ref={ref} style={{ visibility: "hidden" }}>
        <p>.</p>
      </div>
    </>
  );
});
