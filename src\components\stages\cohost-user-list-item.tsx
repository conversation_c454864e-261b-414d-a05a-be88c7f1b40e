"use client";

import { useState } from "react";

import {
  useLocalParticipant,
  useParticipantAttribute,
  useRemoteParticipant,
  useTrackMutedIndicator,
} from "@livekit/components-react";
import { RemoteParticipant, Track } from "livekit-client";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useUpdateRoleMutation } from "@/queries";
import { StageUser } from "@/queries/types";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";
import { cn } from "@/utils";

import {
  ArrowDownFilledIcon,
  BanOutlineIcon,
  MicOutlineIcon,
  MutedOutlineIcon,
} from "../icons";
import { Button } from "../ui/button";
import { Drawer, DrawerContent, DrawerTrigger } from "../ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { BlockStageUserModal } from "./block-stage-user-modal";
import { ROLES } from "./constants";
import { useDataChannelsContext } from "./stores/data-channels-context";
import { UserListItem } from "./user-list-item";

export const CohostUserListItem = ({ user }: { user: StageUser }) => {
  const participant = useRemoteParticipant({
    identity: user.userId,
  });

  if (!participant) return null;

  return (
    <CohostUserListItemWithParticipant user={user} participant={participant} />
  );
};

const CohostUserListItemWithParticipant = ({
  user,
  participant,
}: {
  user: StageUser;
  participant: RemoteParticipant;
}) => {
  const { user: me } = useUser();
  const local = useLocalParticipant();
  const role = useParticipantAttribute("role", {
    participant: local.localParticipant,
  });
  const { isMuted } = useTrackMutedIndicator({
    participant,
    source: Track.Source.Microphone,
  });
  const [open, setOpen] = useState(false);
  const [isBlockOpen, setIsBlockOpen] = useState(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const id = useStageStore((state) => state.id!);
  const { sendInvalidateStageInfo, sendMuteMic } = useDataChannelsContext();
  const { mutateAsync: updateRole, isPending } = useUpdateRoleMutation({
    onSuccess: () => {
      sendInvalidateStageInfo();
    },
  });

  const currentUser = {
    id: user?.user.id ?? "",
    name: user?.user.twitterName ?? "",
    avatar: user?.user.twitterPicture ?? "",
    username: user?.user.twitterHandle ?? "",
    role: user.role ?? "",
  };

  const handleUpdateRole = async (role: "LISTENER" | "SPEAKER") => {
    setOpen(false);
    await updateRole({
      stageId: id,
      role,
      userId: user.userId,
    });
  };

  const handleBlockUser = async () => {
    setOpen(false);
    setIsBlockOpen(true);
  };

  const handleMute = async () => {
    setOpen(false);
    sendMuteMic({
      identity: participant.identity,
    });
  };

  return (
    <>
      <UserListItem
        user={user.user}
        badge={user.isPresent ? undefined : "left"}
        isMuted={isMuted}
      >
        {user.userId !== me?.id && role === ROLES.HOST && (
          <>
            {isTablet && (
              <DropdownMenu open={open} onOpenChange={setOpen}>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="h-[34px] w-28">
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[240px]">
                  {(role === ROLES.HOST || role === ROLES.COHOST) && (
                    <DropdownMenuItem
                      className={cn("w-full gap-4", isMuted && "opacity-50")}
                      asChild
                    >
                      <button
                        onClick={handleMute}
                        disabled={isPending || isMuted}
                      >
                        <MutedOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                        <span>Mute</span>
                      </button>
                    </DropdownMenuItem>
                  )}
                  {role === ROLES.HOST && (
                    <DropdownMenuItem className="w-full gap-4" asChild>
                      <button
                        onClick={() => {
                          handleUpdateRole("SPEAKER");
                        }}
                        disabled={isPending}
                      >
                        <MicOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                        <span>Downgrade to Speaker</span>
                      </button>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem className="w-full gap-4" asChild>
                    <button
                      onClick={() => {
                        handleUpdateRole("LISTENER");
                      }}
                      disabled={isPending}
                    >
                      <ArrowDownFilledIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Downgrade to Listener</span>
                    </button>
                  </DropdownMenuItem>
                  {role === ROLES.HOST || role === ROLES.COHOST ? (
                    <DropdownMenuItem className="w-full gap-4" asChild>
                      <button onClick={handleBlockUser} disabled={isPending}>
                        <BanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                        <span>Remove</span>
                      </button>
                    </DropdownMenuItem>
                  ) : null}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            {!isTablet && (
              <Drawer open={open} onOpenChange={setOpen}>
                <DrawerTrigger asChild>
                  <Button variant="outline" className="h-[34px] w-28">
                    Actions
                  </Button>
                </DrawerTrigger>
                <DrawerContent className="px-4 pt-4">
                  {role === ROLES.HOST || role === ROLES.COHOST ? (
                    <button
                      className={cn(
                        "flex items-center gap-2 p-2 text-base leading-5",
                        isMuted && "opacity-50",
                      )}
                      onClick={handleMute}
                      disabled={isPending || isMuted}
                    >
                      <MutedOutlineIcon className="size-6 text-gray-text" />{" "}
                      Mute
                    </button>
                  ) : null}
                  {role === ROLES.HOST && (
                    <button
                      className="flex items-center gap-2 p-2 text-base leading-5"
                      onClick={() => {
                        handleUpdateRole("SPEAKER");
                      }}
                      disabled={isPending}
                    >
                      <MicOutlineIcon className="size-6 text-gray-text" />{" "}
                      Downgrade to Speaker
                    </button>
                  )}
                  <button
                    className="flex items-center gap-2 p-2 text-base leading-5"
                    onClick={() => {
                      handleUpdateRole("LISTENER");
                    }}
                    disabled={isPending}
                  >
                    <ArrowDownFilledIcon className="size-6 text-gray-text" />{" "}
                    Downgrade to Listener
                  </button>
                  {role === ROLES.HOST || role === ROLES.COHOST ? (
                    <button
                      className="flex items-center gap-2 p-2 text-base leading-5"
                      onClick={handleBlockUser}
                      disabled={isPending}
                    >
                      <BanOutlineIcon className="size-6 text-gray-text" />{" "}
                      Remove
                    </button>
                  ) : null}
                </DrawerContent>
              </Drawer>
            )}
          </>
        )}
      </UserListItem>
      <BlockStageUserModal
        open={isBlockOpen}
        setOpen={setIsBlockOpen}
        user={currentUser}
        stageId={id}
      />
    </>
  );
};
