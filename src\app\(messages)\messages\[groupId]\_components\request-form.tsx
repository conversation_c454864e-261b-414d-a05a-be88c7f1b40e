import { FC } from "react";

import { ProgressBarLink } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";

interface RequestFormProps {
  chatMateName: string;
  handleDelete: () => void;
  handleAccept: () => void;
}

export const RequestForm: FC<RequestFormProps> = ({
  chatMateName,
  handleDelete,
  handleAccept,
}) => {
  const hint = `Do you want to let ${chatMateName} message you? They won’t know you’ve seen their message until you accept.`;
  return (
    <div className="flex w-full flex-col gap-4 border-r-2 border-t-2 border-dark-gray bg-gray-bg p-6">
      <span className="text-sm">{hint}</span>
      <div className="flex w-full gap-2.5">
        <Button className="flex w-1/2" onClick={handleAccept}>
          Accept
        </Button>
        <ProgressBarLink href={"/messages"} className="flex w-1/2">
          <Button variant="outline" className="w-full" onClick={handleDelete}>
            Delete
          </Button>
        </ProgressBarLink>
      </div>
    </div>
  );
};
