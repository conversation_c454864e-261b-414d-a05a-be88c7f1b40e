"use client";

import { useState } from "react";

import {
  ArrowBackOutlineIcon,
  InformationCircleOutlineIcon,
  LogoIcon,
} from "@/components/icons";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useAirdropQuery } from "@/queries/airdrop-queries";
import { useUser } from "@/stores";

interface AirdropPointsModalProps {
  children?: React.ReactNode;
  open?: boolean;
  setOpen?: (open: boolean) => void;
}

export const AirdropPointsModal = ({
  children,
  open,
  setOpen,
}: AirdropPointsModalProps) => {
  const { user } = useUser();
  const { data: airdropData } = useAirdropQuery({
    // disable the query when the modal is closed
    enabled: open,
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {children && <DialogTrigger asChild>{children}</DialogTrigger>}
      <DialogContent className="isolate flex h-full w-full flex-col overflow-hidden p-0 px-6 backdrop-blur-sm sm:h-auto">
        <div className="pt-pwa mt-5 flex items-center">
          <div className="flex-1 sm:mr-3 sm:flex-none">
            <DialogClose className="flex flex-shrink-0">
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </DialogClose>
          </div>
          <h4 className="text-base font-semibold leading-5 text-white">
            Airdrop points
          </h4>
          <div className="flex-1" />
        </div>
        <Avatar className="mt-9 size-[118px] border border-[#E8E8E8] sm:mx-auto">
          <AvatarImage src={user?.twitterPicture} />
          <AvatarFallback />
        </Avatar>
        <div className="mt-5 flex flex-col justify-between gap-1 sm:text-center">
          <div className="text-xl font-medium leading-4 text-white">
            {user?.twitterName}
          </div>
          <div className="text-base text-gray-text">@{user?.twitterHandle}</div>
        </div>
        <div className="rounded-[10px] bg-purple-gradient p-3 pt-6 text-off-white">
          <div className="flex flex-col items-center gap-[10px]">
            <div className="text-lg">Arena Global Rank</div>
            <div className="flex items-center gap-[10px]">
              <LogoIcon className="w-7" />
              <span className="text-[44px] font-medium leading-none">
                {airdropData?.airdrop.rank || "N/A"}
              </span>
            </div>
          </div>
          <div className="mt-6 flex flex-col items-center justify-between gap-1 rounded-md bg-white/15 px-4 py-3 text-sm">
            <div>Current Points</div>
            <div className="text-3xl font-medium">
              {airdropData?.airdrop.points || 0}
            </div>
          </div>
        </div>

        {/* <div className="flex items-center justify-center gap-[10px] rounded-full border border-gray-text py-3 text-off-white">
          <span className="text-sm font-semibold">Share</span>
          <ExternalLinkOutlineIcon className="size-6" />
        </div> */}
        <AirdropPointsInfoModal />
        {user?.twitterPicture && (
          <img
            src={user?.twitterPicture || ""}
            alt=""
            className="absolute inset-0 -z-20 h-full w-full object-cover"
          />
        )}
        <div className="absolute inset-0 -z-10 bg-[#0F0F0F]/80 backdrop-blur-[44px]" />
      </DialogContent>
    </Dialog>
  );
};

const AirdropPointsInfoModal = () => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [open, setOpen] = useState(false);

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <div className="flex w-full justify-center">
          <DialogTrigger asChild>
            <Button
              variant="outline"
              className="mb-4 mt-auto flex items-center gap-[10px] border-none text-off-white"
            >
              <InformationCircleOutlineIcon className="size-6" />
              <span className="text-sm font-semibold underline">
                How do you earn airdrop points?
              </span>
            </Button>
          </DialogTrigger>
        </div>
        <DialogContent className="gap-4">
          <AirdropPointsInfoModalContent />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button
          variant="outline"
          className="mb-4 mt-auto flex w-full items-center gap-[10px] border-none text-off-white"
        >
          <InformationCircleOutlineIcon className="size-6" />
          <span className="text-sm font-semibold underline">
            How do you earn airdrop points?
          </span>
        </Button>
      </DrawerTrigger>
      <DrawerContent className="gap-4">
        <AirdropPointsInfoModalContent />
      </DrawerContent>
    </Drawer>
  );
};

const AirdropPointsInfoModalContent = () => {
  return (
    <>
      <h3 className="text-base font-semibold leading-[22px] text-off-white">
        Airdrop Points
      </h3>
      <div className="flex flex-col gap-2">
        <h4 className="text-base font-semibold text-off-white">
          How do you earn airdrop points?
        </h4>
        <p className="text-sm text-[#B5B5B5]">
          You accumulate points by engaging with the platform. Airdrop points
          are distributed{" "}
          <span className="font-medium text-off-white">
            every Monday at 14:00 EST
          </span>
          . Airdrop points serve as the key to unlock airdrop rewards.
        </p>
      </div>
      <div className="flex flex-col gap-2">
        <h4 className="text-base font-semibold text-off-white">
          What actions earn points?
        </h4>
        <ul className="list-inside list-disc text-sm text-[#B5B5B5]">
          <li>General activity</li>
          <li>Your portfolio value</li>
          <li>Your ticket price</li>
          <li>Referrals</li>
          <li>Tipping volume</li>
          <li>Trading volume</li>
          <li>Total views</li>
        </ul>
      </div>
    </>
  );
};
