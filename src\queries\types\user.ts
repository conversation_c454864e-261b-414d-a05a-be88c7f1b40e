import { User } from "./top-users-response";

export interface ReferralStatsResponse {
  stats: {
    referralsEarned: number;
    referrerCount: string;
  };
}

export interface UserSearchResponse {
  users: User[];
  numberOfPages: number;
  numberOfResults: number;
  pageSize: number;
}
export interface UserPreferences {
  id: number;
  userId: string;
  notifyLikes: boolean;
  notifyReposts: boolean;
  notifyComments: boolean;
  notifyTags: boolean;
}
export interface UserPreferencesResponse {
  userPreferences: UserPreferences;
}
export interface UpdateUserPreferencesVariables {
  userPreferences: Partial<{
    notifyLikes: boolean;
    notifyReposts: boolean;
    notifyComments: boolean;
    notifyTags: boolean;
  }>;
}
