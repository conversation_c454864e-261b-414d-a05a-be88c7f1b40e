"use client";

import { ChangeEvent, useEffect, useMemo, useRef, useState } from "react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import * as RadioGroup from "@radix-ui/react-radio-group";
import { useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { ethers } from "ethers";
import { OptimalRate } from "paraswap-core";
import { Hex } from "viem";

import { RefreshIcon } from "@/components/icons";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { TextInput } from "@/components/ui/text-input";
import { swapTokens } from "@/environments/tokens";
import { useSwap } from "@/hooks/use-swap";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import { useSupportedCurrenciesQuery } from "@/queries/currency-queries";
import { useBuyTickerMutation } from "@/queries/trade-mutations";
import { useUser } from "@/stores";
import { formatUnits } from "@/utils";
import { isNumber } from "@/utils/is-number";
import {
  formatNumericValue,
  handleNumericInput,
  parseFormattedValue,
} from "@/utils/number";

export const BuyArena = () => {
  const queryClient = useQueryClient();
  const { primaryWallet } = useDynamicContext();
  const searchParams = useSearchParams();
  const [payToken] = useState(swapTokens["AVAX"]);
  const [payTokenAmountRaw, setPayTokenAmountRaw] = useState<string>("");
  const [payTokenAmount, setPayTokenAmount] = useState<string>("");
  const [buyTokenAmountRaw, setBuyTokenAmountRaw] = useState<string>("");
  const currentAbortController = useRef<AbortController | null>(null);
  const currentRequestId = useRef<number>(0);

  const [exchangeRate, setExchangeRate] = useState<OptimalRate>();
  const [slippage, setSlippage] = useState<string>("0.5");

  const [isPendingBuyTickerDynamic, setIsPendingBuyTickerDynamic] =
    useState<boolean>(false);

  const { user } = useUser();
  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useSupportedCurrenciesQuery();
  const { balances } = useWalletCurrencies({
    user,
    currenciesData,
    isCurrenciesLoading,
  });

  const { getRate, buildSwap, getBuyRate } = useSwap();

  const buyToken = swapTokens[searchParams.get("ticker")?.toUpperCase() || ""];

  if (!buyToken) {
    throw new Error("Token not found");
  }

  if (!user) {
    throw new Error("User not specified");
  }

  const { mutateAsync: buyTicker, isPending: isBuyTickerPending } =
    useBuyTickerMutation({
      onSuccess: () => {
        toast.green(`Successfully bought $${buyToken.symbol}`);
        reset();
      },
    });

  const payTokenBalance = useMemo(() => {
    if (!balances || !balances[payToken.symbol]) return "0";

    return formatNumericValue(
      formatUnits(
        balances[payToken.symbol].balance.toString(),
        payToken.decimals,
      ),
    );
  }, [balances, payToken]);

  const buyTokenBalance = useMemo(() => {
    if (!balances || !balances[buyToken.symbol]) return "0";

    return formatNumericValue(
      formatUnits(
        balances[buyToken.symbol].balance.toString(),
        buyToken.decimals,
      ),
    );
  }, [balances, buyToken]);

  const reset = () => {
    setPayTokenAmountRaw("");
    setPayTokenAmount("");
    setBuyTokenAmountRaw("");
  };

  const refreshExchangeRate = async () => {
    try {
      const rate = await getRate({
        srcToken: payToken,
        destToken: buyToken,
        srcAmount: ethers.parseUnits("0.01", payToken.decimals).toString(),
      });

      setExchangeRate(rate);
    } catch (error: unknown) {
      console.error("Staking error:", error);
      toast.red(`Get price failed: estimated loss greater than max impact`);

      if (error instanceof Error) {
        if (
          error instanceof AxiosError &&
          error?.response?.data?.error ===
            "ESTIMATED_LOSS_GREATER_THAN_MAX_IMPACT"
        ) {
          toast.red(`Get price failed: estimated loss greater than max impact`);
        } else {
          toast.red(`Get price failed: ${error.message}`);
        }
      } else {
        toast.red("Get price failed. Please try again.");
      }
    }
  };

  const buyTickerDynamic = async ({
    srcToken,
    destToken,
    srcAmount,
  }: {
    srcToken: any;
    destToken: any;
    srcAmount: string;
  }) => {
    setIsPendingBuyTickerDynamic(true);
    try {
      const parsedAmount = parseFormattedValue(srcAmount).toString();

      const rate = await getRate({
        srcToken,
        destToken,
        srcAmount: ethers
          .parseUnits(parsedAmount, srcToken.decimals)
          .toString(),
      });

      if (!primaryWallet) {
        throw new Error("Dynamic wallet is not initialized");
      }
      if (!isEthereumWallet(primaryWallet)) {
        throw new Error("This wallet is not a Ethereum wallet");
      }

      const walletClient = await primaryWallet.getWalletClient();
      const publicClient = await primaryWallet.getPublicClient();

      const res = await buildSwap({
        srcToken: payToken,
        destToken: buyToken,
        srcAmount: ethers
          .parseUnits(parsedAmount, payToken.decimals)
          .toString(),
        priceRoute: rate,
        userAddress: user.address,
        slippage: Number(slippage),
      });

      const buyTx = await walletClient.sendTransaction({
        account: primaryWallet.address as Hex,
        chain: walletClient.chain,
        to: res.to as Hex,
        value: res.value as any,
        data: res.data as any,
        gas: res.gas as any,
        gasPrice: res.gasPrice as any,
      });

      const buyReceipt = await publicClient.waitForTransactionReceipt({
        hash: buyTx,
      });
      console.log("Buy transaction completed:", buyReceipt);

      toast.green(`Successfully bought $${buyToken.symbol}`);
      reset();
    } catch (error: unknown) {
      console.error("Get price error:", error);

      if (error instanceof Error) {
        if (
          error instanceof AxiosError &&
          error?.response?.data?.error ===
            "ESTIMATED_LOSS_GREATER_THAN_MAX_IMPACT"
        ) {
          toast.red(`Get price failed: estimated loss greater than max impact`);
        } else {
          toast.red(`Get price failed: ${error.message}`);
        }
      } else {
        toast.red("Get price failed. Please try again.");
      }
    } finally {
      setIsPendingBuyTickerDynamic(false);
    }
  };

  const update = async (amount: string) => {
    // Cancel any previous request
    if (currentAbortController.current) {
      currentAbortController.current.abort();
    }

    // Create new abort controller and increment request ID
    const abortController = new AbortController();
    const requestId = currentRequestId.current + 1;
    currentAbortController.current = abortController;
    currentRequestId.current = requestId;

    let buyAmountStr = "0";
    try {
      if (!amount || Number(amount) === 0) return;

      const parsedAmount = parseFormattedValue(amount).toString();

      const rate = await getRate({
        srcToken: payToken,
        destToken: buyToken,
        srcAmount: ethers
          .parseUnits(parsedAmount, payToken.decimals)
          .toString(),
        signal: abortController.signal,
      });

      // Check if this is still the most recent request
      if (requestId !== currentRequestId.current) {
        return;
      }

      buyAmountStr = ethers.formatUnits(rate.destAmount, buyToken.decimals);
    } catch (error: unknown) {
      // If request was aborted, don't show error
      if (
        error instanceof Error &&
        (error.name === "AbortError" || error.name === "CanceledError")
      ) {
        return;
      }

      console.error("Staking error:", error);

      if (error instanceof Error) {
        if (
          error instanceof AxiosError &&
          error?.response?.data?.error ===
            "ESTIMATED_LOSS_GREATER_THAN_MAX_IMPACT"
        ) {
          toast.red(`Get price failed: estimated loss greater than max impact`);
        } else {
          toast.red(`Get price failed: ${error.message}`);
        }
      } else {
        toast.red("Get price failed. Please try again.");
      }
    }

    // Only update if this is still the most recent request
    if (requestId === currentRequestId.current) {
      const formattedBuyAmountStr = formatNumericValue(buyAmountStr);
      setBuyTokenAmountRaw(formattedBuyAmountStr);
    }
  };

  const handleRefreshRateClick = async () => {
    await refreshExchangeRate();
  };

  const handleBuy = async () => {
    const parsedAmount = parseFormattedValue(payTokenAmount).toString();

    if (user.address === user.dynamicAddress?.toLowerCase()) {
      await buyTickerDynamic({
        srcToken: payToken,
        destToken: buyToken,
        srcAmount: parsedAmount,
      });
    } else {
      await buyTicker({
        srcToken: payToken.address,
        destToken: buyToken.address,
        srcAmount: ethers
          .parseUnits(parsedAmount, payToken.decimals)
          .toString(),
      });
    }
    queryClient.invalidateQueries({
      queryKey: ["wallet", "balance", user?.address, buyToken.symbol],
    });
    queryClient.invalidateQueries({
      queryKey: ["wallet", "balance", user?.address, payToken.symbol],
    });
  };

  const handlePayChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const input = e.target;
    const position = input.selectionStart ?? 0;
    const rawValue = input.value;

    const { value, cursorPosition } = handleNumericInput(rawValue, position);

    if (!isNumber(value) || value == "") {
      // Cancel any previous request
      if (currentAbortController.current) {
        currentAbortController.current.abort();
      }
      reset();

      // Update cursor position after React updates the DOM
      setTimeout(() => {
        if (input) {
          input.setSelectionRange(cursorPosition, cursorPosition);
        }
      }, 0);
      return;
    }

    setPayTokenAmountRaw(value);

    // Update cursor position after React updates the DOM
    setTimeout(() => {
      if (input) {
        input.setSelectionRange(cursorPosition, cursorPosition);
      }
    }, 0);

    try {
      const parsedValue = parseFormattedValue(value).toString();
      const units = ethers.parseUnits(parsedValue, payToken.decimals);

      if (
        BigInt(units) >
        BigInt(ethers.parseUnits(payTokenBalance, payToken.decimals))
      ) {
        const payTokenBalanceFormatted = parseFormattedValue(
          formatNumericValue(payTokenBalance),
        ).toString();
        setPayTokenAmountRaw(payTokenBalanceFormatted);
        setPayTokenAmount(payTokenBalanceFormatted);
        await update(payTokenBalance);
      } else {
        setPayTokenAmount(value);
        await update(value);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleBuyChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const input = e.target;
    const position = input.selectionStart ?? 0;
    const rawValue = input.value;

    const { value, cursorPosition } = handleNumericInput(rawValue, position);

    if (!isNumber(value) || value == "") {
      reset();

      // Update cursor position after React updates the DOM
      setTimeout(() => {
        if (input) {
          input.setSelectionRange(cursorPosition, cursorPosition);
        }
      }, 0);
      return;
    }

    setBuyTokenAmountRaw(value);

    // Update cursor position after React updates the DOM
    setTimeout(() => {
      if (input) {
        input.setSelectionRange(cursorPosition, cursorPosition);
      }
    }, 0);

    try {
      const parsedValue = parseFormattedValue(value).toString();
      const units = ethers.parseUnits(parsedValue, buyToken.decimals);

      const rate = await getBuyRate({
        srcToken: payToken,
        destToken: buyToken,
        destAmount: units.toString(),
      });

      if (!rate) {
        return;
      }

      if (
        BigInt(rate.srcAmount) >
        BigInt(ethers.parseUnits(payTokenBalance, payToken.decimals))
      ) {
        await handleSetMaxAmount();
      } else {
        const parsedValue = parseFormattedValue(
          formatNumericValue(
            ethers.formatUnits(rate.srcAmount, payToken.decimals),
          ),
        ).toString();
        setPayTokenAmount(parsedValue);
        setPayTokenAmountRaw(parsedValue);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleSetMaxAmount = async () => {
    setPayTokenAmount(payTokenBalance);
    setPayTokenAmountRaw(formatNumericValue(payTokenBalance));
    await update(payTokenBalance);
  };

  const handleOptionChange = async (value: string) => {
    setSlippage(value);
  };

  const handleSlippage = (e: ChangeEvent<HTMLInputElement>) => {
    const input = e.target;
    const position = input.selectionStart ?? 0;
    const rawValue = input.value;

    let { value, cursorPosition } = handleNumericInput(rawValue, position);

    if (value.startsWith(".")) value = "0" + value;
    if (value.endsWith(".")) {
      setSlippage(value);

      // Update cursor position after React updates the DOM
      setTimeout(() => {
        if (input) {
          input.setSelectionRange(cursorPosition, cursorPosition);
        }
      }, 0);

      return;
    }
    let numValue = parseFloat(value);
    if (isNaN(numValue)) numValue = 0;
    numValue = Math.max(0, Math.min(49, numValue));
    setSlippage(numValue.toString());

    // Update cursor position after React updates the DOM
    setTimeout(() => {
      if (input) {
        input.setSelectionRange(cursorPosition, cursorPosition);
      }
    }, 0);
  };

  useEffect(() => {
    void refreshExchangeRate();
  }, []);

  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-3">
      <div className="w-full px-6">
        <div className="mb-9 flex w-full flex-row justify-between gap-4">
          <div className="flex flex-col">
            <div className="text-xl font-semibold text-off-white">
              ${buyToken.symbol}
            </div>
            <div className="text-xs text-light-gray-text">
              0.01 {payToken.symbol} ={" "}
              {formatUnits(
                exchangeRate?.destAmount || "0",
                exchangeRate?.destDecimals || 18,
              )}{" "}
              ${buyToken.symbol}
            </div>
          </div>
          <div
            className="my-auto cursor-pointer"
            onClick={handleRefreshRateClick}
          >
            <RefreshIcon />
          </div>
        </div>
        <div className="flex flex-col">
          <p className="ml-1 text-xs text-light-gray-text">
            SLIPPAGE TOLERANCE
          </p>
          <RadioGroup.Root
            onValueChange={handleOptionChange}
            className="mt-3.5 flex gap-2"
          >
            <RadioGroup.Item
              value={"0.1"}
              className="rounded-[26px] border border-dark-gray aria-checked:border-none aria-checked:bg-[linear-gradient(109deg,#F36A27_10.68%,#CE4909_79.68%)]"
              asChild
              checked={slippage === "0.1"}
            >
              <button className="flex w-[60px] items-center justify-center gap-0.5 px-3 py-[6px] text-center text-sm text-off-white">
                <span>0.1</span>
                <span>%</span>
              </button>
            </RadioGroup.Item>
            <RadioGroup.Item
              value={"0.5"}
              className="rounded-[26px] border border-dark-gray aria-checked:border-none aria-checked:bg-[linear-gradient(109deg,#F36A27_10.68%,#CE4909_79.68%)]"
              asChild
              checked={slippage === "0.5"}
            >
              <button className="flex w-[60px] items-center justify-center gap-0.5 px-3 py-[6px] text-center text-sm text-off-white">
                <span>0.5</span>
                <span>%</span>
              </button>
            </RadioGroup.Item>
            <RadioGroup.Item
              value={"1"}
              className="rounded-[26px] border border-dark-gray aria-checked:border-none aria-checked:bg-[linear-gradient(109deg,#F36A27_10.68%,#CE4909_79.68%)]"
              asChild
              checked={slippage === "1"}
            >
              <button className="flex w-[60px] items-center justify-center gap-0.5  px-3 py-[6px] text-center text-sm text-off-white">
                <span>1</span>
                <span>%</span>
              </button>
            </RadioGroup.Item>
            <RadioGroup.Item value={slippage} asChild>
              <button className="relative">
                <TextInput
                  placeholder="Enter amount"
                  type="string"
                  className="h-8 w-20 border-white pl-2 pr-6"
                  value={slippage}
                  onChange={handleSlippage}
                />
                <span className="absolute right-2 top-1/2 -translate-y-1/2 transform">
                  %
                </span>
              </button>
            </RadioGroup.Item>
          </RadioGroup.Root>
        </div>
        <div className="mt-8 flex flex-col">
          <div className="mb-2 text-xs">
            <span className="mr-1 text-light-gray-text">BALANCE:</span>
            <span className="text-white">{payTokenBalance}</span>
          </div>
          <div className="relative">
            <Image
              src={payToken.icon}
              width={40}
              height={40}
              className="pointer-events-none absolute left-4 top-1/2 size-4 -translate-y-1/2 select-none rounded-full"
              alt={payToken.symbol + " logo"}
            />
            <TextInput
              placeholder="0"
              type="string"
              className="border-white pl-10"
              value={payTokenAmountRaw}
              onChange={handlePayChange}
            />
            <div
              className="absolute right-4 top-1/2 -translate-y-1/2 cursor-pointer"
              onClick={handleSetMaxAmount}
            >
              <span className="text-xs underline">Max</span>
            </div>
          </div>
        </div>
        <div className="mt-6 flex flex-col">
          <div className="mb-2 text-xs">
            <span className="mr-1 text-light-gray-text">BALANCE:</span>
            <span className="text-white">{buyTokenBalance}</span>
          </div>
          <div className="relative">
            <Image
              src={buyToken.icon}
              width={40}
              height={40}
              className="pointer-events-none absolute left-4 top-1/2 size-4 -translate-y-1/2 select-none rounded-full"
              alt={buyToken.symbol + " logo"}
            />
            <TextInput
              placeholder="0"
              type="string"
              className="border-white pl-10"
              value={buyTokenAmountRaw}
              onChange={handleBuyChange}
            />
          </div>
        </div>
        <Button
          disabled={isBuyTickerPending || isPendingBuyTickerDynamic}
          className="mt-6 w-full flex-1 gap-1"
          onClick={handleBuy}
          loading={isBuyTickerPending || isPendingBuyTickerDynamic}
        >
          <span className="text-xs font-medium leading-5 text-off-white">
            BUY ${buyToken.symbol}
          </span>
        </Button>
      </div>
    </div>
  );
};
