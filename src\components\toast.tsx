import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import { toast as sonner } from "sonner";

import { CheckmarkCircleSharp, XCircleOutlineIcon } from "./icons";

const green = (message: string) => {
  return sonner.custom(() => {
    return (
      <div className="relative top-[env(safe-area-inset-top)] flex items-center gap-[10px] rounded-lg border border-[#6FB672] bg-[#52A655] px-4 py-3">
        <CheckmarkCircleSharp className="h-4 w-4 fill-[#EDEDED]" />
        <span className="text-xs leading-5 text-[#F4F4F4]">{message}</span>
      </div>
    );
  });
};

const red = (message: string) => {
  return sonner.custom(() => {
    return (
      <div className="relative top-[env(safe-area-inset-top)] flex items-center gap-[10px] rounded-lg border border-[#BE5D5D] bg-[#D14848] px-4 py-3">
        <CheckmarkCircleSharp className="h-4 w-4 fill-[#EDEDED]" />
        <span className="text-xs leading-5 text-[#F4F4F4]">{message}</span>
      </div>
    );
  });
};

const danger = (message: string) => {
  return sonner.custom(() => {
    return (
      <div className="relative top-[env(safe-area-inset-top)] flex items-center gap-[10px] rounded-lg border border-[#BE5D5D] bg-[#D14848] px-4 py-3">
        <XCircleOutlineIcon className="h-4 w-4 text-[#EDEDED]" />
        <span className="text-xs leading-5 text-[#F4F4F4]">{message}</span>
      </div>
    );
  });
};

const neutral = (message: string) => {
  return sonner.custom(() => {
    return (
      <div className="relative top-[env(safe-area-inset-top)] flex items-center gap-[10px] rounded-lg border border-[#5F5F5F] bg-[#515151] px-4 py-3">
        <CheckmarkCircleSharp className="h-4 w-4 fill-[#EDEDED]" />
        <span className="text-xs leading-5 text-[#F4F4F4]">{message}</span>
      </div>
    );
  });
};

const loading = (message: string) => {
  return sonner.custom(
    () => {
      return (
        <div className="relative top-[env(safe-area-inset-top)] flex items-center gap-[10px] rounded-lg border border-lighter-background bg-light-background px-4 py-3">
          <DotLottieReact
            src="/assets/animations/loader.lottie"
            loop
            speed={1.3}
            className="size-6 flex-shrink-0"
            autoplay={true}
          />
          <span className="text-xs leading-5 text-off-white">{message}</span>
        </div>
      );
    },
    { duration: Infinity },
  );
};

export const toast = {
  green,
  red,
  neutral,
  danger,
  loading,
  dismiss: sonner.dismiss,
};
