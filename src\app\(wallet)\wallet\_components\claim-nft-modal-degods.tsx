import { useEffect, useState } from "react";

import { useMutation } from "@tanstack/react-query";
import {
  useWeb3Modal,
  useWeb3ModalAccount,
  useWeb3ModalProvider,
} from "@web3modal/ethers/react";
import { AxiosError } from "axios";
import { <PERSON>rows<PERSON><PERSON>rovider } from "ethers";

import { postClaimNft, postClaimNftDegods } from "@/api/client/chain";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Dialog, DialogClose, DialogContent } from "@/components/ui/dialog";
import { welcomeMessage } from "@/utils/welcome-message";
import { welcomeMessageSolana } from "@/utils/welcome-message-solana";

interface ClaimNftModalProps {
  open: boolean;
  setOpen: (value: boolean) => void;
}

export const ClaimNftModalDegods = ({ open, setOpen }: ClaimNftModalProps) => {
  // const [claimed, setClaimed] = useState(false);
  const [success, setSuccess] = useState(false);

  // const web3Modal = useWeb3Modal();
  // const { address, isConnected } = useWeb3ModalAccount();
  // const { walletProvider } = useWeb3ModalProvider();

  const connectWalletDirectly = async (): Promise<{
    provider: any;
    account: string;
  }> => {
    let provider: any = null;
    let account = "";
    provider = (window as any).phantom?.solana;
    if (!provider) {
      toast.danger(
        "No Phantom wallet detected. Try using Phantom browser extension or Phantom Mobile Browser",
      );
    }
    const { publicKey } = await provider.connect();
    account = publicKey.toBase58();
    return { provider, account };
  };

  const signMessageDirectly = async (provider: any): Promise<string> => {
    const message = welcomeMessageSolana;

    const encodedMessage = new TextEncoder().encode(message);
    const { signature } = await provider.signMessage(encodedMessage, "utf8");
    return Buffer.from(signature).toString("base64");
  };

  // async function signMessage() {
  //   if (!walletProvider) throw new Error("no walletProvider");
  //
  //   const provider = new BrowserProvider(walletProvider);
  //   const signer = await provider.getSigner();
  //   return signer?.signMessage(welcomeMessage);
  // }
  //
  // const connectWallet = useMutation({
  //   mutationFn: async () => {
  //     if (!isConnected) {
  //       toast.danger("No wallet connected");
  //       return;
  //     }
  //
  //     try {
  //       const signature = await signMessage();
  //       const userAddress = address || "";
  //       await web3Modal.close();
  //
  //       const response = await postClaimNft(userAddress, signature);
  //
  //       if (response.success) {
  //         setSuccess(true);
  //       } else {
  //         toast.danger("An unexpected error occurred. Please try again later.");
  //       }
  //     } catch (error) {
  //       await web3Modal.close();
  //       if (error instanceof AxiosError) {
  //         console.error(error.response?.data);
  //         toast.danger(
  //           error.response?.data.message ||
  //             "An unexpected error occurred. Please try again later.",
  //         );
  //       }
  //     }
  //     return;
  //   },
  // });

  // useEffect(() => {
  //   if (isConnected && claimed) {
  //     connectWallet.mutate();
  //     setClaimed(false);
  //   }
  // }, [isConnected, claimed]);

  const handleConnect = async () => {
    // EVM WalletConnect connection code
    // await web3Modal.open();
    // setClaimed(true);

    // Direct Solana connection code
    const connection = await connectWalletDirectly();
    const sign = await signMessageDirectly(connection.provider);

    try {
      const response = await postClaimNftDegods(connection.account, sign);

      if (response.success) {
        setSuccess(true);
      } else {
        toast.danger("An unexpected error occurred. Please try again later.");
      }
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error(error.response?.data);
        toast.danger(
          error.response?.data.message ||
            "An unexpected error occurred. Please try again later.",
        );
      }
    }
  };

  const handleDone = () => {
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="flex h-full w-full flex-col overflow-auto bg-dark-bk p-0 px-6 backdrop-blur-sm sm:h-auto"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <div className="flex items-center gap-2 pt-[calc(1rem+env(safe-area-inset-top))]">
          <div className="flex-1">
            <DialogClose className="flex flex-shrink-0">
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </DialogClose>
          </div>
          <h2 className="text-base font-semibold leading-[22px] text-off-white">
            Claim DeGods NFT Badge
          </h2>
          <div className="flex-1" />
        </div>
        <div className="mx-auto my-8 flex items-center justify-center">
          <div className="size-[200px] rounded-[10px] p-4">
            <img
              src="/assets/badges/badge-type-2.png"
              className="object-cover"
              alt="DeGods"
            />
          </div>
        </div>
        {!success && (
          <>
            <p className="whitespace-pre-wrap text-xl leading-[24px] text-off-white">
              Connect the Solana wallet that holds either your DeGods or y00ts
              NFT
            </p>
            <p className="text-sm text-[#B5B5B5]">
              Please click the connect button where you will be asked to sign a
              message to prove that you are the rightful owner of the NFT.
            </p>

            <p className="mb-8 text-sm text-[#B5B5B5]">
              Only Phantom wallet is supported. After signing the message, you
              will receive a special badge on your Arena profile.
            </p>
            <div className="mt-auto pb-[calc(1.5rem+env(safe-area-inset-bottom))]">
              <Button
                onClick={handleConnect}
                // disabled={connectWallet.isPending}
                className="w-full"
              >
                Connect
              </Button>
            </div>
          </>
        )}
        {success && (
          <>
            <p className="whitespace-pre-wrap text-xl leading-[20px] text-off-white">
              Congratulations!
            </p>
            <p className="mb-8 text-sm text-[#B5B5B5]">
              You have claimed the DeGods NFT Badge. From now on, you will see
              it next to your username on your profile.
            </p>
            <div className="mb-4">
              <Button onClick={handleDone} className="w-full">
                Done
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};
