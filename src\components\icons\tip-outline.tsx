import { ComponentProps } from "react";

export const TipOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 19 20"
    fill="none"
    {...props}
  >
    <circle
      cx={9.5}
      cy={10}
      r={8.864}
      stroke="currentColor"
      strokeWidth={1.272}
    />
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeWidth={1.272}
      d="M9.513 4.66v10.68M12.19 7.787c0-.728-.742-2.12-2.65-2.12s-2.724 1.206-2.703 2.12c.02.915.583 2.332 2.703 2.332s2.65 1.312 2.65 2.2c0 .675-.615 1.987-2.65 1.987s-2.65-1.325-2.703-1.987"
    />
  </svg>
);
