"use client";

import { FC, useEffect } from "react";
import { useParams, usePathname, useRouter } from "next/navigation";

import {
  SystemCurrency,
  SystemCurrencyCategoryEnum,
} from "@/api/client/admin-currency";
import { PlusCircleIcon } from "@/components/icons-v2/account-security";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useCurrenciesQuery } from "@/queries/currencies-query";
import { cn } from "@/utils";

import { useAdmin } from "../context/admin-context";

interface SupportedProjectsProps {
  projects?: SystemCurrency[];
  isLoading?: boolean;
}

export const SupportedProjects: FC<SupportedProjectsProps> = ({
  projects: externalProjects,
  isLoading: externalLoading,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();
  const currentProjectId = params?.projectId as string;
  const isLargeTablet = useMediaQuery(BREAKPOINTS.lg);
  const { selectedProject, setSelectedProject, setSelectedReward } = useAdmin();

  const {
    data: hookProjects,
    isLoading: hookLoading,
    isError,
  } = useCurrenciesQuery();

  const projects = externalProjects || hookProjects;
  const isLoading =
    externalLoading !== undefined ? externalLoading : hookLoading;

  const isNewProjectSelected = pathname === "/moderation/admin/projects/new";

  useEffect(() => {
    if (projects && currentProjectId && currentProjectId !== "new") {
      const projectFromUrl = projects.find(
        (project) => project.id.toString() === currentProjectId,
      );

      if (projectFromUrl) {
        setSelectedProject(projectFromUrl);
      }
    } else if (currentProjectId === "new") {
      setSelectedProject(null);
    }
  }, [projects, currentProjectId, setSelectedProject]);

  const handleProjectClick = (
    project: SystemCurrency,
    event: React.MouseEvent,
  ) => {
    if (isLargeTablet) {
      event.preventDefault();

      setSelectedProject(project);
      setSelectedReward(null);

      router.replace(`/moderation/admin/projects/${project.id}`, {
        scroll: false,
      });
    }
  };

  if (isLoading) {
    return <div className="p-4">Loading...</div>;
  }

  if (isError || !projects) {
    return <div className="p-4">Error loading projects</div>;
  }

  const formatCategories = (
    categories: SystemCurrencyCategoryEnum[] | null | undefined,
  ): string => {
    if (!categories || !categories.length) return "Excluded";

    return categories
      .map((category) => {
        const key = typeof category === "string" ? category : String(category);
        return key.charAt(0).toUpperCase() + key.slice(1);
      })
      .join(", ");
  };

  return (
    <div className="flex flex-col">
      <h2 className="font-semibold">Edit Supported Projects</h2>
      <p className="text-sm font-normal">
        Add / remove projects as; supported coins, tipping coins and social
        exchange.
      </p>

      <div className="mt-4 flex flex-col gap-3 overflow-y-auto">
        {projects.map((project) => {
          const isSelected = selectedProject?.id === project.id;

          return (
            <ProgressBarLink
              key={project.id}
              href={`/moderation/admin/projects/${project.id}`}
              onClick={(e) => handleProjectClick(project, e)}
              className={cn(
                "relative flex items-center justify-between rounded-[10px] border border-[#2a2a2a] p-4",
                isSelected
                  ? "bg-[#2A2A2A]"
                  : "bg-chat-bubble hover:bg-dark-gray",
              )}
            >
              <div className="flex items-center gap-3">
                <Avatar className="size-[30px]">
                  <AvatarImage src={project.image} />
                  <AvatarFallback />
                </Avatar>

                <div>
                  <h2 className="text-sm font-semibold">{project.symbol}</h2>
                  <p className="text-xs text-gray-text">{project.name}</p>
                </div>
              </div>
              <div className="text-right text-sm">
                <h2 className="text-sm font-semibold">Included In</h2>
                <p className="text-sm text-gray-text">
                  {formatCategories(project.categories)}
                </p>
              </div>
              {isSelected && (
                <div className="absolute inset-y-0 right-0 w-1.5 rounded-r-[10px] bg-brand-orange" />
              )}
            </ProgressBarLink>
          );
        })}
      </div>

      <ProgressBarLink
        href="/moderation/admin/projects/new"
        className={cn(
          "relative mt-3 flex items-center justify-between rounded-[10px] border border-[#2a2a2a] p-4",
          isNewProjectSelected ? "bg-[#2A2A2A]" : "hover:bg-dark-gray",
        )}
        onClick={(e) => {
          if (isLargeTablet) {
            e.preventDefault();
            setSelectedProject(null);
            setSelectedReward(null);
            router.replace("/moderation/admin/projects/new", {
              scroll: false,
            });
          }
        }}
      >
        <div className="flex items-center gap-3">
          <PlusCircleIcon height={32} width={32} strokeWidth={2} />
          <div>
            <h3 className="font-medium">Add New project</h3>
            <p className="text-sm text-gray-text">to the platform</p>
          </div>
        </div>
        {isNewProjectSelected && (
          <div className="absolute inset-y-0 right-0 w-1.5 rounded-r-[10px] bg-brand-orange" />
        )}
      </ProgressBarLink>
    </div>
  );
};
