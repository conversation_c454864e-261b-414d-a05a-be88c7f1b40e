import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { Dialog, DialogClose, DialogContent } from "@/components/ui/dialog";
import { cn } from "@/utils/cn";

import { BuyArena } from "./buy-arena-content";

export const Banner = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [type, setType] = useState<"champion" | "buy-arena">("buy-arena");
  const router = useRouter();

  useEffect(() => {
    const interval = setInterval(() => {
      setType((prevType) =>
        prevType === "champion" ? "buy-arena" : "champion",
      );
    }, 5000); // 5 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex sm:p-0 xl:py-2 xl:pr-1">
      {type === "champion" ? (
        <ProgressBarLink
          href="/champions"
          className="flex h-[52px] cursor-pointer items-center justify-between rounded-[10px] border border-[#eb540a] bg-center sm:h-12 sm:w-12 xl:w-[230px] xl:px-[16px] xl:py-[12px]"
          style={{
            backgroundImage: "url('/icons/champion-banner-desktop.svg')",
          }}
        >
          <div className="hidden xl:flex xl:flex-col xl:gap-0">
            <span className="text-xs font-normal">Become an</span>
            <span className="text-sm font-bold text-[#EB540A]">
              Arena Champion
            </span>
          </div>
          <img
            src="/icons/arena-champion-user.svg"
            className="mx-auto h-7 w-7 rounded-full drop-shadow-badge xl:mx-0"
            alt="Champions logo"
          />
        </ProgressBarLink>
      ) : (
        <button
          className="flex h-[52px] cursor-pointer items-center justify-between rounded-[10px] border border-[#eb540a] bg-center text-left sm:h-12 sm:w-12 xl:w-[230px] xl:px-[16px] xl:py-[12px]"
          style={{
            backgroundImage: "url('/icons/champion-banner-desktop.svg')",
          }}
          onClick={() => setIsOpen(true)}
        >
          <div className="hidden xl:flex xl:flex-col xl:gap-0">
            <span className="text-xs font-normal">Still not a champion?</span>
            <span className="text-sm font-bold text-[#EB540A]">Buy ARENA</span>
          </div>
          <img
            src="/icons/arena-champion-user.svg"
            className="mx-auto h-7 w-7 rounded-full drop-shadow-badge xl:mx-0"
            alt="Champions logo"
          />
        </button>
      )}

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent
          className={cn(
            "flex h-full w-full  flex-grow flex-col justify-between bg-[#0F0F0F]/90 px-0 pt-[calc(58px+env(safe-area-inset-top))] backdrop-blur-sm sm:h-auto sm:gap-6 sm:py-6",
          )}
        >
          <div className="flex items-center gap-3 sm:hidden">
            <DialogClose className="absolute left-5 top-[calc(2rem+env(safe-area-inset-top))] rounded-full p-1 sm:relative sm:left-0 sm:top-0">
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </DialogClose>
          </div>
          <BuyArena
            ticker="ARENA"
            onSuccess={() => {
              router.push("/champions");
              setIsOpen(false);
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export const MiniBanner = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [type, setType] = useState<"champion" | "buy-arena">("buy-arena");
  const router = useRouter();

  useEffect(() => {
    const interval = setInterval(() => {
      setType((prevType) =>
        prevType === "champion" ? "buy-arena" : "champion",
      );
    }, 5000); // 5 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      {type === "champion" ? (
        <ProgressBarLink
          href={"/champions"}
          className="inline-flex h-7 w-[156px] cursor-pointer flex-row items-center justify-between gap-1 overflow-hidden rounded-[33px] border border-[#eb540a] bg-center px-[12px] py-[6px]"
          style={{
            backgroundImage: "url('/icons/champion-banner-mobile.svg')",
          }}
        >
          <span className="ml-auto text-xs font-bold text-[#EB540A]">
            Arena Champion
          </span>
          <img
            src="/icons/arena-champion-user.svg"
            className="mr-auto h-4 w-4 rounded-full drop-shadow-badge"
            alt="Champions logo"
          />
        </ProgressBarLink>
      ) : (
        <button
          className="inline-flex h-7 w-[156px] cursor-pointer flex-row items-center justify-between gap-1 overflow-hidden rounded-[33px] border border-[#eb540a] bg-center px-[12px] py-[6px] text-left"
          style={{
            backgroundImage: "url('/icons/champion-banner-mobile.svg')",
          }}
          onClick={() => setIsOpen(true)}
        >
          <span className="ml-auto text-xs font-bold text-[#EB540A]">
            Buy ARENA
          </span>
          <img
            src="/icons/arena-champion-user.svg"
            className="mr-auto h-4 w-4 rounded-full drop-shadow-badge"
            alt="Champions logo"
          />
        </button>
      )}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent
          className={cn(
            "flex h-full w-full  flex-grow flex-col justify-between bg-[#0F0F0F]/90 px-0 pt-[calc(58px+env(safe-area-inset-top))] backdrop-blur-sm sm:h-auto sm:gap-6 sm:py-6",
          )}
        >
          <div className="flex items-center gap-3">
            <DialogClose className="absolute left-5 top-[calc(2rem+env(safe-area-inset-top))] rounded-full p-1 sm:relative sm:left-0 sm:top-0">
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </DialogClose>
          </div>
          <BuyArena
            ticker="ARENA"
            onSuccess={() => {
              router.push("/champions");
              setIsOpen(false);
            }}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export const LargeBanner = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [type, setType] = useState<"champion" | "buy-arena">("buy-arena");
  const router = useRouter();

  useEffect(() => {
    const interval = setInterval(() => {
      setType((prevType) =>
        prevType === "champion" ? "buy-arena" : "champion",
      );
    }, 5000); // 5 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      {type === "champion" ? (
        <ProgressBarLink
          href={"/champions"}
          className="mb-4 flex w-full flex-row items-center justify-between rounded-[10px] border border-[#eb540a] bg-center p-4"
          style={{ backgroundImage: "url('/images/champion.png')" }}
        >
          <div className="relative flex flex-col">
            <span className="text-xl font-bold text-[#eb540a]">
              ARENA CHAMPION
            </span>
            <span className="max-w-[100%] pt-[8px] text-xs font-normal text-[#B4B4B4]">
              <div className="md:hidden">
                <p>Get the ultimate benefits &</p>
                <p>rewards for staking ARENA</p>
              </div>
              <div className="hidden md:block">
                Get the ultimate benefits & rewards for staking ARENA
              </div>
            </span>
          </div>

          <div className="relative flex">
            <img
              src="/icons/arena-champion-user.svg"
              className="h-[40px] w-[40px] rounded-full drop-shadow-badge"
              alt="Champion User Icon"
            />
          </div>
        </ProgressBarLink>
      ) : (
        <button
          onClick={() => setIsOpen(true)}
          className="mb-4 flex w-full flex-row items-center justify-between rounded-[10px] border border-[#eb540a] bg-center p-4 text-left"
          style={{ backgroundImage: "url('/images/champion.png')" }}
        >
          <div className="relative flex flex-col">
            <span className="text-xl font-bold text-[#eb540a]">Buy ARENA</span>
            <span className="max-w-[100%] pt-[8px] text-xs font-normal text-[#B4B4B4]">
              <div className="md:hidden">
                <p>Get it in one click & stake it</p>
                <p>to become a Champion</p>
              </div>
              <div className="hidden md:block">
                Get it in one click & stake it to become a Champion
              </div>
            </span>
          </div>
          <div className="relative flex">
            <img
              src="/icons/arena-champion-user.svg"
              className="h-[40px] w-[40px] rounded-full drop-shadow-badge"
              alt="Champion User Icon"
            />
          </div>
        </button>
      )}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent
          className={cn(
            "flex h-full w-full  flex-grow flex-col justify-between bg-[#0F0F0F]/90 px-0 pt-[calc(58px+env(safe-area-inset-top))] backdrop-blur-sm sm:h-auto sm:gap-6 sm:py-6",
          )}
        >
          <div className="flex items-center gap-3">
            <DialogClose className="absolute left-5 top-[calc(2rem+env(safe-area-inset-top))] rounded-full p-1 sm:relative sm:left-0 sm:top-0">
              <ArrowBackOutlineIcon className="size-5 text-off-white" />
            </DialogClose>
          </div>
          <BuyArena
            ticker="ARENA"
            onSuccess={() => {
              router.push("/champions");
              setIsOpen(false);
            }}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};
