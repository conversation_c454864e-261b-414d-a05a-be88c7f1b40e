import { Thread<PERSON><PERSON>, User } from "@/types";

import { MutedOutlineIcon, UnmutedOutlineIcon } from "../icons";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Badge } from "../ui/badge";

export const UserListItem = ({
  user,
  badge,
  children,
  isMuted,
}: {
  user: ThreadUser | User;
  badge?: "invited" | "left";
  children?: React.ReactNode;
  isMuted?: boolean;
}) => {
  return (
    <div className="flex items-center gap-4">
      <Avatar className="size-[42px]">
        <AvatarImage src={user.twitterPicture} />
        <AvatarFallback />
      </Avatar>
      <div className="flex flex-col overflow-hidden">
        <div className="flex min-w-0 items-center gap-2">
          <p className="truncate text-sm font-medium text-off-white">
            {user.twitterName}
          </p>
          {badge === "invited" && (
            <Badge className="flex-shrink-0">Invited</Badge>
          )}
          {badge === "left" && (
            <Badge className="flex-shrink-0 px-2" variant="destructive">
              left
            </Badge>
          )}
        </div>
        <p className="flex items-center gap-0.5 truncate text-sm text-gray-text">
          {typeof isMuted === "boolean" && (
            <>
              {isMuted ? (
                <MutedOutlineIcon className="size-4 flex-shrink-0" />
              ) : (
                <UnmutedOutlineIcon className="size-4 flex-shrink-0" />
              )}
            </>
          )}
          {user.twitterHandle}
        </p>
      </div>
      <div className="ml-auto">{children}</div>
    </div>
  );
};

export const ParticipantListItem = ({
  user,
  children,
  badge,
}: {
  user: {
    id: string;
    name: string;
    avatar: string;
    username: string;
    role: string;
  };
  badge?: "invited" | "left";
  children?: React.ReactNode;
}) => {
  return (
    <div className="flex items-center gap-4">
      <Avatar className="size-[42px]">
        <AvatarImage src={user.avatar} />
        <AvatarFallback />
      </Avatar>
      <div className="flex flex-col overflow-hidden">
        <div className="flex min-w-0 items-center gap-2">
          <p className="truncate text-sm font-medium text-off-white">
            {user.name}
          </p>
          {badge === "invited" && (
            <Badge className="flex-shrink-0">Invited</Badge>
          )}
          {badge === "left" && (
            <Badge className="flex-shrink-0" variant="destructive">
              Left
            </Badge>
          )}
        </div>
        <p className="truncate text-sm text-gray-text">{user.username}</p>
      </div>
      <div className="ml-auto">{children}</div>
    </div>
  );
};
