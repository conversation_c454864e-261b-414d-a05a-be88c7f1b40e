"use client";

import { memo } from "react";

import { Virtuoso } from "react-virtuoso";

import { UserListItem } from "@/components/user-list-item";
import { UserListItemLoadingSkeleton } from "@/components/user-list-item-loading-skeleton";
import { useTopUsersQuery } from "@/queries";

export const TopUsersTab = memo(function TopUsersTab() {
  const { data, isLoading } = useTopUsersQuery();

  return (
    <>
      <p className="px-6 text-sm text-[#808080]">
        Users with the highest ticket price
      </p>
      <div className="mt-[10px]">
        {isLoading &&
          Array.from({ length: 15 }).map((_, index) => {
            return <UserListItemLoadingSkeleton key={index} />;
          })}
        {!isLoading && data?.users.length !== 0 && (
          <Virtuoso
            useWindowScroll
            data={data?.users || []}
            increaseViewportBy={500}
            itemContent={(index, user) => {
              return <UserListItem user={user} />;
            }}
          />
        )}
      </div>
    </>
  );
});
