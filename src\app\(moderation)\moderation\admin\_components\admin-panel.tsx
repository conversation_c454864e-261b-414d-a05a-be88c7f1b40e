"use client";

import { FC, useEffect, useRef, useState } from "react";
import { redirect } from "next/navigation";

import { SystemCurrency } from "@/api/client/admin-currency";
import { RewardCurrencies } from "@/api/client/admin-rewards";
import { Search } from "@/app/(messages)/messages/_components/search";
import { ChampionsRewards } from "@/app/(moderation)/moderation/admin/_components/champions-rewards";
import { SupportedProjects } from "@/app/(moderation)/moderation/admin/_components/supported-projects";
import { useCurrenciesQuery } from "@/queries/currencies-query";
import { useRewardsQuery } from "@/queries/rewards-query";
import { useUser } from "@/stores/user";

export const AdminPanel: FC = () => {
  const { user } = useUser();
  const [search, setSearch] = useState("");
  const contentRef = useRef<HTMLDivElement>(null);

  const { data: projects, isLoading: projectsLoading } = useCurrenciesQuery();
  const { data: rewards, isLoading: rewardsLoading } = useRewardsQuery();

  const [filteredProjects, setFilteredProjects] = useState<SystemCurrency[]>(
    [],
  );
  const [filteredRewards, setFilteredRewards] = useState<RewardCurrencies[]>(
    [],
  );

  useEffect(() => {
    if (projects) {
      const filtered = projects.filter(
        (project) =>
          project.name.toLowerCase().includes(search.toLowerCase()) ||
          project.symbol.toLowerCase().includes(search.toLowerCase()),
      );
      setFilteredProjects(filtered);
    }

    if (rewards) {
      const filtered = rewards.filter(
        (reward) =>
          reward.systemCurrency.name
            .toLowerCase()
            .includes(search.toLowerCase()) ||
          reward.systemCurrency.symbol
            .toLowerCase()
            .includes(search.toLowerCase()),
      );
      setFilteredRewards(filtered);
    }
  }, [search, projects, rewards]);

  if (!user?.isMod) {
    redirect("/");
  }

  return (
    <div className="mt-4">
      <div className="flex flex-col divide-dark-gray">
        <Search
          placeholder="Search for projects or rewards"
          search={search}
          inputOnChange={(e) => setSearch(e.target.value)}
          closeOnClick={() => setSearch("")}
        />
        <div
          ref={contentRef}
          className="flex max-h-[calc(100vh-80px)] flex-col overflow-y-auto p-6 sm:p-8"
        >
          <SupportedProjects
            projects={filteredProjects}
            isLoading={projectsLoading}
          />
          <ChampionsRewards
            rewards={filteredRewards}
            isLoading={rewardsLoading}
          />
        </div>
      </div>
    </div>
  );
};
