import { ComponentProps } from "react";

export const ShoppingCartOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 20 20"
    fill="none"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0 0.75C0 0.335786 0.335786 0 0.75 0H2.75C3.10751 0 3.41532 0.252345 3.48544 0.602913L3.76485 2H18.75C19.0099 2 19.2513 2.13459 19.388 2.3557C19.5246 2.57681 19.5371 2.85292 19.4208 3.08541L15.4208 11.0854C15.2938 11.3395 15.0341 11.5 14.75 11.5H5.06066L2.98733 13.5733C2.82971 13.731 2.94131 14 3.164 14H14.75C15.4793 14 16.1788 14.2897 16.6945 14.8055C17.2103 15.3212 17.5 16.0207 17.5 16.75C17.5 17.4793 17.2103 18.1788 16.6945 18.6945C16.1788 19.2103 15.4793 19.5 14.75 19.5C14.0207 19.5 13.3212 19.2103 12.8055 18.6945C12.2897 18.1788 12 17.4793 12 16.75C12 16.3114 12.1048 15.8835 12.3005 15.5H7.19949C7.39521 15.8835 7.5 16.3114 7.5 16.75C7.5 17.4793 7.21027 18.1788 6.69454 18.6945C6.17882 19.2103 5.47935 19.5 4.75 19.5C4.02065 19.5 3.32118 19.2103 2.80546 18.6945C2.28973 18.1788 2 17.4793 2 16.75C2 16.2447 2.13904 15.7538 2.39593 15.3284C1.38281 14.8391 1.02514 13.4142 1.92667 12.5127L3.93585 10.5035L2.13515 1.5H0.75C0.335786 1.5 0 1.16421 0 0.75ZM5.36485 10H14.2865L17.5365 3.5H4.06485L5.36485 10ZM4.75 15.5C4.41848 15.5 4.10054 15.6317 3.86612 15.8661C3.6317 16.1005 3.5 16.4185 3.5 16.75C3.5 17.0815 3.6317 17.3995 3.86612 17.6339C4.10054 17.8683 4.41848 18 4.75 18C5.08152 18 5.39946 17.8683 5.63388 17.6339C5.8683 17.3995 6 17.0815 6 16.75C6 16.4185 5.8683 16.1005 5.63388 15.8661C5.39946 15.6317 5.08152 15.5 4.75 15.5ZM14.75 15.5C14.4185 15.5 14.1005 15.6317 13.8661 15.8661C13.6317 16.1005 13.5 16.4185 13.5 16.75C13.5 17.0815 13.6317 17.3995 13.8661 17.6339C14.1005 17.8683 14.4185 18 14.75 18C15.0815 18 15.3995 17.8683 15.6339 17.6339C15.8683 17.3995 16 17.0815 16 16.75C16 16.4185 15.8683 16.1005 15.6339 15.8661C15.3995 15.6317 15.0815 15.5 14.75 15.5Z"
      fill="currentColor"
    />
  </svg>
);
