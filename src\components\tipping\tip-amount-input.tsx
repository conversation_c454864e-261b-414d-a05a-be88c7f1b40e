import React, { ReactElement } from "react";

import { TextInput } from "../ui/text-input";

interface TipAmountInputProps {
  name: string;
  value: string;
  totalTipUSD: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: () => void;
  onMaxClick?: () => void;
  ref?: React.Ref<any>;
  errorMessage?: string;
  placeholder?: string;
}

export const TipAmountInput: React.FC<TipAmountInputProps> = ({
  name,
  value,
  totalTipUSD,
  onChange,
  onBlur,
  onMaxClick,
  ref,
  errorMessage,
  placeholder = "How much do you want to tip?",
}) => {
  const labelWithAmount = (
    <div className="flex flex-row">
      <div className="mr-1 font-normal text-[#B5B5B5]">Tip amount:</div>
      <div className="text-[#F4F4F4]">
        {Number(value.replace(/,/g, "")) === 0
          ? "$0"
          : Number(totalTipUSD.replace(/,/g, "")) < 0.01
            ? "< $0.01"
            : `$${totalTipUSD}`}
      </div>
    </div>
  );

  return (
    <TextInput
      labelWithAmount={labelWithAmount}
      placeholder={placeholder}
      name={name}
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      onMaxClick={onMaxClick}
      ref={ref}
      errorMessage={errorMessage}
    />
  );
};
