import { useState } from "react";

import { InformationCircleOutlineIcon } from "@/components/icons";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerContent,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

export const TradeERC20TnCs = () => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [open, setOpen] = useState(false);

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <div className="mt-4 flex w-full justify-center">
          <DialogTrigger asChild>
            <Button
              variant="outline"
              className="mb-4 flex items-center gap-[10px] border-none text-off-white"
            >
              <InformationCircleOutlineIcon className="size-6" />
              <span className="text-sm font-semibold underline">
                Trading Terms & Conditions
              </span>
            </Button>
          </DialogTrigger>
        </div>
        <DialogTitle className="hidden" />
        <DialogContent className="gap-4">
          <TradeERC20TnCsModalContent />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <div className="mt-4 flex w-full justify-center">
        <DrawerTrigger asChild>
          <Button
            variant="outline"
            className="mb-4 mt-auto flex w-full items-center gap-[10px] border-none text-off-white"
          >
            <InformationCircleOutlineIcon className="size-6" />
            <span className="text-sm font-semibold underline">
              Trading Terms & Conditions
            </span>
          </Button>
        </DrawerTrigger>
      </div>
      <DrawerTitle className="hidden" />
      <DrawerContent className="gap-4">
        <TradeERC20TnCsModalContent />
      </DrawerContent>
    </Drawer>
  );
};

const TradeERC20TnCsModalContent = () => (
  <div>
    <h3 className="text-base font-semibold leading-[22px] text-off-white">
      Trading Terms &amp; Conditions
    </h3>
    <div className="mt-4 text-[12px] text-gray-text">
      None of the information presented by the Arena is intended to constitute
      legal, financial, investment, or other professional advice. Digital assets
      and trading or transactions thereof are inherently risky, and you may lose
      any value you use to purchase or transact in such assets.
    </div>
    <div className="mt-2 text-[12px] font-semibold text-gray-text">
      THE PRICE AND LIQUIDITY OF DIGITAL ASSETS HAS BEEN SUBJECT TO LARGE
      FLUCTUATIONS IN THE PAST AND MAY BE SUBJECT TO LARGE FLUCTUATIONS IN THE
      FUTURE. YOU SHOULD CAREFULLY CONSIDER THE RISKS AND AGREE TO CONSULT YOUR
      OWN ADVISORS BEFORE DECIDING TO BUY OR SELL DIGITAL ASSETS.
    </div>
    <div className="mt-2 text-[12px] text-gray-text">
      Pricing or data in the app may be inaccurate or delayed. By using the
      platform, you hereby agree and acknowledge that Stars Arena, Inc. is in no
      way liable for any losses you may incur by trading or transacting in any
      digital assets released on the platform or based on information you may
      have received from the Arena. In addition to the terms above, you fully
      agree to the terms and conditions at{" "}
      <a className="text-off-white" target="_blank">
        https://arena.social/terms-of-use
      </a>
    </div>
  </div>
);
