"use client";

import { useMemo } from "react";
import { useParams } from "next/navigation";

import { Virtuoso } from "react-virtuoso";

import { PostLoadingSkeleton } from "@/components/post-loading-skeleton";
import { useThreadAnswersInfiniteQuery } from "@/queries";
import { Thread } from "@/types";

import { CommentPost } from "./comment-post";

export const Comments = () => {
  const params = useParams() as { id: string };
  const {
    data: answersData,
    fetchNextPage: fetchAnswersNextPage,
    isFetchingNextPage: isFetchingAnswersNextPage,
    isLoading: isAnswersLoading,
    hasNextPage,
  } = useThreadAnswersInfiniteQuery(params.id);

  const threads = useMemo(() => {
    if (!answersData) return [];

    return answersData.pages.reduce((prev, current) => {
      return [...prev, ...current.threads];
    }, [] as Thread[]);
  }, [answersData]);

  return (
    <div className="pb-pwa">
      <Virtuoso
        useWindowScroll
        data={threads}
        endReached={() => {
          if (hasNextPage) {
            fetchAnswersNextPage();
          }
        }}
        overscan={200}
        itemContent={(index, thread) => {
          return <CommentPost thread={thread} />;
        }}
        components={{
          Footer: () => {
            if (isAnswersLoading || isFetchingAnswersNextPage) {
              return (
                <>
                  {Array.from({ length: 5 }).map((_, i) => (
                    <PostLoadingSkeleton key={i} />
                  ))}
                </>
              );
            }
            return null;
          },
        }}
      />
    </div>
  );
};
