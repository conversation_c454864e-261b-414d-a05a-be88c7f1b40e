"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import { useSearchParams } from "next/navigation";

import { AnimatePresence, motion } from "framer-motion";

import { acceptAppStore } from "@/api/client/user";
import {
  ArrowsExpandOutlineIcon,
  BanOutlineIcon,
  ExclamationCircleOutlineIcon,
  XCircleOutlineIcon,
} from "@/components/icons";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { IS_ANDROID, IS_IOS } from "@/utils/window-environment";

export function PlayPittButton() {
  const [isDisclaimerOpen, setIsDisclaimerOpen] = useState(false);
  const [isDisclaimerAccepted, setIsDisclaimerAccepted] = useLocalStorage(
    "the-pit-disclaimer-accepted",
    false,
  );
  const [open, setOpen] = useState(false);
  const [confirmantionOpen, setConfirmationOpen] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showFullscreenPopup, setShowFullscreenPopup] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const searchParams = useSearchParams().toString();

  // const { user } = useUser();
  // if (!user) return;

  // const { data: disclaimerAccepted } = useIsUserAcceptedAppStore(user.id);

  const iframeUrl = useMemo(() => {
    const url = IS_ANDROID
      ? "https://android.thepitgame.xyz/"
      : "https://thepitgame.xyz/";

    if (searchParams) {
      return url + "?" + searchParams;
    }

    return url;
  }, [searchParams]);

  const enterFullscreen = async () => {
    try {
      if (iframeRef.current) {
        await iframeRef.current.requestFullscreen();
      }
    } catch (error) {
      console.error("Failed to enter fullscreen:", error);
    }
  };

  const exitFullscreen = async () => {
    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      }
    } catch (error) {
      console.error("Failed to exit fullscreen:", error);
    }
  };

  // useEffect(() => {
  //   if (disclaimerAccepted) setIsDisclaimerAccepted(true);
  // }, [disclaimerAccepted]);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () =>
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, []);

  useEffect(() => {
    let timerRef: NodeJS.Timeout | null = null;
    if (isFullscreen) {
      setShowFullscreenPopup(true);
      timerRef = setTimeout(() => {
        setShowFullscreenPopup(false);
      }, 3000);
    } else {
      setShowFullscreenPopup(false);
    }

    return () => {
      if (timerRef) {
        clearTimeout(timerRef);
      }
    };
  }, [isFullscreen]);

  const handleClick = () => {
    if (isDisclaimerAccepted) {
      setOpen(true);
    } else {
      setIsDisclaimerOpen(true);
    }
  };

  const handleAcceptDisclaimer = async () => {
    setIsDisclaimerAccepted(true);
    // acceptAppStore({ userId: user.id });
  };

  if (IS_IOS) {
    return (
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button>Play The Pit</Button>
        </AlertDialogTrigger>
        <AlertDialogContent className="max-w-xs gap-6 rounded-[10px] border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)] backdrop-blur-sm">
          <div className="flex flex-col items-center gap-2">
            <BanOutlineIcon className="mx-auto size-6 text-off-white" />
            <h3 className="text-center text-base font-semibold text-off-white">
              iOS devices not supported
            </h3>
          </div>
          <div className="flex flex-col gap-2 text-sm text-gray-text">
            <p>
              Unfortunately, The Pit doesn’t support iOS devices yet! If you
              still want to play the game, we recommend trying it on a desktop
              browser.
            </p>
          </div>
          <AlertDialogCancel className="flex-1 border-none" asChild>
            <Button>Close</Button>
          </AlertDialogCancel>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  return (
    <>
      <Button onClick={handleClick}>Play The Pit</Button>
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent
          className="max-w-7xl border-none  p-0 sm:rounded-none"
          onEscapeKeyDown={(event) => {
            event.preventDefault();
            setConfirmationOpen(true);
          }}
        >
          <div className="absolute -top-16 left-6 flex items-center gap-4 lg:left-0">
            <AlertDialog
              open={confirmantionOpen}
              onOpenChange={setConfirmationOpen}
            >
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  className="size-10 border-none bg-chat-bubble p-0 hover:bg-chat-bubble/90"
                >
                  <XCircleOutlineIcon className="size-7 text-off-white" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="max-w-sm gap-6 bg-[rgba(15,15,15,0.90)] px-8 py-6 backdrop-blur-sm">
                <AlertDialogHeader className="gap-6 space-y-0">
                  <div className="flex flex-col items-center gap-2">
                    <div className="flex size-10 items-center justify-center rounded-full bg-chat-bubble">
                      <XCircleOutlineIcon className="size-7 text-off-white" />
                    </div>
                    <AlertDialogTitle className="text-center text-lg font-semibold leading-5">
                      Are you sure you want to quit?
                    </AlertDialogTitle>
                  </div>
                  <AlertDialogDescription className="text-center text-sm text-light-gray-text">
                    All unsaved progress will be lost if you close this
                    application now.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel className="sm:flex-[1]">
                    Back
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => {
                      setOpen(false);
                    }}
                    className="sm:flex-[2]"
                  >
                    Close App
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
            <Button
              variant="outline"
              className="size-10 border-none bg-chat-bubble p-0 hover:bg-chat-bubble/90"
              onClick={enterFullscreen}
            >
              <ArrowsExpandOutlineIcon className="size-6 text-off-white" />
            </Button>
          </div>
          <div
            ref={iframeRef}
            className="relative aspect-[3/4] w-full sm:aspect-video"
          >
            <iframe
              src={iframeUrl}
              className="size-full"
              title={"The Pit"}
              allow="fullscreen"
              allowFullScreen
              onKeyDown={(event) => {
                if (event.key === "Escape" && isFullscreen) {
                  exitFullscreen();
                }
              }}
            />
            <AnimatePresence>
              {showFullscreenPopup && (
                <motion.div
                  className="pointer-events-none fixed left-[50%] top-[50%] z-50 box-border grid w-full max-w-xs translate-x-[-50%] translate-y-[-50%] gap-6 rounded-[20px] border border-dark-gray bg-dark-bk px-8 py-6 shadow-lg backdrop-blur-sm"
                  initial={{
                    opacity: 0,
                  }}
                  animate={{
                    opacity: 1,
                  }}
                  exit={{
                    opacity: 0,
                  }}
                  transition={{
                    duration: 0.5,
                    ease: "easeInOut",
                  }}
                >
                  <div className="flex flex-col items-center gap-1">
                    <div className="flex size-10 items-center justify-center rounded-full bg-chat-bubble">
                      <ArrowsExpandOutlineIcon className="size-6 text-off-white" />
                    </div>
                    <h3 className="text-center text-lg font-semibold leading-5">
                      Full Screen Mode
                    </h3>
                  </div>
                  <p className="text-center text-sm text-gray-text">
                    Press{" "}
                    <span className="inline-block rounded border border-gray-text px-1.5 py-1 leading-none ">
                      esc
                    </span>{" "}
                    to exit fullscreen
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={isDisclaimerOpen} onOpenChange={setIsDisclaimerOpen}>
        <AlertDialogContent className="max-w-md rounded-[10px] border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)] backdrop-blur-sm">
          <ExclamationCircleOutlineIcon className="mx-auto size-11 text-brand-orange" />
          <h3 className="text-center text-base font-semibold text-off-white">
            Disclaimer: Please read carefully
          </h3>
          <div className="flex flex-col gap-2 text-sm text-gray-text">
            <p>
              The Arena App Store includes applications created by third-party
              developers. By connecting your wallet to any third-party app, you
              acknowledge and accept all associated security risks. Arena is not
              responsible for any loss of funds or wallet compromise resulting
              from interactions with these third-party applications.
            </p>
            <p>
              You are solely responsible for securing your wallet and ensuring
              it is protected against unauthorized access.
            </p>
            <p>
              To stay updated on the terms of use for third-party apps, please
              refer to the following link:{" "}
              <a
                href="https://arena.social/terms-of-use"
                className="underline"
                target="_blank"
                rel="noreferrer"
              >
                Terms of Use
              </a>
            </p>
            <p>
              By clicking “I Accept”, you confirm that you have read,
              understood, and agree to the terms above.
            </p>
          </div>
          <div className="mt-2 flex items-center gap-2">
            <AlertDialogCancel className="flex-1">Decline</AlertDialogCancel>
            <Button
              className="flex-1"
              onClick={() => {
                setIsDisclaimerAccepted(true);
                setIsDisclaimerOpen(false);
                setOpen(true);
                // handleAcceptDisclaimer();
              }}
            >
              I Accept
            </Button>
          </div>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
