"use client";

import { memo, useCallback, useEffect } from "react";

import {
  useLocalParticipant,
  useMaybeRoomContext,
  useMediaDeviceSelect,
  useParticipantAttribute,
  useParticipantPermissions,
  usePersistentUserChoices,
  useTrackToggle,
} from "@livekit/components-react";
import { RoomEvent, Track } from "livekit-client";
import { ErrorBoundary } from "react-error-boundary";

import { useRequestToSpeakMutation } from "@/queries";
import { useStageStore } from "@/stores/stage";
import { cn } from "@/utils";

import {
  CurrencyDollarOutlineIcon,
  EmojiHappyOutlineIcon,
  MicCircleFilledIcon,
  MutedMicCircleFilledIcon,
} from "../icons";
import { ChatbubbleOutlineIcon } from "../icons-v2";
import { UsersOutlineIcon } from "../icons/users-outline";
import { toast } from "../toast";
import { Select, SelectContent, SelectItem, SelectTrigger } from "../ui/select";
import { Role } from "./constants";
import { EmoteModal } from "./emote-modal";
import { useIsRoomMuted } from "./hooks/use-is-room-muted";
import { StageTippingPartyModal } from "./stage-tipping-party-modal";
import { useDataChannelsContext } from "./stores/data-channels-context";

export const StageBottom = memo(() => {
  const local = useLocalParticipant();
  const hasUnseenRequests = useStageStore((state) => state.hasUnseenRequests);
  const actions = useStageStore((state) => state.actions);
  const role = useParticipantAttribute("role", {
    participant: local.localParticipant,
  }) as Role;
  const isHost = role === "HOST" || role === "COHOST";

  return (
    <>
      <div className="flex justify-between py-4 pl-6 pr-4">
        <MicHandler />
        <div className="flex items-center pb-2">
          <button className="p-2" onClick={() => actions.toggleChatOpen()}>
            <ChatbubbleOutlineIcon className="size-7 text-off-white" />
          </button>
          <EmoteModal>
            <button className="p-2">
              <EmojiHappyOutlineIcon
                strokeWidth={1.8}
                className="size-7 text-off-white"
              />
            </button>
          </EmoteModal>
          <ErrorBoundary fallback={null}>
            <StageTippingPartyModal>
              <button className="p-2">
                <CurrencyDollarOutlineIcon
                  strokeWidth={1.8}
                  className="size-7 text-off-white"
                />
              </button>
            </StageTippingPartyModal>
          </ErrorBoundary>
          {isHost ? (
            <button
              className="relative p-2"
              onClick={() => {
                actions.setIsGuestsModalOpen(true);
              }}
            >
              <UsersOutlineIcon
                strokeWidth={1.8}
                className="size-7 text-off-white"
              />
              {hasUnseenRequests && (
                <div className="absolute right-1.5 top-2 size-1 rounded-full bg-brand-orange" />
              )}
            </button>
          ) : null}
        </div>
      </div>
      {/* <div className="flex items-center justify-center gap-3 border-t border-gray-bg px-5 py-4">
        <ReactionButtons />
      </div> */}
    </>
  );
});

StageBottom.displayName = "StageBottom";

const MicHandler = () => {
  const local = useLocalParticipant();

  const data = useParticipantPermissions({
    participant: local.localParticipant,
  });

  if (!data) return <div />;

  const role = local.localParticipant.attributes.role as Role;

  if (role === "HOST" || role === "COHOST" || role === "SPEAKER") {
    return <MicToggleButton />;
  }

  return <RequestMicButton />;
};

const RequestMicButton = () => {
  const isRequestedToSpeak = useStageStore((state) => state.isRequestedToSpeak);
  const id = useStageStore((state) => state.id!);
  const actions = useStageStore((state) => state.actions);
  const { sendInvalidateStageInfo } = useDataChannelsContext();

  const { mutateAsync: requestToSpeak } = useRequestToSpeakMutation({
    onSuccess: () => {
      actions.setIsRequestedToSpeak(true);
      sendInvalidateStageInfo();
    },
  });

  const handleRequestToSpeak = async () => {
    await requestToSpeak({
      stageId: id,
    });
  };

  return (
    <button
      className="flex w-[50px] flex-col items-center gap-1"
      disabled={isRequestedToSpeak}
      onClick={handleRequestToSpeak}
    >
      <div
        className={cn(
          "flex size-10 items-center justify-center rounded-full border",
          isRequestedToSpeak ? "border-[#6FB672]" : "border-brand-orange",
        )}
      >
        <MicCircleFilledIcon
          className={cn(
            "size-10",
            isRequestedToSpeak ? "text-[#6FB672]" : "text-brand-orange",
          )}
        />
      </div>
      <span className="text-xs text-off-white">
        {isRequestedToSpeak ? "Requested" : "Request"}
      </span>
    </button>
  );
};

const MicToggleButton = () => {
  const isRoomMuted = useIsRoomMuted();
  const local = useLocalParticipant();
  const myRole = useParticipantAttribute("role", {
    participant: local.localParticipant,
  }) as Role;

  // Toggle mic and save preferences
  const { saveAudioInputEnabled, saveAudioInputDeviceId } =
    usePersistentUserChoices();
  const {
    buttonProps: { className, onClick, ...buttonProps },
    enabled,
    toggle,
  } = useTrackToggle({
    source: Track.Source.Microphone,
    onChange: (enabled: boolean, isUserInitiated: boolean) =>
      microphoneOnChange(enabled, isUserInitiated),
    onDeviceError: (error) => {
      console.log({ source: Track.Source.Microphone, error });
    },
  });

  const microphoneOnChange = useCallback(
    (enabled: boolean, isUserInitiated: boolean) =>
      isUserInitiated ? saveAudioInputEnabled(enabled) : null,
    [saveAudioInputEnabled],
  );

  const room = useMaybeRoomContext();
  const handleError = useCallback(
    (e: Error) => {
      if (room) {
        // awkwardly emit the event from outside of the room, as we don't have other means to raise a MediaDeviceError
        room.emit(RoomEvent.MediaDevicesError, e);
      }
      console.log(e);
    },
    [room],
  );
  const { devices, activeDeviceId, setActiveMediaDevice } =
    useMediaDeviceSelect({
      kind: "audioinput",
      room,
      onError: handleError,
    });

  const handleActiveDeviceChange = async (deviceId: string) => {
    try {
      await setActiveMediaDevice(deviceId);
      saveAudioInputDeviceId(deviceId ?? "");
    } catch (e) {
      if (e instanceof Error) {
        console.log(e);
      } else {
        throw e;
      }
    }
  };

  const handleMicClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (myRole !== "HOST" && isRoomMuted) {
      toast.danger("Can’t unmute. You were muted by the host.");
    } else {
      onClick?.(event);
    }
  };

  useEffect(() => {
    if (isRoomMuted && myRole !== "HOST") {
      toggle(false);
    }
  }, [isRoomMuted, toggle, myRole]);

  return (
    <div className="flex items-center">
      <div className="flex w-[50px] flex-col items-center gap-1">
        <div className="flex items-center">
          <button
            className={cn(
              "flex size-10 items-center justify-center rounded-full border",
              enabled && "border-purple bg-purple-gradient/50",
              !enabled && "border-off-white/30",
              devices.length > 0 && "rounded-r-none border-r-0 pl-1",
            )}
            {...buttonProps}
            onClick={handleMicClick}
          >
            {enabled ? (
              <MicCircleFilledIcon className={cn("size-10 text-off-white")} />
            ) : (
              <MutedMicCircleFilledIcon
                className={cn("size-6 text-gray-text")}
              />
            )}
          </button>
          {devices.length > 0 ? (
            <Select
              value={activeDeviceId}
              onValueChange={handleActiveDeviceChange}
            >
              <SelectTrigger className="h-10 w-9 justify-center rounded-full rounded-l-none border-off-white/30 p-0 pr-1 focus:ring-0 focus:ring-offset-0"></SelectTrigger>
              <SelectContent position="popper">
                {devices.map((device) => (
                  <SelectItem key={device.deviceId} value={device.deviceId}>
                    {device.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : null}
        </div>
        <span className="text-xs text-off-white">
          {enabled && "Mute"}
          {!enabled && "Unmute"}
        </span>
      </div>
    </div>
  );
};
