import { useCallback, useEffect, useRef, useState } from "react";

type SetStateAction<S> = S | ((prevState: S) => S);

interface StorageEvent extends Event {
  key: string | null;
  newValue: string | null;
}

const dispatchStorageEvent = (key: string, newValue: string | null) => {
  window.dispatchEvent(new StorageEvent("storage", { key, newValue }));
};

const setItem = <T>(key: string, value: T) => {
  const stringifiedValue = JSON.stringify(value);
  window.localStorage.setItem(key, stringifiedValue);
  dispatchStorageEvent(key, stringifiedValue);
};

const getItem = <T>(key: string): T | null => {
  const value = window.localStorage.getItem(key);
  return value ? JSON.parse(value) : null;
};

export function useLocalStorage<T>(
  key: string,
  initialValue: T,
): [T | null, (value: SetStateAction<T>) => void] {
  const [storedValue, setStoredValue] = useState<T | null>(null);
  const isClient = useRef(false);

  useEffect(() => {
    isClient.current = true;
    try {
      const item = getItem<T>(key);
      setStoredValue(item !== null ? item : initialValue);
    } catch (error) {
      console.error(error);
      setStoredValue(initialValue);
    }
  }, [key, initialValue]);

  const setValue = useCallback(
    (value: SetStateAction<T>) => {
      if (!isClient.current) return;
      try {
        const newValue =
          typeof value === "function"
            ? (value as (prevState: T) => T)(storedValue ?? initialValue)
            : value;
        setStoredValue(newValue);
        setItem(key, newValue);
      } catch (error) {
        console.error(error);
      }
    },
    [key, storedValue, initialValue],
  );

  useEffect(() => {
    if (!isClient.current) return;
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === key) {
        setStoredValue(event.newValue ? JSON.parse(event.newValue) : null);
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, [key]);

  return [storedValue, setValue];
}
