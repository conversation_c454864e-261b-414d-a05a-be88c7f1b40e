"use client";

import { useMemo } from "react";

import { SystemCurrencies, SystemCurrency } from "@/api/client/currency";
import { useTokenBalancesQuery } from "@/queries";
import { useAvaxPriceQuery } from "@/queries/currency-queries";
import { Me } from "@/types";
import { formatUnits } from "@/utils";
import { formatPrice } from "@/utils/format-token-price";

const getBalance = (currency: SystemCurrency) =>
  currency.isToken ? formatPrice(currency.balance) : Number(currency.balance);

const getUsd = (currency: SystemCurrency, avaxPrice: number) =>
  currency.isToken
    ? ((getBalance(currency) * Number(currency.systemRate)) / 1e18) * avaxPrice
    : Number(currency.systemRate) * getBalance(currency);

const roundToCents = (value: number | string): string => {
  const num = typeof value === "string" ? parseFloat(value) : value;
  return (Math.floor(num * 100) / 100).toString();
};

interface UseWalletCurrenciesOptions {
  user?: Me | null;
  currenciesData?: SystemCurrencies;
  isCurrenciesLoading: boolean;
  stake?: number;
  hideZeroBalance?: boolean | null;
}

export const useWalletCurrencies = ({
  user,
  currenciesData,
  isCurrenciesLoading,
  stake,
  hideZeroBalance,
}: UseWalletCurrenciesOptions) => {
  const { data: avaxPriceData } = useAvaxPriceQuery();

  const avaxPrice = useMemo(() => {
    const fromCurrencies = currenciesData?.currencies.find(
      (c) => c.symbol === "AVAX",
    )?.systemRate;

    if (fromCurrencies) {
      return Number(fromCurrencies);
    }

    return Number(avaxPriceData?.avax ?? 0);
  }, [currenciesData, avaxPriceData]);

  const avalancheCurrencies =
    currenciesData?.currencies.filter(
      (c) => c.blockchain === "avalanche" && !c.isToken,
    ) || [];

  const { balances, isBalancesLoading } = useTokenBalancesQuery({
    address: user?.address,
    currencies: avalancheCurrencies,
  });

  const balance = useMemo(() => {
    if (!balances || !currenciesData) return {};

    return currenciesData.currencies.reduce(
      (acc, currency) => {
        const raw = balances[currency.symbol as keyof typeof balances];
        if (raw !== undefined) {
          acc[currency.symbol] =
            formatUnits(raw.balance.toString(), currency.decimals) ?? "0.00";
        } else {
          acc[currency.symbol] = "0.00";
        }
        return acc;
      },
      {} as Record<string, string>,
    );
  }, [balances, currenciesData]);

  const sortedCurrencies = useMemo(() => {
    if (isCurrenciesLoading || !currenciesData) return [];

    const updatedCurrencies = currenciesData.currencies.map((currency) => {
      if (!currency.isToken) {
        return {
          ...currency,
          balance: (
            balance[currency.symbol as keyof typeof balance] || "0"
          ).replace(/,/g, ""),
        };
      }
      return { ...currency };
    });

    const currenciesWithUsd = updatedCurrencies.map((currency) => {
      return {
        ...currency,
        balanceUsd: roundToCents(getUsd(currency, avaxPrice)),
      };
    });

    const sorted = [...currenciesWithUsd].sort((a, b) => {
      /* ───────────────────────────────
       * 1.  ARENA always at the top
       * 2.  AVAX right after ARENA
       * 3.  Any **non-zero** balance - sort by USD (isToken doesn't matter)
       * 4.  Zero-balance **isToken=false** (supported tokens)
       * 5.  Zero-balance **isToken=true** (launch tokens)
       * ─────────────────────────────── */
      if (a.symbol === "ARENA") return -1;
      if (b.symbol === "ARENA") return 1;

      if (a.symbol === "AVAX") return -1;
      if (b.symbol === "AVAX") return 1;

      const aZero = getBalance(a) === 0;
      const bZero = getBalance(b) === 0;

      /* 3. Both have non-zero balance -> sort by USD desc */
      if (!aZero && !bZero) {
        return getUsd(b, avaxPrice) - getUsd(a, avaxPrice);
      }

      /* 4. One is zero, one is non-zero -> non-zero goes first */
      if (aZero && !bZero) return 1;
      if (!aZero && bZero) return -1;

      /* 5. Both zero -> isToken=false before isToken=true */
      if (!a.isToken && b.isToken) return -1;
      if (a.isToken && !b.isToken) return 1;

      /* Same group & balance == 0 -> keep original order */
      return 0;
    });

    if (stake) {
      const arenaToken = sorted.find((currency) => currency.symbol === "ARENA");
      if (arenaToken) {
        const stakedArena = {
          ...arenaToken,
          balance: stake.toString(),
          balanceUsd: roundToCents(stake * Number(arenaToken.systemRate)),
          isStakedArena: true,
          contractAddress: "0xEFFb809d99142cE3B51C1796C096f5b01B4AAec4",
        };
        const arenaIndex = sorted.findIndex(
          (currency) => currency.symbol === "ARENA",
        );
        if (arenaIndex !== -1) {
          return [
            ...sorted.slice(0, arenaIndex + 1),
            stakedArena,
            ...sorted.slice(arenaIndex + 1),
          ];
        }
      }
    }

    if (hideZeroBalance) {
      return sorted.filter((currency) => getBalance(currency) > 1);
    }

    return sorted;
  }, [
    isCurrenciesLoading,
    currenciesData,
    balance,
    stake,
    hideZeroBalance,
    avaxPrice,
  ]);

  return {
    balances,
    balance,
    sortedCurrencies,
    isBalancesLoading,
    avaxPrice,
  };
};
