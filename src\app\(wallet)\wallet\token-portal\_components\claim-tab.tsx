import { RefObject, useEffect, useState } from "react";
import { NextRouter } from "next/router";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import * as Collapsible from "@radix-ui/react-collapsible";
import * as Progress from "@radix-ui/react-progress";
import { useQueryClient } from "@tanstack/react-query";
import { ethers } from "ethers";
import Skeleton from "react-loading-skeleton";
import {
  Address,
  encodeFunctionData,
  formatEther,
  Hex,
  parseEventLogs,
} from "viem";

import { getClaimTxParams } from "@/api/client/token-portal";
import { ChevronDownFilled } from "@/components/icons";
import { ChevronUpFilled } from "@/components/icons/chevron-up-filled";
import { LocatedNumber } from "@/components/located-number";
import { toast } from "@/components/toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  airdropVestingAddress,
  ARENA_AIRDROP_VESTING_ABI,
} from "@/environments/stakingABI";
import { ARENA, swapTokens } from "@/environments/tokens";
import { useGetRate } from "@/queries/rate-queries";
import { useTokenPortalClaimableBalanceQuery } from "@/queries/token-portal-queries";
import { User } from "@/queries/types/postreactions";
import { formatDate } from "@/utils/format-date";

import { ArenaTokensInfoModal } from "./token-info";

interface ClaimTabProps {
  user: User;
  videoRef: RefObject<HTMLVideoElement>;
  play: boolean;
  setPlay: (play: boolean) => void;
  router: NextRouter;
}

export const ClaimTab = ({
  user,
  videoRef,
  play,
  setPlay,
  router,
}: ClaimTabProps) => {
  const { primaryWallet } = useDynamicContext();
  const queryClient = useQueryClient();

  const { data, isLoading } = useTokenPortalClaimableBalanceQuery();
  const [totalBalance, setTotalBalance] = useState(0);
  const [isBalanceLoading, setIsBalanceLoading] = useState(true);
  const [isShowSpinner, setIsShowSpinner] = useState(false);
  const [open, setOpen] = useState(false);
  const [isClaimOpen, setIsClaimOpen] = useState(false);
  const [airdropClaimed, setAirdropClaimed] = useState<number | null>(null);
  const [claimableArenaTokens, setClaimableArenaTokens] = useState<
    number | null
  >(null);

  const { data: optimalRate, isLoading: isOptimalRateLoading } = useGetRate({
    srcToken: swapTokens["ARENA"],
    destToken: swapTokens["AVAX"],
    srcAmount: ethers.parseUnits("1", swapTokens["ARENA"].decimals).toString(),
  });

  const handleClaim = async () => {
    setIsShowSpinner(true);
    try {
      if (!primaryWallet) return;
      if (!isEthereumWallet(primaryWallet)) {
        throw new Error("This wallet is not a Ethereum wallet");
      }

      const publicClient = await primaryWallet.getPublicClient();

      const { proof, allocation } = await getClaimTxParams(
        primaryWallet.address as Hex,
      );

      const claimData = encodeFunctionData({
        abi: ARENA_AIRDROP_VESTING_ABI,
        functionName: "claim",
        args: [proof as any, allocation as any],
      });

      const walletClient = await primaryWallet.getWalletClient();

      const baseFee = await publicClient.getGasPrice(); // in wei
      const estimatedMaxPriorityFeePerGas =
        await publicClient.estimateMaxPriorityFeePerGas(); // in wei

      let maxPriorityFeePerGas =
        estimatedMaxPriorityFeePerGas * 2n + 2000000000n;
      if (maxPriorityFeePerGas > 25000000000n)
        maxPriorityFeePerGas = 25000000000n;

      let maxFeePerGas = baseFee * 2n + maxPriorityFeePerGas;

      if (maxFeePerGas > 750000000000n) maxFeePerGas = 750000000000n;

      const tx = await walletClient.sendTransaction({
        account: primaryWallet.address as Hex,
        chain: walletClient.chain,
        to: airdropVestingAddress as Hex,
        data: claimData,
        maxFeePerGas,
        maxPriorityFeePerGas,
      });

      setPlay(true);
      videoRef.current?.play();

      const receipt = await publicClient.waitForTransactionReceipt({
        hash: tx,
      });

      const [log] = parseEventLogs({
        abi: ARENA_AIRDROP_VESTING_ABI,
        eventName: "AirdropClaimed",
        args: {
          claimant: primaryWallet.address as Hex,
        },
        logs: receipt.logs,
      });

      toast.green(
        `${LocatedNumber(Number(formatEther(log.args.amount)))} ARENA Claimed!`,
      );
      setIsClaimOpen(true);
    } catch (error: any) {
      if (error.code === "CALL_EXCEPTION") {
        console.error("Call exception error:", error);
      } else {
        console.error("Unexpected error:", error);
      }
      toast.red("Claim failed");
    } finally {
      setIsShowSpinner(false);
      queryClient.invalidateQueries({
        queryKey: ["token-portal", "claimable_balance"],
      });
    }
  };

  useEffect(() => {
    const fetchAirdropClaimed = async () => {
      if (!primaryWallet) {
        console.error("primaryWallet is not initialized");
        return;
      }
      if (!isEthereumWallet(primaryWallet)) {
        throw new Error("This wallet is not an Ethereum wallet");
      }

      try {
        const publicClient = await primaryWallet.getPublicClient();
        const airdropClaimedRes = await publicClient.readContract({
          address: airdropVestingAddress as Address,
          abi: ARENA_AIRDROP_VESTING_ABI,
          functionName: "airdropClaimed",
          args: [primaryWallet.address as Hex],
        });

        setAirdropClaimed(Number(formatEther(airdropClaimedRes)));
      } catch (error) {
        console.error(error);
        setAirdropClaimed(0);
      }
    };

    void fetchAirdropClaimed();
  }, [primaryWallet]);

  useEffect(() => {
    const calculateTotalBalance = () => {
      if (!data || airdropClaimed === null) return;

      const remainingArenaTokens = Number(
        formatEther(BigInt(data.totalAllocation)),
      );
      const allocation = Number(formatEther(BigInt(data.allocation)));
      const forfeited = Number(formatEther(BigInt(data.forfeitedStr)));

      setTotalBalance(remainingArenaTokens - airdropClaimed - forfeited);
      setClaimableArenaTokens(allocation - airdropClaimed);
      setIsBalanceLoading(false);
    };

    if (!isLoading && airdropClaimed !== null) {
      calculateTotalBalance();
    }
  }, [data, isLoading, airdropClaimed]);

  const dynamicAddress = (
    (user as any).dynamicAddress as string | null | undefined
  )?.toLowerCase();

  const isNotDynamic = user.address !== dynamicAddress;

  const airdropAddressMismatch =
    dynamicAddress !== data?.memberAllocationAddress;

  return (
    <>
      {!data?.remainingArenaTokens && !airdropClaimed ? (
        <div className="mt-[291px] flex flex-col items-center space-y-4 rounded-[10px] border border-dark-gray bg-gray-bg px-4 py-6 ">
          <img
            src={ARENA.icon}
            className="h-[24px] w-[24px]"
            alt="Arena logo"
          />
          <h2 className="text-[16px] font-semibold leading-none">
            Bummer, you missed the Airdrop...
          </h2>
          <p className="gap-2 px-6 text-center text-sm font-normal leading-[21px] text-light-gray-text">
            Keep your eyes open, there will be more reward programs available
            soon to earn ARENA tokens.
          </p>
        </div>
      ) : (
        <>
          <Collapsible.Root
            className="mb-6 ml-0 mt-3 flex-col items-center overflow-y-hidden rounded-[10px] border border-gray-text p-4"
            open={open}
            onOpenChange={setOpen}
          >
            <div className="flex items-center justify-between">
              <div className="flex gap-2">
                <Avatar className="mt-0 h-8 w-8 sm:mx-auto sm:ml-0">
                  <AvatarImage src={user?.twitterPicture} />
                  <AvatarFallback />
                </Avatar>
                <div className="flex flex-col justify-between gap-1 sm:text-left">
                  <div className="flex text-sm font-medium leading-4 text-off-white">
                    {user?.twitterName}
                  </div>
                  <div className="text-xs leading-3 text-off-white">
                    @{user?.twitterHandle}
                  </div>
                </div>
              </div>
              <Collapsible.Trigger asChild>
                <Button variant="ghost" className="bg-none p-1 hover:bg-none">
                  {open ? (
                    <ChevronUpFilled className="size-6 text-off-white" />
                  ) : (
                    <ChevronDownFilled className="size-6 text-off-white" />
                  )}
                </Button>
              </Collapsible.Trigger>
            </div>
            <Collapsible.Content>
              <div className="mt-4 flex flex-col gap-2 rounded-lg bg-[#2A2A2A] p-4 text-xs">
                <div className="flex flex-row justify-between">
                  <div className="font-semibold leading-4 text-off-white">
                    Lifetime Allocation
                  </div>
                  <span className="flex items-center gap-1 font-semibold leading-4 text-off-white">
                    <img
                      src={ARENA.icon}
                      className="h-[14px] w-[14px]"
                      alt="Arena logo"
                    />
                    {isLoading ? (
                      <Skeleton className="h-[14px] w-10" />
                    ) : (
                      LocatedNumber(data?.remainingArenaTokens || 0)
                    )}
                  </span>
                </div>
                <div className="flex flex-row justify-between">
                  <div className="font-semibold leading-4 text-off-white">
                    Total Claimed
                  </div>
                  <span className="flex items-center gap-1 font-semibold leading-4 text-off-white">
                    <img
                      src={ARENA.icon}
                      className="h-[14px] w-[14px]"
                      alt="Arena logo"
                    />
                    {isLoading ? (
                      <Skeleton className="h-[14px] w-10" />
                    ) : (
                      LocatedNumber(airdropClaimed || 0)
                    )}
                  </span>
                </div>
              </div>
            </Collapsible.Content>
          </Collapsible.Root>
          <div className="rounded-[10px] bg-purple-gradient p-3 pt-4 text-off-white">
            <div className="flex flex-col items-center gap-[10px]">
              <div className="text-lg font-semibold">
                Remaining Locked Balance
              </div>
              <div className="flex items-center gap-1">
                <img
                  src={ARENA.icon}
                  className="h-[38px] w-[38px]"
                  alt="Arena logo"
                />
                {!isLoading && !isBalanceLoading && (
                  <span className="text-[32px] font-medium leading-none">
                    {LocatedNumber(totalBalance)}
                  </span>
                )}
              </div>
            </div>
            <div className="m-1 mt-5 flex flex-col items-center justify-between gap-1 rounded-xl bg-white/15 px-4 py-2 text-sm">
              <div className="text-2xl font-semibold leading-[29px]">
                $
                {!isOptimalRateLoading && optimalRate && optimalRate.srcUSD
                  ? LocatedNumber(Number(optimalRate.srcUSD) * totalBalance)
                  : "N/A"}
              </div>
            </div>
          </div>
          <div className="mt-6 flex-col rounded-[10px] border border-gray-text p-4">
            <div className="flex flex-col gap-[2px] px-2">
              <div className="flex flex-row justify-between">
                <h3 className="text-sm font-semibold leading-4 text-off-white">
                  {`Season ${Number(data?.currentSeason) || 0} / 12`}
                </h3>
                <span className="flex items-center gap-1 text-xs font-semibold leading-4 text-off-white">
                  <img
                    src={ARENA.icon}
                    className="h-[14px] w-[14px]"
                    alt="Arena logo"
                  />
                  {isLoading ? (
                    <Skeleton className="h-[14px] w-10" />
                  ) : (
                    LocatedNumber(data?.seasonMaxAllocation || 0)
                  )}
                </span>
              </div>
              <p className="text-xs font-semibold leading-4 text-light-gray-text">
                {/*Claimable on Nov 10th 2024*/}
                {`Claimable on ${formatDate(data?.endsAt || 0)}`}
              </p>
            </div>
            <div className="mt-4 rounded-[10px] border border-dark-gray p-[2px] ">
              <Progress.Root
                className="relative h-[29px] overflow-hidden rounded-[7px] bg-[#1E1E1E]"
                style={{
                  // Fix overflow clipping in Safari
                  // https://gist.github.com/domske/b66047671c780a238b51c51ffde8d3a0
                  transform: "translateZ(0)",
                }}
                value={data?.monthlyProgress || 0}
              >
                <Progress.Indicator
                  className="ease-[cubic-bezier(0.65, 0, 0.35, 1)] duration-[660ms] size-full bg-purple-gradient transition-transform"
                  style={{
                    transform: `translateX(max(-${100 - (data?.monthlyProgress || 0)}%, calc(-100% + 4px)))`,
                  }}
                ></Progress.Indicator>
                <p className="absolute left-2 top-1/2 -translate-y-1/2 text-xs font-semibold leading-5">
                  Unlocked
                </p>
                <div className="absolute right-2 top-1/2 -translate-y-1/2 ">
                  <span className="flex items-center gap-1 text-xs font-semibold leading-4 text-off-white">
                    <img
                      src={ARENA.icon}
                      className="h-[14px] w-[14px]"
                      alt="Arena logo"
                    />
                    {isLoading ? (
                      <Skeleton className="h-[14px] w-10" />
                    ) : (
                      LocatedNumber(data?.seasonAllocation || 0)
                    )}
                  </span>
                </div>
              </Progress.Root>
            </div>
          </div>
          <Button
            variant="outline"
            className="mt-3 px-6 py-3 text-xs font-normal leading-none text-light-gray-text"
            onClick={handleClaim}
            disabled={
              !claimableArenaTokens || isNotDynamic || airdropAddressMismatch
            }
            loading={isShowSpinner}
          >
            {claimableArenaTokens ? (
              <span className="flex items-center gap-1 text-sm font-semibold leading-5 text-off-white">
                {isNotDynamic
                  ? "Please migrate to claim"
                  : airdropAddressMismatch
                    ? "Address mismatch"
                    : "Claim"}
                <img
                  src={ARENA.icon}
                  className="h-[14px] w-[14px]"
                  alt="Arena logo"
                />
                {isLoading ? (
                  <Skeleton className="h-[14px] w-10" />
                ) : (
                  LocatedNumber(claimableArenaTokens || 0)
                )}
              </span>
            ) : (
              <p className="text-sm font-semibold leading-5 text-off-white">
                Season in Progress
              </p>
            )}
          </Button>
        </>
      )}
      <div className="relative ">
        <div className="my-6 flex items-center justify-between gap-9 rounded-[10px] border border-[rgba(244,244,244,0.1)] bg-[linear-gradient(90deg,#D64C05_0%,#FF5626_100%),linear-gradient(0deg,rgba(0,0,0,0.1),rgba(0,0,0,0.1))] px-6 py-4 shadow-[0px_0px_10px_2px_rgba(234,84,10,0.25)]">
          <div className="flex flex-col gap-2 font-semibold text-off-white">
            <p className="text-sm font-semibold leading-4">
              Need more <br />
              ARENA tokens?
            </p>
          </div>
          <Button
            variant="outline"
            className="rounded-10 z-10 border-off-white px-4 py-2 text-xs font-semibold leading-3 text-off-white"
            onClick={() =>
              router.push("/wallet/token-portal/buy-arena?ticker=ARENA")
            }
          >
            Buy Arena
          </Button>
          <img
            src="/icons/gradient-claim-logo.svg"
            alt="logo"
            className="absolute right-0 z-0 rounded-r-[10px] mix-blend-luminosity"
          />
        </div>
      </div>
      <ArenaTokensInfoModal />
      <Dialog open={isClaimOpen} onOpenChange={setIsClaimOpen}>
        <DialogContent className="max-w-[87%] gap-6 rounded-[20px] border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)] backdrop-blur-sm sm:max-w-[524px]">
          <DialogHeader className="flex-col items-center space-y-4">
            <img
              src={ARENA.icon}
              className="h-[24px] w-[24px]"
              alt="Arena logo"
            />
            <DialogTitle className="text-center leading-[22px]">
              Congratulations!
              <br />
              You&apos;ve claimed your airdrop.
            </DialogTitle>
          </DialogHeader>
          <div className="text-sm text-light-gray-text">
            <p>
              Now put your $ARENA to work by staking and unlocking exclusive
              benefits:
            </p>
            <ul className="mt-2 pl-5">
              <li className="relative">
                <span className="absolute left-1 -ml-5">•</span>
                <span>Earn rewards with your staked tokens.</span>
              </li>
              <li className="relative">
                <span className="absolute left-1 -ml-5">•</span>
                <span>Vote on governance proposals.</span>
              </li>
              <li className="relative">
                <span className="absolute left-1 -ml-5">•</span>
                <span>
                  Increase your activity score for the next airdrop claim.
                </span>
              </li>
              <li className="relative">
                <span className="absolute left-1 -ml-5">•</span>
                <span>Enjoy in-app social perks.</span>
              </li>
              <li className="relative">
                <span className="absolute left-1 -ml-5">•</span>
                <span>
                  Become an Arena Champion and qualify for exclusive Arena
                  Launch airdrops.
                </span>
              </li>
            </ul>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              className="grow-1 flex-auto px-2"
              onClick={() => {
                setIsClaimOpen(false);
              }}
            >
              Close
            </Button>
            <Button
              className="grow-2 flex-auto px-2"
              onClick={() => router.push("/wallet/token-portal?tab=stake")}
            >
              Let&apos;s stake them
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
