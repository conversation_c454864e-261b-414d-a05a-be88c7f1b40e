"use client";

import { useMemo } from "react";
import { useRouter } from "next/navigation";

import { useQueryClient } from "@tanstack/react-query";
import { v4 } from "uuid";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { useUser } from "@/stores";

import { DirectMessagesSettings } from "./_components/message-settings";

function DMSettingsPage() {
  const { user } = useUser();
  const uuid = useMemo(v4, []);
  const queryClient = useQueryClient();
  const router = useRouter();
  const handleClick = () => {
    queryClient.invalidateQueries({
      queryKey: ["chat", "settings", { userId: user?.id || uuid }],
    });
    goBack();
  };

  const goBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push("/home");
    }
  };

  return (
    <div className="pt-pwa pb-pwa flex h-full flex-col">
      <div className="flex h-[66px] items-center border-b-2 border-dark-gray px-6 py-[18px] backdrop-blur-sm">
        <div className="flex items-center justify-start gap-2">
          <div className="size-5 cursor-pointer">
            <ArrowBackOutlineIcon
              className="size-5 text-[#F3F3F3]"
              onClick={() => handleClick()}
            />
          </div>
        </div>
        <h4
          className="absolute text-base font-semibold text-white"
          style={{ transform: "translateX(-50%)", left: "50%" }}
        >
          Messages Settings
        </h4>
      </div>
      <DirectMessagesSettings />
    </div>
  );
}

export default DMSettingsPage;
