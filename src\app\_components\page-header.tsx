"use client";

import { FC, ReactNode } from "react";
import { useRouter } from "next/navigation";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { cn } from "@/utils";

interface PageHeaderProps {
  hint?: string;
  renderProp?: () => ReactNode;
  isBorder?: boolean;
  isSticky?: boolean;
  isTransparent?: boolean;
}

export const PageHeader: FC<PageHeaderProps> = ({
  hint,
  renderProp,
  isBorder = false,
  isSticky = false,
  isTransparent = false,
}) => {
  const router = useRouter();

  const handleBack = () => {
    if (window.history.length > 1) router.back();
    else router.push("/home");
  };

  return (
    <div
      className={cn(
        "z-[100] flex items-center px-6 pb-4 pt-[calc(1rem+env(safe-area-inset-top))]",
        isSticky ? "sticky top-0" : "relative",
        isTransparent ? "bg-transparent" : "bg-dark-bk",
        isBorder ? "h-[53px] border-b border-b-dark-gray" : "h-[52px]",
      )}
    >
      <div className="relative flex flex-1 items-center justify-between">
        <button
          className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-dark-bk"
          onClick={handleBack}
        >
          <ArrowBackOutlineIcon className="size-5 text-off-white" />
        </button>
        {hint && (
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-[16px] font-semibold leading-[20px]">
            {hint}
          </div>
        )}
        {renderProp && renderProp()}
      </div>
    </div>
  );
};
