import { Dispatch, FC, SetStateAction } from "react";

import { DMRequestsIcon } from "@/components/icons/direct-message-request-icon";

interface MessageRequestsProps {
  count: number;
  setCurrentPage: Dispatch<
    SetStateAction<"messages" | "request" | "receivedRequest">
  >;
}
export const MessageRequests: FC<MessageRequestsProps> = ({
  count,
  setCurrentPage,
}) => {
  if (count < 1) return null;

  const onClick = () => {
    setCurrentPage("receivedRequest");
  };

  return (
    <button
      onClick={onClick}
      className="flex w-full cursor-pointer items-center py-4 pl-6 pr-2 hover:bg-[#212121] hover:bg-opacity-[0.2]"
    >
      <div className="mr-[10px] flex h-[42px] w-[42px] items-center justify-center">
        <DMRequestsIcon className="h-[40px] w-[40px]" />
      </div>
      <div>
        <div className="flex items-center gap-2">
          <h4 className="truncate text-sm font-semibold leading-4 text-off-white">
            Message Requests
          </h4>
        </div>
        <p className="mt-1 truncate text-sm leading-4 text-gray-text">
          {count + " pending message requests"}
        </p>
      </div>
    </button>
  );
};
