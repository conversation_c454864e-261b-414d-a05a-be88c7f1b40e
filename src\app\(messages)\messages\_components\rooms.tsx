import {
  Dispatch,
  FC,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";

import { UseMutateAsyncFunction } from "@tanstack/react-query";
import { useInView } from "react-intersection-observer";

import {
  MessageItem,
  MessageItemLoadingSkeleton,
} from "@/app/(messages)/messages/_components/message-item";
import {
  ArrowBackOutlineIcon,
  PinOutlineIcon,
  UnpinOutlineIcon,
} from "@/components/icons";
import { useSearchRoomConversationsInfiniteQuery } from "@/queries";
import { Group, PinConversationRequest } from "@/queries/types/chats";

import { TypingsType } from "./messages";
import { NoItemsFound } from "./no-rooms-found";

interface RoomsProps {
  throttledSearch: string;
  groups: Group[] | undefined;
  isLoading: boolean;
  setSeen: (groupId: string, date: number) => void;
  setSelectedGroups: Dispatch<SetStateAction<Group[]>>;
  selectedGroups: Group[];
  typings: TypingsType;
  params: {
    groupId?: string | undefined;
  };
  isFetchingNextPage: boolean;
  pinConversation: UseMutateAsyncFunction<
    any,
    Error,
    PinConversationRequest,
    any
  >;
  fetchNextRoomsPage: any;
}

export const Rooms: FC<RoomsProps> = ({
  throttledSearch,
  groups,
  isLoading,
  setSeen,
  setSelectedGroups,
  selectedGroups,
  typings,
  params,
  isFetchingNextPage,
  pinConversation,
  fetchNextRoomsPage,
}) => {
  const { ref, inView } = useInView();
  const {
    data: searchConversationsData,
    isLoading: isSearchConversationsLoading,
  } = useSearchRoomConversationsInfiniteQuery(throttledSearch);

  if (searchConversationsData && throttledSearch) {
    groups = searchConversationsData?.pages.map((page) => page.groups).flat();
  }

  let rooms: Group[] = [];
  groups?.forEach((group) => {
    if (!group.isDirect) {
      rooms.push(group);
    }
  });

  useEffect(() => {
    if (inView) {
      fetchNextRoomsPage();
    }
  }, [inView]);

  return (
    <>
      <div className="mt-[23px] px-2">
        {throttledSearch !== "" &&
          !isSearchConversationsLoading &&
          rooms?.length === 0 && <NoItemsFound tab="rooms" />}
        {!isLoading &&
          !isSearchConversationsLoading &&
          rooms &&
          rooms?.map((group, i) => {
            return (
              <MessageItem
                key={group.id}
                group={group}
                isCommunity={false}
                setSeen={() => {
                  setSeen(group.id, Date.now());
                }}
                selectGroup={() => {
                  setSelectedGroups((prev) => {
                    if (prev.length === 1) {
                      return prev;
                    }

                    if (prev.find(({ id }) => id === group.id)) {
                      return prev.filter((g) => g.id !== group.id);
                    }

                    return [...prev, group];
                  });
                }}
                isSelected={Boolean(
                  selectedGroups.find(({ id }) => id === group.id),
                )}
                typingUser={
                  typings[group.id] ? typings[group.id].user.name : undefined
                }
                isConversationOpen={group.id === params?.groupId}
                search={throttledSearch}
              />
            );
          })}
        {(isLoading ||
          isFetchingNextPage ||
          (throttledSearch !== "" && isSearchConversationsLoading)) && (
          <>
            {Array(9)
              .fill(null)
              .map((_, index) => (
                <MessageItemLoadingSkeleton key={index} />
              ))}
          </>
        )}
        {!isLoading && !isFetchingNextPage && throttledSearch === "" && (
          <div ref={ref} style={{ visibility: "hidden" }}>
            <p>.</p>
          </div>
        )}
        {selectedGroups.length > 0 && (
          <div className="top-pwa fixed inset-x-0 flex h-[54px] items-center justify-between bg-dark-bk px-6 pt-6 text-off-white">
            <div className="flex items-center gap-4">
              <button
                onClick={() => {
                  setSelectedGroups([]);
                }}
              >
                <ArrowBackOutlineIcon className="size-5" />
              </button>
              <div className="text-base leading-5">{selectedGroups.length}</div>
            </div>
            <div>
              <button
                onClick={async () => {
                  const group = selectedGroups[0];
                  setSelectedGroups([]);
                  await pinConversation({
                    groupId: group.id,
                    isPinned: !group.memberLink?.isPinned,
                  });
                }}
              >
                {selectedGroups[0].memberLink?.isPinned ? (
                  <UnpinOutlineIcon className="size-6" />
                ) : (
                  <PinOutlineIcon className="size-6" />
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
};
