"use client";

import { useMemo, useState } from "react";

import { Virtuoso } from "react-virtuoso";

import { AddOutlineIcon } from "@/components/icons/add-outline";
import { PostLoadingSkeleton } from "@/components/post-loading-skeleton";
import { ProgressBarLink } from "@/components/progress-bar";
import { Button } from "@/components/ui/button";
import { useCanPost } from "@/hooks/use-can-post";
import {
  useCommunityByIdQuery,
  useThreadsByCommunityIdInfiniteQuery,
} from "@/queries";
import { usePostStore } from "@/stores";
import { Thread } from "@/types";
import { cn } from "@/utils";

import { BannedUserModal, FirstPostModal } from "./community-phase-modals";
import { CommunityQuickBuyModal } from "./community-quickBuy-modal";
import { CommunityTimelinePost } from "./community-timeline-post";

interface CommunityThreadsProps {
  communityId: string;
}

export const CommunityThreads: React.FC<CommunityThreadsProps> = ({
  communityId,
}) => {
  const setCommunity = usePostStore((state) => state.setCommunity);
  const reset = usePostStore((state) => state.reset);
  const setReferrer = usePostStore((state) => state.setReferrer);

  const [isBuyModalOpen, setIsBuyModalOpen] = useState(false);
  const [isBanModalOpen, setIsBanModalOpen] = useState(false);

  const { data: communityData } = useCommunityByIdQuery(communityId);

  const { data, fetchNextPage, isLoading, isFetchingNextPage } =
    useThreadsByCommunityIdInfiniteQuery(communityId || "");

  const { setHasEnoughTokens, canPost, isUserBannedFromCommunity } =
    useCanPost(communityData);

  const handlePostClick = () => {
    if (canPost) return;
    if (isUserBannedFromCommunity) {
      setIsBanModalOpen(true);
    } else {
      setIsBuyModalOpen(true);
    }
  };

  const threads = useMemo(() => {
    if (!data) return [];
    return data.pages.reduce((prev, current) => {
      return [...prev, ...current.threads];
    }, [] as Thread[]);
  }, [data]);

  if (threads.length === 0 && !isLoading) {
    return (
      <>
        <FirstPostModal
          buttonRender={() => (
            <Button
              variant="default"
              className="w-full border-brand-orange"
              asChild
              onClick={handlePostClick}
            >
              {canPost ? (
                <ProgressBarLink
                  href="/compose/post"
                  onClick={() => {
                    reset();
                    setCommunity(communityData?.community ?? null);
                    setReferrer(
                      communityData?.community
                        ? `/community/${communityData.community.contractAddress}`
                        : null,
                    );
                  }}
                >
                  Make First Post
                </ProgressBarLink>
              ) : (
                <div className="flex w-full cursor-pointer items-center justify-center">
                  Make First Post
                </div>
              )}
            </Button>
          )}
        />
        {isBuyModalOpen && communityData && (
          <CommunityQuickBuyModal
            community={communityData.community}
            open={isBuyModalOpen}
            setOpen={setIsBuyModalOpen}
            setHasEnoughTokens={setHasEnoughTokens}
          />
        )}
        {isBanModalOpen && (
          <BannedUserModal open={isBanModalOpen} setOpen={setIsBanModalOpen} />
        )}
      </>
    );
  }

  return (
    <>
      <div className="relative">
        <Button
          className={cn(
            "fixed bottom-[calc(70px+env(safe-area-inset-bottom))] right-5 z-50 h-auto rounded-full bg-[linear-gradient(98deg,#FF7817_-10.93%,#D05700_-10.93%,#DD3C09_57.47%)] p-[10px] shadow-[0px_4px_20px_0px_rgba(0,0,0,0.10)] sm:hidden",
            !canPost && "opacity-50",
          )}
          asChild
          onClick={handlePostClick}
        >
          {canPost ? (
            <ProgressBarLink
              href="/compose/post"
              onClick={() => {
                reset();
                setCommunity(communityData?.community ?? null);
                setReferrer(
                  communityData?.community
                    ? `/community/${communityData.community.contractAddress}`
                    : null,
                );
              }}
            >
              <AddOutlineIcon className="h-8 w-8 text-white" />
            </ProgressBarLink>
          ) : (
            <span>
              <AddOutlineIcon className="h-8 w-8 text-white" />
            </span>
          )}
        </Button>
        <Virtuoso
          useWindowScroll
          data={threads}
          endReached={() => fetchNextPage()}
          increaseViewportBy={3000}
          overscan={2000}
          itemContent={(_, thread) => (
            <CommunityTimelinePost
              thread={thread}
              communityId={communityId || ""}
            />
          )}
          components={{
            Footer: () =>
              isLoading || isFetchingNextPage ? (
                <>
                  {Array.from({ length: 5 }).map((_, i) => (
                    <PostLoadingSkeleton key={i} />
                  ))}
                </>
              ) : null,
          }}
        />
      </div>
      {isBuyModalOpen && communityData && (
        <CommunityQuickBuyModal
          community={communityData.community}
          open={isBuyModalOpen}
          setOpen={setIsBuyModalOpen}
          setHasEnoughTokens={setHasEnoughTokens}
        />
      )}
      {isBanModalOpen && (
        <BannedUserModal open={isBanModalOpen} setOpen={setIsBanModalOpen} />
      )}
    </>
  );
};
