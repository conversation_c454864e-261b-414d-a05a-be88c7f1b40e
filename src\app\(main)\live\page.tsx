"use client";

import { useEffect, useState } from "react";

import { AddOutlineIcon } from "@/components/icons";
import { useStageEditor } from "@/components/stages/hooks/use-stage-editor";
import { LiveStagesHeader } from "@/components/stages/live-stages-header";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useFeatureFlagsStore } from "@/stores/flags";
import { useLivestreamStore } from "@/stores/livestream";
import { useStageStore } from "@/stores/stage";
import { useStageRecordingPlayerStore } from "@/stores/stage-recording-player";
import { useStreamTab } from "@/stores/stream-tab";
import { cn } from "@/utils";

import { Header } from "./_components/header";
import { LivestreamsTimeline } from "./_components/livestreams-timeline";
import { StagesTimeline } from "./_components/stages-timeline";

function StagesPage() {
  const [mounted, setMounted] = useState(false);
  const [tab, setTab] = useStreamTab();
  const showStages = useFeatureFlagsStore((state) => state.stages);
  const canCreateStages = useFeatureFlagsStore(
    (state) => state.canCreateStages,
  );

  useEffect(() => {
    setMounted(true);
  }, []);

  if (showStages && mounted) {
    return (
      <div className="h-full w-full">
        <Tabs
          value={tab}
          onValueChange={(value) => {
            if (value === "stages" || value === "livestreams") {
              setTab(value);
            }
          }}
        >
          <div className="sticky top-[calc(env(safe-area-inset-top,0px))] z-50 hidden w-full bg-[rgba(20,20,20,0.88)] backdrop-blur-[9px] sm:block">
            <TabsList className="mt-3 w-full">
              <TabsTrigger value="livestreams" className="flex-1">
                Live Streaming
              </TabsTrigger>
              <TabsTrigger value="stages" className="flex-1">
                Stages
              </TabsTrigger>
            </TabsList>
          </div>
          <Header tab={tab}>
            <LiveStagesHeader />
            <TabsList className="w-full">
              <TabsTrigger value="livestreams" className="flex-1">
                Live Streaming
              </TabsTrigger>
              <TabsTrigger value="stages" className="flex-1">
                Stages
              </TabsTrigger>
            </TabsList>
          </Header>
          <TabsContent value="stages" className="h-[calc(100%-139px)] pb-2">
            <StagesTimeline />
          </TabsContent>
          <TabsContent
            value="livestreams"
            className="h-[calc(100%-139px)] pb-2"
          >
            <LivestreamsTimeline />
          </TabsContent>
        </Tabs>

        {canCreateStages && tab === "stages" ? <ComposeStage /> : null}
      </div>
    );
  }

  return null;
}

const ComposeStage = () => {
  const isLivestreamOn = useLivestreamStore((state) => Boolean(state.token));
  const isStageOn = useStageStore((state) => Boolean(state.token));
  const isPlayerOn = useStageRecordingPlayerStore((state) =>
    Boolean(state.url),
  );

  const [_, setStageEditor] = useStageEditor();

  return (
    <Button
      className={cn(
        "fixed right-5 z-50 h-auto rounded-full bg-[linear-gradient(98deg,#FF7817_-10.93%,#D05700_-10.93%,#DD3C09_57.47%)] p-[10px] shadow-[0px_4px_20px_0px_rgba(0,0,0,0.10)] sm:hidden",
        isStageOn || isPlayerOn || isLivestreamOn
          ? "bottom-[calc(143px+env(safe-area-inset-bottom))]"
          : "bottom-[calc(70px+env(safe-area-inset-bottom))]",
      )}
      onClick={() => {
        setStageEditor({ composeStage: true });
      }}
    >
      <AddOutlineIcon className="h-8 w-8 text-white" />
    </Button>
  );
};

export default StagesPage;
