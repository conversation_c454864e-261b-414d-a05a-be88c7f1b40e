import React, { useState } from "react";

import { Toaster } from "sonner";

import { mockCurrencies } from "@/components/tipping/tip-currency-selector.stories";
import { But<PERSON> } from "@/components/ui/button";

import { TippingPartyModal } from "./tipping-party-modal";

const mockReceivers = [
  {
    traderUser: {
      id: "1",
      twitterName: "Alice",
      twitterHandle: "alice",
      twitterPicture: "https://randomuser.me/api/portraits/women/1.jpg",
      address: "0x1",
    },
    isChecked: true,
  },
  {
    traderUser: {
      id: "2",
      twitterName: "Bob",
      twitterHandle: "bob",
      twitterPicture: "https://randomuser.me/api/portraits/men/2.jpg",
      address: "0x2",
    },
    isChecked: true,
  },
];

export default {
  title: "Modals/TippingPartyModal (chats)",
  component: TippingPartyModal,
  decorators: [
    (Story: any) => (
      <>
        <Story />
        <Toaster position="top-center" />
      </>
    ),
  ],
};

export const Default = () => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Button onClick={() => setOpen(true)}>Open Tipping Party Modal</Button>
      <TippingPartyModal
        open={open}
        setOpen={setOpen}
        ownerUserId="mock-owner"
        // Mocks for tipReceivers and sortedCurrencies will be used inside the component
      />
    </>
  );
};
