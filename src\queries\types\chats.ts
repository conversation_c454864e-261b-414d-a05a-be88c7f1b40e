import { User } from "@/types";
import { CommunityExtended } from "@/types/community";

interface PaginationRequest {
  page: number;
  pageSize: number;
}

export enum GroupTypeEnum {
  SingleUser = 0,
  MultiUser = 1,
  UNRECOGNIZED = -1,
}

export enum GroupPrivacyTypeEnum {
  Public = 0,
  Closed = 1,
  Shareholders = 2,
  UNRECOGNIZED = -1,
}

export enum GroupMessageTypeEnum {
  Text = 0,
  Emoji = 1,
  Image = 2,
  Video = 3,
  Audio = 4,
  Link = 5,
}

export interface Country {
  id: string;
  countryCode3: string;
  name: string;
  iconUrl: string;
  isVisible: boolean;
  isEuMember: boolean;
  defaultCurrency: string;
  callingCode: number;
  postcodeRequired: boolean;
}

export interface CountriesResponse {
  countries: Country[];
  defaultCountry: Country | undefined;
}

export interface IpAndLanguageRequest {
  languageId: string;
  ip: string;
}

export interface Follow {
  id: number;
  createdOn: Date | undefined;
  modifiedOn: Date | undefined;
  isFollowing: boolean;
  followerProfile: CustomerProfile | undefined;
  followerUserId: string;
  followingProfile: CustomerProfile | undefined;
  followingUserId: string;
  confirmed: boolean;
}

export interface CustomerProfile {
  id: string;
  customerId: string;
  userName: string;
  displayName: string;
  description: string;
  profilePictureUrl: string;
  profilePictureId: string;
  countFollowing: number;
  countFollowers: number;
  coverPictureUrl: string;
  coverPictureId: string;
  isDeleted: boolean;
  isSuspended: boolean;
  follow: Follow | undefined;
}

export interface CustomerProfileSettings {
  id: string;
  customerId: string;
  currencyId: string;
  dateTimeFormat: string;
  dateFormat: string;
  displayLanguageId: string;
  timeZone: string;
}

export interface UserIdRequest {
  userId: string;
}

export interface UserNameRequest {
  userName: string;
}

export interface SetProfilePictureRequest {
  profilePictureUrl: string;
  profilePictureId: string;
}

export interface FollowsRequest {
  filter: UserIdRequest | undefined;
  pagination: PaginationRequest | undefined;
}

export interface FollowResponse {
  follow: Follow | undefined;
}

export interface FollowsPaginationResponse {
  follows: Follow[];
  pageSize: number;
  numberOfPages: number;
  numberOfResults: number;
}

export interface SearchStringRequest {
  searchString: string;
}

export interface CustomerProfilesResponse {
  profiles: CustomerProfile[];
}

export interface UserNameExistsResponse {
  exists: boolean;
  userName: string;
}

export interface Group {
  id: string;
  name: string;
  createdOn: number;
  lastMessageOn: number;
  lastMessage: string;
  members: GroupMember[];
  messages: GroupMessage[];
  type: GroupTypeEnum;
  isClosed: boolean;
  referenceId: string;
  privacyType: GroupPrivacyTypeEnum;
  idString: string;
  maxMembers: number;
  profilePictureId: string;
  profilePictureUrl: string;
  coverPictureId: string;
  coverPictureUrl: string;
  description: string;
  ownerUserId: string;
  ownerProfile: CustomerProfile | undefined;
  memberLink: GroupMember | undefined;
  lastName: string;
  lastUserId: string;
  isRequest?: boolean;
  isDirect?: boolean;
  isTemporary?: boolean;
  isBadge?: boolean;
  chatMateId?: string;
  chatMateName?: string;
  communityId?: string;
  community?: CommunityExtended;
}

export interface GroupMember {
  id: string;
  userId: string;
  profile: CustomerProfile | undefined;
  isAdmin: boolean;
  canWrite: boolean;
  canSeeMembers: boolean;
  isDeleted: boolean;
  deletedOn: Date | undefined;
  groupId: string;
  group: Group | undefined;
  isOwner: boolean;
  lastSeen: number;
  isPinned: boolean;
}

export interface GroupMessage {
  id: string;
  userId: string;
  messageType: GroupMessageTypeEnum;
  profile: CustomerProfile | undefined;
  createdOn: number;
  message: string;
  groupId: string;
  group: Group | undefined;
  userName: string;
  picture: string;
  reply?: any;
  attachments?: any[];
  isPinned: boolean;
}

export interface GroupsResponse {
  groups: Group[];
  numberOfPages: number;
  numberOfResults: number;
  pageSize: number;
}

export interface GroupMembersResponse {
  members: User[];
  numberOfPages: number;
  numberOfResults: number;
  pageSize: number;
}

export interface GetGroupMembersRequest {
  groupId: string;
  page: number;
  pageSize: number;
}

export interface GroupResponse {
  group: Group | undefined;
  publicKey: string;
  accessKey: { [key: string]: any } | undefined;
}

export interface HeaderGroupResponse {
  group: Group | null;
  chatMateSettings?: any;
}

export interface HeaderCommunityInterface {
  community: CommunityExtended | null;
}

export interface GroupIdRequest {
  groupId: string;
}

export interface CreateGroupRequest {
  name: string;
  isPrivate: boolean;
  description: string;
  profilePicture: any | undefined;
}

export interface IdStringRequest {
  idString: string;
}

export interface FileType {
  id: string;
  fileType: string;
  mimetype: string;
  url: string;
}

export interface SendMessageRequest {
  text: string;
  type?: GroupMessageTypeEnum;
  replyId?: string;
  groupId: string;
  files: FileType[];
}

export interface GetMessagesRequest {
  groupId: string;
  timeFrom: string;
}

export interface PinConversationRequest {
  groupId: string;
  isPinned: boolean;
}

export interface GroupMessageResponse {
  message: GroupMessage | undefined;
}

export interface GroupMessagesResponse {
  messages: GroupMessage[];
}

export interface GroupIdsResponse {
  groupIds: string[];
}

export interface GetConversationByProfileResponse {
  group: Group | undefined;
  profile: CustomerProfile | undefined;
}

export interface Reaction {
  id: string;
  createdOn: string;
  messageId: string;
  userId: string;
  reaction: string;
}

export interface MessageType {
  createdOn: number;
  id: string;
  groupId: string;
  message: string;
  messageType: GroupMessageTypeEnum;
  userName: string;
  picture: string;
  userId: string;
  replyId: string | null;
  reply: any | null;
  attachments: any[];
  isPinned: boolean;
  user: {
    threadCount: number;
    followerCount: number;
    followingsCount: number;
    twitterFollowers: number;
    id: string;
    createdOn: string;
    twitterId: string;
    twitterHandle: string;
    twitterName: string;
    twitterPicture: string;
    lastLoginTwitterPicture: string;
    bannerUrl: string;
    address: string;
    ethereumAddress: string;
    solanaAddress: string;
    prevAddress: null | string;
    addressConfirmed: boolean;
    twitterDescription: string;
    signedUp: boolean;
    subscriptionCurrency: string;
    subscriptionCurrencyAddress: null | string;
    subscriptionPrice: string;
    keyPrice: string;
    subscriptionsEnabled: boolean;
    userConfirmed: boolean;
    twitterConfirmed: boolean;
    flag: number;
    ixHandle: string;
    handle: string;
  };
  reactions: Reaction[];
}

export interface ChatSettings {
  ownerId: string;
  holders: boolean;
  followers: boolean;
}

export enum CommunityActionsEnum {
  SELL = "sell",
  BUY = "buy",
  VOTE = "vote",
  IS_MODERATOR = "is_moderator",
  REMOVE_MODERATOR = "remove_moderator",
  MUTE = "mute",
  UNMUTE = "unmute",
  DELETE_MESSAGE = "delete_message",
}

export interface ActionType {
  id: string;
  user: User;
  communityId: string;
  groupId: string;
  targetUserId: string;
  type: CommunityActionsEnum;
  quantity: number;
  createdOn: number;
}

export interface ChatMessagesResponse {
  messages: MessageType[];
  settings?: ChatSettings;
  count: number;
}

export interface ChatPinnedMessagesResponse {
  messages: MessageType[];
}

export interface ChatMessagesWithActionsResponse {
  messages: MessageType[];
  actions: ActionType[];
}

export interface CommunityMessagesOrActionsType {
  eventType: string;
  message?: MessageType;
  action?: ActionType;
  createdOn: number;
}

export interface ConvervationByProfileResponse {
  group: {
    createdOn: string;
    id: string;
    idString: string;
    description: string;
    lastMessage: string;
    lastName: string;
    lastUserId: string;
    lastMessageOn: string;
    maxMembers: number | null;
    name: string;
    privacyType: GroupPrivacyTypeEnum;
    referenceId: string | null;
    type: GroupTypeEnum;
    profilePictureUrl: string;
    profilePictureId: string | null;
    ownerUserId: string;
    memberLink: {
      id: string;
      groupId: string;
      canSeeMembers: boolean;
      canWrite: boolean;
      deletedOn: string | null;
      isAdmin: boolean;
      memberMuted: boolean;
      notificationsMuted: boolean;
      isDeleted: boolean;
      isOwner: boolean;
      lastSeen: string;
      userId: string;
      isPinned: boolean;
    };
  } | null;
}

export interface PinMessageRequest {
  groupId: string;
  messageId: string;
  isPinned: boolean;
}

export interface PostTippingPartyNotifs {
  currency: string;
  txHash: string;
  txData: string;
}

export interface PostTippingPartyNotifsBundle {
  currency: string;
  txHash: string[];
  txData: string[];
}
