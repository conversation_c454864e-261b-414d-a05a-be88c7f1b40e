"use client";

import { memo, useMemo, useState } from "react";
import Image from "next/image";

import {
  useLocalParticipant,
  useParticipantAttribute,
} from "@livekit/components-react";
import { useQuery } from "@tanstack/react-query";
import DOMPurify from "isomorphic-dompurify";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { userQueries, useSharesStatsQuery } from "@/queries";
import { useUser } from "@/stores";
import { useLivestreamStore } from "@/stores/livestream";
import { User } from "@/types";
import { checkContent, cn, formatAvax } from "@/utils";

import { ProgressBarLink } from "../progress-bar";
import { TradeTicketsModal } from "../trade-tickets-modal";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { Drawer, DrawerContent, DrawerTrigger } from "../ui/drawer";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { BlockLivestreamUserModal } from "./block-livestream-user-modal";
import { ROLES } from "./constants";
import { CustomTipModal } from "./livestream-custom-tip-modal";
import { LivestreamTipModal } from "./livestream-tip-modal";

interface LivestreamUserProfileModalProps {
  user: {
    id: string;
    name: string;
    avatar: string;
    username: string;
    role: string;
  };
  children?: React.ReactNode;
  open?: boolean;
  setOpen?: (open: boolean) => void;
}

const LivestreamUserProfileModalComponent = ({
  children,
  user,
  open: externalOpen,
  setOpen: setExternalOpen,
}: LivestreamUserProfileModalProps) => {
  const isLaptop = useMediaQuery(BREAKPOINTS.lg);
  const [internalOpen, setInternalOpen] = useState(false);
  const [customTipOpen, setCustomTipOpen] = useState(false);
  const [isBlockOpen, setIsBlockOpen] = useState(false);
  const livestreamId = useLivestreamStore((state) => state.id!);

  const [open, setOpen] = useMemo(() => {
    if (externalOpen !== undefined && setExternalOpen !== undefined) {
      return [externalOpen, setExternalOpen];
    }
    return [internalOpen, setInternalOpen];
  }, [externalOpen, setExternalOpen, internalOpen, setInternalOpen]);

  const memoizedUser = useMemo(() => user, [user]);
  const { data: userData, isLoading: isUserDataLoading } = useQuery({
    ...userQueries.byHandle(memoizedUser.username),
    enabled: !!memoizedUser.username && open,
  });
  const { data: statsData, isLoading: isStatsDataLoading } =
    useSharesStatsQuery({
      userId: open ? memoizedUser?.id : undefined,
    });

  const profileDescription = useMemo(() => {
    if (!userData) return "";

    const [content] = checkContent({
      content: userData?.user.twitterDescription ?? "",
      truncate: false,
    });

    return DOMPurify.sanitize(content);
  }, [userData]);

  const ticketPrice = useMemo(() => {
    if (isUserDataLoading || isStatsDataLoading) return "";

    const formattedEther = formatAvax(
      statsData?.stats?.keyPrice ?? userData?.user?.keyPrice ?? "",
    );

    if (parseFloat(formattedEther) >= 1) {
      return parseFloat(formattedEther).toFixed(2);
    }

    return formattedEther;
  }, [
    statsData?.stats?.keyPrice,
    userData?.user?.keyPrice,
    isUserDataLoading,
    isStatsDataLoading,
  ]);

  const handleBlockUser = async () => {
    setOpen(false);
    setIsBlockOpen(true);
  };

  if (isLaptop) {
    return (
      <>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>{children}</PopoverTrigger>
          <PopoverContent className="w-[350px] overflow-hidden p-0">
            {userData && userData.user && (
              <LivestreamUserProfileModalContent
                user={userData.user}
                isUserDataLoading={isUserDataLoading}
                profileDescription={profileDescription}
                ticketPrice={ticketPrice}
                setOpen={setOpen}
                setCustomTipOpen={setCustomTipOpen}
                customTipOpen={customTipOpen}
                role={memoizedUser.role}
                handleBlockUser={handleBlockUser}
              />
            )}
          </PopoverContent>
        </Popover>
        <BlockLivestreamUserModal
          open={isBlockOpen}
          setOpen={setIsBlockOpen}
          user={memoizedUser}
          livestreamId={livestreamId}
        />
      </>
    );
  }

  return (
    <>
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerTrigger asChild>{children}</DrawerTrigger>
        <DrawerContent className="pb-pwa overflow-hidden rounded-t-xl p-0">
          {userData && userData.user && (
            <LivestreamUserProfileModalContent
              user={userData.user}
              isUserDataLoading={isUserDataLoading}
              profileDescription={profileDescription}
              ticketPrice={ticketPrice}
              setOpen={setOpen}
              setCustomTipOpen={setCustomTipOpen}
              customTipOpen={customTipOpen}
              role={memoizedUser.role}
              handleBlockUser={handleBlockUser}
            />
          )}
        </DrawerContent>
      </Drawer>
      <BlockLivestreamUserModal
        open={isBlockOpen}
        setOpen={setIsBlockOpen}
        user={memoizedUser}
        livestreamId={livestreamId}
      />
    </>
  );
};

export const LivestreamUserProfileModal = memo(
  LivestreamUserProfileModalComponent,
);

const LivestreamUserProfileModalContent = ({
  user,
  profileDescription,
  ticketPrice,
  setOpen,
  setCustomTipOpen,
  customTipOpen,
  isUserDataLoading,
  role,
  handleBlockUser,
}: {
  user: User;
  profileDescription: string;
  ticketPrice: string;
  setOpen: (open: boolean) => void;
  setCustomTipOpen: (open: boolean) => void;
  customTipOpen: boolean;
  isUserDataLoading: boolean;
  role: string;
  handleBlockUser: () => void;
}) => {
  const local = useLocalParticipant();
  const myRole = useParticipantAttribute("role", {
    participant: local.localParticipant,
  });
  const { user: me } = useUser();

  const onProfileClick = () => {
    setOpen(false);
  };

  return (
    <>
      <div
        className="aspect-[4/1] w-full bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${user.bannerUrl})`,
        }}
      ></div>
      <div className={cn("px-5 ", me?.id === user.id ? "pb-2" : "pb-8")}>
        <div className="-mt-5 flex items-end gap-5">
          <ProgressBarLink
            href={`/${user?.twitterHandle}`}
            onClick={onProfileClick}
          >
            <Avatar className="size-[40px] border border-off-white">
              <AvatarImage src={user.twitterPicture} />
              <AvatarFallback />
            </Avatar>
          </ProgressBarLink>
        </div>
        <div className="mt-2 flex flex-col gap-0.5">
          <ProgressBarLink
            href={`/${user?.twitterHandle}`}
            onClick={onProfileClick}
            className="flex items-center gap-1.5 text-base font-medium leading-5 text-off-white"
          >
            {user.twitterName}
          </ProgressBarLink>
          <ProgressBarLink
            href={`/${user?.twitterHandle}`}
            onClick={onProfileClick}
            className="text-xs text-gray-text"
          >
            @{user.twitterHandle}
          </ProgressBarLink>
        </div>
        <div
          className="post-content mt-3 text-sm text-off-white"
          dangerouslySetInnerHTML={{
            __html: profileDescription,
          }}
        />
        {me?.id !== user.id && (
          <>
            <div className="mt-4 flex items-center gap-2">
              {(myRole === ROLES.HOST ||
                myRole === ROLES.MODERATOR ||
                me?.isMod) &&
              role === ROLES.VIEWER ? (
                <Button
                  variant="outline"
                  className="flex-1 gap-1 py-2"
                  onClick={handleBlockUser}
                >
                  Remove Viewer
                </Button>
              ) : (
                <TradeTicketsModal userHandle={user.twitterHandle}>
                  <Button className="flex-1 gap-1 py-2">
                    Buy{" "}
                    <Image
                      src="/assets/coins/avax.png"
                      className="ml-0.5 size-3 rounded-full brightness-110 grayscale"
                      alt={`AVAX logo`}
                      width={12}
                      height={12}
                    />
                    <span className="text-xs font-medium leading-5 text-off-white">
                      {ticketPrice}
                    </span>
                  </Button>
                </TradeTicketsModal>
              )}
              <LivestreamTipModal
                user={user}
                setCustomTipOpen={setCustomTipOpen}
              >
                <Button
                  variant="outline"
                  className="flex-1 gap-1 border-brand-orange py-2"
                >
                  Tip
                </Button>
              </LivestreamTipModal>
            </div>
          </>
        )}
      </div>
      <CustomTipModal
        userToSend={user}
        open={customTipOpen}
        setOpen={setCustomTipOpen}
      />
    </>
  );
};
