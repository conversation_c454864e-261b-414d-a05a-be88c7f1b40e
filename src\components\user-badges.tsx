import { Badge } from "@/queries/types/top-users-response";
import { cn } from "@/utils";

interface UserBadgesProps {
  badges: Badge[];
  className?: string;
}

const orders = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 17];

export const UserBadges = ({ badges, className }: UserBadgesProps) => {
  const sortedBadges = badges.sort((a, b) => {
    const indexA = orders.indexOf(a.order);
    const indexB = orders.indexOf(b.order);
    return indexA - indexB;
  });
  return (
    <div className="flex items-center gap-1.5">
      {sortedBadges.map((badge, index) => (
        <img
          src={`/assets/badges/badge-type-${badge.badgeType}.png`}
          key={`${badge.badgeType}-${index}`}
          alt="Badge"
          className={cn("h-[14px] w-auto", className)}
        />
      ))}
    </div>
  );
};
