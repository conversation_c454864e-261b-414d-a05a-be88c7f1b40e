"use client";

import {
  memo,
  MutableRefObject,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useRouter } from "next/navigation";

import {
  isTrackReference,
  TrackReference,
  useRoomContext,
  useStartAudio,
  useTracks,
  VideoTrack,
} from "@livekit/components-react";
import { RoomEvent, Track } from "livekit-client";
import { useHotkeys } from "react-hotkeys-hook";

import { useLivestreamStore } from "@/stores/livestream";
import { cn } from "@/utils/cn";
import { IS_IPHONE } from "@/utils/window-environment";

import { CloseOutlineIcon, ExternalLinkOutlineIcon } from "../icons";

interface VideoPlayerProps {
  type?: "full" | "mini";
  device?: "desktop" | "mobile";
}

export const VideoPlayerIOS = memo(
  ({ type = "full", device }: VideoPlayerProps) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const router = useRouter();
    const twitterHandle = useLivestreamStore((state) => state.twitterHandle!);
    const actions = useLivestreamStore((state) => state.actions);

    useHotkeys(
      ["f", "F"],
      () => {
        if (isFullscreen) {
          exitFullscreen();
        } else {
          enterFullscreen();
        }
      },
      {
        enabled: type === "full",
      },
    );

    const screenShareTracks = useTracks(
      [{ source: Track.Source.ScreenShare, withPlaceholder: false }],
      {
        onlySubscribed: false,
        updateOnlyOn: [
          RoomEvent.TrackSubscribed,
          RoomEvent.TrackUnsubscribed,
          RoomEvent.TrackMuted,
          RoomEvent.TrackUnmuted,
          RoomEvent.TrackPublished,
          RoomEvent.TrackUnpublished,
        ],
      },
    )
      .filter(isTrackReference)
      .filter((track) => !track.publication.isMuted);

    const cameraTracks = useTracks(
      [{ source: Track.Source.Camera, withPlaceholder: false }],
      {
        onlySubscribed: false,
        updateOnlyOn: [
          RoomEvent.TrackSubscribed,
          RoomEvent.TrackUnsubscribed,
          RoomEvent.TrackMuted,
          RoomEvent.TrackUnmuted,
          RoomEvent.TrackPublished,
          RoomEvent.TrackUnpublished,
        ],
      },
    )
      .filter(isTrackReference)
      .filter((track) => !track.publication.isMuted);

    const micTracks = useTracks(
      [
        { source: Track.Source.Microphone, withPlaceholder: false },
        { source: Track.Source.ScreenShareAudio, withPlaceholder: false },
      ],
      {
        onlySubscribed: false,
      },
    ).filter(isTrackReference);

    const enterFullscreen = async () => {
      try {
        if (containerRef.current) {
          await containerRef.current.requestFullscreen();
        }
      } catch (error) {
        console.error("Failed to enter fullscreen:", error);
      }
    };

    const exitFullscreen = async () => {
      try {
        if (document.fullscreenElement) {
          await document.exitFullscreen();
        }
      } catch (error) {
        console.error("Failed to exit fullscreen:", error);
      }
    };

    useEffect(() => {
      const handleFullscreenChange = () => {
        setIsFullscreen(!!document.fullscreenElement);
      };

      document.addEventListener("fullscreenchange", handleFullscreenChange);
      return () =>
        document.removeEventListener(
          "fullscreenchange",
          handleFullscreenChange,
        );
    }, []);

    return (
      <div
        className={cn(
          "relative flex-shrink-0 overflow-hidden lg:rounded-xl",
          screenShareTracks.length === 0 &&
            cameraTracks.length === 0 &&
            !isFullscreen &&
            "border-off-white/30 lg:border",
          isFullscreen ? "h-full" : "max-h-[700px]",
          type === "mini" && device === "mobile" && "h-[70px]",
        )}
        ref={containerRef}
      >
        {screenShareTracks.length === 0 && cameraTracks.length === 0 ? (
          <div
            className={cn(
              "flex w-full items-center justify-center bg-dark-bk",
              isFullscreen ? "h-full" : "aspect-video lg:rounded-xl",
              type === "mini" && device === "mobile" && "max-h-[70px]",
            )}
          >
            <span
              className={cn(
                "pointer-events-none select-none text-center text-sm text-off-white",
                type === "mini" && device === "mobile" && "text-[8px]",
              )}
            >
              Waiting for screen share or camera
            </span>
          </div>
        ) : (
          <PlayerSwitcher
            screenShareTracks={screenShareTracks}
            cameraTracks={cameraTracks}
            micTracks={micTracks}
            type={type}
          />
        )}

        {type === "mini" && device === "desktop" ? (
          <>
            <button
              className="absolute left-2 top-2 rounded-full bg-dark-bk/75 p-1 backdrop-blur-sm"
              onClick={() => {
                router.push(`/live/${twitterHandle}`);
              }}
            >
              <ExternalLinkOutlineIcon
                className="size-5 rotate-[270deg] text-off-white"
                strokeWidth={1.5}
              />
            </button>
            <button
              className="absolute right-2 top-2 rounded-full bg-dark-bk/75 p-1 backdrop-blur-sm"
              onClick={() => {
                actions.reset();
              }}
            >
              <CloseOutlineIcon className="size-5 text-off-white" />
            </button>
          </>
        ) : null}
      </div>
    );
  },
);

VideoPlayerIOS.displayName = "VideoPlayerIOS";

const PlayerSwitcher = memo(
  ({
    screenShareTracks,
    cameraTracks,
    micTracks,
    type,
  }: {
    screenShareTracks: TrackReference[];
    cameraTracks: TrackReference[];
    micTracks: TrackReference[];
    type?: "full" | "mini";
  }) => {
    const selectedScreenShare = useMemo(
      () => screenShareTracks[0],
      [screenShareTracks],
    );
    const selectedCamera = useMemo(() => cameraTracks[0], [cameraTracks]);

    return (
      <CanvasPlayer
        screenShareTrack={selectedScreenShare}
        cameraTrack={selectedCamera}
        micTracks={micTracks}
        type={type}
      />
    );
  },
  (prevProps, nextProps) => {
    const screenShareEqual =
      prevProps.screenShareTracks.length ===
        nextProps.screenShareTracks.length &&
      prevProps.screenShareTracks[0]?.publication.trackSid ===
        nextProps.screenShareTracks[0]?.publication.trackSid;

    const cameraEqual =
      prevProps.cameraTracks.length === nextProps.cameraTracks.length &&
      prevProps.cameraTracks[0]?.publication.trackSid ===
        nextProps.cameraTracks[0]?.publication.trackSid;

    const micEqual =
      prevProps.micTracks.length === nextProps.micTracks.length &&
      prevProps.micTracks.every(
        (track, index) =>
          track.publication.trackSid ===
          nextProps.micTracks[index]?.publication.trackSid,
      );

    return screenShareEqual && cameraEqual && micEqual;
  },
);

PlayerSwitcher.displayName = "PlayerSwitcher";

const CanvasPlayer = memo(
  ({
    screenShareTrack,
    cameraTrack,
    micTracks,
    type,
  }: {
    screenShareTrack?: TrackReference;
    cameraTrack?: TrackReference;
    micTracks: TrackReference[];
    type?: "full" | "mini";
  }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const screenVideoRef = useRef<HTMLVideoElement>(null);
    const cameraVideoRef = useRef<HTMLVideoElement>(null);
    const [stream, setStream] = useState<MediaStream | null>(null);
    const animationFrameRef = useRef<number>();
    const [isScreenVideoReady, setIsScreenVideoReady] = useState(false);
    const [isCameraVideoReady, setIsCameraVideoReady] = useState(false);
    const [audioTracks, setAudioTracks] = useState<MediaStreamTrack[]>([]);
    const isMuted = useLivestreamStore((state) => state.isMuted);
    const isMutedRef = useRef(isMuted);
    const setIsMuted = useLivestreamStore((state) => state.actions.setIsMuted);

    const room = useRoomContext();
    const { canPlayAudio } = useStartAudio({
      room,
      props: {},
    });

    useEffect(() => {
      if (!screenShareTrack) {
        setIsScreenVideoReady(false);
      }
      if (!cameraTrack) {
        setIsCameraVideoReady(false);
      }
    }, [screenShareTrack, cameraTrack]);

    useEffect(() => {
      const tracks = micTracks
        .map((track) => track.publication.track?.mediaStreamTrack)
        .filter(Boolean) as MediaStreamTrack[];

      setAudioTracks(tracks);
    }, [micTracks]);

    useEffect(() => {
      if (!canvasRef.current) return;

      const canvas = canvasRef.current;
      const ctx = canvas.getContext("2d", { alpha: false });
      if (!ctx) return;

      canvas.width = 1920;
      canvas.height = 1080;

      if (!stream) {
        const newStream = canvas.captureStream(30);

        audioTracks.forEach((track) => {
          try {
            newStream.addTrack(track);
          } catch (e) {
            console.error("Failed to add audio track to stream:", e);
          }
        });

        setStream(newStream);
      } else if (audioTracks.length > 0) {
        const currentAudioTracks = stream.getAudioTracks();

        currentAudioTracks.forEach((track) => {
          stream.removeTrack(track);
        });

        audioTracks.forEach((track) => {
          try {
            stream.addTrack(track);
          } catch (e) {
            console.error("Failed to add audio track to stream:", e);
          }
        });
      }

      const fps = 30;
      const interval = 1000 / fps;
      let then = Date.now();

      const drawCanvas = () => {
        const now = Date.now();
        const delta = now - then;

        if (delta > interval) {
          then = now - (delta % interval);

          if (!ctx) return;

          ctx.fillStyle = "#000000";
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          try {
            let hasDrawnMainVideo = false;

            if (
              cameraTrack &&
              cameraVideoRef.current &&
              isCameraVideoReady &&
              cameraVideoRef.current.videoWidth > 0 &&
              !screenShareTrack
            ) {
              const cameraWidth = cameraVideoRef.current.videoWidth;
              const cameraHeight = cameraVideoRef.current.videoHeight;
              canvas.width = 1920;
              canvas.height = Math.round((1920 * cameraHeight) / cameraWidth);

              ctx.drawImage(
                cameraVideoRef.current,
                0,
                0,
                canvas.width,
                canvas.height,
              );
              hasDrawnMainVideo = true;
            } else if (
              screenShareTrack &&
              screenVideoRef.current &&
              isScreenVideoReady &&
              screenVideoRef.current.videoWidth > 0
            ) {
              const screenWidth = screenVideoRef.current.videoWidth;
              const screenHeight = screenVideoRef.current.videoHeight;

              canvas.width = 1920;
              canvas.height = Math.round((1920 * screenHeight) / screenWidth);

              ctx.drawImage(
                screenVideoRef.current,
                0,
                0,
                canvas.width,
                canvas.height,
              );
              hasDrawnMainVideo = true;

              if (
                cameraTrack &&
                cameraVideoRef.current &&
                isCameraVideoReady &&
                cameraVideoRef.current.videoWidth > 0
              ) {
                const cameraWidth = cameraVideoRef.current.videoWidth;
                const cameraHeight = cameraVideoRef.current.videoHeight;

                const maxPipWidth = Math.min(canvas.width * 0.2, 400);
                const pipWidth = maxPipWidth;
                const pipHeight = Math.round(
                  (pipWidth * cameraHeight) / cameraWidth,
                );

                ctx.save();
                ctx.beginPath();
                ctx.beginPath();
                ctx.roundRect(
                  canvas.width - pipWidth - 20,
                  canvas.height - pipHeight - 20,
                  pipWidth,
                  pipHeight,
                  10,
                );
                ctx.clip();

                ctx.drawImage(
                  cameraVideoRef.current,
                  canvas.width - pipWidth - 20,
                  canvas.height - pipHeight - 20,
                  pipWidth,
                  pipHeight,
                );

                ctx.restore();
              }
            }
          } catch (error) {
            console.error("Canvas drawing error:", error);
          }
        }

        animationFrameRef.current = requestAnimationFrame(drawCanvas);
      };

      drawCanvas();

      return () => {
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
      };
    }, [
      screenShareTrack,
      cameraTrack,
      isScreenVideoReady,
      isCameraVideoReady,
      stream,
      audioTracks,
    ]);

    useEffect(() => {
      return () => {
        if (stream) {
          const currentAudioTracks = stream.getAudioTracks();

          currentAudioTracks.forEach((track) => {
            stream.removeTrack(track);
          });

          setIsMuted(isMutedRef.current);
        }
      };
    }, [stream]);

    return (
      <>
        <canvas ref={canvasRef} className="hidden" />
        {screenShareTrack && (
          <VideoTrack
            trackRef={screenShareTrack}
            className="hidden"
            ref={(el: HTMLVideoElement | null) => {
              if (el) {
                (
                  screenVideoRef as MutableRefObject<HTMLVideoElement | null>
                ).current = el;
                el.addEventListener("loadedmetadata", () => {
                  setIsScreenVideoReady(true);
                });
                el.play().catch(console.error);
              }
            }}
          />
        )}
        {cameraTrack && (
          <VideoTrack
            trackRef={cameraTrack}
            className="hidden"
            ref={(el: HTMLVideoElement | null) => {
              if (el) {
                (
                  cameraVideoRef as MutableRefObject<HTMLVideoElement | null>
                ).current = el;
                el.addEventListener("loadedmetadata", () => {
                  setIsCameraVideoReady(true);
                });
                el.play().catch(console.error);
              }
            }}
          />
        )}
        <video
          className={cn("h-full object-contain", type !== "mini" && "w-full")}
          autoPlay
          playsInline
          muted={isMutedRef.current || !canPlayAudio}
          ref={(el) => {
            if (el && stream) {
              el.srcObject = stream;
              el.play().catch(console.error);
            }
          }}
          controls={IS_IPHONE && type !== "mini"}
          onVolumeChange={(e) => {
            const video = e.target as HTMLVideoElement;
            isMutedRef.current = video.muted;
          }}
        />
      </>
    );
  },
);

CanvasPlayer.displayName = "CanvasPlayer";
