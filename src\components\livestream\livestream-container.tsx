"use client";

import { startTransition, useEffect, useRef, useState } from "react";
import { useRouter, useSelectedLayoutSegments } from "next/navigation";

import { LiveKitRoom } from "@livekit/components-react";

import { env } from "@/env";
import { useLivestreamStore } from "@/stores/livestream";

import { DataChannelsProvider } from "./stores/data-channels-context";

export function LivestreamContainer({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isDisconnected, setIsDisconnected] = useState(false);
  const router = useRouter();
  const segments = useSelectedLayoutSegments();
  const token = useLivestreamStore((state) => state.token);
  const isNewStream = useLivestreamStore((state) => state.isNewStream);
  const actions = useLivestreamStore((state) => state.actions);
  const oldSegments = useRef(segments);

  useEffect(() => {
    if (oldSegments.current !== segments) {
      if (isDisconnected) {
        actions.reset();
      }
      oldSegments.current = segments;
      setIsDisconnected(false);
    }
  }, [segments, isDisconnected, actions]);

  if (token) {
    return (
      <LiveKitRoom
        audio={false}
        token={token}
        serverUrl={env.NEXT_PUBLIC_LIVEKIT_URL}
        onDisconnected={() => {
          if (isNewStream) {
            return;
          }
          startTransition(() => {
            if (segments.length === 3 && segments[0] === "(livestream)") {
              router.push("/live?streamTab=livestreams");
            }
            setIsDisconnected(true);
          });
        }}
      >
        <DataChannelsProvider>{children}</DataChannelsProvider>
      </LiveKitRoom>
    );
  }

  return children;
}
