"use client";

import { createContext, useContext, useState } from "react";

import * as R from "remeda";
import { createStore, StoreApi, useStore } from "zustand";

import { Role } from "@/components/livestream/constants";
import { LivestreamContainer } from "@/components/livestream/livestream-container";

export interface LivestreamUser {
  emotes: string[];
  tips: {
    currency: string;
    amount: number;
    from: {
      name: string;
      avatar: string;
      username: string;
    };
    id: string;
  }[];
}

interface Store {
  id: string | null;
  twitterHandle: string | null;
  token: string | null;
  isFullScreen: boolean;
  isGuestsModalOpen: boolean;
  users: {
    [id: string]: LivestreamUser | undefined;
  };
  reactions: {
    value: string;
    id: number;
  }[];
  chat: {
    isOpen: boolean;
    isFullScreen: boolean;
    isOpenInNewWindow: boolean;
  };
  lastConfettiId: {
    current: string | null;
  };
  canShowCurrentlyListening: boolean;
  startedTime: number;
  speakingDuration: number;
  myRole: Role | null;
  supportersType: "LIVESTREAM" | "CREATOR";
  didFollowHost: boolean;
  isMuted: boolean;
  volume: number;
  isNewStream: boolean;
}

interface Actions {
  actions: {
    reset: () => void;
    setId: (id: string | null) => void;
    setTwitterHandle: (twitterHandle: string | null) => void;
    setToken: (token: string | null) => void;
    setFullScreen: (isFullScreen: boolean) => void;
    setIsGuestsModalOpen: (isGuestsModalOpen: boolean) => void;
    addUser: (id: string, user: LivestreamUser) => void;
    getUser: (id: string) => LivestreamUser | undefined;
    removeUser: (id: string) => void;
    setEmotes: (userId: string, emotes: string[]) => void;
    addEmote: (userId: string, emote: string) => void;
    setTips: (
      userId: string,
      callback: (tips: LivestreamUser["tips"]) => LivestreamUser["tips"],
    ) => void;
    addTip: (userId: string, tip: LivestreamUser["tips"][0]) => void;
    addReaction: (reaction: { value: string; id: number }) => void;
    removeReaction: (reactionId: number) => void;
    setChatOpen: (isOpen: boolean) => void;
    toggleChatOpen: () => void;
    setChatFullScreen: (isFullScreen: boolean) => void;
    setCanShowCurrentlyListening: (canShowCurrentlyListening: boolean) => void;
    setSpeakingDuration: (duration: number) => void;
    addSpeakingDuration: (duration: number) => void;
    getSpeakingDuration: () => number;
    getSpeakingDurationAndReset: () => number;
    setStartedTime: (startedTime: number) => void;
    setMyRole: (role: Role) => void;
    setSupportersType: (supportersType: "LIVESTREAM" | "CREATOR") => void;
    setChatOpenInNewWindow: (isOpenInNewWindow: boolean) => void;
    setDidFollowHost: (didFollowHost: boolean) => void;
    setIsMuted: (isMuted: boolean) => void;
    setVolume: (volume: number) => void;
    setIsNewStream: (isNewStream: boolean) => void;
  };
}

type LivestreamState = Store & Actions;

const LivestreamStoreContext = createContext<StoreApi<LivestreamState> | null>(
  null,
);

export const LivestreamStoreProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [store] = useState(() =>
    createStore<LivestreamState>((set, get) => ({
      id: null,
      twitterHandle: null,
      token: null,
      isFullScreen: true,
      isGuestsModalOpen: false,
      isRequestedToSpeak: false,
      users: {},
      reactions: [],
      chat: {
        isOpen: true,
        isFullScreen: false,
        isOpenInNewWindow: false,
      },
      lastConfettiId: { current: null },
      canShowCurrentlyListening: true,
      speakingDuration: 0,
      startedTime: 0,
      myRole: null,
      supportersType: "LIVESTREAM",
      didFollowHost: false,
      isMuted: false,
      volume: 100,
      isNewStream: false,
      actions: {
        reset: () => {
          set({
            token: null,
            users: {},
            reactions: [],
            id: null,
            isFullScreen: true,
            isGuestsModalOpen: false,
            canShowCurrentlyListening: false,
            speakingDuration: 0,
            lastConfettiId: { current: null },
            chat: {
              isOpen: false,
              isFullScreen: false,
              isOpenInNewWindow: false,
            },
            volume: 100,
            isNewStream: false,
          });
        },
        setTwitterHandle: (twitterHandle) => {
          set({ twitterHandle });
        },
        setId: (id) => {
          set({ id });
        },
        setToken: (token) => {
          set({ token });
        },
        setFullScreen: (isFullScreen) => {
          set({ isFullScreen });
        },
        setIsGuestsModalOpen: (isGuestsModalOpen) => {
          set({
            isGuestsModalOpen,
          });
        },
        addUser: (id, user) => {
          set({
            users: {
              [id]: user,
            },
          });
        },
        getUser: (id) => {
          return get().users[id];
        },
        removeUser: (id) => {
          const users = get().users;
          delete users[id];
          set({ users });
        },
        setEmotes: (id, emotes) => {
          const users = get().users;
          let user = users[id];
          if (!user) {
            user = {
              emotes: [],
              tips: [],
            };
            users[id] = user;
          }
          user.emotes = emotes;
          set({ users });
        },
        addEmote: (id, emote) => {
          const users = get().users;
          let user = users[id];
          if (!user) {
            user = {
              emotes: [],
              tips: [],
            };
            users[id] = user;
          }
          user.emotes = [...user.emotes, emote];
          set({ users });
        },
        setTips: (id, callback) => {
          const users = get().users;
          let user = R.clone(users[id]);
          if (!user) {
            user = {
              emotes: [],
              tips: [],
            };
          }

          user.tips = callback(user.tips);
          users[id] = user;

          set({ users });
        },
        addTip: (id, tip) => {
          const users = get().users;
          let user = R.clone(users[id]);
          if (!user) {
            user = {
              emotes: [],
              tips: [],
            };
          }

          user.tips = [...user.tips, tip];
          users[id] = user;

          set({
            users,
          });
        },
        addReaction: (reaction) => {
          set({ reactions: [...get().reactions, reaction] });
        },
        removeReaction: (reactionId) => {
          set({
            reactions: get().reactions.filter(
              (reaction) => reaction.id !== reactionId,
            ),
          });
        },
        setChatOpen: (isOpen) => {
          const chat = R.clone(get().chat);
          chat.isOpen = isOpen;

          set({ chat });
        },
        toggleChatOpen: () => {
          const chat = R.clone(get().chat);
          chat.isOpen = !chat.isOpen;
          set({ chat });
        },
        setChatFullScreen: (isFullScreen) => {
          const chat = R.clone(get().chat);
          chat.isFullScreen = isFullScreen;
          set({ chat });
        },
        setCanShowCurrentlyListening: (canShowCurrentlyListening) => {
          set({ canShowCurrentlyListening });
        },
        setSpeakingDuration: (duration) => {
          set({ speakingDuration: duration });
        },
        addSpeakingDuration: (duration) => {
          set({ speakingDuration: get().speakingDuration + duration });
        },
        getSpeakingDuration: () => get().speakingDuration,
        getSpeakingDurationAndReset: () => {
          const store = get();
          let rawDuration = store.speakingDuration;
          if (store.startedTime > 0) {
            rawDuration = rawDuration + (Date.now() - store.startedTime);
          }
          set({ speakingDuration: 0, startedTime: 0 });
          const duration = Math.round(rawDuration / 1000);
          return duration;
        },
        setStartedTime: (startedTime) => {
          set({ startedTime });
        },
        setMyRole: (role) => {
          set({ myRole: role });
        },
        setSupportersType: (supportersType) => {
          set({ supportersType });
        },
        setChatOpenInNewWindow: (isOpenInNewWindow) => {
          const chat = R.clone(get().chat);
          chat.isOpenInNewWindow = isOpenInNewWindow;
          set({ chat });
        },
        setDidFollowHost: (didFollowHost) => {
          set({ didFollowHost });
        },
        setIsMuted: (isMuted) => {
          set({ isMuted });
        },
        setVolume: (volume) => {
          set({ volume });
        },
        setIsNewStream: (isNewStream) => {
          set({ isNewStream });
        },
      },
    })),
  );

  return (
    <LivestreamStoreContext.Provider value={store}>
      <LivestreamContainer>{children}</LivestreamContainer>
    </LivestreamStoreContext.Provider>
  );
};

export function useLivestreamStore(): LivestreamState;
export function useLivestreamStore<T>(
  selector: (state: LivestreamState) => T,
): T;
export function useLivestreamStore<T>(
  selector?: (state: LivestreamState) => T,
) {
  const store = useContext(LivestreamStoreContext);
  if (!store) {
    throw new Error("Missing LivestreamStoreProvider");
  }
  return useStore(store, selector!);
}
