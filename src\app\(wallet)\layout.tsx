import { SideSearch } from "@/app/_components/side-search";
import { SideNav } from "@/app/(main)/_components/side-nav";
import { WalletBottomNav } from "@/app/(wallet)/wallet/_components/wallet-bottom-nav";
import { MinifiedStageTablet } from "@/components/stages/stage-mobile";

interface WalletLayoutProps {
  children: React.ReactNode;
}

function WalletLayout({ children }: WalletLayoutProps) {
  return (
    <div className="mx-auto flex min-h-[100svh] max-w-[1350px] flex-col flex-nowrap items-stretch justify-center sm:flex-row">
      <header className="relative z-[3] hidden sm:flex sm:flex-shrink-0 sm:flex-grow sm:flex-col sm:items-end">
        {/* eslint-disable-next-line react/jsx-no-undef */}
        <SideNav />
      </header>
      <div className="flex flex-shrink flex-grow-[2] justify-start gap-4 xl:gap-7">
        <div className="relative w-full sm:w-[610px] sm:border-x sm:border-dark-gray">
          {children}
          <MinifiedStageTablet />
        </div>
        <div className="hidden lg:flex lg:w-[290px] lg:flex-col xl:w-[380px]">
          <SideSearch />
        </div>
      </div>
      <div className="fixed inset-x-0 bottom-0 z-10 flex-shrink-0 sm:hidden">
        <WalletBottomNav />
      </div>
    </div>
  );
}

export default WalletLayout;
