import React, { createContext, useContext } from "react";

// --- Stages shape ---
const stagesDefaultValue = {
  sendEmote: () => {},
  sendTip: () => {},
  sendInvalidateStageInfo: () => {},
  sendMuteMic: () => {},
  chat: {
    send: async () => ({}),
    update: async () => ({}),
    chatMessages: [],
    isSending: false,
  },
};

export const StagesDataChannelsContext = createContext(stagesDefaultValue);
export const useStagesDataChannelsContext = () =>
  useContext(StagesDataChannelsContext);
export const MockStagesDataChannelsProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => (
  <StagesDataChannelsContext.Provider value={stagesDefaultValue}>
    {children}
  </StagesDataChannelsContext.Provider>
);

// --- Livestream shape ---
const livestreamDefaultValue = {
  chat: {
    send: async () => ({}),
    update: async () => ({}),
    chatMessages: [],
    isSending: false,
  },
  sendInvalidateTippingStats: () => {},
};

export const LivestreamDataChannelsContext = createContext(
  livestreamDefaultValue,
);
export const useLivestreamDataChannelsContext = () =>
  useContext(LivestreamDataChannelsContext);
export const MockLivestreamDataChannelsProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => (
  <LivestreamDataChannelsContext.Provider value={livestreamDefaultValue}>
    {children}
  </LivestreamDataChannelsContext.Provider>
);
