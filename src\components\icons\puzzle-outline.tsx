import { ComponentProps } from "react";

export const PuzzleOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 20 20"
    fill="none"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.25 1.5C11.9185 1.5 11.6005 1.6317 11.3661 1.86612C11.1317 2.10054 11 2.41848 11 2.75V3.75C11 4.21413 10.8156 4.65925 10.4874 4.98744C10.1592 5.31563 9.71413 5.5 9.25 5.5H6.25C6.1837 5.5 6.12011 5.52634 6.07322 5.57322C6.02634 5.62011 6 5.6837 6 5.75V8.75C6 9.21413 5.81563 9.65925 5.48744 9.98744C5.15925 10.3156 4.71413 10.5 4.25 10.5H3.25C2.91848 10.5 2.60054 10.6317 2.36612 10.8661C2.1317 11.1005 2 11.4185 2 11.75C2 12.0815 2.1317 12.3995 2.36612 12.6339C2.60054 12.8683 2.91848 13 3.25 13H4.25C4.71413 13 5.15925 13.1844 5.48744 13.5126C5.81563 13.8408 6 14.2859 6 14.75V17.75C6 17.8163 6.02634 17.8799 6.07322 17.9268C6.12011 17.9737 6.18369 18 6.25 18H9.25C9.31631 18 9.37989 17.9737 9.42678 17.9268C9.47366 17.8799 9.5 17.8163 9.5 17.75V16.75C9.5 16.0207 9.78973 15.3212 10.3055 14.8055C10.8212 14.2897 11.5207 14 12.25 14C12.9793 14 13.6788 14.2897 14.1945 14.8055C14.7103 15.3212 15 16.0207 15 16.75V17.75C15 17.8163 15.0263 17.8799 15.0732 17.9268C15.1201 17.9737 15.1837 18 15.25 18H18.25C18.3163 18 18.3799 17.9737 18.4268 17.9268C18.4737 17.8799 18.5 17.8163 18.5 17.75V14.75C18.5 14.6837 18.4737 14.6201 18.4268 14.5732C18.3799 14.5263 18.3163 14.5 18.25 14.5H17.25C16.5207 14.5 15.8212 14.2103 15.3055 13.6945C14.7897 13.1788 14.5 12.4793 14.5 11.75C14.5 11.0207 14.7897 10.3212 15.3055 9.80546C15.8212 9.28973 16.5207 9 17.25 9H18.25C18.3163 9 18.3799 8.97366 18.4268 8.92678C18.4737 8.87989 18.5 8.81631 18.5 8.75V5.75C18.5 5.68369 18.4737 5.62011 18.4268 5.57322C18.3799 5.52634 18.3163 5.5 18.25 5.5H15.25C14.7859 5.5 14.3408 5.31563 14.0126 4.98744C13.6844 4.65925 13.5 4.21413 13.5 3.75V2.75C13.5 2.41848 13.3683 2.10054 13.1339 1.86612C12.8995 1.6317 12.5815 1.5 12.25 1.5ZM10.3055 0.805456C10.8212 0.289731 11.5207 0 12.25 0C12.9793 0 13.6788 0.289731 14.1945 0.805456C14.7103 1.32118 15 2.02065 15 2.75V3.75C15 3.8163 15.0263 3.87989 15.0732 3.92678C15.1201 3.97366 15.1837 4 15.25 4H18.25C18.7141 4 19.1592 4.18437 19.4874 4.51256C19.8156 4.84075 20 5.28587 20 5.75V8.75C20 9.21413 19.8156 9.65925 19.4874 9.98744C19.1592 10.3156 18.7141 10.5 18.25 10.5H17.25C16.9185 10.5 16.6005 10.6317 16.3661 10.8661C16.1317 11.1005 16 11.4185 16 11.75C16 12.0815 16.1317 12.3995 16.3661 12.6339C16.6005 12.8683 16.9185 13 17.25 13H18.25C18.7141 13 19.1592 13.1844 19.4874 13.5126C19.8156 13.8408 20 14.2859 20 14.75V17.75C20 18.2141 19.8156 18.6592 19.4874 18.9874C19.1592 19.3156 18.7141 19.5 18.25 19.5H15.25C14.7859 19.5 14.3408 19.3156 14.0126 18.9874C13.6844 18.6592 13.5 18.2141 13.5 17.75V16.75C13.5 16.4185 13.3683 16.1005 13.1339 15.8661C12.8995 15.6317 12.5815 15.5 12.25 15.5C11.9185 15.5 11.6005 15.6317 11.3661 15.8661C11.1317 16.1005 11 16.4185 11 16.75V17.75C11 18.2141 10.8156 18.6592 10.4874 18.9874C10.1592 19.3156 9.71413 19.5 9.25 19.5H6.25C5.78587 19.5 5.34075 19.3156 5.01256 18.9874C4.68437 18.6592 4.5 18.2141 4.5 17.75V14.75C4.5 14.6837 4.47366 14.6201 4.42678 14.5732C4.37989 14.5263 4.3163 14.5 4.25 14.5H3.25C2.52065 14.5 1.82118 14.2103 1.30546 13.6945C0.789731 13.1788 0.5 12.4793 0.5 11.75C0.5 11.0207 0.789731 10.3212 1.30546 9.80546C1.82118 9.28973 2.52065 9 3.25 9H4.25C4.3163 9 4.37989 8.97366 4.42678 8.92678C4.47366 8.87989 4.5 8.8163 4.5 8.75V5.75C4.5 5.28587 4.68437 4.84075 5.01256 4.51256C5.34075 4.18437 5.78587 4 6.25 4H9.25C9.3163 4 9.37989 3.97366 9.42678 3.92678C9.47366 3.87989 9.5 3.8163 9.5 3.75V2.75C9.5 2.02065 9.78973 1.32118 10.3055 0.805456Z"
      fill="currentColor"
    />
  </svg>
);
