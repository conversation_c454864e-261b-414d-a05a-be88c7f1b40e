"use client";

import { useEffect, useMemo, useRef, useState } from "react";

import { useQueryClient } from "@tanstack/react-query";
import { StateSnapshot, Virtuoso, VirtuosoHandle } from "react-virtuoso";

import { ArrowDownFilledIcon } from "@/components/icons";
import { PostLoadingSkeleton } from "@/components/post-loading-skeleton";
import { usePullToRefresh } from "@/hooks/use-pull-to-refresh";
import { useTrendingThreadsInfiniteQuery } from "@/queries";
import { useHomeStore } from "@/stores";
import { Thread } from "@/types";
import { cn } from "@/utils";

import { TimelinePost } from "./timeline-post";

export const TrendingTimeline = () => {
  const queryClient = useQueryClient();
  const timelineState = useRef<StateSnapshot>();
  const containerRef = useRef<HTMLDivElement>(null);
  const virtuoso = useRef<VirtuosoHandle | null>(null);
  const timelineRef = useHomeStore((state) => state.trendingTimelineRef);
  const [startReached, setStartReached] = useState(true);
  const state = usePullToRefresh(
    containerRef,
    () => {
      queryClient.resetQueries({
        queryKey: ["home", "threads", "trending-feed"],
      });
    },
    startReached,
  );

  const { snapshot, setSnapshot } = useHomeStore((state) => ({
    snapshot: state.trendingSnapshot,
    setSnapshot: state.setTrendingSnapshot,
  }));

  const { data, fetchNextPage, hasNextPage, isLoading, isFetchingNextPage } =
    useTrendingThreadsInfiniteQuery();

  const threads = useMemo(() => {
    if (!data) return [];

    return data.pages.reduce((prev, current) => {
      return [...prev, ...current.threads];
    }, [] as Thread[]);
  }, [data]);

  useEffect(() => {
    return () => {
      if (timelineState.current) {
        setSnapshot(timelineState.current);
        timelineRef.current = null;
      }
    };
  }, []);

  return (
    <>
      {state !== "idle" && (
        <div className="absolute left-1/2 top-[calc(8.75rem+env(safe-area-inset-top))] -translate-x-1/2">
          <ArrowDownFilledIcon
            className={cn(
              "h-6 w-6 text-gray-text transition-transform",
              state === "release" && "rotate-180",
            )}
          />
        </div>
      )}
      <div ref={containerRef}>
        <Virtuoso
          ref={(node) => {
            virtuoso.current = node;
            timelineRef.current = node;
          }}
          useWindowScroll
          data={threads}
          endReached={() => {
            if (hasNextPage) {
              fetchNextPage();
            }
          }}
          atTopStateChange={(state) => {
            setStartReached(state);
          }}
          increaseViewportBy={3000}
          overscan={2000}
          restoreStateFrom={snapshot}
          itemContent={(index, thread) => {
            return <TimelinePost thread={thread} />;
          }}
          isScrolling={() => {
            virtuoso.current?.getState((state) => {
              timelineState.current = state;
            });
          }}
          components={{
            Footer: () => {
              if (isLoading || isFetchingNextPage) {
                return (
                  <>
                    {Array.from({ length: 7 }).map((_, i) => (
                      <PostLoadingSkeleton key={i} />
                    ))}
                  </>
                );
              }
              return null;
            },
          }}
        />
      </div>
    </>
  );
};
