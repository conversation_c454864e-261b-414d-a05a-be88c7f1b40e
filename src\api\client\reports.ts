import { axios } from "@/lib/axios";
import { ModeratorAction } from "@/queries/report-tickets-mutations";
import {
  GroupedTicketsResponse,
  ReportTicket,
} from "@/queries/types/report-tickets";

export const getReportTickets = async (params: {
  status: "Active" | "Closed";
  page: number;
  pageSize: number;
  searchString?: string;
}) => {
  const response = await axios.get("/report/tickets", { params });
  return response.data;
};

export const getReportTicketById = async (ticketId: number) => {
  const response = await axios.get<ReportTicket>(`/report/tickets/${ticketId}`);
  return response.data;
};

export const processTicket = async ({
  ticketId,
  action,
}: {
  ticketId: number;
  action: ModeratorAction;
}) => {
  const response = await axios.post(`/report/tickets/${ticketId}/process`, {
    action,
  });
  return response.data;
};

export const getGroupedTickets = async (params: {
  status: "Active" | "Closed";
  page: number;
  pageSize: number;
  sortBy: "date" | "count" | "score" | "live";
  searchString?: string;
}) => {
  const response = await axios.get<GroupedTicketsResponse>(
    "/report/grouped-tickets",
    { params },
  );
  return response.data;
};

export const markAsSeen = async (ticketId: number) => {
  const response = await axios.post("/report/seen", { ticketId });
  return response.data;
};

export const getUnseenCount = async () => {
  const response = await axios.get<number>("/report/unseen-count");
  return response.data;
};
