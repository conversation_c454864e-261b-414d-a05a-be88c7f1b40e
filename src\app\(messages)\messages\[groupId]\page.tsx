import { ChatConversation } from "./_components/chat-conversation-main";
import { <PERSON><PERSON><PERSON>ead<PERSON> } from "./_components/chat-header";
import { PinnedMessages } from "./_components/pinned-messages";
import { SuspendedUserMessages } from "./_components/suspended-user-message";
import { GroupProvider } from "./context/group-context";

function ChatConversationPage() {
  return (
    <GroupProvider>
      <div className="relative flex h-full max-h-svh flex-col">
        <div className="">
          <ChatHeader />
          <SuspendedUserMessages />
          <PinnedMessages />
        </div>
        <ChatConversation />
      </div>
    </GroupProvider>
  );
}

export default ChatConversationPage;
