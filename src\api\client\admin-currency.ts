import { axios } from "@/lib/axios";

export enum SystemCurrencyCategoryEnum {
  SUPPORTED = "supported",
  TIPPING = "tipping",
  EXCHANGE = "exchange",
}

export interface SystemCurrency {
  id: number;
  symbol: string;
  systemRate: string;
  name: string;
  image: string;
  decimals?: number;
  address?: string;
  categories: SystemCurrencyCategoryEnum[];
}

export const getCurrencies = async () => {
  const response = await axios.get<SystemCurrency[]>(`/admin/currencies`);

  return response.data;
};

export const getCurrencyById = async (id: string | number) => {
  const response = await axios.get<SystemCurrency>(`/admin/currencies/${id}`);

  return response.data;
};

export const createCurrency = async (currency: Omit<SystemCurrency, "id">) => {
  const response = await axios.post<SystemCurrency>(
    "/admin/currencies",
    currency,
  );
  return response.data;
};

export const updateCurrency = async (
  id: string | number,
  currency: Partial<Omit<SystemCurrency, "id">>,
) => {
  const response = await axios.put<SystemCurrency>(
    `/admin/currencies/${id}`,
    currency,
  );
  return response.data;
};

export const deleteCurrency = async (id: string | number) => {
  const response = await axios.delete<{ success: boolean }>(
    `/admin/currencies/${id}`,
  );
  return response.data;
};
