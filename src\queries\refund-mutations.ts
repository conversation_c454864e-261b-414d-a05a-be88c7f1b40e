import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import { postRefund } from "@/api/client/refund";

type RefundMutation = MutationOptions<
  unknown,
  DefaultError,
  {
    addresses: string;
    transactions: string;
    amount: string;
  },
  any
>;

export const useRefundMutation = (options?: RefundMutation) => {
  return useMutation({
    mutationFn: postRefund,
    ...options,
  });
};
