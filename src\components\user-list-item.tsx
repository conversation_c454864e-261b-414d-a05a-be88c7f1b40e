import { useMemo } from "react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User } from "@/queries/types/top-users-response";
import { abbreviateNumber, cn, divideBigInt, formatAvax } from "@/utils";

import { TriangleDownOutlineIcon } from "./icons/triangle-down-outline";
import { TriangleUpOutlineIcon } from "./icons/triangle-up-outline";
import { ProgressBarLink } from "./progress-bar";
import { UserBadges } from "./user-badges";

interface UserListItemProps {
  user: User;
  className?: string;
}

export const UserListItem = ({ user, className }: UserListItemProps) => {
  const tickerPrice = useMemo(() => {
    if (user.stats?.keyPrice && !user.keyPrice) {
      return "0";
    }

    return formatAvax(user.stats?.keyPrice || user.keyPrice);
  }, [user.stats?.keyPrice, user.keyPrice]);

  const [isNegative, percentageIncrease] = useMemo(() => {
    const keyPrice = BigInt(user.stats?.keyPrice || 0);
    const lastKeyPrice = BigInt(user?.lastKeyPrice || 0);

    const percentage = lastKeyPrice
      ? 100 * (divideBigInt(keyPrice, lastKeyPrice) - 1)
      : keyPrice
        ? 100
        : 0;

    return [percentage < 0, abbreviateNumber(percentage, 2, false)];
  }, [user.stats?.keyPrice, user?.lastKeyPrice]);

  return (
    <ProgressBarLink
      href={`/${user.twitterHandle}`}
      className={cn("flex w-full justify-between gap-4 px-6 py-4", className)}
    >
      <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
        <Avatar className="size-[42px]">
          <AvatarImage src={user.twitterPicture} />
          <AvatarFallback />
        </Avatar>
        <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
          <div className="flex gap-1.5">
            <h4 className="truncate text-[#F4F4F4]">{user.twitterName}</h4>
            {user.badges && <UserBadges badges={user.badges} />}
          </div>
          <div className="truncate text-[#808080]">@{user.twitterHandle}</div>
        </div>
      </div>
      <div className="flex flex-shrink-0 flex-col items-end justify-center gap-[2px]">
        <div className="flex items-center gap-[6px]">
          <img
            src="/images/avax.png"
            className="h-4 w-4 rounded-full"
            alt="AVAX logo"
          />
          <span className="text-sm font-medium text-[#F4F4F4]">
            {tickerPrice}
          </span>
        </div>
        <span
          className={cn(
            "flex items-center gap-[4px] text-sm",
            isNegative ? "text-danger" : "text-[#40B877]",
          )}
        >
          {percentageIncrease !== "0" && (
            <>
              {isNegative ? (
                <TriangleDownOutlineIcon className="h-4 w-4" />
              ) : (
                <TriangleUpOutlineIcon className="h-4 w-4" />
              )}
            </>
          )}
          <span>{percentageIncrease}%</span>
        </span>
      </div>
    </ProgressBarLink>
  );
};
