"use client";

import { WarningOutlineIcon } from "@/components/icons/warning-outline";
import { ProgressBarLink } from "@/components/progress-bar";
import { useUserByIdQuery } from "@/queries";
import { useUser } from "@/stores";
import { UserFlaggedEnum } from "@/types";

import { useGroup } from "../context/group-context";

export const SuspendedUserMessages = () => {
  const { user } = useUser();
  const { data } = useGroup();
  const owner = useUserByIdQuery(data?.group?.ownerUserId || "");
  const chatMate = useUserByIdQuery(data?.group?.chatMateId || "");
  const isMe = user !== null && user?.id === data?.group?.ownerUserId;

  let isSuspended = false;
  if (data?.group?.isDirect) {
    isSuspended = isMe
      ? chatMate?.data?.user?.flag === UserFlaggedEnum.SUSPENDED
      : owner?.data?.user?.flag === UserFlaggedEnum.SUSPENDED;
  } else {
    isSuspended = owner?.data?.user?.flag === UserFlaggedEnum.SUSPENDED;
  }

  if (!isSuspended) {
    return null;
  }

  return (
    <div className="flex h-11 w-full select-none items-center gap-3 bg-[#141414] bg-opacity-[0.88] px-3 outline-none">
      <div className="flex h-full flex-shrink-0 flex-col gap-0.5 py-2">
        <WarningOutlineIcon color="#F4F4F4" />
      </div>
      <div className="flex flex-grow flex-col gap-0.5 overflow-hidden text-left">
        <h4 className="text-xs font-semibold leading-none text-white">
          Account Suspended
        </h4>
        <p className="text-xs leading-4 text-gray-text">
          This account has been suspended for violating The Arena&apos;s{" "}
          <ProgressBarLink
            href="/terms-of-use"
            className="font-semibold text-white"
          >
            terms of use.
          </ProgressBarLink>
        </p>
      </div>
    </div>
  );
};
