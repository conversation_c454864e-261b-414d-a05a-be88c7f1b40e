import { cookies } from "next/headers";
import { redirect } from "next/navigation";

import { FeeSettings } from "./_components/fee-settings";
import { Header } from "./_components/header";

function EditProfilePage({ params }: { params: { userHandle: string } }) {
  const userCookie = cookies().get("user");
  const user = userCookie ? JSON.parse(userCookie.value || "{}") : null;

  if (user && user.twitterHandle !== params.userHandle) {
    redirect(`/${params.userHandle}`);
  }

  return (
    <div className="pt-pwa flex h-full w-full flex-col">
      <Header />
      <FeeSettings />
    </div>
  );
}

export default EditProfilePage;
