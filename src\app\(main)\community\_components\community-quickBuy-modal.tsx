"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, SetStateAction, useEffect, useState } from "react";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { useQueryClient } from "@tanstack/react-query";
import Skeleton from "react-loading-skeleton";
import { Address, formatEther, Hex, parseEther } from "viem";

import { fetchTokenBalance } from "@/api/balance";
import { ChatbubbleOutlineIcon } from "@/components/icons";
import { MobileOutlineIcon } from "@/components/icons-v2/mobile-outline";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { ERC20_CONTRACT_ABI } from "@/environments/BACKEND_FRIENDS_CONTRACT";
import {
  COMMUNITIES_CONTRACT,
  COMMUNITY_ABI,
} from "@/environments/COMMUNITY_CONTRACT";
import { swapTokens } from "@/environments/tokens";
import { useLaunchToken } from "@/hooks/use-launch-token";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useSwap } from "@/hooks/use-swap";
import { useUser } from "@/stores";
import { CommunityExtended } from "@/types/community";
import { abbreviateNumber } from "@/utils/abbreviate-number";
import { formatInputNumber } from "@/utils/format-token-price";

import { updatePricesPeriod } from "../../../_components/trade-erc20/consts";
import { HANDLE_PHASE } from "./consts";

interface CommunityQuickBuyModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  setHasEnoughTokens: Dispatch<SetStateAction<boolean>>;
  community: CommunityExtended;
}

export const CommunityQuickBuyModal: FC<CommunityQuickBuyModalProps> = ({
  open,
  setOpen,
  setHasEnoughTokens,
  community,
}) => {
  const [isFetching, setIsFetching] = useState(false);
  const [isProcess, setIsProcess] = useState(false);
  const [userTokenBalance, setUserTokenBalance] = useState<bigint>(0n);
  const [userBalance, setUserBalance] = useState<bigint>(0n);
  const [avaxAmount, setAvaxAmount] = useState<bigint>(0n);
  const [tokenAmount, setTokenAmount] = useState<bigint>(0n);
  const [commission, setCommission] = useState<bigint>(0n);

  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const queryClient = useQueryClient();
  const { user } = useUser();
  const { primaryWallet } = useDynamicContext();
  const { getBuyRate, buildSwapWithCommission } = useSwap();
  const { calculateAndBuy } = useLaunchToken();

  const postingThresholdNumeric = Number(
    formatEther(BigInt(community.postingThreshold)),
  );
  const headerAmount = abbreviateNumber(postingThresholdNumeric, 1, true, {
    padding: false,
  });

  const calculateCommission = (amount: bigint): bigint => amount / 200n;

  const refreshPrices = async (tokenBalance: bigint) => {
    if (!primaryWallet || !isEthereumWallet(primaryWallet)) return;

    let tokensToBuy = BigInt(community.postingThreshold) - tokenBalance;
    if (tokensToBuy <= 0n) return;
    tokensToBuy += 100n;

    if (tokenAmount !== tokensToBuy) setTokenAmount(tokensToBuy);

    try {
      if (community.isLP) {
        const destToken = {
          decimals: 18,
          address: community.contractAddress,
          icon: community.photoURL,
          symbol: community.ticker,
        };

        const rate = await getBuyRate({
          srcToken: swapTokens["AVAX"],
          destToken,
          destAmount: tokensToBuy.toString(),
        });
        const newAvaxAmount = BigInt(rate.srcAmount);
        const newCommission = calculateCommission(newAvaxAmount);

        if (avaxAmount !== newAvaxAmount) setAvaxAmount(newAvaxAmount);
        if (commission !== newCommission) setCommission(newCommission);
      } else {
        const publicClient = await primaryWallet.getPublicClient();
        const newAvaxAmount = await publicClient.readContract({
          address: COMMUNITIES_CONTRACT.addressMainnet as Address,
          abi: COMMUNITY_ABI,
          functionName: "calculateCostWithFees",
          args: [
            BigInt(Math.ceil(Number(formatEther(tokensToBuy)))),
            BigInt(community.bcGroupId),
          ],
          account: primaryWallet.address as Address,
        });

        if (avaxAmount !== newAvaxAmount) setAvaxAmount(newAvaxAmount);
      }
    } catch (error) {
      console.error("Error refreshing prices:", error);
    }
  };

  const refreshBalancesAndPrices = async () => {
    if (!primaryWallet || !isEthereumWallet(primaryWallet)) return;

    setIsFetching(true);
    try {
      const publicClient = await primaryWallet.getPublicClient();
      const [balance, tokenBalance] = await Promise.all([
        fetchTokenBalance(primaryWallet.address, { symbol: "AVAX" }),
        publicClient.readContract({
          address: community.contractAddress as Address,
          abi: ERC20_CONTRACT_ABI,
          functionName: "balanceOf",
          args: [primaryWallet.address as Address],
        }),
      ]);

      setUserTokenBalance(tokenBalance);
      setUserBalance(balance.balance);

      await refreshPrices(tokenBalance);
    } catch (error) {
      console.error("Error refreshing values:", error);
    } finally {
      setIsFetching(false);
    }
  };

  useEffect(() => {
    if (!open) return;
    refreshBalancesAndPrices();

    const interval = setInterval(async () => {
      await refreshPrices(userTokenBalance);
    }, updatePricesPeriod);

    return () => clearInterval(interval);
  }, [open, community, primaryWallet, userTokenBalance]);

  const handleBuy = async () => {
    if (!primaryWallet) {
      throw new Error("Dynamic wallet is not initialized");
    }
    if (!isEthereumWallet(primaryWallet)) {
      throw new Error("This wallet is not a Ethereum wallet");
    }
    if (!user) {
      throw new Error("User not specified");
    }
    if (avaxAmount === 0n) return;
    if (userBalance - parseEther("0.005") < avaxAmount) {
      toast.red("Insufficient balance");
      return;
    }
    setIsProcess(true);

    try {
      const walletClient = await primaryWallet.getWalletClient();
      const publicClient = await primaryWallet.getPublicClient();

      if (community.isLP) {
        const destToken = {
          decimals: 18,
          address: community.contractAddress,
          icon: community.photoURL,
          symbol: community.ticker,
        };

        const rate = await getBuyRate({
          srcToken: swapTokens["AVAX"],
          destToken,
          destAmount: tokenAmount.toString(),
        });

        const res = await buildSwapWithCommission({
          srcToken: swapTokens["AVAX"],
          destToken,
          srcAmount: (avaxAmount + commission).toString(),
          priceRoute: rate,
          userAddress: primaryWallet.address,
          slippage: 1,
        });

        const swapHash = await walletClient.sendTransaction({
          account: primaryWallet.address as Hex,
          chain: walletClient.chain,
          to: res.to as Hex,
          value: res.value as any,
          data: res.data as any,
        });

        await publicClient.waitForTransactionReceipt({
          hash: swapHash as Hex,
          confirmations: 1,
        });
      } else {
        await calculateAndBuy(
          primaryWallet,
          publicClient,
          walletClient,
          user,
          BigInt(community.bcGroupId),
          avaxAmount,
        );
      }

      await updateData();
    } catch (error: any) {
      console.error("Buy error:", error);
      toast.red("Failed to buy tokens");
      setIsProcess(false);
      setOpen(false);
    }
  };

  const updateData = async () => {
    const param =
      community.tokenPhase >= HANDLE_PHASE
        ? community.name
        : community.contractAddress;
    await queryClient.invalidateQueries({
      queryKey: ["currency", "system"],
    });
    await queryClient.refetchQueries({
      queryKey: ["community", "param", param],
    });
    toast.green("Successfully bought");
    setHasEnoughTokens(true);
    setIsProcess(false);
    setOpen(false);
  };

  const content = (
    <>
      <div className="mb-6 flex flex-col justify-start gap-2">
        <div className="flex items-center gap-2">
          <ChatbubbleOutlineIcon className="size-5 text-gray-text" />
          <span className="text-sm text-gray-text">
            Get access to the private chatroom
          </span>
        </div>
        <div className="flex items-center gap-2">
          <MobileOutlineIcon className="size-5 text-gray-text" />
          <span className="text-sm text-gray-text">Post inside this feed</span>
        </div>
      </div>
      <div className="mb-4 flex items-center gap-1 pl-1 text-xs font-semibold">
        <span className="text-gray-text">BALANCE:</span>
        {isFetching ? (
          <Skeleton className="ml-1 h-3 w-20" />
        ) : (
          <span className="text-off-white">
            {formatInputNumber(formatEther(userTokenBalance))}
          </span>
        )}
      </div>
      <div className="flex justify-end gap-2">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => setOpen(false)}
          disabled={isProcess}
        >
          Cancel
        </Button>
        {isFetching ? (
          <Skeleton
            width={200}
            height={44}
            className="flex items-center justify-center"
            style={{ borderRadius: "50px" }}
          />
        ) : (
          <Button
            variant="default"
            className="min-w flex-grow items-center"
            onClick={handleBuy}
            disabled={
              isFetching || isProcess || Number(formatEther(avaxAmount)) === 0
            }
            loading={isProcess}
          >
            <div className="flex items-center gap-1">
              <span className="text-sm font-medium text-off-white">
                {userTokenBalance === 0n ? "Buy 1M Tokens" : "Top my balance"}
              </span>
              <img
                src="/assets/coins/avax.png"
                className="size-3 rounded-full brightness-110 grayscale"
                alt="AVAX logo"
              />
              <span className="text-xs font-medium text-off-white">
                {Number(formatEther(avaxAmount + commission)).toFixed(2)}
              </span>
            </div>
          </Button>
        )}
      </div>
    </>
  );

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-sm bg-[#0F0F0F]/90">
          <DialogHeader>
            <DialogTitle className="text-base font-semibold text-off-white">
              Buy {headerAmount} tokens to unlock these perks!
            </DialogTitle>
          </DialogHeader>
          {content}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="p-4">
        <DrawerHeader className="justify-start">
          <DrawerTitle className="text-base font-semibold text-off-white">
            Buy {headerAmount} tokens to unlock these perks!
          </DrawerTitle>
        </DrawerHeader>
        <div className="p-4">{content}</div>
      </DrawerContent>
    </Drawer>
  );
};
