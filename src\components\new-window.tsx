/**
 * Component dependencies.
 */

import React from "react";

import ReactDOM from "react-dom";

interface WindowFeatures {
  width?: string | number;
  height?: string | number;
  left?: number;
  top?: number;
  [key: string]: string | number | boolean | undefined;
}

interface NewWindowProps {
  children?: React.ReactNode;
  url?: string;
  name?: string;
  title?: string;
  features?: WindowFeatures;
  onUnload?: (window: Window | null) => void;
  onBlock?: (window: Window | null) => void;
  onOpen?: (window: Window) => void;
  center?: "parent" | "screen";
  copyStyles?: boolean;
  closeOnUnmount?: boolean;
}

interface NewWindowState {
  mounted: boolean;
}

class NewWindow extends React.PureComponent<NewWindowProps, NewWindowState> {
  private container: HTMLDivElement | null;
  private window: Window | null;
  private windowCheckerInterval: number | null;
  private released: boolean;

  static defaultProps: NewWindowProps = {
    url: "",
    name: "",
    title: "",
    features: { width: "600px", height: "640px" },
    onBlock: undefined,
    onOpen: undefined,
    onUnload: undefined,
    center: "parent",
    copyStyles: true,
    closeOnUnmount: true,
  };

  /**
   * The NewWindow function constructor.
   * @param {Object} props
   */
  constructor(props: NewWindowProps) {
    super(props);
    this.container = null;
    this.window = null;
    this.windowCheckerInterval = null;
    this.released = false;
    this.state = {
      mounted: false,
    };
  }

  /**
   * Render the NewWindow component.
   */
  render() {
    if (!this.state.mounted) return null;
    return this.container
      ? ReactDOM.createPortal(this.props.children, this.container)
      : null;
  }

  componentDidMount() {
    if (!this.window && !this.container) {
      this.openChild();
      this.setState({ mounted: true });
    }
  }

  /**
   * Create the new window when NewWindow component mount.
   */
  openChild() {
    const {
      url,
      title,
      name,
      features = {},
      onBlock,
      onOpen,
      center,
    } = this.props;

    if (
      typeof center === "string" &&
      (features.width === undefined || features.height === undefined)
    ) {
      console.warn(
        "width and height window features must be present when a center prop is provided",
      );
    } else if (center === "parent" && window.top) {
      features.left =
        window.top.outerWidth / 2 +
        window.top.screenX -
        (typeof features.width === "string"
          ? parseInt(features.width)
          : features.width || 0) /
          2;
      features.top =
        window.top.outerHeight / 2 +
        window.top.screenY -
        (typeof features.height === "string"
          ? parseInt(features.height)
          : features.height || 0) /
          2;
    } else if (center === "screen") {
      const screenLeft =
        window.screenLeft !== undefined ? window.screenLeft : window.screenX;
      const screenTop =
        window.screenTop !== undefined ? window.screenTop : window.screenY;

      const width =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        screen.width;
      const height =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        screen.height;

      features.left =
        width / 2 -
        (typeof features.width === "string"
          ? parseInt(features.width)
          : features.width || 0) /
          2 +
        screenLeft;
      features.top =
        height / 2 -
        (typeof features.height === "string"
          ? parseInt(features.height)
          : features.height || 0) /
          2 +
        screenTop;
    }

    const newWindow = window.open(url, name, toWindowFeatures(features));
    this.window = newWindow;

    if (newWindow) {
      newWindow.document.title = title || "";

      // Create HTML structure
      newWindow.document.documentElement.innerHTML = `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title || ""}</title>
            <style data-base-styles>
              html, body {
                margin: 0;
                padding: 0;
                height: 100%;
                overflow: hidden;
                background: #000;
              }
              #new-window-container {
                width: 100%;
                height: 100vh;
                position: relative;
                overflow-y: auto;
                display: flex;
                flex-direction: column;
              }
              /* Animation base styles */
              .framer-motion-layout-transition {
                position: relative !important;
                transform-origin: 0 0 !important;
              }
            </style>
          </head>
          <body>
            <div id="new-window-container"></div>
          </body>
        </html>
      `;

      this.container = newWindow.document.getElementById(
        "new-window-container",
      ) as HTMLDivElement;

      if (this.props.copyStyles) {
        // Initial style copy
        this.copyAndSyncStyles(newWindow.document);

        // Set up a MutationObserver to watch for style changes in the main window
        const styleObserver = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (
              mutation.type === "childList" &&
              Array.from(mutation.addedNodes).some(
                (node) =>
                  node instanceof HTMLStyleElement ||
                  node instanceof HTMLLinkElement,
              )
            ) {
              this.copyAndSyncStyles(newWindow.document);
            }
          });
        });

        styleObserver.observe(document.head, {
          childList: true,
          subtree: true,
        });

        // Clean up observer when window is closed
        newWindow.addEventListener("beforeunload", () => {
          styleObserver.disconnect();
        });
      }

      if (typeof onOpen === "function") {
        onOpen(newWindow);
      }

      this.windowCheckerInterval = window.setInterval(() => {
        if (!this.window || this.window.closed) {
          this.release();
        }
      }, 50);

      newWindow.addEventListener("beforeunload", () => this.release());
    } else {
      if (typeof onBlock === "function") {
        onBlock(null);
      } else {
        console.warn("A new window could not be opened. Maybe it was blocked.");
      }
    }
  }

  copyAndSyncStyles(targetDocument: Document) {
    // Remove existing styles except base styles
    const existingStyles = targetDocument.head.querySelectorAll(
      'style:not([data-base-styles]), link[rel="stylesheet"]',
    );
    existingStyles.forEach((style) => style.remove());

    const headFrag = targetDocument.createDocumentFragment();

    // Copy <style> tags
    Array.from(document.getElementsByTagName("style")).forEach((style) => {
      const newStyle = targetDocument.createElement("style");
      newStyle.textContent = style.textContent;
      headFrag.appendChild(newStyle);
    });

    // Copy stylesheets
    Array.from(document.styleSheets).forEach((styleSheet) => {
      try {
        if (styleSheet.cssRules) {
          const newStyleEl = targetDocument.createElement("style");
          const rules: string[] = [];

          Array.from(styleSheet.cssRules).forEach((rule) => {
            if (rule instanceof CSSKeyframesRule) {
              rules.push(getKeyFrameText(rule));
            } else if (rule instanceof CSSStyleRule) {
              rules.push(rule.cssText);
            } else {
              rules.push(rule.cssText);
            }
          });

          newStyleEl.textContent = rules.join("\n");
          headFrag.appendChild(newStyleEl);
        } else if (styleSheet.href) {
          const linkEl = targetDocument.createElement("link");
          linkEl.rel = "stylesheet";
          linkEl.href = styleSheet.href;
          headFrag.appendChild(linkEl);
        }
      } catch (err) {
        if (styleSheet.href) {
          const linkEl = targetDocument.createElement("link");
          linkEl.rel = "stylesheet";
          linkEl.href = styleSheet.href;
          headFrag.appendChild(linkEl);
        }
      }
    });

    targetDocument.head.appendChild(headFrag);
  }

  /**
   * Closes the opened window (if any) when NewWindow will unmount if the
   * prop {closeOnUnmount} is true, otherwise the NewWindow will remain open
   */
  componentWillUnmount() {
    if (this.state.mounted && this.window) {
      if (this.props.closeOnUnmount) {
        this.window.close();
      } else if (this.props.children && this.container) {
        const clone = this.container.cloneNode(true) as HTMLDivElement;
        clone.setAttribute("id", "new-window-container-static");
        this.window.document.body.appendChild(clone);
      }
    }
  }

  /**
   * Release the new window and anything that was bound to it.
   */
  release() {
    if (this.released) {
      return;
    }
    this.released = true;

    if (this.windowCheckerInterval) {
      clearInterval(this.windowCheckerInterval);
    }

    const { onUnload } = this.props;

    if (typeof onUnload === "function") {
      onUnload(null);
    }
  }
}

/**
 * Utility functions.
 * @private
 */

/**
 * Make keyframe rules.
 * @param {CSSRule} cssRule
 * @return {String}
 * @private
 */

function getKeyFrameText(cssRule: CSSKeyframesRule): string {
  const tokens = ["@keyframes", cssRule.name, "{"];
  Array.from(cssRule.cssRules).forEach((rule) => {
    if (rule instanceof CSSKeyframeRule) {
      tokens.push(rule.keyText, "{", rule.style.cssText, "}");
    }
  });
  tokens.push("}");
  return tokens.join(" ");
}

/**
 * Handle local import urls.
 * @param {CSSRule} cssRule
 * @return {String}
 * @private
 */

function fixUrlForRule(cssRule: CSSRule): string {
  return cssRule.cssText
    .split("url(")
    .map((line) => {
      if (line[1] === "/") {
        return `${line.slice(0, 1)}${window.location.origin}${line.slice(1)}`;
      }
      return line;
    })
    .join("url(");
}

/**
 * Convert features props to window features format (name=value,other=value).
 * @param {Object} obj
 * @return {String}
 * @private
 */

function toWindowFeatures(obj: WindowFeatures): string {
  return Object.keys(obj)
    .reduce((features: string[], name) => {
      const value = obj[name];
      if (typeof value === "boolean") {
        features.push(`${name}=${value ? "yes" : "no"}`);
      } else if (value !== undefined) {
        features.push(`${name}=${value}`);
      }
      return features;
    }, [])
    .join(",");
}

/**
 * Component export.
 * @private
 */

export { NewWindow };
