import { FC } from "react";

import { formatEther } from "viem";

import { SlippageIcon } from "@/components/icons-v2/slippage";
import { UpdateIcon } from "@/components/icons-v2/update";
import { formatInputNumber } from "@/utils/format-token-price";

interface SlippageSettingProps {
  handleUpdate: () => void;
  handleSlippageSetup: () => void;
  conversionRate: bigint;
  symbol: string;
}

export const SlippageSetting: FC<SlippageSettingProps> = ({
  handleUpdate,
  handleSlippageSetup,
  conversionRate,
  symbol,
}) => {
  const conversionString = `1 AVAX = ${formatInputNumber(
    formatEther(conversionRate),
  ).replace(/\.?0+$/, "")} ${symbol}`;

  return (
    <div className="flex flex-col gap-1 py-8">
      <div className="flex h-[54px] w-full items-center justify-between rounded-[6px] bg-chat-bubble px-4 text-[11px] font-semibold text-[#b4b4b4]">
        {conversionString}
        <div className="flex gap-4">
          <button
            className="flex h-[30px] w-[30px] items-center justify-center rounded-full bg-[#2A2A2A] drop-shadow-[0_0_1px_rgba(255,255,255,0.6)]"
            onClick={handleUpdate}
          >
            <UpdateIcon />
          </button>
          <button
            className="flex h-[30px] w-[30px] items-center justify-center rounded-[6px] bg-[#2A2A2A] drop-shadow-[0_0_1px_rgba(255,255,255,0.6)]"
            onClick={handleSlippageSetup}
          >
            <SlippageIcon />
          </button>
        </div>
      </div>
    </div>
  );
};
