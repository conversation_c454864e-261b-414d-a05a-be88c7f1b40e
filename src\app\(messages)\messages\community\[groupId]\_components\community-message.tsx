"use client";

import { memo, useEffect, useMemo, useRef, useState } from "react";

import * as ContextMenuPrimitive from "@radix-ui/react-context-menu";
import {
  InfiniteData,
  useQueries,
  useQueryClient,
} from "@tanstack/react-query";
import { rubberbandIfOutOfBounds, useDrag } from "@use-gesture/react";
import { format } from "date-fns";
import { Emoji } from "emoji-picker-react";
import { motion, useMotionValue, useTransform } from "framer-motion";
import CopyToClipboard from "react-copy-to-clipboard";
import { v4 } from "uuid";

import { getUserById } from "@/api/client/user";
import {
  DuplicateOutlineIcon,
  PinOutlineIcon,
  ReplyOutlineIcon,
  UnpinOutlineIcon,
} from "@/components/icons";
import { ImagePreviewModal } from "@/components/image-preview-modal";
import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ContextMenu, ContextMenuTrigger } from "@/components/ui/context-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import {
  usePinMessage,
  useReactToMessage,
  useUnreactToMessage,
} from "@/queries";
import {
  ChatMessagesResponse,
  ChatPinnedMessagesResponse,
  MessageType,
  Reaction,
} from "@/queries/types/chats";
import { useUser } from "@/stores";
import { User } from "@/types";
import { abbreviateNumber, checkContent, cn } from "@/utils";

import { Reply } from "../../../_components/reply";
import { useCommunity, useCommunityStore } from "../context/community-context";

interface MessageProps {
  message: MessageType;
  isPreviousSameUser: boolean;
  isNextSameUser: boolean;
  setReply: (message: MessageType | null) => void;
  scrollToBottom: () => void;
}

export const CommunityMessage = memo(
  ({
    message: _message,
    isPreviousSameUser,
    isNextSameUser,
    setReply,
    scrollToBottom,
  }: MessageProps) => {
    const pinnedMessageId = useCommunityStore((state) => state.messageId);
    const pinnedMessagesCount = useCommunityStore(
      (state) => state.pinnedMessagesCount,
    );
    const { data: group } = useCommunity();
    const { user } = useUser();
    const queryClient = useQueryClient();
    const [open, setOpen] = useState(false);
    const ref = useRef<HTMLDivElement>(null);
    const messageRef = useRef<HTMLDivElement>(null);

    const { mutateAsync: reactToMessage } = useReactToMessage({
      onMutate: async ({ messageId, groupId, reaction }) => {
        await queryClient.cancelQueries({
          queryKey: [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId, messageId: pinnedMessageId },
          ],
        });

        const previousMessages = queryClient.getQueryData([
          "chat",
          "group-infinite-messages-with-actions",
          {
            groupId: group?.community?.groupId || "",
            messageId: pinnedMessageId,
          },
        ]);

        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId, messageId: pinnedMessageId },
          ],
          (old: InfiniteData<ChatMessagesResponse>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                if (page.messages.find((message) => message.id === messageId)) {
                  return {
                    ...page,
                    messages: page.messages.map((message) => {
                      if (message.id === messageId) {
                        const filteredReactions = message.reactions?.filter(
                          (reaction) => reaction.userId !== user?.id,
                        );
                        return {
                          ...message,
                          reactions: [
                            ...filteredReactions,
                            {
                              id: v4(),
                              createdOn: Date.now().toString(),
                              messageId,
                              userId: user?.id ?? "",
                              reaction,
                            },
                          ],
                        };
                      }
                      return message;
                    }),
                  };
                }

                return page;
              }),
            };
          },
        );

        scrollToBottom();

        return { previousMessages };
      },
      onError(_, variables, context) {
        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId: variables.groupId, messageId: pinnedMessageId },
          ],
          context?.previousMessages,
        );
      },
    });

    const { mutateAsync: unreactToMessage } = useUnreactToMessage({
      onMutate: async ({ messageId, groupId }) => {
        await queryClient.cancelQueries({
          queryKey: [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId, messageId: pinnedMessageId },
          ],
        });

        const previousMessages = queryClient.getQueryData([
          "chat",
          "group-infinite-messages-with-actions",
          { groupId, messageId: pinnedMessageId },
        ]);

        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId, messageId: pinnedMessageId },
          ],
          (old: InfiniteData<ChatMessagesResponse>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                if (page.messages.find((message) => message.id === messageId)) {
                  return {
                    ...page,
                    messages: page.messages.map((message) => {
                      if (message.id === messageId) {
                        const filteredReactions = message.reactions?.filter(
                          (reaction) => reaction.userId !== user?.id,
                        );
                        return {
                          ...message,
                          reactions: filteredReactions,
                        };
                      }
                      return message;
                    }),
                  };
                }

                return page;
              }),
            };
          },
        );

        return { previousMessages };
      },
      onError(err, variables, context) {
        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId: variables.groupId, messageId: pinnedMessageId },
          ],
          context?.previousMessages,
        );
      },
    });

    const { message, youtubeEmbedUrl } = useMemo(() => {
      const [content, , youtubeEmbedUrl] = checkContent({
        content: _message.message ?? "",
        truncate: false,
      });

      return {
        message: {
          ..._message,
          message: content,
        },
        youtubeEmbedUrl,
      };
    }, [_message]);

    const { mutateAsync: pin } = usePinMessage({
      onMutate: async ({ groupId, messageId, isPinned }) => {
        const previousMessages = queryClient.getQueryData([
          "chat",
          "group-infinite-messages-with-actions",
          { groupId, messageId: pinnedMessageId },
        ]);

        const previousPinnedMessages = queryClient.getQueryData([
          "chat",
          "messages",
          "pinned",
          groupId,
        ]);

        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId, messageId: pinnedMessageId },
          ],
          (old: InfiniteData<ChatMessagesResponse>) => {
            if (!old) return old;
            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  messages: page.messages.map((message) =>
                    message.id === messageId
                      ? {
                          ...message,
                          isPinned,
                        }
                      : message,
                  ),
                };
              }),
            };
          },
        );

        queryClient.setQueryData(
          ["chat", "messages", "pinned", groupId],
          (old: ChatPinnedMessagesResponse) => {
            if (!old) return old;

            if (isPinned) {
              return {
                ...old,
                messages: [...old.messages, { ..._message, isPinned }].sort(
                  (a, b) => {
                    return a.createdOn > b.createdOn ? -1 : 1;
                  },
                ),
              };
            } else {
              return {
                ...old,
                messages: old.messages.filter((m) => m.id !== messageId),
              };
            }
          },
        );

        return { previousMessages, previousPinnedMessages };
      },
      onError: (err, variables, context) => {
        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages-with-actions",
            {
              groupId: group?.community?.groupId || "",
              messageId: pinnedMessageId,
            },
          ],
          context?.previousMessages,
        );
        queryClient.setQueryData(
          ["chat", "messages", "pinned", group?.community?.groupId || ""],
          context?.previousPinnedMessages,
        );
      },
    });

    const date = new Date(+_message.createdOn);

    const attachment = message.attachments?.[0];
    const isImage = attachment?.messageType === 2;
    const isVideo = attachment?.messageType === 3;
    const canPin = group?.community?.ownerId === user?.id;

    const messageText = useMemo(() => {
      const _message = message.message.replace(/<br>/g, "\n");
      const parser = new DOMParser();
      const html = parser.parseFromString(_message, "text/html");
      const text = html.body.textContent || _message;

      return text;
    }, [message.message]);
    const showCopyText = message.message?.trim() !== "";

    const isDesktop = useMediaQuery(BREAKPOINTS.lg);

    const x = useMotionValue(0);
    const scale = useTransform(x, [0, 40], [0.3, 1.1]);
    const opacity = useTransform(x, [0, 40], [0, 1]);

    const handleReply = () => {
      setReply(_message);
    };

    const handlePin = async () => {
      if (
        pinnedMessagesCount &&
        pinnedMessagesCount >= 3 &&
        Boolean(message.isPinned) === false
      ) {
        toast.danger("Please unpin one of your messages");
        return;
      }

      await pin({
        groupId: group?.community?.groupId || "",
        messageId: message.id,
        isPinned: !message.isPinned,
      });
    };

    useDrag(
      ({ last, direction: [dx], offset: [ox] }) => {
        if (last) {
          if (dx > 0 && ox > 10) {
            handleReply();
          }
          x.set(0);
          return;
        }

        const withRubberband = rubberbandIfOutOfBounds(ox, 0, 40, 1);
        x.set(withRubberband);
      },
      {
        rubberband: true,
        from: [0, 0],
        bounds: { right: 25, left: 0 },
        target: messageRef,
        enabled: !isDesktop,
      },
    );

    useEffect(() => {
      if (open) {
        setTimeout(() => {
          if (!isDesktop) {
            const transformStyle: any = ref.current?.parentElement
              ?.computedStyleMap()
              .get("transform");

            ref.current?.parentElement?.style.setProperty(
              "transform",
              `translate(53px, ${
                transformStyle[0]
                  ? transformStyle[0].y.value
                  : messageRef.current?.getBoundingClientRect().top
              }px)`,
            );
          }
        }, 0);
      }
    }, [isDesktop, open]);

    return (
      <ContextMenu onOpenChange={setOpen}>
        <ContextMenuTrigger asChild>
          <div
            className={cn(
              "disable-select lg:undo-disable-select relative flex touch-pan-y items-end justify-start pl-[53px] lg:touch-auto",
              open && "bg-dark-gray/20",
              isNextSameUser ? "py-0.5" : "pb-[5px] pt-0.5",
              message.reactions?.length > 0 && "pb-[1.15rem]",
            )}
            ref={messageRef}
            id={`message-${message.id}`}
          >
            {!isNextSameUser && (
              <ProgressBarLink href={`/${message?.user?.twitterHandle}`}>
                <Avatar className="absolute bottom-[7px] left-[12px] size-[35px]">
                  <AvatarImage src={message?.user?.twitterPicture} />
                  <AvatarFallback />
                </Avatar>
              </ProgressBarLink>
            )}
            <motion.div
              className="absolute left-14 top-1/2 flex size-5 items-center justify-center rounded-full bg-dark-gray/20"
              style={{
                scale,
                opacity,
                y: "-50%",
              }}
            >
              <ReplyOutlineIcon className="size-3 text-off-white" />
            </motion.div>
            <motion.div
              className={cn(
                "relative flex min-w-0 max-w-[min(20rem,100vw-6.25rem)] flex-col rounded-r-[0.9375rem] bg-chat-bubble px-2 py-1",
                isNextSameUser ? "rounded-bl-[0.375rem]" : "rounded-bl-none",
                isPreviousSameUser
                  ? "rounded-tl-[0.375rem]"
                  : "rounded-tl-[0.9375rem]",
                attachment && message.message.trim() === "" && "pt-2",
                message.reactions?.length > 0 && "pb-3",
              )}
              style={{ x }}
            >
              {!isPreviousSameUser && (
                <div className="flex justify-between truncate text-xs font-semibold leading-5">
                  <ProgressBarLink
                    href={`/${message?.user?.twitterHandle}`}
                    style={{ color: stringToColor(_message.userId) }}
                  >
                    {message.userName}
                  </ProgressBarLink>
                </div>
              )}
              {message.reply && (
                <div className="my-0.5">
                  <Reply
                    message={message.reply}
                    onClick={() => {
                      const element = document.getElementById(
                        `message-${message.reply.id}`,
                      );

                      if (element) {
                        element.scrollIntoView({
                          behavior: "smooth",
                          block: "center",
                        });

                        const observer = new IntersectionObserver(
                          (entries) => {
                            if (entries[0].isIntersecting) {
                              element.classList.add("animate-highlight-bg");
                              setTimeout(() => {
                                element.classList.remove(
                                  "animate-highlight-bg",
                                );
                              }, 1500);

                              observer.disconnect();
                            }
                          },
                          { threshold: 1 },
                        );

                        observer.observe(element);
                      }
                    }}
                  />
                </div>
              )}
              <div className="relative whitespace-pre-wrap break-words text-sm [&_a]:text-brand-orange [&_a]:underline">
                <span
                  dangerouslySetInnerHTML={{
                    __html: message.message,
                  }}
                />
                {attachment && isImage && (
                  <ImagePreviewModal url={attachment.url}>
                    <div className="relative mt-1 aspect-square min-w-[300px] overflow-hidden rounded-[10px]">
                      <img
                        src={attachment.url}
                        alt="attachment"
                        className="absolute inset-0 h-full w-full object-cover"
                      />
                    </div>
                  </ImagePreviewModal>
                )}
                {attachment && isVideo && (
                  <div className="relative mt-1 aspect-square min-w-[300px] overflow-hidden rounded-[10px]">
                    <video
                      src={attachment.url}
                      controls
                      className="absolute inset-0 h-full w-full object-cover"
                    />
                  </div>
                )}
                {youtubeEmbedUrl && (
                  <div className="mt-1">
                    <iframe
                      src={youtubeEmbedUrl}
                      title="YouTube video player"
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                      referrerPolicy="strict-origin-when-cross-origin"
                      allowFullScreen
                      className="aspect-video w-full rounded-2xl"
                    />
                  </div>
                )}
                <span
                  className={cn(
                    "relative top-1.5 float-right ml-2 h-[19px] text-xs text-off-white opacity-50",
                  )}
                >
                  <span className="mr-0.5 whitespace-nowrap">
                    {format(date, "K:mm a")}
                  </span>
                </span>
              </div>
              {!isNextSameUser && (
                <svg
                  width="9"
                  height="20"
                  className="absolute bottom-[-0.0625rem] left-[-0.562rem] h-[1.125rem] w-[.5625rem] !text-[1rem] text-[#1A1A1A]"
                >
                  <defs>
                    <filter
                      x="-50%"
                      y="-14.7%"
                      width="200%"
                      height="141.2%"
                      filterUnits="objectBoundingBox"
                      id="messageAppendix"
                    >
                      <feOffset
                        dy="1"
                        in="SourceAlpha"
                        result="shadowOffsetOuter1"
                      ></feOffset>
                      <feGaussianBlur
                        stdDeviation="1"
                        in="shadowOffsetOuter1"
                        result="shadowBlurOuter1"
                      ></feGaussianBlur>
                      <feColorMatrix
                        values="0 0 0 0 0.0621962482 0 0 0 0 0.138574144 0 0 0 0 0.185037364 0 0 0 0.15 0"
                        in="shadowBlurOuter1"
                      ></feColorMatrix>
                    </filter>
                  </defs>
                  <g fill="none" fillRule="evenodd">
                    <path
                      d="M3 17h6V0c-.193 2.84-.876 5.767-2.05 8.782-.904 2.325-2.446 4.485-4.625 6.48A1 1 0 003 17z"
                      fill="#000"
                      filter="url(#messageAppendix)"
                    ></path>
                    <path
                      d="M3 17h6V0c-.193 2.84-.876 5.767-2.05 8.782-.904 2.325-2.446 4.485-4.625 6.48A1 1 0 003 17z"
                      fill="currentColor"
                    ></path>
                  </g>
                </svg>
              )}
              <div className="absolute -bottom-[1.15rem] right-2">
                <ReactionsList
                  reactions={message.reactions}
                  handleUnreact={() => {
                    unreactToMessage({
                      messageId: _message.id,
                      groupId: _message.groupId,
                    });
                  }}
                />
              </div>
            </motion.div>
          </div>
        </ContextMenuTrigger>
        <ContextMenuPrimitive.Portal>
          <ContextMenuPrimitive.Content
            collisionPadding={{
              top: 100,
              bottom: 140,
            }}
            className={cn(
              "z-50 min-w-[8rem] text-off-white animate-in fade-in-80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
            )}
            asChild
          >
            <div ref={ref}>
              <div className="flex items-center rounded-full bg-chat-bubble shadow-[4px_4px_4px_0px_rgba(0,0,0,0.50)]">
                {REACTIONS.map((emoji) => (
                  <ContextMenuPrimitive.Item
                    key={emoji}
                    className="cursor-pointer px-2 py-3 outline-none transition-transform duration-200 first:pl-4 last:pr-4 hover:scale-125"
                    onClick={() => {
                      reactToMessage({
                        messageId: _message.id,
                        groupId: _message.groupId,
                        reaction: emoji,
                      });
                    }}
                  >
                    <span>
                      <Emoji unified={emoji} size={28} />
                    </span>
                  </ContextMenuPrimitive.Item>
                ))}
              </div>
              <div className="flex items-start">
                <div className="mt-2 flex w-fit flex-col overflow-hidden rounded-2xl bg-chat-bubble shadow-[4px_2px_6px_0px_rgba(0,0,0,0.50)]">
                  {canPin && (
                    <ContextMenuPrimitive.Item
                      className="flex cursor-pointer select-none items-center gap-4 px-4 py-3 text-base leading-5 hover:bg-dark-bk/10"
                      onClick={handlePin}
                    >
                      {message.isPinned ? (
                        <>
                          <UnpinOutlineIcon className="size-6" />
                          <span>Unpin</span>
                        </>
                      ) : (
                        <>
                          <PinOutlineIcon className="size-6" />
                          <span>Pin</span>
                        </>
                      )}
                    </ContextMenuPrimitive.Item>
                  )}
                  <ContextMenuPrimitive.Item
                    className="flex cursor-pointer select-none items-center gap-4 px-4 py-3 text-base leading-5 outline-none hover:bg-dark-bk/10"
                    onClick={handleReply}
                  >
                    <ReplyOutlineIcon className="size-6" />
                    <span>Reply</span>
                  </ContextMenuPrimitive.Item>
                  {showCopyText && (
                    <CopyToClipboard
                      text={messageText}
                      onCopy={() => {
                        toast.green("Copied to clipboard");
                      }}
                    >
                      <ContextMenuPrimitive.Item className="flex cursor-pointer select-none items-center gap-4 px-4 py-3 text-base leading-5 outline-none hover:bg-dark-bk/10">
                        <DuplicateOutlineIcon className="size-6" />
                        <span>Copy Text</span>
                      </ContextMenuPrimitive.Item>
                    </CopyToClipboard>
                  )}
                </div>
                <ContextMenuPrimitive.Item className="flex-grow self-stretch" />
              </div>
            </div>
          </ContextMenuPrimitive.Content>
        </ContextMenuPrimitive.Portal>
      </ContextMenu>
    );
  },
);

CommunityMessage.displayName = "CommunityMessage";

export const MyCommunityMessage = memo(
  ({
    message: _message,
    isNextSameUser,
    isPreviousSameUser,
    setReply,
    scrollToBottom,
  }: MessageProps) => {
    const { user } = useUser();
    const queryClient = useQueryClient();
    const pinnedMessageId = useCommunityStore((state) => state.messageId);
    const pinnedMessagesCount = useCommunityStore(
      (state) => state.pinnedMessagesCount,
    );
    const { data: group } = useCommunity();
    const [open, setOpen] = useState(false);
    const ref = useRef<HTMLDivElement>(null);
    const messageRef = useRef<HTMLDivElement>(null);
    const isDesktop = useMediaQuery(BREAKPOINTS.lg);

    const { mutateAsync: reactToMessage } = useReactToMessage({
      onMutate: async ({ messageId, groupId, reaction }) => {
        await queryClient.cancelQueries({
          queryKey: [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId, messageId: pinnedMessageId },
          ],
        });

        const previousMessages = queryClient.getQueryData([
          "chat",
          "group-infinite-messages-with-actions",
          { groupId, messageId: pinnedMessageId },
        ]);

        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId, messageId: pinnedMessageId },
          ],
          (old: InfiniteData<ChatMessagesResponse>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                if (page.messages.find((message) => message.id === messageId)) {
                  return {
                    ...page,
                    messages: page.messages.map((message) => {
                      if (message.id === messageId) {
                        const filteredReactions = message.reactions?.filter(
                          (reaction) => reaction.userId !== user?.id,
                        );
                        return {
                          ...message,
                          reactions: [
                            ...filteredReactions,
                            {
                              id: v4(),
                              createdOn: Date.now().toString(),
                              messageId,
                              userId: user?.id ?? "",
                              reaction,
                            },
                          ],
                        };
                      }
                      return message;
                    }),
                  };
                }

                return page;
              }),
            };
          },
        );

        scrollToBottom();

        return { previousMessages };
      },
      onError(_, variables, context) {
        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId: variables.groupId, messageId: pinnedMessageId },
          ],
          context?.previousMessages,
        );
      },
    });

    const { mutateAsync: unreactToMessage } = useUnreactToMessage({
      onMutate: async ({ messageId, groupId }) => {
        await queryClient.cancelQueries({
          queryKey: [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId, messageId: pinnedMessageId },
          ],
        });

        const previousMessages = queryClient.getQueryData([
          "chat",
          "group-infinite-messages-with-actions",
          { groupId, messageId: pinnedMessageId },
        ]);

        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId, messageId: pinnedMessageId },
          ],
          (old: InfiniteData<ChatMessagesResponse>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                if (page.messages.find((message) => message.id === messageId)) {
                  return {
                    ...page,
                    messages: page.messages.map((message) => {
                      if (message.id === messageId) {
                        const filteredReactions = message.reactions?.filter(
                          (reaction) => reaction.userId !== user?.id,
                        );
                        return {
                          ...message,
                          reactions: filteredReactions,
                        };
                      }
                      return message;
                    }),
                  };
                }

                return page;
              }),
            };
          },
        );

        return { previousMessages };
      },
      onError(_, variables, context) {
        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId: variables.groupId, messageId: pinnedMessageId },
          ],
          context?.previousMessages,
        );
      },
    });

    const { message, youtubeEmbedUrl } = useMemo(() => {
      const [content, , youtubeEmbedUrl] = checkContent({
        content: _message.message ?? "",
        truncate: false,
      });

      return {
        message: {
          ..._message,
          message: content,
        },
        youtubeEmbedUrl,
      };
    }, [_message]);

    const { mutateAsync: pin } = usePinMessage({
      onMutate: async ({ groupId, messageId, isPinned }) => {
        const previousMessages = queryClient.getQueryData([
          "chat",
          "group-infinite-messages-with-actions",
          { groupId, messageId: pinnedMessageId },
        ]);

        const previousPinnedMessages = queryClient.getQueryData([
          "chat",
          "messages",
          "pinned",
          groupId,
        ]);

        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId, messageId: pinnedMessageId },
          ],
          (old: InfiniteData<ChatMessagesResponse>) => {
            if (!old) return old;
            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  messages: page.messages.map((message) =>
                    message.id === messageId
                      ? {
                          ...message,
                          isPinned,
                        }
                      : message,
                  ),
                };
              }),
            };
          },
        );

        queryClient.setQueryData(
          ["chat", "messages", "pinned", groupId],
          (old: ChatPinnedMessagesResponse) => {
            if (!old) return old;

            if (isPinned) {
              return {
                ...old,
                messages: [...old.messages, { ..._message, isPinned }].sort(
                  (a, b) => {
                    return a.createdOn > b.createdOn ? -1 : 1;
                  },
                ),
              };
            } else {
              return {
                ...old,
                messages: old.messages.filter((m) => m.id !== messageId),
              };
            }
          },
        );

        return { previousMessages, previousPinnedMessages };
      },
      onError: (_, variables, context) => {
        queryClient.setQueryData(
          [
            "chat",
            "group-infinite-messages-with-actions",
            { groupId: variables.groupId, messageId: pinnedMessageId },
          ],
          context?.previousMessages,
        );
        queryClient.setQueryData(
          ["chat", "messages", "pinned", group?.community?.groupId || ""],
          context?.previousPinnedMessages,
        );
      },
    });

    const messageText = useMemo(() => {
      const _message = message.message.replace(/<br>/g, "\n");
      const parser = new DOMParser();
      const html = parser.parseFromString(_message, "text/html");
      const text = html.body.textContent || _message;

      return text;
    }, [message.message]);
    const showCopyText = message.message?.trim() !== "";

    const date = new Date(+_message.createdOn);
    const attachment = message.attachments?.[0];
    const isImage = attachment?.messageType === 2;
    const isVideo = attachment?.messageType === 3;
    const canPin = group?.community?.ownerId === user?.id;

    const x = useMotionValue(0);
    const scale = useTransform(x, [0, 40], [0.5, 1.3]);
    const opacity = useTransform(x, [0, 40], [0, 1]);

    const handleReply = () => {
      setReply(_message);
    };

    const handlePin = async () => {
      if (
        pinnedMessagesCount &&
        pinnedMessagesCount >= 3 &&
        Boolean(message.isPinned) === false
      ) {
        toast.danger("Please unpin one of your messages");
        return;
      }

      await pin({
        groupId: group?.community?.groupId || "",
        messageId: message.id,
        isPinned: !message.isPinned,
      });
    };

    useDrag(
      ({ last, direction: [dx], offset: [ox] }) => {
        if (last) {
          if (dx > 0 && ox > 10) {
            handleReply();
          }
          x.set(0);
          return;
        }

        const withRubberband = rubberbandIfOutOfBounds(ox, 0, 40, 1);
        x.set(withRubberband);
      },
      {
        rubberband: true,
        from: [0, 0],
        bounds: { right: 25, left: 0 },
        target: messageRef,
        enabled: !isDesktop,
      },
    );

    useEffect(() => {
      if (open) {
        setTimeout(() => {
          if (!isDesktop) {
            const transformStyle: any = ref.current?.parentElement
              ?.computedStyleMap()
              .get("transform");

            ref.current?.parentElement?.style.setProperty(
              "transform",
              `translate(53px, ${
                transformStyle[0]
                  ? transformStyle[0].y.value
                  : messageRef.current?.getBoundingClientRect().top
              }px)`,
            );
          }
        }, 0);
      }
    }, [isDesktop, open]);

    return (
      <ContextMenu onOpenChange={setOpen}>
        <ContextMenuTrigger asChild>
          <div
            className={cn(
              "disable-select lg:undo-disable-select relative flex touch-pan-y items-end justify-end gap-2 px-3 py-0.5 lg:touch-auto",
              open && "bg-dark-gray/20",
              message.reactions?.length > 0 && "pb-[1.15rem]",
            )}
            ref={messageRef}
            id={`message-${message.id}`}
          >
            <motion.div
              className="absolute left-0 top-1/2 flex size-5 items-center justify-center rounded-full bg-dark-gray/20"
              style={{
                scale,
                opacity,
                y: "-50%",
                x,
              }}
            >
              <ReplyOutlineIcon className="size-3 text-off-white" />
            </motion.div>
            <motion.div
              className={cn(
                "relative flex min-w-0 max-w-[min(20rem,100vw-6.25rem)] flex-col rounded-[0.9375rem] bg-brand-orange px-2 py-1 xl:max-w-[min(25rem,100vw-6.25rem)]",
                isNextSameUser ? "rounded-br-[0.375rem]" : "rounded-br-none",
                isPreviousSameUser
                  ? "rounded-tr-[0.375rem]"
                  : "rounded-tr-[0.9375rem]",
                attachment && message.message.trim() === "" && "pt-2",
                message.reactions?.length > 0 && "pb-3",
              )}
              style={{ x }}
            >
              {message.reply && (
                <div className="my-0.5">
                  <Reply
                    message={message.reply}
                    variant="mine"
                    onClick={() => {
                      const element = document.getElementById(
                        `message-${message.reply.id}`,
                      );

                      if (element) {
                        element.scrollIntoView({
                          behavior: "smooth",
                          block: "center",
                        });

                        const observer = new IntersectionObserver(
                          (entries) => {
                            if (entries[0].isIntersecting) {
                              element.classList.add("animate-highlight-bg");
                              setTimeout(() => {
                                element.classList.remove(
                                  "animate-highlight-bg",
                                );
                              }, 1500);

                              observer.disconnect();
                            }
                          },
                          { threshold: 1 },
                        );

                        observer.observe(element);
                      }
                    }}
                  />
                </div>
              )}
              <div className="relative whitespace-pre-wrap break-words text-sm [&_a]:underline">
                <span
                  dangerouslySetInnerHTML={{
                    __html: message.message,
                  }}
                />
                {attachment && isImage && (
                  <ImagePreviewModal url={attachment.url}>
                    <div className="relative mt-1 size-[280px] overflow-hidden rounded-[10px]">
                      <img
                        src={attachment.url}
                        alt="attachment"
                        className="absolute inset-0 h-full w-full object-cover"
                      />
                    </div>
                  </ImagePreviewModal>
                )}
                {attachment && isVideo && (
                  <div className="relative mt-1 aspect-square min-w-[300px] overflow-hidden rounded-[10px]">
                    <video
                      src={attachment.url}
                      controls
                      className="absolute inset-0 h-full w-full object-cover"
                    />
                  </div>
                )}
                {youtubeEmbedUrl && (
                  <div className="mt-1">
                    <iframe
                      src={youtubeEmbedUrl}
                      title="YouTube video player"
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                      referrerPolicy="strict-origin-when-cross-origin"
                      allowFullScreen
                      className="aspect-video w-full rounded-2xl"
                    />
                  </div>
                )}
                <span
                  className={cn(
                    "relative top-1.5 float-right ml-2 h-[19px] text-xs text-off-white opacity-50",
                  )}
                >
                  <span className="mr-0.5 whitespace-nowrap">
                    {format(date, "K:mm a")}
                  </span>
                </span>
              </div>
              {!isNextSameUser && (
                <svg
                  width="9"
                  height="20"
                  className="absolute bottom-[-0.0625rem] right-[-0.551rem] h-[1.125rem] w-[.5625rem] !text-[1rem] text-brand-orange"
                >
                  <defs>
                    <filter
                      x="-50%"
                      y="-14.7%"
                      width="200%"
                      height="141.2%"
                      filterUnits="objectBoundingBox"
                      id="messageAppendix"
                    >
                      <feOffset
                        dy="1"
                        in="SourceAlpha"
                        result="shadowOffsetOuter1"
                      ></feOffset>
                      <feGaussianBlur
                        stdDeviation="1"
                        in="shadowOffsetOuter1"
                        result="shadowBlurOuter1"
                      ></feGaussianBlur>
                      <feColorMatrix
                        values="0 0 0 0 0.0621962482 0 0 0 0 0.138574144 0 0 0 0 0.185037364 0 0 0 0.15 0"
                        in="shadowBlurOuter1"
                      ></feColorMatrix>
                    </filter>
                  </defs>
                  <g fill="none" fillRule="evenodd">
                    <path
                      d="M6 17H0V0c.193 2.84.876 5.767 2.05 8.782.904 2.325 2.446 4.485 4.625 6.48A1 1 0 016 17z"
                      fill="#000"
                      filter="url(#messageAppendix)"
                    ></path>
                    <path
                      d="M6 17H0V0c.193 2.84.876 5.767 2.05 8.782.904 2.325 2.446 4.485 4.625 6.48A1 1 0 016 17z"
                      fill="currentColor"
                    ></path>
                  </g>
                </svg>
              )}
              <div className="absolute -bottom-[1.15rem] right-2">
                <ReactionsList
                  reactions={message.reactions}
                  handleUnreact={() => {
                    unreactToMessage({
                      messageId: _message.id,
                      groupId: _message.groupId,
                    });
                  }}
                />
              </div>
            </motion.div>
          </div>
        </ContextMenuTrigger>
        <ContextMenuPrimitive.Portal>
          <ContextMenuPrimitive.Content
            collisionPadding={{
              top: 100,
              bottom: 140,
            }}
            className={cn(
              "z-50 min-w-[8rem] text-off-white animate-in fade-in-80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
            )}
            asChild
          >
            <div ref={ref}>
              <div className="flex items-center rounded-full bg-chat-bubble shadow-[4px_4px_4px_0px_rgba(0,0,0,0.50)]">
                {REACTIONS.map((emoji) => (
                  <ContextMenuPrimitive.Item
                    key={emoji}
                    className="cursor-pointer px-2 py-3 outline-none transition-transform duration-200 first:pl-4 last:pr-4 hover:scale-125"
                    onClick={() => {
                      reactToMessage({
                        messageId: _message.id,
                        groupId: _message.groupId,
                        reaction: emoji,
                      });
                    }}
                  >
                    <span>
                      <Emoji unified={emoji} size={28} />
                    </span>
                  </ContextMenuPrimitive.Item>
                ))}
              </div>
              <div className="flex items-start">
                <div className="mt-2 flex w-fit flex-shrink-0 flex-col overflow-hidden rounded-2xl bg-chat-bubble">
                  {canPin && (
                    <ContextMenuPrimitive.Item
                      className="flex cursor-pointer select-none items-center gap-4 px-4 py-3 text-base leading-5 outline-none hover:bg-dark-bk/10"
                      onClick={handlePin}
                    >
                      {message.isPinned ? (
                        <>
                          <UnpinOutlineIcon className="size-6" />
                          <span>Unpin</span>
                        </>
                      ) : (
                        <>
                          <PinOutlineIcon className="size-6" />
                          <span>Pin</span>
                        </>
                      )}
                    </ContextMenuPrimitive.Item>
                  )}
                  <ContextMenuPrimitive.Item
                    className="flex cursor-pointer select-none items-center gap-4 px-4 py-3 text-base leading-5 outline-none hover:bg-dark-bk/10"
                    onClick={handleReply}
                  >
                    <ReplyOutlineIcon className="size-6" />
                    <span>Reply</span>
                  </ContextMenuPrimitive.Item>
                  {showCopyText && (
                    <CopyToClipboard
                      text={messageText}
                      onCopy={() => {
                        toast.green("Copied to clipboard");
                      }}
                    >
                      <ContextMenuPrimitive.Item className="flex cursor-pointer select-none items-center gap-4 px-4 py-3 text-base leading-5 outline-none hover:bg-dark-bk/10">
                        <DuplicateOutlineIcon className="size-6" />
                        <span>Copy Text</span>
                      </ContextMenuPrimitive.Item>
                    </CopyToClipboard>
                  )}
                </div>
                <ContextMenuPrimitive.Item className="flex-grow self-stretch" />
              </div>
            </div>
          </ContextMenuPrimitive.Content>
        </ContextMenuPrimitive.Portal>
      </ContextMenu>
    );
  },
);

MyCommunityMessage.displayName = "MyCommunityMessage";

const ReactionsList = memo(
  ({
    reactions: _reactions = [],
    handleUnreact,
  }: {
    reactions: Reaction[];
    handleUnreact?: () => void;
  }) => {
    const { user: me } = useUser();

    const sortReactions = (a: Reaction, b: Reaction) => {
      if (a.userId === me?.id) {
        return -1;
      }
      if (b.userId === me?.id) {
        return 1;
      }
      return 0;
    };

    const sortedReactions = _reactions.sort(sortReactions);

    const { data: users, isLoading: isLoadingUsers } = useQueries({
      queries: sortedReactions.map((reactions) => ({
        queryKey: ["user", "id", reactions.userId],
        queryFn: () => {
          return getUserById({ userId: reactions.userId });
        },
      })),
      combine: (results) => {
        return {
          data: results
            .map((result) => result.data?.user)
            .reduce(
              (acc, curr) => {
                if (curr && !acc[curr.id]) {
                  acc[curr.id] = curr;
                }
                return acc;
              },
              {} as Record<string, User>,
            ),
          isLoading: results.some((result) => result.isLoading),
        };
      },
    });

    const groupedReactions = useMemo(() => {
      const groupedReactions = sortedReactions.reduce(
        (acc, reaction) => {
          if (!acc[reaction.reaction]) {
            acc[reaction.reaction] = [reaction];
          } else {
            acc[reaction.reaction] = [...acc[reaction.reaction], reaction].sort(
              sortReactions,
            );
          }
          return acc;
        },
        {} as Record<string, Reaction[]>,
      );
      return Object.entries(groupedReactions);
    }, [sortedReactions]);

    if (!_reactions || _reactions.length === 0) return null;

    return (
      <Popover modal>
        <PopoverTrigger>
          <div className="flex items-center gap-1.5 rounded-full border border-dark-bk bg-chat-bubble px-2 py-1">
            {groupedReactions.map(([reaction]) => (
              <Emoji unified={reaction} size={14} key={reaction} />
            ))}
            <span className="text-xs text-gray-text">
              {abbreviateNumber(_reactions.length)}
            </span>
          </div>
        </PopoverTrigger>
        <PopoverContent
          align="end"
          className="w-[calc(100vw-3rem)] p-0 pt-2 sm:w-80"
          sideOffset={4}
          alignOffset={-12}
          collisionPadding={{
            right: 12,
            left: 12,
          }}
        >
          <Tabs defaultValue="reactions">
            <TabsList className="w-full">
              <TabsTrigger value="reactions" className="text-off-white sm:px-5">
                Reactions
                <span className="ml-2 text-gray-text">
                  {abbreviateNumber(_reactions.length)}
                </span>
              </TabsTrigger>
              {groupedReactions.map(([reaction, reactions]) => (
                <TabsTrigger
                  value={reaction}
                  key={reaction}
                  className="text-off-white sm:px-5"
                >
                  <Emoji unified={reaction} size={14} />
                  <span className="ml-2 text-gray-text">
                    {abbreviateNumber(reactions.length)}
                  </span>
                </TabsTrigger>
              ))}
            </TabsList>
            <TabsContent value="reactions" className="max-h-72 overflow-y-auto">
              <div className="flex flex-col gap-2 py-2">
                {!isLoadingUsers &&
                  sortedReactions.map((reaction, index) => {
                    const user = users[reaction.userId];
                    const isMe = user.id === me?.id;

                    return (
                      <UserListItem
                        key={reaction.id + index}
                        reaction={reaction}
                        user={user}
                        isMe={isMe}
                        onClick={isMe ? handleUnreact : undefined}
                      />
                    );
                  })}
              </div>
            </TabsContent>
            {groupedReactions.map(([reaction, reactions]) => (
              <TabsContent
                value={reaction}
                className="max-h-72 overflow-y-auto"
                key={reaction + "-content"}
              >
                <div className="flex flex-col gap-2 py-2">
                  {!isLoadingUsers &&
                    reactions.map((reaction, index) => {
                      const user = users[reaction.userId];
                      const isMe = user.id === me?.id;

                      return (
                        <UserListItem
                          key={reaction.id + index}
                          reaction={reaction}
                          user={user}
                          isMe={isMe}
                          onClick={isMe ? handleUnreact : undefined}
                        />
                      );
                    })}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </PopoverContent>
      </Popover>
    );
  },
);

ReactionsList.displayName = "ReactionsList";

const UserListItem = ({
  user,
  reaction,
  isMe,
  onClick,
}: {
  user: User;
  reaction: Reaction;
  isMe?: boolean;
  onClick?: () => void;
}) => {
  return (
    <div
      className="flex items-center justify-between gap-2 px-4 py-1"
      role={onClick ? "button" : undefined}
      onClick={onClick}
    >
      <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
        <Avatar className="size-[42px]">
          <AvatarImage src={user.twitterPicture} />
          <AvatarFallback />
        </Avatar>
        <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
          <h4 className="truncate text-off-white">
            {isMe ? "You" : user.twitterName}
          </h4>
          {isMe && (
            <p className="text-xs leading-none text-gray-text">
              Click to remove
            </p>
          )}
        </div>
      </div>
      <Emoji unified={reaction.reaction} size={20} />
    </div>
  );
};

const DEFAULT_COLOR_LIST = [
  "#D53535",
  "#EB540A",
  "#B6B840",
  "#40B877",
  "#40B8B8",
  "#0059E0",
  "#8340B8",
  "#B84081",
];

const REACTIONS = [
  "1f44d",
  "2694-fe0f",
  "1f525",
  "1f44f",
  "1f92f",
  "1f923",
  "1f44e",
];

function stringToColor(str: string, colorList: string[] = DEFAULT_COLOR_LIST) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }

  const index = Math.abs(hash) % colorList.length;
  return colorList[index];
}
