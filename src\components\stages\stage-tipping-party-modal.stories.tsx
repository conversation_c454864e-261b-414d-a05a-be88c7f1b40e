import React from "react";

import { LiveKitRoom } from "@livekit/components-react";
import { Toaster } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";

import { Role, ROLES } from "./constants";
import { StageTippingPartyModal } from "./stage-tipping-party-modal";

const mockParticipant = {
  id: "1",
  name: "<PERSON>",
  avatar: "https://randomuser.me/api/portraits/women/1.jpg",
  username: "alice",
  role: ROLES.LISTENER as Role,
};

const mockSpeaker = {
  userId: "2",
  name: "<PERSON>",
  avatar: "https://randomuser.me/api/portraits/men/2.jpg",
  username: "bob",
  role: ROLES.SPEAKER as Role,
};

const mockCurrencies = [
  { symbol: "ARENA", balance: "1000", isToken: false, systemRate: "1" },
  { symbol: "AVAX", balance: "5", isToken: true, systemRate: "15" },
];

const mockToken = {
  symbol: "ARENA",
  balance: "1000",
  isToken: false,
  systemRate: "1",
};

const mockStageStoreSnapshot = {
  id: "mock-stage-id",
  participants: [mockParticipant],
  speakers: [mockSpeaker],
  sortedCurrencies: mockCurrencies,
  token: mockToken,
};

const mockReceivedChatMessage = {
  id: "msg-1",
  timestamp: Date.now(),
  message: JSON.stringify({ type: "text", message: "Hello!" }),
  severity: undefined,
};

const mockDataChannelsContext = {
  chat: {
    send: async () => mockReceivedChatMessage,
    update: async () => ({
      message: "Hello!",
      editTimestamp: Date.now(),
      id: "msg-1",
      timestamp: Date.now(),
    }),
    chatMessages: [mockReceivedChatMessage],
    isSending: false,
  },
  sendTip: () => {},
  sendEmote: () => {},
  sendInvalidateStageInfo: () => {},
  sendMuteMic: () => {},
};

export default {
  title: "Modals/StageTippingPartyModal",
  component: StageTippingPartyModal,
  decorators: [
    (Story: React.FC) => (
      <LiveKitRoom
        token="dummy-token"
        serverUrl="wss://dummy-url"
        connect={false}
      >
        <Story />
        <Toaster position="top-center" />
      </LiveKitRoom>
    ),
  ],
};

export const Default = () => (
  <StageTippingPartyModal
    dataChannelsContext={mockDataChannelsContext}
    stageStoreSnapshot={mockStageStoreSnapshot}
    user={{ id: "mock-user-id" }}
  >
    <Button>Open Stage Tipping Party Modal</Button>
  </StageTippingPartyModal>
);
