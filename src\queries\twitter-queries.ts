import { UndefinedInitialDataOptions, useQuery } from "@tanstack/react-query";

import { getPrivateKey } from "@/api/client/twitter";

type PrivateKeyResponse =
  ReturnType<typeof getPrivateKey> extends Promise<infer T>
    ? T
    : ReturnType<typeof getPrivateKey>;

type PrivateKeysQueryOptions = Omit<
  UndefinedInitialDataOptions<PrivateKeyResponse>,
  "queryKey"
>;

export const usePrivateKeys = (options?: PrivateKeysQueryOptions) => {
  return useQuery({
    queryKey: ["twitter", "export"],
    queryFn: getPrivateKey,
    ...options,
  });
};
