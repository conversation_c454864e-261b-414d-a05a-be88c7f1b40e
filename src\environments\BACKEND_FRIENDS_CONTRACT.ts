export const BACKEND_FRIENDS_CONTRACT = {
  addressMainnet: "0xC605C2cf66ee98eA925B1bb4FeA584b71C00cC4C",
  addressTestnet: "0xBc317c5270e2BA49C0Ac37bfC2a5A30928bCe689",
};

export const BACKEND_FRIENDS_CONTRACT_ABI = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "returnData",
        type: "bytes",
      },
    ],
    type: "error",
    name: "SendToProtocolFailed",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "returnData",
        type: "bytes",
      },
    ],
    type: "error",
    name: "SendToSubjectFailed",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newFeeDestination",
        type: "address",
        indexed: false,
      },
      {
        internalType: "address",
        name: "previousFeeDestination",
        type: "address",
        indexed: false,
      },
    ],
    type: "event",
    name: "FeeDestination",
    anonymous: false,
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newFeeDestination2",
        type: "address",
        indexed: false,
      },
      {
        internalType: "address",
        name: "previousFeeDestination2",
        type: "address",
        indexed: false,
      },
    ],
    type: "event",
    name: "FeeDestination2",
    anonymous: false,
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "user",
        type: "address",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "individualFeePercent",
        type: "uint256",
        indexed: false,
      },
    ],
    type: "event",
    name: "IndividualSubjectFeeSet",
    anonymous: false,
  },
  {
    inputs: [
      {
        internalType: "uint8",
        name: "version",
        type: "uint8",
        indexed: false,
      },
    ],
    type: "event",
    name: "Initialized",
    anonymous: false,
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "previousOwner",
        type: "address",
        indexed: true,
      },
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
        indexed: true,
      },
    ],
    type: "event",
    name: "OwnershipTransferred",
    anonymous: false,
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "newProtocolFeePercen",
        type: "uint256",
        indexed: false,
      },
    ],
    type: "event",
    name: "ProtocolFeePercent",
    anonymous: false,
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "newReferralFeePercent",
        type: "uint256",
        indexed: false,
      },
    ],
    type: "event",
    name: "ReferralFeePercent",
    anonymous: false,
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "user",
        type: "address",
        indexed: false,
      },
      {
        internalType: "address",
        name: "referrer",
        type: "address",
        indexed: false,
      },
    ],
    type: "event",
    name: "ReferralSet",
    anonymous: false,
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "user",
        type: "address",
        indexed: false,
      },
      {
        internalType: "address",
        name: "signer",
        type: "address",
        indexed: false,
      },
      {
        internalType: "address",
        name: "previousSigner",
        type: "address",
        indexed: false,
      },
    ],
    type: "event",
    name: "SignerSet",
    anonymous: false,
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "newSubjectFeePercent",
        type: "uint256",
        indexed: false,
      },
    ],
    type: "event",
    name: "SubjectFeePercent",
    anonymous: false,
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "trader",
        type: "address",
        indexed: false,
      },
      {
        internalType: "address",
        name: "subject",
        type: "address",
        indexed: false,
      },
      {
        internalType: "bool",
        name: "isBuy",
        type: "bool",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "shareAmount",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "protocolAmount",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "subjectAmount",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "referralAmount",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "supply",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "buyPrice",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "myShares",
        type: "uint256",
        indexed: false,
      },
    ],
    type: "event",
    name: "Trade",
    anonymous: false,
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "trader",
        type: "address",
        indexed: false,
      },
      {
        internalType: "address",
        name: "subject",
        type: "address",
        indexed: false,
      },
      {
        internalType: "bool",
        name: "isBuy",
        type: "bool",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "shareAmount",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "protocolAmount",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "subjectAmount",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "referralAmount",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "fractionalSupply",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "buyPrice",
        type: "uint256",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "myFractionalShares",
        type: "uint256",
        indexed: false,
      },
    ],
    type: "event",
    name: "TradeFractionalShares",
    anonymous: false,
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
        indexed: false,
      },
      {
        internalType: "address",
        name: "from",
        type: "address",
        indexed: false,
      },
      {
        internalType: "address",
        name: "to",
        type: "address",
        indexed: false,
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
        indexed: false,
      },
    ],
    type: "event",
    name: "TransferFractionalShares",
    anonymous: false,
  },
  {
    inputs: [],
    stateMutability: "view",
    type: "function",
    name: "TICKET_TO_FRACTION_SCALER",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "allowedTokens",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "payable",
    type: "function",
    name: "buyFractionalShares",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "referrer",
        type: "address",
      },
    ],
    stateMutability: "payable",
    type: "function",
    name: "buyFractionalSharesWithReferrerForUser",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "payable",
    type: "function",
    name: "buySharesForUser",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "referrer",
        type: "address",
      },
    ],
    stateMutability: "payable",
    type: "function",
    name: "buySharesWithReferrerForUser",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "convertRemainingSharesToFractionalShares",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "subject",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "convertSharesToFractionalShares",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "fractionalSharesBalance",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "fractionalSharesSupply",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getBuyPrice",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getBuyPriceAfterFee",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getBuyPriceForFractionalShares",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getBuyPriceForFractionalSharesAfterFee",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getMyFractionalShares",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getMyShares",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getMyShares",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "subject",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "supply",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getPrice",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "supply",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getPrice",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "supply",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getPriceForFractionalShares",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getReferrer",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getSellPrice",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getSellPriceAfterFee",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getSellPriceForFractionalShares",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getSellPriceForFractionalSharesAfterFee",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getSharesSupply",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "subject",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getSubjectFeePercent",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getSubjectTvl",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "getTotalFractionalSupply",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [],
    stateMutability: "view",
    type: "function",
    name: "initialPrice",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "function",
    name: "initialize",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newSigner",
        type: "address",
      },
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "migrateSigner",
  },
  {
    inputs: [],
    stateMutability: "view",
    type: "function",
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
  },
  {
    inputs: [],
    stateMutability: "view",
    type: "function",
    name: "paused",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "pendingTokenWithdrawals",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "pendingWithdrawals",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [],
    stateMutability: "view",
    type: "function",
    name: "protocolFeeDestination",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
  },
  {
    inputs: [],
    stateMutability: "view",
    type: "function",
    name: "protocolFeeDestination2",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
  },
  {
    inputs: [],
    stateMutability: "view",
    type: "function",
    name: "protocolFeePercent",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [],
    stateMutability: "view",
    type: "function",
    name: "referralFeePercent",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "function",
    name: "renounceOwnership",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "revenueShare",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "payable",
    type: "function",
    name: "sellFractionalShares",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "referrer",
        type: "address",
      },
    ],
    stateMutability: "payable",
    type: "function",
    name: "sellFractionalSharesWithReferrerForUser",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "payable",
    type: "function",
    name: "sellSharesForUser",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "referrer",
        type: "address",
      },
    ],
    stateMutability: "payable",
    type: "function",
    name: "sellSharesWithReferrerForUser",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_feeDestination",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "setFeeDestination",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_feeDestination2",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "setFeeDestination2",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "feeAmountInPercent",
        type: "uint256",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "setIndividualSubjectFee",
  },
  {
    inputs: [
      {
        internalType: "bool",
        name: "_paused",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "setPaused",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "_feePercent",
        type: "uint256",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "setProtocolFeePercent",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "_feePercent",
        type: "uint256",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "setReferralFeePercent",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "_feePercent",
        type: "uint256",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "setSubjectFeePercent",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "_tester",
        type: "address",
      },
      {
        internalType: "bool",
        name: "value",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "setTester",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "shareholders",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "sharesBalance",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "sharesSupply",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [],
    stateMutability: "view",
    type: "function",
    name: "subjectFeePercent",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "subjectToFeePercent",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "subscribers",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [],
    stateMutability: "view",
    type: "function",
    name: "subscriptionDuration",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "subscriptionPrice",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "subscriptionTokenAddress",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "subscriptionsEnabled",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "testers",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "sharesSubject",
        type: "address",
      },
      {
        internalType: "address",
        name: "from",
        type: "address",
      },
      {
        internalType: "address",
        name: "to",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "transferFractionalShares",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "transferOwnership",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "user",
        type: "address",
      },
      {
        internalType: "address",
        name: "referrer",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "updateReferrer",
  },
  {
    inputs: [
      {
        internalType: "address[]",
        name: "users",
        type: "address[]",
      },
      {
        internalType: "address[]",
        name: "referrers",
        type: "address[]",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
    name: "updateReferrers",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "userToReferrer",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "userToSigner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "weightA",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "weightB",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "weightC",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
    name: "weightD",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
  },
  {
    inputs: [],
    stateMutability: "payable",
    type: "receive",
  },
];

export const ERC20_CONTRACT_ABI = [
  {
    constant: true,
    inputs: [],
    name: "name",
    outputs: [
      {
        name: "",
        type: "string",
      },
    ],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  {
    constant: false,
    inputs: [
      {
        name: "_spender",
        type: "address",
      },
      {
        name: "_value",
        type: "uint256",
      },
    ],
    name: "approve",
    outputs: [
      {
        name: "",
        type: "bool",
      },
    ],
    payable: false,
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    constant: true,
    inputs: [],
    name: "totalSupply",
    outputs: [
      {
        name: "",
        type: "uint256",
      },
    ],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  {
    constant: false,
    inputs: [
      {
        name: "_from",
        type: "address",
      },
      {
        name: "_to",
        type: "address",
      },
      {
        name: "_value",
        type: "uint256",
      },
    ],
    name: "transferFrom",
    outputs: [
      {
        name: "",
        type: "bool",
      },
    ],
    payable: false,
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    constant: true,
    inputs: [],
    name: "decimals",
    outputs: [
      {
        name: "",
        type: "uint8",
      },
    ],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  {
    constant: true,
    inputs: [
      {
        name: "_owner",
        type: "address",
      },
    ],
    name: "balanceOf",
    outputs: [
      {
        name: "balance",
        type: "uint256",
      },
    ],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  {
    constant: true,
    inputs: [],
    name: "symbol",
    outputs: [
      {
        name: "",
        type: "string",
      },
    ],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  {
    constant: false,
    inputs: [
      {
        name: "_to",
        type: "address",
      },
      {
        name: "_value",
        type: "uint256",
      },
    ],
    name: "transfer",
    outputs: [
      {
        name: "",
        type: "bool",
      },
    ],
    payable: false,
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    constant: true,
    inputs: [
      {
        name: "_owner",
        type: "address",
      },
      {
        name: "_spender",
        type: "address",
      },
    ],
    name: "allowance",
    outputs: [
      {
        name: "",
        type: "uint256",
      },
    ],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  {
    payable: true,
    stateMutability: "payable",
    type: "fallback",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        name: "owner",
        type: "address",
      },
      {
        indexed: true,
        name: "spender",
        type: "address",
      },
      {
        indexed: false,
        name: "value",
        type: "uint256",
      },
    ],
    name: "Approval",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        name: "from",
        type: "address",
      },
      {
        indexed: true,
        name: "to",
        type: "address",
      },
      {
        indexed: false,
        name: "value",
        type: "uint256",
      },
    ],
    name: "Transfer",
    type: "event",
  },
] as const;
