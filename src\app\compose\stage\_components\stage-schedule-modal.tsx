"use client";

import { useState } from "react";

import { Time } from "@internationalized/date";
import {
  endOfYesterday,
  format,
  isThisYear,
  parseISO,
  roundToNearestHours,
  set,
} from "date-fns";
import {
  DateInput,
  DateSegment,
  TimeField,
  TimeValue,
} from "react-aria-components";

import {
  ArrowBackOutlineIcon,
  CalendarOutlineIcon,
  ClockOutlineIcon,
} from "@/components/icons";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/utils";
import { IS_IOS, IS_MOBILE } from "@/utils/window-environment";

export function StageScheduleModal({
  children,
  scheduledDate,
  setScheduledDate,
}: {
  children: React.ReactNode;
  scheduledDate: string | null;
  setScheduledDate: (date: string | null) => void;
}) {
  const today = roundToNearestHours(new Date(), {
    roundingMethod: "ceil",
  });
  const [open, setOpen] = useState(false);
  const [date, setDate] = useState<Date>(() => {
    if (scheduledDate) {
      return parseISO(scheduledDate);
    }
    return today;
  });
  const [time, setTime] = useState<TimeValue | null>(() => {
    if (scheduledDate) {
      const scheduledDateTime = parseISO(scheduledDate);
      return new Time(
        scheduledDateTime.getHours(),
        scheduledDateTime.getMinutes(),
      );
    }
    return new Time(today.getHours(), today.getMinutes());
  });
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const todayDate = format(today, "yyyy-MM-dd");

  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (!isOpen) {
      if (scheduledDate) {
        const scheduledDateTime = parseISO(scheduledDate);
        setDate(scheduledDateTime);
        setTime(
          new Time(
            scheduledDateTime.getHours(),
            scheduledDateTime.getMinutes(),
          ),
        );
      } else {
        setDate(today);
        setTime(new Time(today.getHours(), today.getMinutes()));
      }
    }
  };

  const handleSchedule = () => {
    if (!date || !time) return;

    const newDateTime = set(date, {
      hours: time?.hour,
      minutes: time?.minute,
    });

    if (newDateTime < new Date()) {
      toast.danger("Please select a future date and time");
      return;
    }

    setScheduledDate(newDateTime.toISOString());
    setOpen(false);
  };

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <DialogContent className="max-w-md">
          <ScheduleModalContent
            date={date}
            setDate={setDate}
            time={time}
            setTime={setTime}
            onClose={() => handleOpenChange(false)}
            onSchedule={handleSchedule}
            todayDate={todayDate}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={handleOpenChange} dismissible={false}>
      <DrawerTrigger asChild>{children}</DrawerTrigger>
      <DrawerContent className="justify-start gap-8 text-left">
        <ScheduleModalContent
          date={date}
          setDate={setDate}
          time={time}
          setTime={setTime}
          onClose={() => handleOpenChange(false)}
          onSchedule={handleSchedule}
          todayDate={todayDate}
        />
      </DrawerContent>
    </Drawer>
  );
}

function ScheduleModalContent({
  date,
  setDate,
  time,
  setTime,
  onClose,
  onSchedule,
  todayDate,
}: {
  date: Date;
  setDate: (date: Date) => void;
  time: TimeValue | null;
  setTime: (time: TimeValue | null) => void;
  onClose: () => void;
  onSchedule: () => void;
  todayDate: string;
}) {
  return (
    <>
      <div className="flex items-center gap-3">
        <button className="p-1" onClick={onClose}>
          <ArrowBackOutlineIcon className="size-5 text-off-white" />
        </button>
        <h3 className="text-base font-semibold leading-[22px] text-off-white">
          Schedule your Stage
        </h3>
      </div>
      <div className="flex flex-col gap-3">
        {IS_MOBILE ? (
          <>
            <Input
              type="date"
              value={format(date, "yyyy-MM-dd")}
              min={todayDate}
              onChange={(e) => {
                const [year, month, day] = e.target.value.split("-");
                setDate(
                  new Date(Number(year), Number(month) - 1, Number(day), 12),
                );
              }}
              className={cn(
                "time-input-mobile rounded-full px-4 py-3 ps-12 text-left",
                !IS_IOS && "time-input",
              )}
            />
            <Input
              type="time"
              value={`${time?.hour.toString().padStart(2, "0")}:${time?.minute.toString().padStart(2, "0")}`}
              onChange={(e) => {
                const [hours, minutes] = e.target.value.split(":");
                setTime(new Time(parseInt(hours), parseInt(minutes)));
              }}
              className={cn(
                "time-input-mobile rounded-full py-3 pl-12 pr-4 text-left",
                !IS_IOS && "time-input",
              )}
            />
          </>
        ) : (
          <>
            <Popover modal>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn(
                    "w-full justify-start py-3 text-left font-normal",
                    !date && "text-muted-foreground",
                  )}
                >
                  <CalendarOutlineIcon className="mr-2 size-6" />
                  {date ? (
                    format(date, isThisYear(date) ? "E, MMM d" : "E, PP")
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(date) => {
                    if (!date) return;
                    setDate(date);
                  }}
                  autoFocus
                  disabled={(date) => date <= endOfYesterday()}
                />
              </PopoverContent>
            </Popover>
            <TimeField
              aria-label="Time"
              className="space-y-2"
              hourCycle={12}
              value={time}
              onChange={setTime}
            >
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 start-0 z-10 flex items-center justify-center ps-4 text-off-white/80">
                  <ClockOutlineIcon
                    className="size-6 text-off-white"
                    aria-hidden="true"
                  />
                </div>
                <DateInput className="relative inline-flex w-full items-center overflow-hidden whitespace-nowrap rounded-full border border-gray-text px-4 py-3 ps-12 text-sm shadow-sm outline-none transition-shadow data-[disabled]:opacity-50 data-[focus-within]:outline-none">
                  {(segment) => (
                    <DateSegment
                      segment={segment}
                      className="data-[invalid]:data-[focused]:text-off-white-foreground inline rounded p-0.5 text-off-white caret-transparent outline outline-0 data-[disabled]:cursor-not-allowed data-[focused]:bg-dark-gray data-[invalid]:data-[focused]:bg-red-600 data-[type=literal]:px-0 data-[focused]:data-[placeholder]:text-gray-text data-[focused]:text-off-white data-[invalid]:data-[focused]:data-[placeholder]:text-off-white data-[invalid]:data-[placeholder]:text-off-white data-[invalid]:text-off-white data-[placeholder]:text-off-white/70 data-[type=literal]:text-off-white/70 data-[disabled]:opacity-50"
                    />
                  )}
                </DateInput>
              </div>
            </TimeField>
          </>
        )}
      </div>
      <Button onClick={onSchedule}>Schedule Stage</Button>
    </>
  );
}
