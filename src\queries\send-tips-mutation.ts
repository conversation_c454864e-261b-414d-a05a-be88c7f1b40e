"use client";

import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import { sendTips } from "@/api/client/chain";
import { toast } from "@/components/toast";
import { SendTipsData } from "@/queries/types/send-tips-data";
import { minDelay } from "@/utils/min-delay";

type SendTipsMutationType = MutationOptions<
  unknown,
  DefaultError,
  SendTipsData,
  any
>;

async function sendTipsFn({ currency, tips }: SendTipsData) {
  if (
    ["SOL", "Bonk", "$WIF", "USEDCAR", "Moutai", "HARAMBE"].includes(currency)
  ) {
    throw new Error("Solana send tips not implemented yet");
  } else if (currency.toLowerCase() === "champ") {
    toast.danger("Please migrate your wallet to tip $CHAMP!");
    return;
  } else if (currency.toLowerCase() === "ket") {
    toast.danger("Please migrate your wallet to tip $KET!");
    return;
  } else if (currency.toLowerCase() === "wink") {
    toast.danger("Please migrate your wallet to tip $WINK!");
    return;
  } else if (currency.toLowerCase() === "abc") {
    toast.danger("Please migrate your wallet to tip $ABC!");
    return;
  } else if (currency.toLowerCase() === "mu") {
    toast.danger("Please migrate your wallet to tip $MU!");
    return;
  } else if (currency.toLowerCase() === "blub") {
    toast.danger("Please migrate your wallet to tip $BLUB!");
    return;
  } else if (currency.toLowerCase() === "boi") {
    toast.danger("Please migrate your wallet to tip $BOI!");
    return;
  } else if (currency.toLowerCase() === "erol") {
    toast.danger("Please migrate your wallet to tip $EROL!");
    return;
  } else {
    return await sendTips({
      currency,
      tips,
    });
  }
}

export const useSendTipsMutation = (options?: SendTipsMutationType) => {
  return useMutation({
    mutationFn: async (data) => await minDelay(sendTipsFn(data)),
    ...options,
  });
};
