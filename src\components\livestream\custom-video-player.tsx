"use client";

import { memo, useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";

import {
  AudioTrack,
  isTrackReference,
  TrackReference,
  useRoomContext,
  useStartAudio,
  useTracks,
  VideoTrack,
} from "@livekit/components-react";
import * as SliderPrimitive from "@radix-ui/react-slider";
import { RoomEvent, Track } from "livekit-client";
import { useHotkeys } from "react-hotkeys-hook";

import { useLivestreamStore } from "@/stores/livestream";
import { cn } from "@/utils/cn";

import {
  ArrowsExpandOutlineIcon,
  CloseOutlineIcon,
  ExternalLinkOutlineIcon,
  VolumeOffOutlineIcon,
  VolumeUpOutlineIcon,
} from "../icons";

interface CustomVideoPlayerProps {
  type?: "full" | "mini";
  device?: "desktop" | "mobile";
}

export const CustomVideoPlayer = memo(
  ({ type = "full", device }: CustomVideoPlayerProps) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [showControls, setShowControls] = useState(true);
    const timeoutRef = useRef<NodeJS.Timeout>();
    const router = useRouter();
    const twitterHandle = useLivestreamStore((state) => state.twitterHandle!);
    const isMuted = useLivestreamStore((state) => state.isMuted);
    const volume = useLivestreamStore((state) => state.volume);
    const actions = useLivestreamStore((state) => state.actions);

    const room = useRoomContext();
    const { mergedProps, canPlayAudio } = useStartAudio({
      room,
      props: {
        onClick: () => actions.setIsMuted(!isMuted),
      },
    });

    useHotkeys(
      ["f", "F"],
      () => {
        if (isFullscreen) {
          exitFullscreen();
        } else {
          enterFullscreen();
        }
      },
      {
        enabled: type === "full",
      },
    );

    const screenShareTracks = useTracks(
      [{ source: Track.Source.ScreenShare, withPlaceholder: false }],
      {
        onlySubscribed: false,
        updateOnlyOn: [
          RoomEvent.TrackSubscribed,
          RoomEvent.TrackUnsubscribed,
          RoomEvent.TrackMuted,
          RoomEvent.TrackUnmuted,
          RoomEvent.TrackPublished,
          RoomEvent.TrackUnpublished,
        ],
      },
    )
      .filter(isTrackReference)
      .filter((track) => !track.publication.isMuted);

    const cameraTracks = useTracks(
      [{ source: Track.Source.Camera, withPlaceholder: false }],
      {
        onlySubscribed: false,
        updateOnlyOn: [
          RoomEvent.TrackSubscribed,
          RoomEvent.TrackUnsubscribed,
          RoomEvent.TrackMuted,
          RoomEvent.TrackUnmuted,
          RoomEvent.TrackPublished,
          RoomEvent.TrackUnpublished,
        ],
      },
    )
      .filter(isTrackReference)
      .filter((track) => !track.publication.isMuted);

    const micTracks = useTracks(
      [
        { source: Track.Source.Microphone, withPlaceholder: false },
        { source: Track.Source.ScreenShareAudio, withPlaceholder: false },
      ],
      {
        onlySubscribed: false,
      },
    ).filter(isTrackReference);

    const resetTimer = () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      setShowControls(true);
      timeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 5000);
    };

    const handleMouseMove = () => {
      resetTimer();
    };

    const handleMouseLeave = () => {
      setShowControls(false);
    };

    const handleTouchStart = () => {
      resetTimer();
    };

    const enterFullscreen = async () => {
      try {
        if (containerRef.current) {
          await containerRef.current.requestFullscreen();
        }
      } catch (error) {
        console.error("Failed to enter fullscreen:", error);
      }
    };

    const exitFullscreen = async () => {
      try {
        if (document.fullscreenElement) {
          await document.exitFullscreen();
        }
      } catch (error) {
        console.error("Failed to exit fullscreen:", error);
      }
    };

    useEffect(() => {
      const handleFullscreenChange = () => {
        setIsFullscreen(!!document.fullscreenElement);
      };

      document.addEventListener("fullscreenchange", handleFullscreenChange);
      return () =>
        document.removeEventListener(
          "fullscreenchange",
          handleFullscreenChange,
        );
    }, []);

    useEffect(() => {
      resetTimer();
      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }, []);

    useEffect(() => {
      if (!canPlayAudio) {
        actions.setIsMuted(true);
      }
    }, [canPlayAudio]);

    return (
      <div
        className={cn(
          "relative flex-shrink-0 overflow-hidden lg:rounded-xl",
          screenShareTracks.length === 0 &&
            cameraTracks.length === 0 &&
            !isFullscreen &&
            "border-off-white/30 lg:border",
          isFullscreen ? "h-full" : "max-h-[700px]",
          type === "mini" && device === "mobile" && "h-[70px]",
        )}
        ref={containerRef}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleTouchStart}
      >
        {screenShareTracks.length === 0 && cameraTracks.length === 0 ? (
          <div
            className={cn(
              "flex w-full items-center justify-center bg-dark-bk",
              isFullscreen ? "h-full" : "aspect-video lg:rounded-xl",
              type === "mini" && device === "mobile" && "max-h-[70px]",
            )}
          >
            <span
              className={cn(
                "pointer-events-none select-none text-center text-sm text-off-white",
                type === "mini" && device === "mobile" && "text-[8px]",
              )}
            >
              Waiting for screen share or camera
            </span>
          </div>
        ) : (
          <PlayerSwitcher
            screenShareTracks={screenShareTracks}
            cameraTracks={cameraTracks}
            type={type}
            device={device}
            isFullscreen={isFullscreen}
          />
        )}
        {type !== "mini" ? (
          <>
            <div
              className={cn(
                "pointer-events-none absolute inset-x-0 bottom-0 transition-opacity duration-150",
                showControls ? "opacity-100" : "opacity-0",
              )}
              style={{
                height: "99px",
                backgroundImage:
                  "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAADGCAYAAAAT+OqFAAAAdklEQVQoz42QQQ7AIAgEF/T/D+kbq/RWAlnQyyazA4aoAB4FsBSA/bFjuF1EOL7VbrIrBuusmrt4ZZORfb6ehbWdnRHEIiITaEUKa5EJqUakRSaEYBJSCY2dEstQY7AuxahwXFrvZmWl2rh4JZ07z9dLtesfNj5q0FU3A5ObbwAAAABJRU5ErkJggg==)",
                backgroundRepeat: "repeat-x",
                backgroundPosition: "bottom",
              }}
            />
            <div
              className={cn(
                "absolute inset-x-0 bottom-0 z-10 flex items-center justify-end p-1 transition-opacity duration-150",
                showControls ? "opacity-100" : "pointer-events-none opacity-0",
              )}
            >
              {micTracks.length > 0 && (
                <>
                  {canPlayAudio ? (
                    <>
                      {isMuted ? null : (
                        <SliderPrimitive.Root
                          className={cn(
                            "relative flex w-16 touch-none select-none items-center",
                          )}
                          defaultValue={[volume]}
                          max={100}
                          step={1}
                          onValueChange={(value) => actions.setVolume(value[0])}
                        >
                          <SliderPrimitive.Track className="relative h-1 w-full grow overflow-hidden rounded-full bg-off-white/30">
                            <SliderPrimitive.Range className="absolute h-full bg-off-white" />
                          </SliderPrimitive.Track>
                          <SliderPrimitive.Thumb className="block size-3 rounded-full bg-off-white transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50" />
                        </SliderPrimitive.Root>
                      )}
                      <button
                        className="ml-1 flex items-center gap-2 p-2"
                        onClick={() => actions.setIsMuted(!isMuted)}
                      >
                        {isMuted ? (
                          <VolumeOffOutlineIcon className="size-6 text-off-white" />
                        ) : (
                          <VolumeUpOutlineIcon className="size-6 text-off-white" />
                        )}
                      </button>
                    </>
                  ) : (
                    <button
                      {...mergedProps}
                      className="ml-1 flex items-center gap-2 p-2"
                    >
                      <VolumeOffOutlineIcon className="size-6 text-off-white" />
                    </button>
                  )}
                </>
              )}
              <button
                className="mr-1 rounded-full p-2"
                onClick={isFullscreen ? exitFullscreen : enterFullscreen}
              >
                <ArrowsExpandOutlineIcon className="size-6 text-off-white" />
              </button>
            </div>
          </>
        ) : null}

        {type === "mini" && device === "desktop" ? (
          <>
            <button
              className="absolute left-2 top-2 rounded-full bg-dark-bk/75 p-1 backdrop-blur-sm"
              onClick={() => {
                router.push(`/live/${twitterHandle}`);
              }}
            >
              <ExternalLinkOutlineIcon
                className="size-5 rotate-[270deg] text-off-white"
                strokeWidth={1.5}
              />
            </button>
            <button
              className="absolute right-2 top-2 rounded-full bg-dark-bk/75 p-1 backdrop-blur-sm"
              onClick={() => {
                actions.reset();
              }}
            >
              <CloseOutlineIcon className="size-5 text-off-white" />
            </button>
          </>
        ) : null}

        {micTracks.map((track, index) => (
          <AudioTrack
            trackRef={track}
            key={"mic-" + index}
            volume={volume / 100}
            muted={isMuted}
          />
        ))}
      </div>
    );
  },
);

CustomVideoPlayer.displayName = "CustomVideoPlayer";

const PlayerSwitcher = memo(
  ({
    screenShareTracks,
    cameraTracks,
    type,
    isFullscreen,
    device,
  }: {
    screenShareTracks: TrackReference[];
    cameraTracks: TrackReference[];
    type?: "full" | "mini";
    isFullscreen: boolean;
    device?: "desktop" | "mobile";
  }) => {
    const mainRef = useRef<HTMLVideoElement>(null);
    const cameraRef = useRef<HTMLVideoElement>(null);

    const selectedScreenShare = useMemo(
      () => screenShareTracks[0],
      [screenShareTracks],
    );
    const selectedCamera = useMemo(() => cameraTracks[0], [cameraTracks]);

    useEffect(() => {
      if (mainRef.current && cameraRef.current && selectedCamera) {
        const { width } = mainRef.current?.getBoundingClientRect();
        const newCameraWidth = width / 5;

        cameraRef.current.style.width = `${newCameraWidth}px`;
      }
    }, [isFullscreen, selectedCamera]);

    if (!selectedScreenShare && !selectedCamera) {
      return null;
    }

    if (selectedScreenShare && !selectedCamera) {
      return (
        <VideoTrack
          trackRef={selectedScreenShare}
          className={cn(
            "object-contain",
            device === "mobile" && "h-full",
            type !== "mini" && "w-full",
          )}
        />
      );
    }

    if (selectedCamera && !selectedScreenShare) {
      return (
        <VideoTrack
          trackRef={selectedCamera}
          className={cn(
            "object-contain",
            device === "mobile" && "h-full",
            type !== "mini" && "w-full",
          )}
        />
      );
    }

    return (
      <>
        <VideoTrack
          ref={mainRef}
          trackRef={selectedScreenShare}
          className={cn(
            "object-contain",
            device === "mobile" && "h-full",
            type !== "mini" && "w-full",
          )}
        />
        <VideoTrack
          ref={cameraRef}
          trackRef={selectedCamera}
          className={cn(
            "absolute rounded bg-dark-bk lg:rounded-lg ", // max-w-[80px]
            type === "mini" && "bottom-1 right-1 max-w-[70px]",
            type === "full" && !isFullscreen && "bottom-2 right-2 ", //lg:max-w-[200px]
            type === "full" && isFullscreen && "bottom-6 right-6 ", //lg:max-w-[300px]
          )}
        />
      </>
    );
  },
  (prevProps, nextProps) => {
    const screenShareEqual =
      prevProps.screenShareTracks.length ===
        nextProps.screenShareTracks.length &&
      prevProps.screenShareTracks[0]?.publication.trackSid ===
        nextProps.screenShareTracks[0]?.publication.trackSid;

    const cameraEqual =
      prevProps.cameraTracks.length === nextProps.cameraTracks.length &&
      prevProps.cameraTracks[0]?.publication.trackSid ===
        nextProps.cameraTracks[0]?.publication.trackSid;

    const fullscreenEqual = prevProps.isFullscreen === nextProps.isFullscreen;

    return screenShareEqual && cameraEqual && fullscreenEqual;
  },
);

PlayerSwitcher.displayName = "PlayerSwitcher";
