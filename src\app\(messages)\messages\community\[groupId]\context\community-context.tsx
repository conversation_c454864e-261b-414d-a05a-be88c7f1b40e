"use client";

import { createContext, ReactNode, useContext, useState } from "react";
import { useParams } from "next/navigation";

import { createStore, StoreApi, useStore } from "zustand";

import { useCommunityByIdQuery } from "@/queries";
import { HeaderCommunityInterface } from "@/queries/types/chats";

type CommunityContextType = {
  data: HeaderCommunityInterface | undefined;
  isLoading: boolean;
  groupId: string;
  store: StoreApi<CommunityState>;
};

interface Store {
  messageId: string;
  initialLoad: boolean;
  pinnedMessagesCount: number | null;
}

interface Actions {
  actions: {
    setMessageId: (messageId: string) => void;
    setInitialLoad: (initialLoad: boolean) => void;
    resetMessageId: () => void;
    setPinnedMessagesCount: (pinnedMessagesCount: number) => void;
  };
}

type CommunityState = Store & Actions;

const CommunityContext = createContext<CommunityContextType | undefined>(
  undefined,
);

export function CommunityProvider({ children }: { children: ReactNode }) {
  const params = useParams() as { groupId: string };

  const [store] = useState(() =>
    createStore<CommunityState>((set, get) => ({
      messageId: "",
      initialLoad: true,
      pinnedMessagesCount: null,
      actions: {
        setMessageId: (messageId) => {
          set({ messageId, initialLoad: true });
        },
        resetMessageId: () => {
          set({ messageId: "", initialLoad: true });
        },
        setInitialLoad: (initialLoad) => {
          set({ initialLoad });
        },
        setPinnedMessagesCount: (pinnedMessagesCount) => {
          set({ pinnedMessagesCount });
        },
      },
    })),
  );

  const { data, isLoading } = useCommunityByIdQuery(params.groupId);

  return (
    <CommunityContext.Provider
      value={{
        data,
        isLoading,
        groupId: data?.community?.groupId || "",
        store,
      }}
    >
      {children}
    </CommunityContext.Provider>
  );
}

export function useCommunity() {
  const context = useContext(CommunityContext);
  const data = context && context.data;
  const isLoading = context ? context.isLoading : true;
  const groupId = (context && context.groupId) || "";
  return { data, isLoading, groupId };
}

export function useCommunityStore(): CommunityState;
export function useCommunityStore<T>(selector: (state: CommunityState) => T): T;
export function useCommunityStore<T>(selector?: (state: CommunityState) => T) {
  const context = useContext(CommunityContext);
  if (!context) {
    throw new Error("Missing CommunityContextProvider");
  }
  const store = context.store;

  return useStore(store, selector!);
}
