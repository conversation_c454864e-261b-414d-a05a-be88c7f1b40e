"use client";

import { useQueryClient } from "@tanstack/react-query";
import Skeleton from "react-loading-skeleton";

import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useFollowMutation, useUnfollowMutation } from "@/queries";
import { TopUsersResponse, User } from "@/queries/types/top-users-response";

interface UserListItemProps {
  user: User;
  onFollow: () => void;
  onUnfollow: () => void;
}

export const UserListItem = ({
  user,
  onFollow,
  onUnfollow,
}: UserListItemProps) => {
  const queryClient = useQueryClient();
  const { mutateAsync: follow } = useFollowMutation({
    onMutate: async () => {
      onFollow();
      toast.green(`You're now following ${user.twitterName}!`);

      const previousTopUsers = queryClient.getQueriesData({
        queryKey: ["user", "top"],
      });

      queryClient.setQueryData(["user", "top"], (old: TopUsersResponse) => {
        if (!old) return old;

        return {
          ...old,
          users: old.users.map((u) => {
            if (u.id === user.id) {
              return { ...u, following: true };
            }

            return u;
          }),
        };
      });

      return { previousTopUsers };
    },
    onError(err, variables, context) {
      onUnfollow();
      toast.danger(`Failed to follow ${user.twitterName}.`);
      queryClient.setQueryData(["user", "top"], context?.previousTopUsers);
    },
  });

  const { mutateAsync: unfollow } = useUnfollowMutation({
    onMutate: async () => {
      onUnfollow();

      const previousTopUsers = queryClient.getQueriesData({
        queryKey: ["user", "top"],
      });

      queryClient.setQueryData(["user", "top"], (old: TopUsersResponse) => {
        if (!old) return old;

        return {
          ...old,
          users: old.users.map((u) => {
            if (u.id === user.id) {
              return { ...u, following: false };
            }

            return u;
          }),
        };
      });

      return { previousTopUsers };
    },
    onError(err, variables, context) {
      onFollow();
      toast.danger(`Failed to unfollow ${user.twitterName}.`);
      queryClient.setQueryData(["user", "top"], context?.previousTopUsers);
    },
  });

  function handleFollow() {
    if (user.following) {
      unfollow({ userId: user.id });
    } else {
      follow({ userId: user.id });
    }
  }

  return (
    <div className="flex w-full justify-between gap-4 px-6 py-4">
      <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
        <ProgressBarLink href={`/${user.twitterHandle}`}>
          <Avatar className="size-[42px]">
            <AvatarImage src={user.twitterPicture} />
            <AvatarFallback />
          </Avatar>
        </ProgressBarLink>
        <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
          <div className="flex gap-1.5">
            <ProgressBarLink
              href={`/${user.twitterHandle}`}
              className="truncate text-[#F4F4F4]"
            >
              {user.twitterName}
            </ProgressBarLink>
          </div>
          <ProgressBarLink
            href={`/${user.twitterHandle}`}
            className="truncate text-[#808080]"
          >
            @{user.twitterHandle}
          </ProgressBarLink>
        </div>
      </div>
      <div className="flex flex-shrink-0 flex-col items-end justify-center gap-[2px]">
        <Button
          variant={user.following ? "outline" : "secondary"}
          onClick={handleFollow}
          className="h-[34px] w-24"
        >
          {user.following ? "Unfollow" : "Follow"}
        </Button>
      </div>
    </div>
  );
};

export const UserListItemLoadingSkeleton = () => {
  return (
    <div className="flex w-full justify-between gap-4 px-6 py-4">
      <div className="flex items-center gap-[10px]">
        <Skeleton circle className="size-[42px]" />
        <div className="flex w-full flex-col gap-1 leading-4">
          <Skeleton className="mt-1 h-[14px] w-28" />
          <Skeleton className="h-[12px] w-20" />
        </div>
      </div>
    </div>
  );
};
