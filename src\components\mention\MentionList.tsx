import {
  ComponentProps,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UserBadges } from "@/components/user-badges";
import { User } from "@/queries/types/top-users-response";
import { cn } from "@/utils";

interface MentionListProps {
  items: User[];
  command: ({ id }: { id: string }) => void;
}

export const MentionList = forwardRef<ComponentProps<"div">, MentionListProps>(
  (props, ref) => {
    const [selectedIndex, setSelectedIndex] = useState(0);

    const selectItem = (index: number) => {
      const item = props.items[index];

      if (item) {
        props.command({ id: item.twitterHandle });
      }
    };

    const upHandler = () => {
      setSelectedIndex(
        (selectedIndex + props.items.length - 1) % props.items.length,
      );
    };

    const downHandler = () => {
      setSelectedIndex((selectedIndex + 1) % props.items.length);
    };

    const enterHandler = () => {
      selectItem(selectedIndex);
    };

    useEffect(() => setSelectedIndex(0), [props.items]);

    useImperativeHandle(ref, () => ({
      onKeyDown: ({ event }: any) => {
        if (event.key === "ArrowUp") {
          upHandler();
          return true;
        }
        if (event.key === "ArrowDown") {
          downHandler();
          return true;
        }
        if (event.key === "Enter") {
          enterHandler();
          return true;
        }
        return false;
      },
    }));

    return (
      <div className="flex max-h-[420px] w-[calc(100vw-5rem)] flex-col overflow-y-auto rounded-lg bg-chat-bubble text-left shadow-[4px_4px_4px_0px_rgba(0,0,0,0.50)] sm:w-[420px]">
        {props.items.length ? (
          props.items.map((user, index) => (
            <button
              key={index}
              onClick={() => selectItem(index)}
              onMouseEnter={() => setSelectedIndex(index)}
              className={cn(
                "flex w-full justify-between gap-4 px-6 py-4",
                index === selectedIndex && "bg-dark-bk/30",
              )}
            >
              <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
                <Avatar className="size-[42px]">
                  <AvatarImage src={user.twitterPicture} />
                  <AvatarFallback />
                </Avatar>
                <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
                  <div className="flex gap-1.5">
                    <h4 className="truncate text-[#F4F4F4]">
                      {user.twitterName}
                    </h4>
                    {user.badges && <UserBadges badges={user.badges} />}
                  </div>
                  <div className="truncate text-left text-[#808080]">
                    @{user.twitterHandle}
                  </div>
                </div>
              </div>
            </button>
          ))
        ) : (
          <div className="px-4 py-3">No users found!</div>
        )}
      </div>
    );
  },
);

MentionList.displayName = "MentionList";
