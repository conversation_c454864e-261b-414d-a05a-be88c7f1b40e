import { formatEther } from "viem";

export const formatAddress = (
  address: string,
  start = 5,
  end = 5,
  dots = 4,
): string => {
  if (!address) return "";
  if (address.length <= start + end) return address;
  const dotString = ".".repeat(dots);
  return `${address.slice(0, start)}${dotString}${address.slice(-end)}`;
};

export const formatPrice = (price?: string): number => {
  if (!price) return 0;
  return Number(formatEther(BigInt(price)));
};

export const formatInputNumber = (value: string, digits = 4) => {
  if (!value) return "0";

  const number = Number(value.replace(/,/g, ""));
  if (isNaN(number)) return value;

  let parts = value.replace(/,/g, "").split(".");
  let integerPartRaw = parts[0] || "0";
  let decimalPartRaw = parts[1] || "";

  if (decimalPartRaw.length > digits) {
    decimalPartRaw = decimalPartRaw.slice(0, digits);
  }

  let integerPart = integerPartRaw.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  if (value.endsWith(".")) {
    return `${integerPart}.`;
  }
  return decimalPartRaw ? `${integerPart}.${decimalPartRaw}` : integerPart;
};
