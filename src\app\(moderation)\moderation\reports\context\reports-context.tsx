"use client";

import { createContext, useContext, useState } from "react";

import { GroupedTicket } from "@/queries/types/report-tickets";

interface ReportsContextType {
  activeGroups: GroupedTicket[];
  closedGroups: GroupedTicket[];
  setActiveGroups: (groups: GroupedTicket[]) => void;
  setClosedGroups: (groups: GroupedTicket[]) => void;
}

const ReportsContext = createContext<ReportsContextType | null>(null);

export const ReportsProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [activeGroups, setActiveGroups] = useState<GroupedTicket[]>([]);
  const [closedGroups, setClosedGroups] = useState<GroupedTicket[]>([]);

  return (
    <ReportsContext.Provider
      value={{ activeGroups, closedGroups, setActiveGroups, setClosedGroups }}
    >
      {children}
    </ReportsContext.Provider>
  );
};

export const useReportsContext = () => {
  const context = useContext(ReportsContext);
  if (!context) {
    throw new Error("useReportsContext must be used within a ReportsProvider");
  }
  return context;
};
