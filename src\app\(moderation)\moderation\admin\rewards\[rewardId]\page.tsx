"use client";

import { FC } from "react";
import { useParams } from "next/navigation";

import { RewardCreate } from "@/app/(moderation)/moderation/admin/rewards/[rewardId]/_components/reward-create";
import { RewardEdit } from "@/app/(moderation)/moderation/admin/rewards/[rewardId]/_components/reward-edit";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

const RewardDetailPage: FC = () => {
  const params = useParams();
  const rewardId = params.rewardId as string;
  const isLargeTablet = useMediaQuery(BREAKPOINTS.lg);

  if (rewardId === "new") {
    return <RewardCreate />;
  }

  return <RewardEdit rewardId={rewardId} />;
};

export default RewardDetailPage;
