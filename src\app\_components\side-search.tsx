"use client";

import { ChangeEvent, useState } from "react";
import { usePathname } from "next/navigation";

import { CloseOutlineIcon, SearchFilledIcon } from "@/components/icons";
import { LiveStagesSidebar } from "@/components/stages/live-stages-sidebar";
import { Input } from "@/components/ui/input";
import { UserListItem } from "@/components/user-list-item";
import { UserListItemLoadingSkeleton } from "@/components/user-list-item-loading-skeleton";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import useThrottle from "@/hooks/use-throttle";
import {
  useTopUsersQuery,
  useTrendingUsersQuery,
  useUsersSearchQuery,
} from "@/queries";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";
import { useStageRecordingPlayerStore } from "@/stores/stage-recording-player";
import { useTutorialStore } from "@/stores/tutorial";
import { UserFlaggedEnum } from "@/types";
import { cn } from "@/utils";

import { TrendingProfiles } from "./trending-profiles";

export const SideSearch = () => {
  const isStageOn = useStageStore((state) => Boolean(state.token));
  const isPlayerOn = useStageRecordingPlayerStore((state) =>
    Boolean(state.url),
  );
  const { user } = useUser();
  const pathname = usePathname();
  const isBanned = user?.flag === UserFlaggedEnum.SUSPENDED;
  const isLaptop = useMediaQuery(BREAKPOINTS.lg);
  const isTutorialOpen = useTutorialStore((state) => state.isTutorialOpen);
  const isBlurred = isTutorialOpen && pathname === "/" + user?.twitterHandle;

  const [searchValue, setSearchValue] = useState("");
  const throttledSearchValue = useThrottle(searchValue);
  const { data: searchData, isLoading: isSearchDataLoading } =
    useUsersSearchQuery(throttledSearchValue);

  const { data: trendingUsersData, isLoading: isTrendingUsersLoading } =
    useTrendingUsersQuery({
      // Enabled fetching trending users only on laptop screens
      enabled: isLaptop,
    });

  const { data: topUsersData, isLoading: isTopUsersLoading } = useTopUsersQuery(
    {
      // Enabled fetching top users only on laptop screens
      enabled: isLaptop,
    },
  );

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  return (
    <div
      className={cn(
        "fixed top-0 flex max-h-screen w-full flex-col overflow-y-auto lg:w-[290px] lg:px-[3px] lg:py-8 xl:w-[380px] ",
        isStageOn || isPlayerOn ? "h-[calc(100%-81px)]" : "h-full",
        isBanned && "pointer-events-none opacity-20",
      )}
      tabIndex={isBanned ? -1 : 0}
    >
      {isBlurred && <div className="absolute inset-0 z-40 bg-dark-bk/65" />}
      <div className="relative">
        <Input
          value={searchValue}
          onChange={handleSearch}
          placeholder="Search Users in The Arena"
          className="h-[47px] rounded-full border border-dark-gray bg-dark-bk bg-transparent py-2 pl-11 pr-10 placeholder:text-gray-text"
          disabled={isBanned}
        />
        <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
        {searchValue && (
          <button className="absolute right-[10px]  top-1/2 -translate-y-1/2 rounded-full border border-dark-gray p-1">
            <CloseOutlineIcon
              className="pointer-events-auto size-[14px] select-none text-off-white"
              onClick={() => setSearchValue("")}
            />
          </button>
        )}
      </div>
      {throttledSearchValue && isSearchDataLoading && (
        <div className="mt-2">
          {Array(15)
            .fill(null)
            .map((_, index) => (
              <UserListItemLoadingSkeleton key={index} className="px-0" />
            ))}
        </div>
      )}
      {throttledSearchValue &&
        !isSearchDataLoading &&
        searchData &&
        searchData.users.length > 0 && (
          <div className="mt-2">
            {searchData.users.map((user) => (
              <UserListItem user={user} key={user.id} className="px-0" />
            ))}
          </div>
        )}

      {!throttledSearchValue && (
        <>
          <LiveStagesSidebar />
          {trendingUsersData &&
            trendingUsersData.users.length > 0 &&
            !isTrendingUsersLoading && (
              <TrendingProfiles
                users={trendingUsersData?.users || []}
                isLoading={isTrendingUsersLoading}
              />
            )}
          <div className="hide-scrollbar relative z-20 mt-4 flex-grow overflow-y-auto rounded-[20px] border border-dark-gray pb-2">
            <div className="sticky top-0 z-10 bg-dark-bk/80 px-6 py-4 backdrop-blur-md">
              <h2 className="text-base font-semibold leading-5 text-off-white">
                Top
              </h2>
              <p className="mt-1 text-sm text-gray-text">
                Users with the highest ticket price
              </p>
            </div>
            {!topUsersData && isTopUsersLoading && (
              <>
                {Array(15)
                  .fill(null)
                  .map((_, index) => (
                    <UserListItemLoadingSkeleton
                      key={index}
                      className="px-4 py-3"
                    />
                  ))}
              </>
            )}
            {topUsersData &&
              topUsersData.users.length > 0 &&
              topUsersData.users.map((user) => {
                return (
                  <UserListItem
                    user={user}
                    key={user.id}
                    className="px-4 py-3"
                  />
                );
              })}
          </div>
        </>
      )}
    </div>
  );
};
