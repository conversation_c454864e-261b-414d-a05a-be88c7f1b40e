"use client";

import { useLayoutEffect, useState } from "react";

import {
  EllipsisVerticalFilledIcon,
  LogoIcon,
  ShareOutlineIcon,
} from "@/components/icons";
import { Button } from "@/components/ui/button";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { isIOS } from "@/components/ui/vaul/browser";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

export const PWAInstruction = () => {
  const [open, setOpen] = useState(true);
  const [IOS, setIOS] = useState(false);
  const [isPWA, setIsPWA] = useState(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  useLayoutEffect(() => {
    const ios = isIOS();
    if (ios) {
      setIOS(ios);
    }

    if (
      typeof window !== "undefined" &&
      ((window.matchMedia != null &&
        window.matchMedia("(display-mode: standalone)").matches) ||
        (window.navigator != null &&
          (window.navigator as any).standalone === true))
    ) {
      setIsPWA(true);
    }
  }, []);

  if (isTablet || isPWA) return null;

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="justify-start gap-8 text-left">
        <h2 className="text-base font-semibold leading-[22px] text-off-white">
          Install the app for easier access!
        </h2>
        <ol className="flex flex-col gap-4 text-sm text-light-gray-text">
          <li className="flex items-center gap-1">
            1. Tap on the
            <div className="flex size-[30px] items-center justify-center rounded bg-[#242424]">
              {IOS ? (
                <ShareOutlineIcon className="w-4 text-[#3b82f6]" />
              ) : (
                <EllipsisVerticalFilledIcon className="w-4 fill-off-white" />
              )}
            </div>
            button in the browser menu
          </li>
          <li className="flex items-center gap-1">
            2. Scroll down and select add to homescreen
          </li>
          <li className="flex items-center gap-1">
            3. Look for the
            <div className="flex size-[30px] items-center justify-center rounded-full bg-[#242424]">
              <LogoIcon className="w-3 text-brand-orange" />
            </div>
            icon on your homescreen
          </li>
        </ol>
        <Button
          className="w-full"
          onClick={() => {
            setOpen(false);
          }}
        >
          I already installed the app
        </Button>
      </DrawerContent>
    </Drawer>
  );
};
