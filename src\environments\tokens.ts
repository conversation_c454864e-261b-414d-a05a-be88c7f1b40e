import { SystemCurrency } from "@/api/client/currency";

export const AVAX = {
  name: "AVAX",
  icon: "/assets/coins/avax.svg",
};

export const ARENA = {
  name: "ARENA",
  icon: "/assets/coins/arena.png",
  address: "0xB8d7710f7d8349A506b75dD184F05777c82dAd0C",
};

export interface MinTokenData {
  decimals: number;
  symbol: string;
  address: string;
  icon: string;
}

export const fallbackAvax: SystemCurrency = {
  symbol: "AVAX",
  systemRate: "0",
  name: "Avalanche",
  balance: "0.00",
  isToken: false,
  photoURL: "/assets/coins/avax.svg",
  contractAddress: "0xEeeeeEeeeEeEeeEeEeEeeEEEeeeeEeeeeeeeEEeE",
  decimals: 18,
  blockchain: "avalanche",
};

export const fallbackArena: SystemCurrency = {
  symbol: "ARENA",
  systemRate: "0",
  name: "Arena",
  balance: "0.00",
  isToken: false,
  photoURL: "/assets/coins/arena.png",
  contractAddress: "0xB8d7710f7d8349A506b75dD184F05777c82dAd0C",
  decimals: 18,
  blockchain: "avalanche",
};

export const swapTokens: Record<string, MinTokenData> = {
  ["AVAX"]: {
    decimals: 18,
    symbol: "AVAX",
    address: "0xEeeeeEeeeEeEeeEeEeEeeEEEeeeeEeeeeeeeEEeE",
    icon: "/assets/coins/avax.png",
  },
  ["ARENA"]: {
    decimals: 18,
    symbol: "ARENA",
    address: ARENA.address,
    icon: "/assets/coins/arena.png",
  },
  ["COQ"]: {
    decimals: 18,
    symbol: "COQ",
    address: "0x420FcA0121DC28039145009570975747295f2329",
    icon: "/assets/coins/coq.png",
  },
  ["NOCHILL"]: {
    decimals: 18,
    symbol: "NOCHILL",
    address: "0xAcFb898Cff266E53278cC0124fC2C7C94C8cB9a5",
    icon: "/assets/coins/nochill.png",
  },
  ["GURS"]: {
    decimals: 18,
    symbol: "GURS",
    address: "0x223a368ad0e7396165fc629976d77596a51f155c",
    icon: "/assets/coins/gurs.png",
  },
  ["JOE"]: {
    decimals: 18,
    symbol: "JOE",
    address: "0x6e84a6216ea6dacc71ee8e6b0a5b7322eebc0fdd",
    icon: "/assets/coins/joe.png",
  },
  ["TECH"]: {
    decimals: 18,
    symbol: "TECH",
    address: "0x5ac04b69bde6f67c0bd5d6ba6fd5d816548b066a",
    icon: "/assets/coins/tech.png",
  },
  ["CHAMP"]: {
    decimals: 18,
    symbol: "CHAMP",
    address: "0xb0Aa388A35742F2d54A049803BFf49a70EB99659",
    icon: "/assets/coins/champ.png",
  },
  ["KET"]: {
    decimals: 18,
    symbol: "KET",
    address: "0xffff003a6bad9b743d658048742935fffe2b6ed7",
    icon: "/assets/coins/ket.png",
  },
  ["EROL"]: {
    decimals: 18,
    symbol: "EROL",
    address: "0xcac4904e1db1589aa17a2ec742f5a6bcf4c4d037",
    icon: "/assets/coins/erol.png",
  },
  ["WINK"]: {
    decimals: 18,
    symbol: "WINK",
    address: "0x7698A5311DA174A95253Ce86C21ca7272b9B05f8",
    icon: "/assets/coins/wink.jpg",
  },
  ["ABC"]: {
    decimals: 18,
    symbol: "ABC",
    address: "0x18cffcbb1ea17ae665dfd44e6464f09d8618d8cd",
    icon: "/assets/coins/abc.jpg",
  },
  ["MU"]: {
    decimals: 18,
    symbol: "MU",
    address: "0xD036414fa2BCBb802691491E323BFf1348C5F4Ba",
    icon: "/assets/coins/mu.jpg",
  },
  ["BLUB"]: {
    decimals: 18,
    symbol: "BLUB",
    address: "0x0f669808d88b2b0b3d23214dcd2a1cc6a8b1b5cd",
    icon: "/assets/coins/blub.png",
  },
  ["BOI"]: {
    decimals: 18,
    symbol: "BOI",
    address: "0xACC95Afa65768aa74044E6f6e267AD6417CD3e55",
    icon: "/assets/coins/boi.png",
  },
};
