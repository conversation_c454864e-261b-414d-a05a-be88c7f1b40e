"use client";

import { useEffect, useMemo, useState } from "react";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import Skeleton from "react-loading-skeleton";
import { Address, parseUnits } from "viem";

import { batchMultiSendDynamic } from "@/api/client/dynamic/send-funds-dynamic";
import {
  MULTI_SEND_CONTRACT_ABI,
  MULTI_SEND_CONTRACT_ADDRESS,
} from "@/environments/MULTI_SEND_CONTRACT";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import { useSolanaAddressQuery, useTipMutation } from "@/queries";
import { useTippableCurrenciesQuery } from "@/queries/currency-queries";
import { stageQueries } from "@/queries/stage-queries";
import { useUser } from "@/stores";
import { User } from "@/types";
import { formatEther, formatUnits } from "@/utils";

import { CloseOutlineIcon } from "../icons";
import { toast } from "../toast";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "../ui/dialog";
import { Label } from "../ui/label";
import { useDataChannelsContext } from "./stores/data-channels-context";

const wallets = {
  ARENA: "The Arena",
  PHANTOM: "Phantom",
};

export const LivestreamTipModal = ({
  children,
  user,
  setCustomTipOpen,
}: {
  children: React.ReactNode;
  user: User;
  setCustomTipOpen: (open: boolean) => void;
}) => {
  const { user: me } = useUser();
  const queryClient = useQueryClient();
  const {
    chat: { send },
  } = useDataChannelsContext();
  const [open, setOpen] = useState(false);
  const [isPendingDynamic, setIsPendingDynamic] = useState(false);
  const [feePercentage, setFeePercentage] = useState(2);

  const { primaryWallet } = useDynamicContext();
  const { data, isLoading } = useQuery(stageQueries.mostRecentTip());
  const { data: solanaAddressData } = useSolanaAddressQuery();

  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useTippableCurrenciesQuery();

  const { sortedCurrencies } = useWalletCurrencies({
    user: me,
    currenciesData,
    isCurrenciesLoading,
  });

  const token = useMemo(() => {
    if (!data) return null;

    return sortedCurrencies?.find((token) => token.symbol === data.currency);
  }, [data, sortedCurrencies]);

  const recentTipAmount = useMemo(() => {
    if (!data) return null;

    if (isNaN(parseFloat(data.amount))) {
      return null;
    }

    if (!token) {
      return null;
    }

    if (
      data.currency === "SOL" ||
      data.currency === "Bonk" ||
      data.currency === "$WIF" ||
      data.currency === "USEDCAR" ||
      data.currency === "Moutai" ||
      data.currency === "HARAMBE"
    ) {
      return data.amount ?? "0.00";
    }

    if (data.currency === "MEAT") {
      return formatUnits(data.amount, 6);
    }

    return formatEther(data.amount);
  }, [data]);

  const { mutateAsync: tip, isPending } = useTipMutation({
    onSuccess: async (_data, variables) => {
      toast.green("Tip sent!");
      setOpen(false);
      queryClient.invalidateQueries({
        queryKey: stageQueries.mostRecentTipKey(),
      });
      queryClient.invalidateQueries({
        queryKey: ["wallet", "balance", user?.address, variables.currency],
      });
      queryClient.invalidateQueries({
        queryKey: [
          "wallet",
          "solana_balance",
          solanaAddressData?.response.solanaAddress,
        ],
      });

      if (
        variables.tipAmount &&
        !isNaN(+variables.tipAmount) &&
        +variables.tipAmount > 0
      ) {
        if (recentTipAmount) {
          const message = {
            type: "tip",
            data: {
              amount: +variables.tipAmount,
              currency: variables.currency,
              token: {
                name: variables.currency,
                icon: token?.photoURL,
              },
              to: {
                id: user.id,
                name: user.twitterName,
                avatar: user.twitterPicture,
                username: user.twitterHandle,
              },
            },
          };
          await send(JSON.stringify(message));
        }
      }
    },
  });

  const handleTip = async () => {
    if (!me || !token || !recentTipAmount || !data) {
      toast.danger("Something went wrong, please try again!");
      return;
    }

    let transactionHash = "";
    let transactionData = "";
    let isDynamic = false;
    const isSolanaCurrency = [
      "SOL",
      "Bonk",
      "$WIF",
      "USEDCAR",
      "Moutai",
      "HARAMBE",
    ].includes(token.name);

    if (!isSolanaCurrency) {
      if (me && me.address === me.dynamicAddress?.toLowerCase()) {
        const tipAmount = parseUnits(data.amount.replace(/,/g, ""), 0);
        isDynamic = true;
        setIsPendingDynamic(true);
        try {
          const {
            txHash: [txHash],
            txData: [txData],
          } = await batchMultiSendDynamic(
            primaryWallet,
            [user.address],
            [tipAmount],
            token.name,
            token?.isToken || false,
            token?.contractAddress,
          );

          transactionHash = txHash;
          transactionData = txData;

          await tip({
            currency: token.name,
            tipAmount: (
              parseFloat(recentTipAmount.replace(/,/g, "")) -
              (parseFloat(recentTipAmount.replace(/,/g, "")) * feePercentage) /
                100
            ).toString(),
            userId: user.id,
            wallet: isSolanaCurrency ? wallets.ARENA : wallets.ARENA,
            isDynamic,
            txHash: transactionHash,
            txData: transactionData,
          });
        } catch (e) {
          toast.danger("Error while tipping post, please try again!");
          console.log("Something went wrong with sendFundsDynamic:", e);
        } finally {
          setIsPendingDynamic(false);
        }
      }
    }
  };

  useEffect(() => {
    async function fetchFeePercentage() {
      if (!primaryWallet) {
        return;
      }
      if (!isEthereumWallet(primaryWallet)) {
        toast.danger("This wallet is not an Ethereum wallet");
        setIsPendingDynamic(false);
        return;
      }

      const publicClient = await primaryWallet.getPublicClient();

      const percentage = await publicClient.readContract({
        address: MULTI_SEND_CONTRACT_ADDRESS as Address,
        abi: MULTI_SEND_CONTRACT_ABI,
        functionName: "feePercentage",
      });

      setFeePercentage(Number(percentage));
    }

    void fetchFeePercentage();
  }, []);

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
      }}
    >
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="flex max-w-[350px] flex-col gap-0 rounded-[10px] bg-[#0F0F0F]/90 p-0 backdrop-blur-sm">
        <div className="flex w-full items-center border-b border-dark-gray px-6 py-4">
          <div className="flex-1" />
          <h4 className="text-base font-semibold leading-5 text-white">
            Quick Tip
          </h4>
          <div className="flex flex-1 justify-end">
            <DialogClose className="flex flex-shrink-0">
              <CloseOutlineIcon className="size-6 text-off-white" />
            </DialogClose>
          </div>
        </div>
        <div className="flex flex-col gap-5 p-5">
          <div className="flex flex-col gap-2">
            <Label>Your most recent tip</Label>
            <div className="flex items-center justify-between gap-1.5 rounded-lg border border-gray-text px-4 py-3 text-sm text-off-white">
              {isLoading && <Skeleton className="h-4 w-40" />}
              {!isLoading && data && token && recentTipAmount && (
                <>
                  <img
                    src={token.photoURL}
                    className="h-4 w-4 rounded-full"
                    alt={token.name + " logo"}
                  />
                  <span className="flex-grow truncate font-medium">
                    {recentTipAmount} {token.name}
                  </span>
                </>
              )}
              {!isLoading && !data && (
                <span className="flex-grow truncate font-medium">
                  No recent tip
                </span>
              )}
            </div>
          </div>
          <Button
            loading={isLoading || isPending || isPendingDynamic}
            disabled={recentTipAmount === null}
            onClick={handleTip}
          >
            Quick Tip
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              setOpen(false);
              setCustomTipOpen(true);
            }}
          >
            Custom Tip
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
