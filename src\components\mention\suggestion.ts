import { MentionNodeAttrs } from "@tiptap/extension-mention";
import { ReactRenderer } from "@tiptap/react";
import { SuggestionOptions } from "@tiptap/suggestion";
import tippy, { Instance as TippyInstance } from "tippy.js";

import { getUsersSearch } from "@/api/client/user";
import { User } from "@/queries/types/top-users-response";

import { MentionList } from "./MentionList";

export const suggestion = {
  items: async ({ query }: { query: string }) => {
    const response = await getUsersSearch({
      searchString: query,
    });

    return response.users;
  },

  render: () => {
    let reactRenderer: ReactRenderer;
    let popup: TippyInstance[];

    return {
      onStart: (props) => {
        if (!props.clientRect) {
          return;
        }

        reactRenderer = new ReactRenderer(MentionList, {
          props,
          editor: props.editor,
        });

        const dialog = document.querySelector('[role="dialog"]');

        if (dialog) {
          popup = tippy("div", {
            getReferenceClientRect: () =>
              props.clientRect?.() as unknown as DOMRect,
            appendTo: () => dialog,
            content: reactRenderer.element,
            showOnCreate: true,
            interactive: true,
            trigger: "manual",
            placement: "bottom-start",
          });
        } else {
          popup = tippy("body", {
            getReferenceClientRect: () =>
              props.clientRect?.() as unknown as DOMRect,
            appendTo: () => document.body,
            content: reactRenderer.element,
            showOnCreate: true,
            interactive: true,
            trigger: "manual",
            placement: "bottom-start",
          });
        }
      },

      onUpdate(props) {
        reactRenderer.updateProps(props);

        if (!props.clientRect) {
          return;
        }

        popup[0].setProps({
          getReferenceClientRect: () =>
            props.clientRect?.() as unknown as DOMRect,
        });
      },

      onKeyDown(props) {
        if (props.event.key === "Escape") {
          popup[0].hide();

          return true;
        }

        // @ts-ignore
        return reactRenderer?.ref?.onKeyDown(props);
      },

      onExit() {
        popup[0].destroy();
        reactRenderer.destroy();
      },
    };
  },
} satisfies Omit<SuggestionOptions<User, MentionNodeAttrs>, "editor">;
