This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.

## Build push and deploy

Docker registry us-central1-docker.pkg.dev/imposing-yen-405409/arena/arena-frontend-react

To start build please commit you changes into your branch in origin repo, tag by template and push tag:

- ^arena-frontend-react@\d+.\d+.\d+-prod$ - for production infrastructure
- ^arena-frontend-react@\d+.\d+.\d+-stage$ - for stage infrastructure

Image tag will calculate from git tag, if git tag is arena-frontend-react@0.0.1-stage then image tag is 0.0.1-stage.

**Tags with the same numbers should be avoided.**

Examples:

- arena-frontend-react@0.0.1-stage - build image and pull it in registry as us-central1-docker.pkg.dev/imposing-yen-405409/arena/arena-frontend-react:0.0.1-stage and deploy this image in k8s stage cluster
- arena-frontend-react@0.0.2-prod - build image and pull it in registry as us-central1-docker.pkg.dev/imposing-yen-405409/arena/arena-frontend-react:0.0.2-prod and deploy this image in k8s production cluster

Deploy to k8s **stage** and **production** clusters will occurring automatically with argocd from github repo starsarenaorg/infra

### help commands

| Description                 | Command                                                    |
| --------------------------- | ---------------------------------------------------------- |
| Get current tag             | git describe --abbrev=0 --tags (pipe) awk -F@ '{print $2}' |
| Add tag locally             | git tag arena-frontend-react@0.0.1-stage                   |
| Add tag to remote repo      | git push origin arena-frontend-react@0.0.1-stage           |
| Delete tag locally          | git tag --delete arena-frontend-react@0.0.1-stage          |
| Delete tag from remote repo | git push --delete origin arena-frontend-react@0.0.1-stage  |
