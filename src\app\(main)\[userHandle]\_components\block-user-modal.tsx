"use client";

import { useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useBlockUserMutation } from "@/queries/user-mutations";
import { User } from "@/types";

interface BlockUserModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: User;
}

export const BlockUserModal = ({
  open,
  setOpen,
  user,
}: BlockUserModalProps) => {
  const queryClient = useQueryClient();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const { mutateAsync: blockUser, isPending } = useBlockUserMutation({
    onMutate: async () => {
      toast.red(`${user.twitterName} is now blocked!`);

      await queryClient.cancelQueries({
        queryKey: ["user", "isBlocked", user.id],
      });

      const previousIsBlocked = queryClient.getQueryData([
        "user",
        "isBlocked",
        user.id,
      ]);

      queryClient.setQueryData(["user", "isBlocked", user.id], true);

      return { previousIsBlocked };
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to block ${user.twitterName}.`);
      queryClient.setQueryData(
        ["user", "isBlocked", user.id],
        context?.previousIsBlocked,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
      queryClient.invalidateQueries({
        queryKey: ["chat", "group", "profile", user.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["chat", "conversations"],
      });
    },
  });

  const handleBlock = async () => {
    setOpen(false);
    await blockUser({ userId: user.id });
  };

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-sm">
          <BlockUserModalContent
            user={user}
            setOpen={setOpen}
            isPending={isPending}
            handleBlock={handleBlock}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="justify-start gap-6 text-left">
        <BlockUserModalContent
          user={user}
          setOpen={setOpen}
          isPending={isPending}
          handleBlock={handleBlock}
        />
      </DrawerContent>
    </Drawer>
  );
};

interface BlockUserModalContentProps {
  user: User;
  setOpen: (open: boolean) => void;
  isPending: boolean;
  handleBlock: () => void;
}

const BlockUserModalContent = ({
  user,
  setOpen,
  isPending,
  handleBlock,
}: BlockUserModalContentProps) => {
  return (
    <>
      <DialogHeader className="space-y-4">
        <DialogTitle>Block @{user.twitterHandle}?</DialogTitle>
        <DialogDescription className="text-gray-text">
          @{user.twitterHandle} will not be able to follow you or view your
          posts, and you will not see posts or notifications from @
          {user.twitterHandle}. Are you sure you want to proceed?
        </DialogDescription>
      </DialogHeader>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => {
            setOpen(false);
          }}
        >
          Cancel
        </Button>
        <Button
          variant="destructive"
          className="flex-1"
          onClick={handleBlock}
          disabled={isPending}
        >
          Block
        </Button>
      </div>
    </>
  );
};
