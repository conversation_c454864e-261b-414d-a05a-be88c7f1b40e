"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useBlockStageUserMutation } from "@/queries";

import { useDataChannelsContext } from "./stores/data-channels-context";

interface User {
  id: string;
  name: string;
  avatar: string;
  username: string;
  role: string;
}

interface BlockStageUserModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: User;
  stageId: string;
}

export const BlockStageUserModal = ({
  open,
  setOpen,
  user,
  stageId,
}: BlockStageUserModalProps) => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const { sendInvalidateStageInfo } = useDataChannelsContext();
  const { mutateAsync: blockUser, isPending } = useBlockStageUserMutation({
    onSuccess: () => {
      sendInvalidateStageInfo();
    },
  });

  const handleBlock = async () => {
    setOpen(false);
    await blockUser({ stageId, userId: user.id });
  };

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-sm">
          <BlockStageUserModalContent
            user={user}
            setOpen={setOpen}
            isPending={isPending}
            handleBlock={handleBlock}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="justify-start gap-6 text-left">
        <BlockStageUserModalContent
          user={user}
          setOpen={setOpen}
          isPending={isPending}
          handleBlock={handleBlock}
        />
      </DrawerContent>
    </Drawer>
  );
};

interface BlockStageUserModalContentProps {
  user: User;
  setOpen: (open: boolean) => void;
  isPending: boolean;
  handleBlock: () => void;
}

const BlockStageUserModalContent = ({
  user,
  setOpen,
  isPending,
  handleBlock,
}: BlockStageUserModalContentProps) => {
  return (
    <>
      <DialogHeader className="space-y-4">
        <DialogTitle>Remove @{user.username}?</DialogTitle>
        <DialogDescription className="text-gray-text">
          @{user.username} will not be able to join this Stage again. Are you
          sure you want to proceed?
        </DialogDescription>
      </DialogHeader>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => {
            setOpen(false);
          }}
        >
          Cancel
        </Button>
        <Button
          variant="destructive"
          className="flex-1"
          onClick={handleBlock}
          disabled={isPending}
        >
          Remove
        </Button>
      </div>
    </>
  );
};
