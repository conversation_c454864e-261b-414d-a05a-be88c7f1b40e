import { useQueries, useQuery } from "@tanstack/react-query";

import {
  fetchTokenBalance,
  getFee<PERSON><PERSON><PERSON>,
  getPrice,
  getSolanaBalance,
  getTokenPrice,
} from "@/api/balance";

export const useTokenBalancesQuery = ({
  address,
  currencies = [],
}: {
  address?: string;
  currencies?: { symbol: string; contractAddress?: string }[];
}) => {
  const queries = useQueries({
    queries: (currencies || []).map((currency) => ({
      queryKey: ["wallet", "balance", address, currency.symbol],
      queryFn: () =>
        address
          ? fetchTokenBalance(address, currency)
          : Promise.resolve({ symbol: currency.symbol, balance: BigInt(0) }),
      enabled: !!address,
      retry: 3,
      retryDelay: () => 2000,
    })),
  });

  const balances = Object.fromEntries(
    queries
      .map((q) => [
        q.data?.symbol,
        {
          balance: q.data?.balance ?? BigInt(0),
          error: q.error ? (q.error as Error).message : undefined,
          isLoading: q.isLoading,
        },
      ])
      .filter(([symbol]) => !!symbol),
  );

  const isBalancesLoading = queries.some((q) => q.isLoading);
  const isAnyBalanceLoaded = queries.some((q) => q.isSuccess);

  return { balances, queries, isBalancesLoading, isAnyBalanceLoaded };
};

export const usePriceQuery = ({
  address,
  amount,
}: {
  address?: string;
  amount: bigint;
}) => {
  return useQuery({
    queryKey: ["wallet", "price", address, amount.toString()],
    queryFn: async () => {
      if (!address) return null;
      const price = await getPrice(address, amount);
      return price;
    },
  });
};

export const useFeePercentQuery = ({ address }: { address?: string }) => {
  return useQuery({
    queryKey: ["wallet", "fee", address],
    queryFn: async () => {
      if (!address) return null;
      const fees = await getFeePercent(address);
      return fees;
    },
  });
};

export const useSolanaBalanceQuery = ({ address }: { address?: string }) => {
  return useQuery({
    queryKey: ["wallet", "solana_balance", address],
    queryFn: async () => {
      if (!address) return null;
      return await getSolanaBalance(address);
    },
    enabled: !!address,
  });
};

export const useGetTokenPriceQuery = () => {
  return useQuery({
    queryKey: ["token", "price"],
    queryFn: async () => {
      return await getTokenPrice();
    },
  });
};
