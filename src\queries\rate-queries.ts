import { useQuery } from "@tanstack/react-query";
import { NumberAsString, OptimalRate } from "paraswap-core";

import { MinTokenData } from "@/environments/tokens";
import { useSwap } from "@/hooks/use-swap";

interface SwapperParams {
  srcToken: Pick<MinTokenData, "address" | "decimals">;
  destToken: Pick<MinTokenData, "address" | "decimals">;
  srcAmount: NumberAsString;
}

export const useGetRate = ({
  srcToken,
  destToken,
  srcAmount,
}: SwapperParams) => {
  const { getRate } = useSwap();
  return useQuery<OptimalRate, Error>({
    queryKey: [
      "getRate",
      srcToken.address,
      destToken.address,
      srcAmount.toString(),
    ],
    queryFn: async () => getRate({ srcToken, destToken, srcAmount }),
  });
};
