import { ComponentProps } from "react";

export const MutedMicCircleFilledIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 27 26"
    fill="none"
    {...props}
  >
    <path
      d="M19.5184 12.9941C19.5184 14.6681 18.8799 16.2735 17.7432 17.4572C16.6065 18.6408 15.0648 19.3058 13.4572 19.3058M13.4572 19.3058C11.8497 19.3058 10.308 18.6408 9.17129 17.4572C8.03459 16.2735 7.396 14.6681 7.396 12.9941M13.4572 19.3058V21.9023"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.3139 15.1371C11.8823 15.7055 12.6531 16.0248 13.4569 16.0248C14.2607 16.0248 15.0315 15.7055 15.5998 15.1371C16.1682 14.5688 16.4875 13.7979 16.4875 12.9942V6.93296C16.4875 6.12919 16.1682 5.35834 15.5998 4.78999C15.0315 4.22164 14.2607 3.90234 13.4569 3.90234C12.6531 3.90234 11.8823 4.22164 11.3139 4.78999C10.7456 5.35834 10.4263 6.12919 10.4263 6.93296V12.9942C10.4263 13.7979 10.7456 14.5688 11.3139 15.1371Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.38574 19.5596L20.0235 5.92188"
      stroke="#020202"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M5.88037 18.0448L19.013 4.91211"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);
