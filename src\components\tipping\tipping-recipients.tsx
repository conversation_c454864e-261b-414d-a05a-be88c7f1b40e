import React from "react";

import { cn } from "@/utils";

import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

export interface TippingRecipient {
  twitterHandle: string;
  twitterPicture: string;
  twitterName: string;
  address: string;
  id: string;
}

interface TippingRecipientsProps {
  recipients: TippingRecipient[];
}

export const TippingRecipients: React.FC<TippingRecipientsProps> = ({
  recipients,
}) => {
  if (recipients.length === 1) {
    const r = recipients[0];
    return (
      <div className="flex items-center gap-3 rounded-xl bg-[#232323] px-3 py-4">
        <Avatar className="size-9">
          <AvatarImage src={r.twitterPicture} />
          <AvatarFallback />
        </Avatar>
        <div className="flex flex-col gap-1">
          <div className="text font-semibold text-white">{r.twitterName}</div>
          <div className="text-sm text-gray-text">@{r.twitterHandle}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-3 rounded-xl border border-gray-text px-3 py-4">
      <div className="flex -space-x-2">
        {recipients.slice(0, 3).map((r, i) => (
          <Avatar
            key={r.twitterHandle}
            className={cn("size-9 border-2 border-dark-bk", i > 0 && "-ml-2")}
          >
            <AvatarImage src={r.twitterPicture} />
            <AvatarFallback />
          </Avatar>
        ))}
        {recipients.length > 3 && (
          <div className="bg-gray-700 -ml-2 flex size-9 items-center justify-center rounded-full border-2 border-dark-bk text-xs text-white">
            +{recipients.length - 3}
          </div>
        )}
      </div>
      <div className="flex flex-col gap-1">
        <div className="text-sm font-semibold text-white">
          {recipients.length} recipients
        </div>
        <div className="text-xs text-gray-text">$166 total tips</div>
      </div>
    </div>
  );
};
