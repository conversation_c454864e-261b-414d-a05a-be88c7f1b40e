import { ComponentProps } from "react";

export const BeakerOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 21 18"
    fill="none"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.05713 0.462987C6.17322 0.182732 6.44669 0 6.75004 0H14.75C15.0534 0 15.3269 0.182732 15.443 0.462987C15.559 0.743243 15.4949 1.06583 15.2804 1.28033L14.5 2.06066V6.92184C14.5 6.92179 14.5 6.92189 14.5 6.92184C14.5002 7.25328 14.6319 7.57126 14.8663 7.80559L18.7078 11.6471C18.7077 11.647 18.708 11.6473 18.7078 11.6471C18.7079 11.6472 18.7085 11.6478 18.7086 11.6479L19.8664 12.8057C21.5993 14.5386 20.3706 17.5 17.921 17.5H3.57804C1.12773 17.5 -0.0986656 14.538 1.63371 12.8057L6.63371 7.80567C6.86811 7.57134 6.99993 7.25344 7.00004 6.922C7.00004 6.92195 7.00004 6.92205 7.00004 6.922V2.06066L6.21971 1.28033C6.00521 1.06583 5.94105 0.743243 6.05713 0.462987ZM17.6488 12.7094C17.6484 12.709 17.6481 12.7087 17.6477 12.7083C17.4732 12.5338 17.2509 12.4148 17.0089 12.3664L14.6221 11.8895C13.4773 11.6605 12.2887 11.8197 11.2445 12.3418L11.2428 12.3427L10.9265 12.4998C10.9262 12.4999 10.9267 12.4997 10.9265 12.4998C9.5841 13.1708 8.05558 13.3757 6.58397 13.0814L4.65311 12.6955C4.45136 12.6552 4.24278 12.6653 4.04588 12.7249C3.84898 12.7846 3.66984 12.8919 3.52436 13.0373C3.51658 13.0451 3.50866 13.0527 3.50063 13.0601L2.69437 13.8663C1.90675 14.654 2.46436 16 3.57804 16H17.921C19.0355 16 19.5928 14.6534 18.8057 13.8663L17.6488 12.7094ZM5.27133 11.2894L6.87806 11.6105C8.02287 11.8395 9.2114 11.6803 10.2556 11.1582L10.2573 11.1573L10.5736 11.0002C10.5733 11.0003 10.5739 11.0001 10.5736 11.0002C11.916 10.3292 13.4445 10.1243 14.9161 10.4186L15.4683 10.5289L13.8058 8.86641C13.8058 8.86638 13.8058 8.86644 13.8058 8.86641C13.2901 8.35081 13.0002 7.65141 13 6.92216V1.75C13 1.66401 13.0148 1.57955 13.0429 1.5H8.45715C8.48528 1.57955 8.50004 1.66401 8.50004 1.75V6.922C8.49989 7.65125 8.21008 8.35073 7.69437 8.86633C7.69434 8.86636 7.6944 8.8663 7.69437 8.86633L5.27133 11.2894Z"
      fill="currentColor"
    />
  </svg>
);
