import { ComponentProps } from "react";

export const LockOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 20 20"
    fill="none"
    {...props}
  >
    <path
      d="M10.0002 12.5V14.1667M5.00016 17.5H15.0002C15.4422 17.5 15.8661 17.3244 16.1787 17.0118C16.4912 16.6993 16.6668 16.2754 16.6668 15.8333V10.8333C16.6668 10.3913 16.4912 9.96738 16.1787 9.65482C15.8661 9.34226 15.4422 9.16667 15.0002 9.16667H5.00016C4.55814 9.16667 4.13421 9.34226 3.82165 9.65482C3.50909 9.96738 3.3335 10.3913 3.3335 10.8333V15.8333C3.3335 16.2754 3.50909 16.6993 3.82165 17.0118C4.13421 17.3244 4.55814 17.5 5.00016 17.5ZM13.3335 9.16667V5.83333C13.3335 4.94928 12.9823 4.10143 12.3572 3.47631C11.7321 2.85119 10.8842 2.5 10.0002 2.5C9.11611 2.5 8.26826 2.85119 7.64314 3.47631C7.01802 4.10143 6.66683 4.94928 6.66683 5.83333V9.16667H13.3335Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
