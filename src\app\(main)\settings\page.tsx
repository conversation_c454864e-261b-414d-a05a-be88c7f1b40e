import { Header } from "@/components/header";
import { NotificationsOutlineIcon } from "@/components/icons-v2";
import { AccountSecurityIcon } from "@/components/icons-v2/account-security";
import { BugReportOutlineIcon } from "@/components/icons-v2/bugreport-outline";
import { DmSettingOutlineIcon } from "@/components/icons-v2/dmsetting-outline";
import { MuteSettingOutlineIcon } from "@/components/icons-v2/mutesetting-outline";
import { RefundformOutlineIcon } from "@/components/icons-v2/refundform-outline";
import { SupportOutlineIcon } from "@/components/icons-v2/support-outline";
import { ProgressBarLink } from "@/components/progress-bar";

import { BugReportModal } from "./_components/bug-report-modal";

type SettingItemProps = {
  href: string;
  icon: React.ElementType;
  title: string;
  description: string;
};

function SettingsPage() {
  const SettingItem: React.FC<SettingItemProps> = ({
    href,
    icon: Icon,
    title,
    description,
  }) => (
    <ProgressBarLink
      href={href}
      className="w-full rounded-[10px] border border-[#78716C] p-4"
    >
      <div className="flex w-full flex-col items-start justify-center gap-0.5">
        <div className="inline-flex items-center justify-start gap-4 self-stretch">
          <div className="relative h-6 w-6">
            <Icon className="size-6" />
          </div>
          <div>
            <h4 className="text-sm font-medium normal-case leading-5 text-off-white">
              {title}
            </h4>
            <p className="text-sm font-normal normal-case text-light-gray-text">
              {description}
            </p>
          </div>
        </div>
      </div>
    </ProgressBarLink>
  );

  return (
    <div>
      <Header defaultBackUrl="/home">Settings & Support</Header>
      <div className=" flex w-full flex-col items-center justify-center gap-[18px] px-[24px] py-[32px]">
        <SettingItem
          href="/settings/notifications"
          icon={NotificationsOutlineIcon}
          title="Customize Notifications"
          description="Choose who you allow to trigger notifications."
        />
        <SettingItem
          href="/settings/account-settings"
          icon={AccountSecurityIcon}
          title="Account Security"
          description="Add a recovery email and enable 2FA to better protect your account."
        />
        <SettingItem
          href="/settings/direct-messages"
          icon={DmSettingOutlineIcon}
          title="DM's Settings"
          description="Choose who you allow to send you direct messages."
        />
        <BugReportModal>
          <div className="flex w-full cursor-pointer flex-col items-start justify-center gap-0.5 rounded-[10px] border border-[#78716C] p-4">
            <div className="inline-flex items-center justify-start gap-4 self-stretch">
              <div className="relative h-6 w-6">
                <BugReportOutlineIcon />
              </div>
              <div className="inline-flex shrink grow basis-0 flex-col items-start justify-start gap-0.5">
                <h4 className="text-sm font-medium normal-case leading-5 text-off-white">
                  Bug Report Form
                </h4>
                <p className="text-sm font-normal normal-case text-light-gray-text">
                  Report bugs you find on The Arena!
                </p>
              </div>
            </div>
          </div>
        </BugReportModal>
        <a
          href="https://discord.gg/a5Fw3TFP5n"
          target="_blank"
          rel="noreferrer"
          className="flex w-full flex-col items-start justify-center gap-0.5 rounded-[10px] border border-[#78716C] p-4"
        >
          <div className="inline-flex items-center justify-start gap-4 self-stretch">
            <div className="relative h-6 w-6">
              <SupportOutlineIcon />
            </div>
            <div className="inline-flex shrink grow basis-0 flex-col items-start justify-start gap-0.5">
              <h4 className="text-sm font-medium normal-case leading-5 text-off-white">
                Support
              </h4>
              <p className="text-sm font-normal normal-case text-light-gray-text">
                Get help on any issue on The Arena. (External link)
              </p>
            </div>
          </div>
        </a>
      </div>
    </div>
  );
}

export default SettingsPage;
