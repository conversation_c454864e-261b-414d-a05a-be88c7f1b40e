"use client";

import { memo, useEffect, useLayoutEffect, useRef, useState } from "react";

import {
  useIsSpeaking,
  useParticipantAttributes,
  useRoomInfo,
  useTrackMutedIndicator,
} from "@livekit/components-react";
import { Emoji } from "emoji-picker-react";
import { AnimatePresence, motion } from "framer-motion";
import { LocalParticipant, RemoteParticipant, Track } from "livekit-client";

import { useTippableCurrenciesQuery } from "@/queries/currency-queries";
import { cn } from "@/utils";

import { MutedOutlineIcon, UnmutedOutlineIcon } from "../icons";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Role, ROLE_NAMES, ROLES } from "./constants";
import { useEmotesQueue } from "./hooks/use-emotes-queue";
import { useTipQueue } from "./hooks/use-tips-queue";
import { StageUserProfileModal } from "./stage-user-profile-modal";

export const StageUser = memo(
  ({ participant }: { participant: LocalParticipant | RemoteParticipant }) => {
    const roomInfo = useRoomInfo();
    const { attributes } = useParticipantAttributes({ participant });
    const isSpeaking = useIsSpeaking(participant);
    const { isMuted } = useTrackMutedIndicator({
      participant,
      source: Track.Source.Microphone,
    });

    const { first: emote, remove: removeEmote } = useEmotesQueue(
      participant?.identity ?? "",
    );
    const { currentTip: tip } = useTipQueue(participant?.identity ?? "");

    const metadata = roomInfo.metadata
      ? JSON.parse(roomInfo.metadata)
      : { raisedHands: [] };
    const isHandRaised =
      metadata?.raisedHands?.includes(participant.identity) ?? false;

    const currentUser = {
      id: attributes?.id ?? "",
      name: attributes?.name ?? "",
      avatar: attributes?.avatar ?? "",
      username: attributes?.username ?? "",
      role: attributes?.role ?? "",
    };

    useEffect(() => {
      if (emote) {
        const timer = setTimeout(() => {
          removeEmote();
        }, 1500);

        return () => clearTimeout(timer);
      }
    }, [emote, removeEmote]);

    if (
      !attributes ||
      !attributes?.id ||
      !attributes?.name ||
      !attributes?.avatar ||
      !attributes?.username ||
      !attributes?.role
    ) {
      return null;
    }

    const { data: currenciesData } = useTippableCurrenciesQuery();
    const token = currenciesData?.currencies.find(
      (c) => c.symbol === tip?.currency,
    );

    return (
      <StageUserProfileModal user={currentUser} isMuted={isMuted}>
        <div className="flex w-full min-w-0 flex-shrink flex-col items-center">
          <div className="flex h-[70px] w-[67px] min-w-0 flex-shrink flex-grow items-center justify-center overflow-hidden">
            <div className="relative isolate">
              <Avatar className="size-[60px]">
                <AvatarImage src={currentUser.avatar} />
                <AvatarFallback />
              </Avatar>
              <div
                className={cn(
                  "absolute bottom-0 left-0 right-0 top-0 -z-10 flex items-center justify-center rounded-full bg-white transition-transform duration-200",
                  isSpeaking ? "scale-105" : "scale-75",
                )}
              />
              <div className="absolute -inset-0.5 isolate rounded-full">
                <AnimatePresence>
                  {(emote || tip) && (
                    <motion.div
                      key="reaction-background"
                      className="absolute -inset-px -z-10 rounded-full bg-dark-bk/80 backdrop-blur-sm"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{
                        opacity: 0,
                        transition: { duration: 1, delay: tip ? 0.5 : 0 },
                      }}
                      transition={{ duration: 0.2 }}
                    />
                  )}
                </AnimatePresence>
                <AnimatePresence mode="wait">
                  {emote && (
                    <motion.div key={emote}>
                      <motion.div
                        initial={{ opacity: 0, transform: "scale(0.5)" }}
                        animate={{ opacity: 1, transform: "scale(1)" }}
                        exit={{ opacity: 0, transform: "scale(2)" }}
                        transition={{ duration: 0.5 }}
                        className="absolute inset-0 z-10 flex items-center justify-center text-2xl"
                      >
                        <Emoji unified={emote} size={32} />
                      </motion.div>
                    </motion.div>
                  )}
                </AnimatePresence>
                <AnimatePresence mode="wait">
                  {isHandRaised && (
                    <motion.div key={"hand-raided-" + isHandRaised}>
                      <motion.div
                        initial={{ opacity: 0, transform: "scale(0.5)" }}
                        animate={{ opacity: 1, transform: "scale(1)" }}
                        exit={{
                          opacity: 0,
                          transform: "scale(1)",
                          transition: { duration: 0.3 },
                        }}
                        transition={{
                          ease: "easeOut",
                          bounce: 0,
                          duration: 0.2,
                        }}
                        className="absolute right-0 top-0 z-50 flex items-center justify-center rounded-full bg-chat-bubble/90 p-1 text-2xl backdrop-blur-sm"
                      >
                        <Emoji unified="270b" size={20} />
                      </motion.div>
                    </motion.div>
                  )}
                </AnimatePresence>
                <AnimatePresence mode="wait">
                  {tip && (
                    <motion.div
                      key={tip.id + "-tip"}
                      className="absolute inset-0 z-20 flex items-center justify-center"
                    >
                      <motion.img
                        src={token?.photoURL}
                        className="size-10 rounded-full"
                        alt={`${tip.currency} logo`}
                        initial={{
                          opacity: 0,
                          transform: "translateY(-150%) scale(0.5)",
                        }}
                        animate={{
                          opacity: 1,
                          transform: "translateY(0%) scale(1)",
                        }}
                        exit={{
                          opacity: 0,
                          transform: "translateY(0%) scale(1.5)",
                          transition: {
                            duration: 0.5,
                            ease: "easeIn",
                          },
                        }}
                        transition={{ duration: 0.7, ease: "easeOut" }}
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>
          <div className="relative mt-2 flex w-full flex-col">
            <div className="flex w-full min-w-0 flex-shrink flex-col overflow-hidden">
              <div className="truncate text-center text-sm font-medium text-off-white">
                {currentUser.name}
              </div>
            </div>
            <div className="flex items-center justify-center gap-0.5 self-center truncate text-gray-text">
              {currentUser.role === ROLES.HOST ||
              currentUser.role === ROLES.COHOST ||
              currentUser.role === ROLES.SPEAKER ? (
                isMuted ? (
                  <MutedOutlineIcon className="size-4 flex-shrink-0" />
                ) : (
                  <UnmutedOutlineIcon className="size-4 flex-shrink-0" />
                )
              ) : null}
              <span className="whitespace-nowrap text-sm">
                {ROLE_NAMES[currentUser.role as Role]}
              </span>
            </div>
            <AnimatePresence>
              {tip && (
                <motion.div
                  key="text-background"
                  className="absolute -inset-x-2 inset-y-0 flex flex-col overflow-hidden rounded-full bg-[#161616] py-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{
                    opacity: 0,
                  }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                />
              )}
            </AnimatePresence>
            <AnimatePresence mode="wait">
              {tip && (
                <TipAnimation
                  amount={tip.amount.toLocaleString()}
                  token={tip.currency as any}
                  from={tip.from}
                  key={tip.id + "-tip-handle"}
                />
              )}
            </AnimatePresence>
          </div>
        </div>
      </StageUserProfileModal>
    );
  },
);

StageUser.displayName = "StageUser";

const TipAnimation = ({
  amount,
  token,
  from,
}: {
  amount: string;
  token:
    | "COQ"
    | "AVAX"
    | "ARENA"
    | "CHAMP"
    | "KET"
    | "EROL"
    | "WINK"
    | "ABC"
    | "MU"
    | "BLUB"
    | "BOI"
    | "GURS"
    | "NOCHILL"
    | "MEAT"
    | "KIMBO"
    | "JOE"
    | "TECH"
    | "SOL"
    | "$WIF"
    | "USEDCAR"
    | "HARAMBE"
    | "BONK"
    | "MOUTAI";
  from: {
    name: string;
    avatar: string;
    username: string;
  };
}) => {
  const amountRef = useRef<HTMLDivElement>(null);
  const nameRef = useRef<HTMLDivElement>(null);
  const [textsWidth, setTextsWidth] = useState({
    amountContainerWidth: 0,
    nameContainerWidth: 0,
    amountTextWidth: 0,
    nameTextWidth: 0,
  });

  useLayoutEffect(() => {
    if (amountRef.current && nameRef.current) {
      const amountContainerWidth =
        amountRef.current.parentElement?.getBoundingClientRect().width || 0;
      const nameContainerWidth =
        nameRef.current.parentElement?.getBoundingClientRect().width || 0;
      const amountTextWidth = amountRef.current.getBoundingClientRect().width;
      const nameTextWidth = nameRef.current.getBoundingClientRect().width;

      setTextsWidth({
        amountContainerWidth,
        nameContainerWidth,
        amountTextWidth,
        nameTextWidth,
      });
    }
  }, []);

  return (
    <motion.div
      className="absolute -inset-x-2 inset-y-0 flex flex-col overflow-hidden rounded-full bg-[#161616] py-1"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{
        opacity: 0,
      }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      <div className="p-0 text-xs">
        <motion.span
          key={textsWidth.amountContainerWidth + "-tip-amount"}
          className="inline-block whitespace-nowrap p-0 text-xs font-semibold text-off-white"
          initial={{
            transform: `translateX(${Math.max(
              textsWidth.amountContainerWidth,
              textsWidth.amountTextWidth,
            )}px)`,
          }}
          animate={{
            transform: `translateX(${-textsWidth.amountTextWidth}px)`,
          }}
          transition={{ duration: 5, ease: "linear" }}
          ref={amountRef}
        >
          {amount} {token}
        </motion.span>
      </div>
      <div className="p-0 text-xs">
        <motion.span
          key={textsWidth.nameContainerWidth + "-tip-name"}
          className="inline-block whitespace-nowrap text-xs text-gray-text"
          initial={{
            transform: `translateX(${Math.max(
              textsWidth.nameContainerWidth,
              textsWidth.nameTextWidth,
            )}px)`,
          }}
          animate={{
            transform: `translateX(${-textsWidth.nameTextWidth}px)`,
          }}
          transition={{ duration: 5, ease: "linear" }}
          ref={nameRef}
        >
          {from.name}
        </motion.span>
      </div>
      <div className="absolute inset-y-0 left-0 w-4 bg-gradient-to-r from-[rgba(22,22,22,1)] via-[rgba(22,22,22,0.7)] to-[rgba(22,22,22,0.1)]" />
      <div className="absolute inset-y-0 right-0 w-4 bg-gradient-to-l from-[rgba(22,22,22,1)] via-[rgba(22,22,22,0.7)] to-[rgba(22,22,22,0.1)]" />
    </motion.div>
  );
};
