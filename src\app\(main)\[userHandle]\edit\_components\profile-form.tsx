"use client";

import { useEffect, useId, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";

import { useQueryClient } from "@tanstack/react-query";

import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useUserByHandleQuery } from "@/queries";
import { useUpdateBioMutation } from "@/queries/profile-mutations";

export const ProfileForm = () => {
  const queryClient = useQueryClient();
  const params = useParams() as { userHandle: string };
  const router = useRouter();
  const id = useId();
  const formItemId = `form-item-${id}`;
  const { data, isLoading } = useUserByHandleQuery(params.userHandle);
  const [bio, setBio] = useState("");

  const { mutateAsync: updateBio, isPending } = useUpdateBioMutation({
    onMutate: async ({ bio }) => {
      await queryClient.cancelQueries({
        queryKey: ["user", "handle", params.userHandle],
      });

      const previousData = queryClient.getQueryData([
        "user",
        "handle",
        params.userHandle,
      ]);

      queryClient.setQueryData(
        ["user", "handle", params.userHandle],
        (old: any) => {
          return {
            ...old,
            user: {
              ...old.user,
              twitterDescription: bio,
            },
          };
        },
      );

      return { previousData };
    },
    onError: (err, variables, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(
          ["user", "handle", params.userHandle],
          context.previousData,
        );
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["user", "handle", params.userHandle],
      });
      router.push(`/${params.userHandle}`);
    },
  });

  const handleSubmit = async () => {
    if (!bio) return;

    if (bio === data?.user.twitterDescription) {
      return toast.danger("No changes to save");
    }

    await updateBio({ bio });
  };

  useEffect(() => {
    if (data) {
      const regex = /<a\b[^>]*>(.*?)<\/a>/gi;
      let bioDescription = (data.user.twitterDescription ?? "")?.replace(
        regex,
        "$1",
      );
      bioDescription = bioDescription.replace(/<br\s*\/?>/gi, "\n");
      setBio(bioDescription);
    }
  }, [data]);

  return (
    <div className="mt-5 flex flex-grow flex-col px-5">
      <div className="flex flex-col justify-between gap-2">
        <div className="flex items-center justify-between gap-2">
          <Label htmlFor={formItemId}>Bio</Label>
        </div>
        <Textarea
          id={formItemId}
          placeholder="Tell us about yourself"
          value={bio}
          onChange={(e) => {
            setBio(e.target.value);
          }}
          maxLength={160}
          disabled={isPending || isLoading}
        />
      </div>
      <div className="mt-6 px-8 text-center text-sm/[22px] font-light text-gray-text">
        <span>
          Can&apos;t find what you&apos;re looking for? <br /> Check the{" "}
          <ProgressBarLink href="./fee-settings" className="text-brand-orange">
            Creator Fee Settings
          </ProgressBarLink>{" "}
          from your profile!
        </span>
      </div>
      <div className="mt-auto sm:mt-6 sm:self-end">
        <Button
          className="mb-4 mt-auto w-full sm:h-8 sm:w-[104px]"
          disabled={isPending || isLoading}
          onClick={handleSubmit}
        >
          Save
        </Button>
      </div>
    </div>
  );
};
