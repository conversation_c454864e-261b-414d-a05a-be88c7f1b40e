import React from "react";

import "../src/app/globals.css";

import type { Preview } from "@storybook/nextjs";

import { EthereumWalletConnectors } from "@dynamic-labs/ethereum";
import { GlobalWalletExtension } from "@dynamic-labs/global-wallet";
import { DynamicContextProvider } from "@dynamic-labs/sdk-react-core";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { initialize, mswLoader } from "msw-storybook-addon";

const queryClient = new QueryClient();

initialize();

export const decorators = [
  (Story: any) => (
    <DynamicContextProvider
      settings={{
        environmentId: process.env.NEXT_PUBLIC_DYNAMIC_ENVIRONMENT_ID || "demo",
        walletConnectors: [EthereumWalletConnectors],
        walletConnectorExtensions: [GlobalWalletExtension],
      }}
    >
      <QueryClientProvider client={queryClient}>
        <Story />
      </QueryClientProvider>
    </DynamicContextProvider>
  ),
];

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
  loaders: [mswLoader],
};

export default preview;
