"use client";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useDropDownToListenerMutation } from "@/queries";
import { useStageStore } from "@/stores/stage";

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogTitle,
} from "../ui/alert-dialog";
import { Button } from "../ui/button";
import { DialogHeader, DialogTitle } from "../ui/dialog";
import { Drawer, DrawerContent } from "../ui/drawer";

export function DropDownToListenerModal({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const id = useStageStore((state) => state.id!);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const { mutateAsync: dropDownToListener, isPending } =
    useDropDownToListenerMutation({
      onSuccess: () => {
        setOpen(false);
      },
    });

  const handleAccept = async () => {
    await dropDownToListener({
      stageId: id,
    });
  };

  const handleDecline = async () => {
    setOpen(false);
  };

  if (isTablet) {
    return (
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent className="max-w-md">
          <DropDownToListenerModalContent
            isPending={isPending}
            handleAccept={handleAccept}
            handleDecline={handleDecline}
          />
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen} dismissible={false}>
      <DrawerContent className="justify-start gap-6 text-left">
        <DropDownToListenerModalContent
          isPending={isPending}
          handleAccept={handleAccept}
          handleDecline={handleDecline}
          isDrawer
        />
      </DrawerContent>
    </Drawer>
  );
}

interface DropDownToListenerModalContentProps {
  isPending: boolean;
  handleAccept: () => void;
  handleDecline: () => void;
  isDrawer?: boolean;
}

const DropDownToListenerModalContent = ({
  isPending,
  handleAccept,
  handleDecline,
  isDrawer,
}: DropDownToListenerModalContentProps) => {
  return (
    <>
      <DialogHeader className="space-y-4">
        {isDrawer ? (
          <DialogTitle className="leading-6">
            Do you want to drop down to <br /> Listener role?
          </DialogTitle>
        ) : (
          <AlertDialogTitle className="leading-6">
            Do you want to drop down to <br /> Listener role?
          </AlertDialogTitle>
        )}
      </DialogHeader>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          className="flex-[1]"
          disabled={isPending}
          onClick={handleDecline}
        >
          Cancel
        </Button>
        <Button className="flex-[2]" onClick={handleAccept} loading={isPending}>
          Confirm
        </Button>
      </div>
    </>
  );
};
