import { useRouter } from "next/navigation";

import { LightningIcon, LightningIconOrange } from "@/components/icons";

export const UprisingBanner = () => {
  const router = useRouter();

  const handleClick = () => {
    router.push("/uprising");
  };

  return (
    <div
      className="relative cursor-pointer overflow-hidden rounded-lg bg-uprising-gradient p-4"
      onClick={handleClick}
    >
      {/* Large background lightning */}
      <div className="absolute -right-[530px] top-1/2 size-[1000px] -translate-y-1/2 rotate-[4.6deg] opacity-80 sm:-right-[400px]">
        <LightningIcon className="size-full text-[#121212]" />
      </div>
      <div className="absolute -right-[50px] top-1/2 size-[220px] -translate-y-1/2 p-0 mix-blend-plus-lighter sm:right-[-50px] sm:size-[300px]">
        {/* Medium background lightning */}
        <LightningIcon className="h-full w-full rotate-[15deg] text-[#121212]" />
        {/* Orange foreground lightning */}
        <LightningIconOrange className="absolute right-[28%] top-1/2 size-[90px] -translate-y-1/2 -rotate-[3deg] sm:size-[120px]" />
      </div>

      {/* Large background lightning */}
      {/* <div className="absolute -left-[160px] -top-[540px] size-[1200px] rotate-[4.6deg] opacity-80">
        <LightningIcon className="h-full w-full scale-100 text-[#121212]" />
      </div> */}
      {/* Medium background lightning */}
      {/* <div className="absolute -top-[200px] left-[210px] size-[500px] w-1/2 p-0  mix-blend-plus-lighter sm:left-[333px]">
        <LightningIcon className="h-full w-full rotate-[8deg] scale-90 text-[#121212]" />
      </div> */}
      {/* Orange foreground lightning */}
      {/* <div className="absolute right-[50px] top-4 size-[66px] -rotate-[3deg]">
        <div className="absolute rounded-full bg-brand-orange/30 blur-lg"></div>
        <LightningIcon className="relative h-full w-full scale-110 text-brand-orange drop-shadow-[0_0_10px_rgba(255,165,0,0.7)]" />
      </div> */}
      <div className="relative z-10 text-left">
        <h2 className="mb-2 text-2xl font-light leading-tight">
          <span className="text-white">ARENA </span>
          <span className="font-semibold text-brand-orange">UPRISING</span>
        </h2>
        <p className="text-sm leading-tight text-off-white">
          Connect, collaborate, collect!
        </p>
      </div>
    </div>
  );
};
