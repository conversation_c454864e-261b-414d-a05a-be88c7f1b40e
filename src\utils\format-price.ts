import {
  formatEther as _formatEther,
  formatUnits as _formatUnit,
} from "ethers";

const formatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 4,
});

export const formatEther = (price: string) => {
  return formatter.format(Number(_formatEther(price)));
};

export const formatAvax = (price: string) => {
  if (!price) return "0";

  const avax = Number(_formatEther(price));
  const formatter = new Intl.NumberFormat("en-US", {
    maximumFractionDigits: avax > 0.1 ? 2 : 4,
    minimumFractionDigits: 2,
  });
  return formatter.format(avax);
};

export const formatAvax6Digits = (price: string) => {
  if (!price) return "0";

  const avax = Number(_formatEther(price));
  const formatter = new Intl.NumberFormat("en-US", {
    maximumFractionDigits: avax > 0.1 ? 2 : avax > 0.001 ? 4 : 6,
    minimumFractionDigits: 2,
  });
  return formatter.format(avax);
};

export const formatUnits = (price: string, decimals: number) => {
  return formatter.format(Number(_formatUnit(price, decimals)));
};

export const formatCurrency = (currency: string, amount: string) => {
  if (
    currency === "SOL" ||
    currency === "Bonk" ||
    currency === "$WIF" ||
    currency === "USEDCAR" ||
    currency === "Moutai" ||
    currency === "HARAMBE"
  ) {
    return amount ?? "0.00";
  }

  if (currency === "MEAT") {
    return formatUnits(amount, 6);
  }

  return formatEther(amount);
};
