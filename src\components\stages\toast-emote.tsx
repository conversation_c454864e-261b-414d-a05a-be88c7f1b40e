"use client";

import { Emoji } from "emoji-picker-react";
import { toast as sonner } from "sonner";

export const toastEmote = ({
  emote,
  name,
}: {
  emote: string;
  name: string;
}) => {
  return sonner.custom(
    () => {
      return (
        <div className="relative top-[env(safe-area-inset-top)] flex justify-center">
          <div className="isolate flex items-center rounded-lg px-4 py-3">
            <div className="flex size-12 items-center justify-center rounded-full border border-off-white/10 bg-dark-bk">
              <Emoji unified={emote} size={28} />
            </div>
            <span className="-z-10 -ml-2 inline-block rounded-lg border border-off-white/10 bg-dark-bk py-1 pl-3 pr-2 text-xs leading-5 text-off-white">
              {name}
            </span>
          </div>
        </div>
      );
    },
    {
      // duration: 10000,
      position: "top-center",
      dismissible: true,
    },
  );
};
