import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import { depositSolana } from "@/api/balance";
import { tipSolana } from "@/api/client/chain";
import {
  deletePinPost,
  postBlockUser,
  postCreateLivestream,
  postEditLivestream,
  postEndLivestream,
  postGenerateLivestreamIngress,
  postJoinLivestream,
  postPinPost,
  postRemindLivestream,
  postStartLivestream,
  postTipLivestream,
  postTipNotifyLivestream,
  postUnblockUser,
} from "@/api/client/livestream";
import { toast } from "@/components/toast";
import { minDelay } from "@/utils/min-delay";

import {
  CreateLivestreamParams,
  CreateLivestreamResponse,
  DeletePinPostParams,
  EditLivestreamParams,
  EditLivestreamResponse,
  EndLivestreamParams,
  GenerateLivestreamIngressParams,
  JoinLivestreamParams,
  JoinLivestreamResponse,
  LivestreamIdParams,
  LivestreamIngressResponse,
  LivestreamOkResponse,
  PostBlockUserParams,
  PostPinPostParams,
} from "./types/livestream";

type CreateLivestreamMutationType = MutationOptions<
  CreateLivestreamResponse,
  DefaultError,
  CreateLivestreamParams,
  any
>;

export const useCreateLivestreamMutation = (
  options?: CreateLivestreamMutationType,
) => {
  return useMutation({
    mutationFn: postCreateLivestream,
    ...options,
  });
};

type EndLivestreamMutationType = MutationOptions<
  LivestreamOkResponse,
  DefaultError,
  EndLivestreamParams,
  any
>;

export const useEndLivestreamMutation = (
  options?: EndLivestreamMutationType,
) => {
  return useMutation({
    mutationFn: postEndLivestream,
    ...options,
  });
};

type JoinLivestreamMutationType = MutationOptions<
  JoinLivestreamResponse,
  DefaultError,
  JoinLivestreamParams,
  any
>;

export const useJoinLivestreamMutation = (
  options?: JoinLivestreamMutationType,
) => {
  return useMutation({
    mutationFn: postJoinLivestream,
    ...options,
  });
};

type GenerateLivestreamIngressMutationType = MutationOptions<
  LivestreamIngressResponse,
  DefaultError,
  GenerateLivestreamIngressParams,
  any
>;

export const useGenerateLivestreamIngressMutation = (
  options?: GenerateLivestreamIngressMutationType,
) => {
  return useMutation({
    mutationFn: postGenerateLivestreamIngress,
    ...options,
  });
};

type StartLivestreamMutationType = MutationOptions<
  JoinLivestreamResponse,
  DefaultError,
  LivestreamIdParams,
  any
>;

export const useStartLivestreamMutation = (
  options?: StartLivestreamMutationType,
) => {
  return useMutation({
    mutationFn: postStartLivestream,
    ...options,
  });
};

type RemindLivestreamMutationType = MutationOptions<
  LivestreamOkResponse,
  DefaultError,
  LivestreamIdParams,
  any
>;

export const useRemindLivestreamMutation = (
  options?: RemindLivestreamMutationType,
) => {
  return useMutation({
    mutationFn: postRemindLivestream,
    ...options,
  });
};

type EditLivestreamMutationType = MutationOptions<
  EditLivestreamResponse,
  DefaultError,
  EditLivestreamParams,
  any
>;

export const useEditLivestreamMutation = (
  options?: EditLivestreamMutationType,
) => {
  return useMutation({
    mutationFn: postEditLivestream,
    ...options,
  });
};

type PinPostMutationType = MutationOptions<
  LivestreamOkResponse,
  DefaultError,
  PostPinPostParams,
  any
>;

export const usePinPostLivestreamMutation = (options?: PinPostMutationType) => {
  return useMutation({
    mutationFn: postPinPost,
    ...options,
  });
};

type DeletePinPostMutationType = MutationOptions<
  LivestreamOkResponse,
  DefaultError,
  DeletePinPostParams,
  any
>;

export const useDeletePinPostLivestreamMutation = (
  options?: DeletePinPostMutationType,
) => {
  return useMutation({
    mutationFn: deletePinPost,
    ...options,
  });
};

type BlockUserMutationType = MutationOptions<
  LivestreamOkResponse,
  DefaultError,
  PostBlockUserParams,
  any
>;

export const useBlockUserLivestreamMutation = (
  options?: BlockUserMutationType,
) => {
  return useMutation({
    mutationFn: postBlockUser,
    ...options,
  });
};

type UnblockUserMutationType = MutationOptions<
  LivestreamOkResponse,
  DefaultError,
  PostBlockUserParams,
  any
>;

export const useUnblockUserLivestreamMutation = (
  options?: UnblockUserMutationType,
) => {
  return useMutation({
    mutationFn: postUnblockUser,
    ...options,
  });
};

// Tip Mutations
interface TipMutationData {
  currency: string;
  tipAmount: string;
  userId: string;
  livestreamId: string;
  wallet: string;
  isDynamic?: boolean;
  txHash: string;
  txData: string;
  isToken?: boolean;
  tokenContractAddress?: string;
}

type TipMutationType = MutationOptions<
  unknown,
  DefaultError,
  TipMutationData,
  any
>;

async function tip({
  currency,
  tipAmount,
  userId,
  livestreamId,
  wallet,
  isDynamic,
  txHash,
  txData,
  isToken,
  tokenContractAddress,
}: TipMutationData) {
  if (
    ["SOL", "Bonk", "$WIF", "USEDCAR", "Moutai", "HARAMBE"].includes(currency)
  ) {
    const provider =
      typeof window !== "undefined" && (window as any).phantom?.solana;

    if (wallet == "Phantom") {
      await depositSolana(
        provider,
        parseFloat(tipAmount),
        currency,
        "finalized",
      );
      const response = tipSolana({
        userIdTo: userId,
        amount: parseFloat(tipAmount),
        currency,
      });

      return response;
    } else if (wallet == "The Arena") {
      const response = tipSolana({
        userIdTo: userId,
        amount: parseFloat(tipAmount),
        currency,
      });

      return response;
    }
  } else {
    let response;
    if (isDynamic) {
      response = await postTipNotifyLivestream({
        livestreamId,
        tipAmount,
        userId,
        currency,
        txHash,
        txData,
        isToken,
        tokenContractAddress,
      });
    } else {
      if (currency.toLowerCase() === "champ") {
        toast.danger("Please migrate your wallet to tip $CHAMP!");
        return;
      }
      if (currency.toLowerCase() === "erol") {
        toast.danger("Please migrate your wallet to tip $EROL!");
        return;
      }
      if (currency.toLowerCase() === "abc") {
        toast.danger("Please migrate your wallet to tip $ABC!");
        return;
      }
      if (currency.toLowerCase() === "mu") {
        toast.danger("Please migrate your wallet to tip $MU!");
        return;
      }
      if (currency.toLowerCase() === "blub") {
        toast.danger("Please migrate your wallet to tip $BLUB!");
        return;
      }
      if (currency.toLowerCase() === "boi") {
        toast.danger("Please migrate your wallet to tip $BOI!");
        return;
      }
      if (currency.toLowerCase() === "ket") {
        toast.danger("Please migrate your wallet to tip $KET!");
        return;
      }
      if (currency.toLowerCase() === "wink") {
        toast.danger("Please migrate your wallet to tip $WINK!");
        return;
      }
      if (isToken) {
        toast.danger(`Please migrate your wallet to tip $${currency}!`);
        return;
      }
      response = await postTipLivestream({
        livestreamId,
        tipAmount,
        userId,
        currency,
      });
    }

    return response;
  }
}

export const useTipLivestreamMutation = (options?: TipMutationType) => {
  return useMutation({
    mutationFn: async (data: TipMutationData) => {
      return await minDelay(tip(data));
    },
    ...options,
  });
};
