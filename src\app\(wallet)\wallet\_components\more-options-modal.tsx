"use client";

import { useState } from "react";

import { useHideTokensWithZeroBalance } from "@/app/(main)/wallet/hooks/use-hide-tokens-with-zero-balance";
import { ClaimNftModal } from "@/app/(wallet)/wallet/_components/claim-nft-modal";
import { ClaimNftModalDegods } from "@/app/(wallet)/wallet/_components/claim-nft-modal-degods";
import { ConnectExternalWalletModal } from "@/app/(wallet)/wallet/_components/connect-external-wallet-modal";
import { Web3Modal } from "@/app/(wallet)/wallet/_components/web3modal";
import { EllipsisHorizontalFilledIcon } from "@/components/icons";
import { EyeSlashOutlineIcon } from "@/components/icons/eye-slash";
import { FolderOpenIcon } from "@/components/icons/folder-open";
import { KeyOutlineIcon } from "@/components/icons/key-outline";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

import { AirdropWalletModal } from "./airdrop-wallet-modal";
import { PrivateKeyModal } from "./private-key-modal";

export const MoreOptionsModal = () => {
  const [open, setOpen] = useState(false);
  const [pkOpen, setPkOpen] = useState(false);
  const [airdropOpen, setAirdropOpen] = useState(false);
  const [claimNftOpen, setClaimNftOpen] = useState(false);
  const [claimNftOpenDegods, setClaimNftOpenDegods] = useState(false);
  const [connectExternalWalletOpen, setConnectExternalWalletOpen] =
    useState(false);

  const [hideZeroBalance, setHideZeroBalance] = useHideTokensWithZeroBalance();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  return (
    <>
      {isTablet && (
        <DropdownMenu open={open} onOpenChange={setOpen}>
          <DropdownMenuTrigger className="outline-none">
            <EllipsisHorizontalFilledIcon className="size-5 fill-off-white" />
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="flex w-[360px] flex-col gap-4 rounded-2xl border border-[#3B3B3B]/30 bg-[#0F0F0F]/90 p-4 backdrop-blur-sm"
          >
            <label className="flex w-full items-center justify-between rounded-[10px] border border-[#646464] p-4">
              <div className="flex items-center gap-4 text-sm font-semibold text-off-white">
                <EyeSlashOutlineIcon
                  className="size-[18px] text-off-white"
                  strokeWidth={2}
                />
                Hide tokens with zero balance
              </div>
              <Switch
                checked={hideZeroBalance ?? false}
                onCheckedChange={setHideZeroBalance}
              />
            </label>
            <DropdownMenuItem
              onSelect={() => {
                setPkOpen(true);
                setOpen(false);
              }}
              className="flex cursor-pointer items-center gap-4 rounded-[10px] border border-[#646464] p-4 hover:bg-gray-text/10"
            >
              <KeyOutlineIcon className="size-[18px] flex-shrink-0 text-off-white" />
              <div>
                <p className="text-sm font-semibold text-off-white">
                  Export your private keys
                </p>
                <p className="mt-0.5 text-sm leading-[18px] text-[#F4F4F4]/80">
                  You can export your private keys to use this wallet in another
                  device.
                </p>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem
              onSelect={() => {
                setConnectExternalWalletOpen(true);
                setOpen(false);
              }}
              className="flex cursor-pointer items-center gap-4 rounded-[10px] bg-[linear-gradient(109deg,#F36A27_10.68%,#CE4909_79.68%)] p-4"
            >
              <FolderOpenIcon className="size-[18px] flex-shrink-0 text-off-white" />
              <div>
                <p className="text-sm font-semibold text-[#EDEDED]">
                  Connect Wallet to Earn Looty Keys
                </p>
                <p className="mt-0.5 text-sm leading-[18px] text-[#F4F4F4CC]">
                  Earn a Looty key for being an active member of The Arena.
                </p>
              </div>
            </DropdownMenuItem>
            {/* <DropdownMenuItem
              onSelect={() => {
                setAirdropOpen(true);
                setOpen(false);
              }}
              className="flex cursor-pointer items-center gap-4 rounded-[10px] border border-[#646464] p-4 hover:bg-gray-text/10"
            >
              <WalletOutlineIcon className="size-[18px] flex-shrink-0 text-off-white" />
              <div>
                <p className="text-sm font-semibold text-[#EDEDED]">
                  Connect Airdrop Wallet
                </p>
                <p className="mt-0.5 text-sm leading-[18px] text-[#9D9D9D]">
                  You need to connect a different wallet to claim the $ARENA
                  airdrop.
                </p>
              </div>
            </DropdownMenuItem> */}
            {/*<DropdownMenuItem*/}
            {/*  onSelect={() => {*/}
            {/*    setClaimNftOpen(true);*/}
            {/*    setOpen(false);*/}
            {/*  }}*/}
            {/*  className="flex cursor-pointer items-center gap-4 rounded-[10px] border border-[#646464] p-4 hover:bg-gray-text/10"*/}
            {/*>*/}
            {/*  <ShieldCheckFilledIcon className="size-[18px] flex-shrink-0 text-off-white" />*/}
            {/*  <div>*/}
            {/*    <p className="text-sm font-semibold text-[#EDEDED]">*/}
            {/*      Claim a Gogonauts NFT Badge*/}
            {/*    </p>*/}
            {/*    <p className="mt-0.5 text-sm leading-[18px] text-[#9D9D9D]">*/}
            {/*      Get your Gogonauts NFT Badge.*/}
            {/*    </p>*/}
            {/*  </div>*/}
            {/*</DropdownMenuItem>*/}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      {!isTablet && (
        <Drawer open={open} onOpenChange={setOpen}>
          <DrawerTrigger className="outline-none">
            <EllipsisHorizontalFilledIcon className="size-5 fill-off-white" />
          </DrawerTrigger>
          <DrawerContent>
            <h2 className="text-base font-semibold leading-[22px] text-off-white">
              More Options
            </h2>
            <label className="mt-8 flex w-full items-center justify-between rounded-[10px] border border-[#646464] p-4">
              <div className="flex items-center gap-4 text-sm font-semibold text-off-white">
                <EyeSlashOutlineIcon
                  className="size-[18px] text-off-white"
                  strokeWidth={2}
                />
                Hide tokens with zero balance
              </div>
              <Switch
                checked={hideZeroBalance ?? false}
                onCheckedChange={setHideZeroBalance}
              />
            </label>
            <button
              className="mt-4 flex items-center gap-4 rounded-[10px] border border-[#646464] p-4 text-left"
              onClick={() => {
                setPkOpen(true);
                setOpen(false);
              }}
            >
              <KeyOutlineIcon className="size-[18px] flex-shrink-0 text-off-white" />
              <div>
                <p className="text-sm font-semibold text-off-white">
                  Export your private keys
                </p>
                <p className="mt-0.5 text-sm leading-[18px] text-[#F4F4F4]/80">
                  You can export your private keys to use this wallet in another
                  device.
                </p>
              </div>
            </button>
            <button
              className="mt-4 flex items-center justify-start gap-4 rounded-[10px] bg-[linear-gradient(109deg,#F36A27_10.68%,#CE4909_79.68%)] p-4 text-left"
              onClick={() => {
                setConnectExternalWalletOpen(true);
                setOpen(false);
              }}
            >
              <FolderOpenIcon className="size-[18px] flex-shrink-0 text-off-white" />
              <div>
                <p className="text-sm font-semibold text-[#EDEDED]">
                  Connect Wallet to Earn Looty Keys
                </p>
                <p className="mt-0.5 text-sm leading-[18px] text-[#F4F4F4CC]">
                  Earn a Looty key for being an active member of The Arena.
                </p>
              </div>
            </button>
            {/* <button
              className="mt-4 flex items-center gap-4 rounded-[10px] border border-[#646464] p-4 text-left"
              onClick={() => {
                setAirdropOpen(true);
                setOpen(false);
              }}
            >
              <WalletOutlineIcon className="size-[18px] flex-shrink-0 text-off-white" />
              <div>
                <p className="text-sm font-semibold text-[#EDEDED]">
                  Connect Airdrop Wallet
                </p>
                <p className="mt-0.5 text-sm leading-[18px] text-[#9D9D9D]">
                  You need to connect a different wallet to claim the $ARENA
                  airdrop.
                </p>
              </div>
            </button> */}
            {/*<button*/}
            {/*  className="mt-4 flex items-center gap-4 rounded-[10px] border border-[#646464] p-4 text-left"*/}
            {/*  onClick={() => {*/}
            {/*    setClaimNftOpen(true);*/}
            {/*    setOpen(false);*/}
            {/*  }}*/}
            {/*>*/}
            {/*  <ShieldCheckFilledIcon className="size-[18px] flex-shrink-0 text-off-white" />*/}
            {/*  <div>*/}
            {/*    <p className="text-sm font-semibold text-[#EDEDED]">*/}
            {/*      Claim a Gogonauts NFT Badge*/}
            {/*    </p>*/}
            {/*    <p className="mt-0.5 text-sm leading-[18px] text-[#9D9D9D]">*/}
            {/*      Get your Gogonauts NFT badge.*/}
            {/*    </p>*/}
            {/*  </div>*/}
            {/*</button>*/}
          </DrawerContent>
        </Drawer>
      )}
      <PrivateKeyModal open={pkOpen} setOpen={setPkOpen} />
      <ConnectExternalWalletModal
        open={connectExternalWalletOpen}
        setOpen={setConnectExternalWalletOpen}
      />
      <Web3Modal>
        <AirdropWalletModal open={airdropOpen} setOpen={setAirdropOpen} />
        <ClaimNftModal open={claimNftOpen} setOpen={setClaimNftOpen} />
        <ClaimNftModalDegods
          open={claimNftOpenDegods}
          setOpen={setClaimNftOpenDegods}
        />
        {/*<ClaimWithWalletAdapter*/}
        {/*  open={claimWithWalletAdapterOpen}*/}
        {/*  setOpen={setClaimWithWalletAdapterOpen}*/}
        {/*/>*/}
      </Web3Modal>
    </>
  );
};
