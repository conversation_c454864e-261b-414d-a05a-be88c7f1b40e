import type { Config } from "tailwindcss";

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    colors: {
      transparent: "transparent",
      current: "currentColor",
      white: "#ffffff",
      "brand-orange": "#EB540A",
      "off-white": "#F4F4F4",
      "gray-text": "#808080",
      "light-gray-text": "#B5B5B5",
      "gray-bg": "#0E0E0E",
      "dark-gray": "#3B3B3B",
      "gray-border": "#333",
      "dark-bk": "#020202",
      "chat-bubble": "#1A1A1A",
      "dark-brand": "#CB4A0B",
      "light-danger": "#BE5D5D",
      "light-background": "#1A1A1A",
      "lighter-background": "#2A2A2A",
      danger: "#D14848",
      green: "#40B877",
      purple: "#6F15C8",
      red: {
        600: "#DC2626",
      },
    },
    extend: {
      backgroundImage: {
        "orange-gradient": "linear-gradient(90deg, #D64C05 0%, #FF5626 100%)",
        "purple-gradient":
          "linear-gradient(287deg, #6F15C8 35.11%, #AC58FF 121.45%)",
        "purple-gradient/50":
          "linear-gradient(287deg, rgba(111, 21, 200, 0.50) 35.11%, rgba(172, 88, 255, 0.50) 121.45%)",
        "gray-gradient":
          "linear-gradient(92deg, #EFEFEF -34.98%, #F1F1F1 -34.97%, #C3C3C3 85.7%)",
        "uprising-gradient":
          "linear-gradient(90deg, #111010 0%, #111010 25.5%, #562B81 100%)",
        "chat-bg-gradient":
          "linear-gradient(180deg, rgba(7, 6, 6, 0.92) 0%, #070606 100%);",
        "chat-overlay-gradient":
          "linear-gradient(180deg, #070606 0%, #070606 30%, rgba(7, 6, 6, 0) 100%);",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "highlight-bg": {
          "0%": {
            backgroundColor: "rgba(235 84 10 / 0)",
          },
          "30%": {
            backgroundColor: "rgb(235 84 10 / 0.15)",
          },
          "70%": {
            backgroundColor: "rgb(235 84 10 / 0.15)",
          },
          "100%": {
            backgroundColor: "rgb(235 84 10 / 0)",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "highlight-bg": "highlight-bg 1.5s ease-in-out",
      },
      screens: {
        xll: "1350px",
        xs: { max: "380px" },
      },
      dropShadow: {
        badge: "0px 0px 4px rgba(255,255,255,0.50)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;

export default config;
