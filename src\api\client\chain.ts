import { axios } from "@/lib/axios";
import { ChainWithdrawData } from "@/queries/types";
import { GetSolanaAddressResponse } from "@/queries/types/chain";
import { SendTipsData } from "@/queries/types/send-tips-data";

export const getChainDegodsBadgeAvailable = async () => {
  const response = await axios.get(`/chain/degods_badge_available`);
  return response.data;
};

export const getChainDokyoBadgeAvailable = async () => {
  const response = await axios.get(`/chain/dokyo_badge_available`);
  return response.data;
};

export const getChainSappySealsBadgeAvailable = async () => {
  const response = await axios.get(`/chain/sappy_seals_badge_available`);
  return response.data;
};

export const checkBadgesAvailable = async (badgeType: number) => {
  const response = await axios.post(`/chain/badge_available/${badgeType}`);
  return response.data;
};

export const getSolanaAddress = async () => {
  const response = await axios.get<GetSolanaAddressResponse>(
    `/chain/getSolanaAddress`,
  );
  return response.data;
};

export const postChainWithdraw = async (data: ChainWithdrawData) => {
  const response = await axios.post(`/chain/withdraw`, data);
  return response.data;
};

export const postChainSetClaimAddress = async (address: string) => {
  const response = await axios.post<boolean>(`/chain/set_claim_address`, {
    address,
  });
  return response.data;
};

export const postClaimNft = async (address: string, signature: string) => {
  const response = await axios.post("/badges/give_badge", {
    address,
    signature,
  });
  return response.data;
};

export const postClaimNftDegods = async (
  address: string,
  signature: string,
) => {
  const response = await axios.post("/badges/give_badge_degods", {
    address,
    signature,
  });
  return response.data;
};

export const getOrCreateSolanaWallet = async () => {
  const response = await axios.post<{
    response: {
      solanaAddress: string;
    };
  }>(`/chain/getOrCreateSolanaWallet`);
  return response.data;
};

export const tipSolana = async (data: {
  userIdTo: string;
  amount: number;
  currency: string;
}) => {
  const response = await axios.post(`/chain/tipSolana`, data);
  return response.data;
};

export const sendTips = async (data: SendTipsData) => {
  const response = await axios.post(`/chain/sendTips`, data);
  return response.data;
};
