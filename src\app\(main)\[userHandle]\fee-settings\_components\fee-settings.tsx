"use client";

import { useEffect, useState } from "react";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { MinusIcon, PlusIcon } from "lucide-react";
import { encodeFunction<PERSON><PERSON>, Hex } from "viem";

import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import {
  BACKEND_FRIENDS_CONTRACT,
  BACKEND_FRIENDS_CONTRACT_ABI,
} from "@/environments/BACKEND_FRIENDS_CONTRACT";
import { useFeePercentQuery } from "@/queries";
import { useUser } from "@/stores";
import { cn } from "@/utils";

const MIN_AUTHOR_FEE = 1;
const MAX_AUTHOR_FEE = 7;

export const FeeSettings = () => {
  const { primaryWallet } = useDynamicContext();

  const { user } = useUser();
  const [authorFee, setAuthorFee] = useState(0);
  const [platformFee, setPlatformFee] = useState(0);
  const [unappliedChanges, setUnappliedChanges] = useState(false);
  const [isPending, setIsPending] = useState(false);

  const { data: fees } = useFeePercentQuery({
    address: user?.addressBeforeDynamicMigration || user?.address,
  });

  const setUserFee = async () => {
    if (isPending) return;

    if (user && user.address === user.dynamicAddress?.toLowerCase()) {
      setIsPending(true);
      console.log("Attempt to set author fee through Dynamic wallet");
      if (!primaryWallet) {
        throw new Error("Dynamic wallet is not initialized");
      }
      if (!isEthereumWallet(primaryWallet)) {
        throw new Error("This wallet is not a Ethereum wallet");
      }

      let userAddress;
      if (user?.addressBeforeDynamicMigration) {
        userAddress = user?.addressBeforeDynamicMigration;
      } else {
        userAddress = user?.address;
      }

      const walletClient = await primaryWallet.getWalletClient();
      try {
        const functionName = "setIndividualSubjectFee";
        const args = [userAddress, authorFee];

        await walletClient.sendTransaction({
          account: primaryWallet.address as Hex,
          chain: walletClient.chain,
          to: BACKEND_FRIENDS_CONTRACT.addressMainnet as Hex,
          value: 0n,
          data: encodeFunctionData({
            abi: BACKEND_FRIENDS_CONTRACT_ABI,
            functionName,
            args,
          }),
        });

        setUnappliedChanges(false);
        toast.green(`Successfully saved`);
      } catch (e: unknown) {
        if (e instanceof Error) {
          if (e.name === "TransactionExecutionError") {
            toast.red(
              e.message || "Transaction execution failed. Please try again.",
            );
          } else {
            toast.red(e.message || "An error occurred");
          }
        } else {
          toast.red("An unknown error occurred");
        }

        console.error({ e });
      } finally {
        setIsPending(false);
      }
    } else {
      toast.red("Dynamic wallet not found. You may not have migrated.");
    }
  };

  useEffect(() => {
    if (fees) {
      setAuthorFee(fees.subjectFee);
      setPlatformFee(fees.protocolFee + fees.referralFee);
    }
  }, [fees]);

  const handleSubmit = () => {
    setUserFee();
  };

  const handleDecrease = (event: React.PointerEvent<SVGSVGElement>) => {
    setAuthorFee(Math.max(authorFee - 1, MIN_AUTHOR_FEE));
    setUnappliedChanges(true);
  };

  const handleIncrease = (event: React.PointerEvent<SVGSVGElement>) => {
    setAuthorFee(Math.min(authorFee + 1, MAX_AUTHOR_FEE));
    setUnappliedChanges(true);
  };

  return (
    <div className="mt-5 flex flex-grow flex-col px-5">
      <span className="mb-4 text-base text-off-white">Set Creator Fee</span>
      <span className="mb-4 text-xs font-light text-gray-text">
        You can adjust the fee to increase your earnings or lower it to make
        your tickets more appealing. The default fee is 7%. You can change this
        anytime.
      </span>
      <div className="mb-4 flex h-[52px] w-full select-none flex-row items-center justify-between rounded-lg border-[1px] border-gray-text px-4">
        <MinusIcon
          onPointerUp={handleDecrease}
          className={cn(
            "w-[12px]",
            authorFee <= MIN_AUTHOR_FEE && "text-dark-gray",
            authorFee > MIN_AUTHOR_FEE && " cursor-pointer",
          )}
        />
        <span className="text-sm text-off-white">{authorFee}</span>
        <PlusIcon
          onPointerUp={handleIncrease}
          className={cn(
            "w-[12px]",
            authorFee >= MAX_AUTHOR_FEE && "text-dark-gray",
            authorFee < MAX_AUTHOR_FEE && " cursor-pointer",
          )}
        />
      </div>
      <div className="flex flex-col gap-2 rounded-[10px] bg-[#1A1A1A] p-[10px]">
        <div className="flex flex-row justify-between">
          <span className="text-xs font-light text-light-gray-text">
            Your earnings per sale
          </span>
          <div className="text-xs text-off-white">{` %${authorFee}`}</div>
        </div>
        <div className="flex flex-row justify-between">
          <span className="text-xs font-light text-light-gray-text">
            Total amount including Platform Fees{" "}
          </span>
          <div className="text-xs text-off-white">{` %${authorFee + platformFee}`}</div>
        </div>
      </div>
      <Button
        className="mb-4 mt-auto w-full sm:mt-8 sm:self-end"
        disabled={!unappliedChanges || isPending}
        onClick={handleSubmit}
      >
        {isPending ? "Saving..." : "Save"}
      </Button>
    </div>
  );
};
