"use client";

import { useState } from "react";

import CopyToClipboard from "react-copy-to-clipboard";

import {
  BanOutlineIcon,
  EllipsisHorizontalFilledIcon,
  LinkOutlineIcon,
  PencilOutlineIcon,
} from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useUser } from "@/stores";
import { CommunityExtended } from "@/types/community";

import { BanCommunityModal } from "./ban-community-modal";

export const CommunityProfileMenu = ({
  isOwner,
  path,
  community,
}: {
  isOwner: boolean;
  path: string;
  community: CommunityExtended;
}) => {
  const [open, setOpen] = useState(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const { user } = useUser();
  const [isBanModalOpen, setIsBanModalOpen] = useState(false);

  const profileLink = typeof window !== "undefined" ? window.location.href : "";

  return (
    <>
      {isTablet && (
        <DropdownMenu open={open} onOpenChange={setOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="ml-1 flex size-[26px] flex-shrink-0 items-center justify-center border-none p-0 outline-none"
            >
              <EllipsisHorizontalFilledIcon className="size-5 fill-off-white" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[220px]">
            {isOwner && (
              <DropdownMenuItem className="w-full gap-4" asChild>
                <ProgressBarLink href={`/community/${path}/edit`}>
                  <button className="flex gap-4">
                    <PencilOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Edit Token Profile</span>
                  </button>
                </ProgressBarLink>
              </DropdownMenuItem>
            )}
            <DropdownMenuItem className="w-full gap-4" asChild>
              <CopyToClipboard
                text={profileLink}
                onCopy={() => {
                  toast.green("Copied the profile link to the clipboard");
                  setOpen(false);
                }}
              >
                <button className="gap-4">
                  <LinkOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>
                    {isOwner ? "Share Profile" : "Copy link to profile"}
                  </span>
                </button>
              </CopyToClipboard>
            </DropdownMenuItem>
            {user && user.isMod && (
              <DropdownMenuItem>
                <button
                  className="flex gap-4"
                  onClick={() => setIsBanModalOpen((prev) => !prev)}
                >
                  <BanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Ban</span>
                </button>
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      {!isTablet && (
        <Drawer open={open} onOpenChange={setOpen}>
          <DrawerTrigger>
            <EllipsisHorizontalFilledIcon className="size-5 fill-off-white" />
          </DrawerTrigger>
          <DrawerContent className="px-4 pt-4">
            {isOwner && (
              <ProgressBarLink href={`/community/${path}/edit`}>
                <button className="flex items-center gap-2 p-2 text-base leading-5">
                  <PencilOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                  <span>Edit Token Profile</span>
                </button>
              </ProgressBarLink>
            )}
            <CopyToClipboard
              text={profileLink}
              onCopy={() => {
                toast.green("Copied the profile link to the clipboard");
                setOpen(false);
              }}
            >
              <button className="flex items-center gap-2 p-2 text-base leading-5">
                <LinkOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>
                  {isOwner ? "Share Profile" : "Copy link to profile"}
                </span>
              </button>
            </CopyToClipboard>
            {user && user.isMod && (
              <button
                className="flex items-center gap-2 p-2 text-base leading-5"
                onClick={() => setIsBanModalOpen((prev) => !prev)}
              >
                <BanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                <span>Ban</span>
              </button>
            )}
          </DrawerContent>
        </Drawer>
      )}
      {isBanModalOpen && (
        <BanCommunityModal
          community={community}
          open={isBanModalOpen}
          setOpen={setIsBanModalOpen}
        />
      )}
    </>
  );
};
