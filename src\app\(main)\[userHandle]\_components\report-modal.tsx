import { useState } from "react";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { toast } from "@/components/toast";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import {
  useReportThreadMutation,
  useReportUserMutation,
} from "@/queries/report-mutations";
import { ThreadUser, User } from "@/types";

import { useUser } from "../../../../stores";
import { DetailsPage } from "./report-modal/details-page";
import { Footer } from "./report-modal/footer";
import { ReasonPage } from "./report-modal/reason-page";
import { SubmitPage } from "./report-modal/submit-page";

interface ReportModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: User | ThreadUser;
  threadId?: string;
}

export const ReportModal = ({
  open,
  setOpen,
  user,
  threadId,
}: ReportModalProps) => {
  const [page, setPage] = useState<"reason" | "details" | "submit">("reason");
  const [reason, setReason] = useState<string | null>(null);
  const [description, setDescription] = useState("");
  const { user: currentUser } = useUser();
  const descriptionLength = description.length;
  const maxDescriptionLength = 280;

  const isContentReport = !!threadId;

  const { mutateAsync: reportUser, isPending: isReportUserPending } =
    useReportUserMutation({
      onSuccess: () => {
        toast.green("User reported!");
        setOpen(false);
        setTimeout(reset, 200);
      },
      onError: () => {
        toast.red("Failed to submit report");
      },
    });

  const { mutateAsync: reportThread, isPending: isReportThreadPending } =
    useReportThreadMutation({
      onSuccess: () => {
        toast.green("Post reported!");
        setOpen(false);
        setTimeout(reset, 200);
      },
      onError: () => {
        toast.red("Failed to submit report");
      },
    });

  const handleReport = async (blockUser: boolean) => {
    if (!reason) return;

    if (user.id === currentUser?.id) {
      toast.red("You cannot report yourself");
      return;
    }

    if (threadId) {
      await reportThread({
        threadId,
        reason,
        details: description || "",
        blockUser,
      });
    } else {
      await reportUser({
        userId: user.id,
        reason,
        details: description || "",
        blockUser,
      });
    }
    reset();
  };

  const reset = () => {
    setReason(null);
    setDescription("");
    setPage("reason");
  };

  const handleBackClick = () => {
    if (page === "reason") {
      setOpen(false);
    } else {
      setPage((prev) => {
        if (prev === "details") return "reason";
        if (prev === "submit") return "details";
        return prev;
      });
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
        if (!open) {
          setTimeout(reset, 200);
        }
      }}
    >
      <DialogContent className="pb-pwa flex h-full w-full flex-col overflow-y-auto bg-dark-bk p-0 sm:max-h-[650px] sm:bg-[rgba(15,15,15)]">
        <div className="sticky top-0 flex items-center justify-between bg-dark-bk px-6 pb-4 pt-[calc(1rem+env(safe-area-inset-top))] sm:bg-[rgba(15,15,15)]">
          <div className="flex flex-1 items-center">
            <button
              onClick={handleBackClick}
              className="hover:bg-gray-800 rounded-full p-1 transition-colors"
            >
              <ArrowBackOutlineIcon className="size-5" />
            </button>
          </div>
          <div className="text-base font-semibold leading-5">
            {page === "submit"
              ? "Submitted"
              : isContentReport
                ? "Report Content"
                : "Report User"}
          </div>
          <div className="flex-1" />
        </div>
        {page === "reason" && (
          <ReasonPage
            setReason={setReason}
            isContentReport={isContentReport}
            selectedReason={reason}
          />
        )}
        {page === "details" && (
          <DetailsPage
            reason={reason}
            description={description}
            setDescription={setDescription}
            maxDescriptionLength={maxDescriptionLength}
          />
        )}
        {page === "submit" && (
          <SubmitPage user={user} handleReport={handleReport} />
        )}
        <Footer
          page={page}
          reason={reason}
          description={description}
          descriptionLength={descriptionLength}
          maxDescriptionLength={maxDescriptionLength}
          isReportUserPending={isReportUserPending}
          isReportThreadPending={isReportThreadPending}
          setPage={setPage}
          handleReport={handleReport}
        />
      </DialogContent>
    </Dialog>
  );
};
