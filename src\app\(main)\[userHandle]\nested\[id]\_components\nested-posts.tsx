"use client";

import { useMemo } from "react";
import { useParams } from "next/navigation";

import { MainPostLoadingSkeleton } from "@/components/post-loading-skeleton";
import { useThreadNestedAnswersInfiniteQuery } from "@/queries";
import { Thread } from "@/types";

import { MainPost } from "./main-post";

export const NestedPosts = () => {
  const params = useParams() as { id: string };
  const { data: nestedAnswersData, isLoading: isAnswersLoading } =
    useThreadNestedAnswersInfiniteQuery(params.id);

  const threads = useMemo(() => {
    if (!nestedAnswersData) return [];

    return nestedAnswersData.pages
      .reduce((prev, current) => {
        return [...prev, ...(current?.threads ?? [])];
      }, [] as Thread[])
      .reverse();
  }, [nestedAnswersData]);

  return (
    <>
      {isAnswersLoading && (
        <>
          <MainPostLoadingSkeleton />
          <MainPostLoadingSkeleton />
        </>
      )}
      {!isAnswersLoading &&
        nestedAnswersData &&
        threads.map((thread, index) => (
          <MainPost
            key={thread.id}
            thread={thread}
            linkType={index === 0 ? "main" : "nested"}
          />
        ))}
    </>
  );
};
