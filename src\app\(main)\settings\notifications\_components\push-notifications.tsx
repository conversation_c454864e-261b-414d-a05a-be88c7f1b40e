"use client";

import { useEffect, useState } from "react";

import { useQueryClient } from "@tanstack/react-query";
import { useCookies } from "react-cookie";
import { v4 as uuid } from "uuid";

import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { generateToken } from "@/lib/firebase";
import {
  useCloudMessagingSettings,
  useSaveCloudMessagingSettingsMutation,
  useUpdateUserPreferencesMutation,
  useUserPreferences,
} from "@/queries";
import { CloudMessagingSettingsResponse } from "@/queries/types/cloud-messaging";
import { UserPreferences, UserPreferencesResponse } from "@/queries/types/user";

export default function PushNotificationsSettings() {
  const queryClient = useQueryClient();

  const [cookies, setCookie] = useCookies(["__uv"]);

  const [isMounted, setIsMounted] = useState(false);
  const [permission, setPermission] = useState<NotificationPermission>();

  const { data: cloudData, isLoading: isCloudLoading } =
    useCloudMessagingSettings();
  const { mutateAsync: saveSettings, isPending: isCloudPending } =
    useSaveCloudMessagingSettingsMutation({
      onMutate: (variables) => {
        queryClient.setQueryData(
          ["cloud-messaging", "settings"],
          (oldData: CloudMessagingSettingsResponse) => {
            if (!oldData) return oldData;
            return {
              settings: {
                ...oldData.settings,
                notificationsEnabled: variables.settings.notificationsEnabled,
              },
            };
          },
        );
      },
    });

  const { data: preferencesData, isLoading: isPreferencesLoading } =
    useUserPreferences();
  const { mutateAsync: updatePreferences, isPending: isPreferencesPending } =
    useUpdateUserPreferencesMutation({
      onMutate: (variables) => {
        queryClient.setQueryData(
          ["user", "preferences"],
          (oldData: UserPreferencesResponse) => {
            if (!oldData) return oldData;
            return {
              userPreferences: {
                ...oldData.userPreferences,
                ...variables.userPreferences,
              },
            };
          },
        );
      },
    });

  const handleNotificationsSwitch = async (checked: boolean) => {
    await saveSettings({
      settings: {
        notificationsEnabled: checked,
      },
    });
  };

  const handlePreferenceChange = async (
    preferenceKey: keyof UserPreferences,
    checked: boolean,
  ) => {
    await updatePreferences({
      userPreferences: {
        [preferenceKey]: checked,
      },
    });
  };

  const toggleNotification = async () => {
    if (typeof window !== "undefined" && "Notification" in window) {
      if (window.Notification.permission === "granted") {
        if (!cloudData?.settings.notificationsEnabled) {
          await generateToken(cookies.__uv);
          setPermission(window.Notification.permission);
        }
      } else {
        const permission = await window.Notification.requestPermission();

        if (permission === "granted") {
          await generateToken(cookies.__uv);
          setPermission(window.Notification.permission);
        }
      }
    }
  };

  useEffect(() => {
    const uv = uuid() + "-" + Date.now().toString(16);
    cookies.__uv ||
      setCookie("__uv", uv, {
        expires: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      });
  }, [cookies, setCookie]);

  useEffect(() => {
    if (!("Notification" in window)) {
      console.error("This browser does not support push notifications");
    } else {
      setPermission(Notification.permission);
    }
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null;
  }

  if (permission === "denied") {
    return (
      <div className="mx-auto mt-20 max-w-80 px-6 text-center">
        <h4 className="text-lg font-semibold text-off-white">
          Turn on notifications?
        </h4>
        <p className="mt-2 text-sm text-light-gray-text">
          To get notifications from The Arena, you&apos;ll need to allow them in
          your browser settings first.
        </p>
      </div>
    );
  }
  const notificationPreferenceTypes: {
    key: keyof UserPreferences;
    label: string;
  }[] = [
    { key: "notifyLikes", label: "LIKES" },
    { key: "notifyReposts", label: "REPOSTS" },
    { key: "notifyComments", label: "COMMENTS" },
    { key: "notifyTags", label: "TAGS" },
  ];
  return (
    <div className="flex flex-col gap-12 px-6 py-6">
      <Label className="flex w-full flex-col gap-2">
        <span className="text-[11px] font-semibold text-[#f3f3f3]">
          PUSH NOTIFICATIONS
        </span>
        <div className="flex w-full items-start justify-between gap-2">
          <p className="text-xs font-normal normal-case text-[#808080]">
            Get push notifications to find out what&apos;s going on when
            you&apos;re not on The Arena.
          </p>
          <Switch
            checked={
              permission === "granted" &&
              cloudData?.settings.notificationsEnabled
            }
            onCheckedChange={handleNotificationsSwitch}
            disabled={isCloudLoading || isCloudPending}
            onClick={toggleNotification}
          />
        </div>
      </Label>
      <div className="flex flex-col gap-6">
        <span className="text-sm font-normal text-[#f3f3f3]">
          Customize who you get notifications from:
        </span>
        <div className="flex flex-col gap-4">
          {notificationPreferenceTypes.map(({ key, label }) => (
            <Label key={key} className="flex w-full flex-col gap-2">
              <span className="text-[11px] font-semibold text-[#f3f3f3]">
                {label}
              </span>
              <div className="flex w-full items-center justify-between gap-2">
                <p className="text-xs font-normal normal-case text-[#808080]">
                  Only from accounts I follow
                </p>
                <Switch
                  checked={Boolean(preferencesData?.userPreferences[key])}
                  onCheckedChange={(checked) =>
                    handlePreferenceChange(key, checked)
                  }
                  disabled={isPreferencesLoading || isPreferencesPending}
                />
              </div>
            </Label>
          ))}
        </div>
      </div>
    </div>
  );
}
