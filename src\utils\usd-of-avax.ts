import { ethers, formatUnits, JsonRpcProvider } from "ethers";

const AVAX_USD_PRICE_FEED_ADDRESS =
  "******************************************";

// Interface for Chainlink Price Feed
const aggregatorV3InterfaceABI = [
  "function latestRoundData() external view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)",
];

const provider = new JsonRpcProvider("https://api.avax.network/ext/bc/C/rpc");

// Function to get AVAX price in USD
export const getAvaxPriceInUsd = async (): Promise<number> => {
  const priceFeed = new ethers.Contract(
    AVAX_USD_PRICE_FEED_ADDRESS,
    aggregatorV3InterfaceABI,
    provider,
  );
  const [, answer] = await priceFeed.latestRoundData();
  return parseFloat(formatUnits(answer, 8)); // Chainlink typically returns 8 decimal places so 6 to have cents
};
