import React, { Dispatch, SetStateAction } from "react";

interface ChampionsTabNavigationProps {
  currentTab: string;
  setCurrentTab: Dispatch<SetStateAction<"leaderboard" | "rewards">>;
}

export const ChampionsTabNavigation: React.FC<ChampionsTabNavigationProps> = ({
  currentTab,
  setCurrentTab,
}) => {
  const tabs = [
    { id: "leaderboard", label: "Leaderboard (Beta)" },
    { id: "rewards", label: "Rewards" },
  ] as { id: "leaderboard" | "rewards"; label: string }[];

  const activeButtonStyles =
    "border-b-2 border-brand-orange text-sm text-off-white";
  const inactiveButtonStyles =
    "border-b-2 border-transparent text-sm text-gray-text";

  return (
    <div className="mt-5 flex w-full gap-5 border-b-2 border-white border-opacity-10 px-6">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          className={`flex-1 pb-3.5 pt-2.5 text-center ${
            currentTab === tab.id ? activeButtonStyles : inactiveButtonStyles
          }`}
          onClick={() => setCurrentTab(tab.id)}
        >
          {tab.label}
        </button>
      ))}
    </div>
  );
};
