"use client";

import { useRef, useState } from "react";

import { MailIcon } from "@/components/icons-v2/account-security";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

interface IVerifyEmailModalProps {
  email: string;
  verifyAction: (verificationToken: string) => Promise<void>;
  resendOTP: () => void;
  closeAction: () => void;
}

export const VerifyEmailModal = (props: IVerifyEmailModalProps) => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const VERIFICATION_LETTER_COUNT = 6;
  const [verificationParts, setVerificationParts] = useState(
    Array(VERIFICATION_LETTER_COUNT).fill(""),
  );
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const handleInputChange = (
    index: number,
    event: React.FormEvent<HTMLInputElement>,
  ) => {
    const value = (event.target as HTMLInputElement).value.toUpperCase();
    if (!!value && value.length === 1) {
      inputRefs.current[index]!.value = value;
      if (index < inputRefs.current.length - 1) {
        inputRefs.current[index + 1]?.focus();
        inputRefs.current[index + 1]?.select();
      }
      const newVerificationParts = [...verificationParts];
      newVerificationParts[index] = value;
      setVerificationParts(newVerificationParts);
    } else {
      inputRefs.current[index]!.value = value.slice(-1);
      const newVerificationParts = [...verificationParts];
      newVerificationParts[index] = value.slice(-1);
      setVerificationParts(newVerificationParts);
    }
  };

  const resendOTP = () => {
    try {
      props.resendOTP();
      toast.green("Verification code re-sent successfully.");
    } catch (error) {
      toast.red(
        "Failed to resend another verification code, please try again later!",
      );
    }
  };

  const [loading, setLoading] = useState(false);

  const verify = async () => {
    setLoading(true);
    try {
      const verificationToken = verificationParts.join("");
      await props.verifyAction(verificationToken);
    } catch (error) {
      console.error("Verification error:", error);
    } finally {
      setLoading(false);
    }
  };

  const isVerificationComplete =
    verificationParts.join("").length === VERIFICATION_LETTER_COUNT;

  return (
    <div
      className={
        isTablet
          ? "fixed inset-0 z-50 flex content-center items-center justify-center "
          : ""
      }
    >
      <div
        className="bg-black fixed inset-0 z-40 bg-[#020202CC]"
        onClick={props.closeAction}
      />
      <div
        className={`" fixed z-50 px-[24px] pb-[48px] pt-[24px] ${isTablet ? "rounded-[20px]" : "bottom-[0px] w-full rounded-t-[20px]"}`}
        style={{ backgroundColor: "rgba(15, 15, 15, 0.9)" }}
      >
        <div className="justify-left flex flex-col items-center gap-[32px]">
          <div className="font-inter flex flex-col items-center gap-[8px] self-stretch text-[16px] font-semibold leading-[22px] text-[#F4F4F4]">
            <MailIcon />
            <p>Verify your email address</p>
          </div>
          <div className="flex flex-col items-start gap-[24px] self-stretch">
            <div className="font-inter self-stretch text-[14px] font-normal leading-[21px] text-[var(--LIGHT-GRAY-TEXT,#B5B5B5)]">
              Verification code sent to{" "}
              <span className="font-semibold text-[var(--OFF-WHITE,#F4F4F4)]">
                {props.email || "<EMAIL>"}.
              </span>
            </div>
            <div className="flex items-center justify-between self-stretch px-[8px]">
              {Array.from({ length: VERIFICATION_LETTER_COUNT }).map(
                (_, index) => (
                  <input
                    key={index}
                    ref={(el) => {
                      inputRefs.current[index] = el;
                    }}
                    maxLength={1}
                    inputMode="numeric"
                    pattern="[0-9]*"
                    className="selection-transparent flex h-[45px] w-[45px] items-center justify-between rounded-[10px] border border-[var(--GRAY-TEXT,#808080)] bg-[var(--LIGHT-BACKGROUND,#1A1A1A)] p-[16px] text-center caret-transparent opacity-85"
                    placeholder="-"
                    onInput={(event) => handleInputChange(index, event)}
                    onClick={(event) => event.currentTarget.select()}
                  />
                ),
              )}
            </div>
            <div
              className="flex flex-col items-center self-stretch"
              onClick={resendOTP}
            >
              <div>
                <span className="font-inter text-[14px] font-normal leading-[21px] text-[var(--LIGHT-GRAY-TEXT,#B5B5B5)]">
                  Didn’t receive a code?{" "}
                </span>
                <span className="font-semibold text-[var(--OFF-WHITE,#F4F4F4)] underline">
                  Re-send
                </span>
              </div>
            </div>
          </div>
          <div className="flex-space-around flex w-full items-center gap-[8px]">
            <Button
              className="w-full"
              onClick={verify}
              disabled={!isVerificationComplete}
              loading={loading}
            >
              Log-in
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
