"use client";

import { useEffect, useState } from "react";

import { PostEditor } from "@/app/compose/post/_components/post-editor-home";
import { AddOutlineIcon } from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { LiveStagesHeader } from "@/components/stages/live-stages-header";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useHomeTabStore, usePostStore, useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";
import { cn } from "@/utils";

import { FollowingTimeline } from "./_components/following-timeline";
import { Header } from "./_components/header";
import { TrenchesTimeline } from "./_components/trenches-timeline";
import { TrendingTimeline } from "./_components/trending-timeline";

import "./home-styles.css";

import { useRouter } from "next/navigation";

import { WarningOutlineIcon } from "@/components/icons/warning-outline";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useFeatureFlagsStore } from "@/stores/flags";
import { useLivestreamStore } from "@/stores/livestream";
import { useStageRecordingPlayerStore } from "@/stores/stage-recording-player";
import { useTutorialStore } from "@/stores/tutorial";
import { UserFlaggedEnum } from "@/types";

function HomePage() {
  const [mounted, setMounted] = useState(false);
  const { user } = useUser();
  const actions = useTutorialStore((state) => state.actions);
  const router = useRouter();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const reset = usePostStore((state) => state.reset);
  const tab = useHomeTabStore((state) => state.tab);
  const setTab = useHomeTabStore((state) => state.setTab);
  const showStages = useFeatureFlagsStore((state) => state.stages);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (user?.tutorial && !user?.tutorial?.followTutorialShown) {
      actions.setIsFollowTutorialOpen(true);
    }
  }, [user]);

  if (user?.flag === UserFlaggedEnum.SUSPENDED) {
    return (
      <div className="relative z-50 mx-auto flex h-full w-full select-none items-center justify-center px-6 py-14 text-light-gray-text opacity-100">
        <div className="flex-col items-center justify-center gap-4 text-center">
          <div className="text-gray-400 flex w-full flex-col items-center justify-center rounded-[10px] bg-[#0e0e0e] p-4 text-center text-base">
            <WarningOutlineIcon color="#EB540A" />
            <span className="mt-2 font-bold text-white">
              Your account has been suspended!
            </span>
            <div className="mt-4 font-medium text-[#808080]">
              Your account has been suspended for violating The Arena&apos;s{" "}
              <ProgressBarLink
                href="/terms-of-use"
                className="font-semibold text-white"
              >
                terms of use
              </ProgressBarLink>
              . Please contact us to appeal the decision.
            </div>
          </div>
          <Button variant="outline" className="mt-8 w-full">
            <a
              href={"https://discord.gg/a5Fw3TFP5n"}
              target="_blank"
              rel="noreferrer"
            >
              Contact Us
            </a>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full">
      {mounted && (
        <Tabs
          value={tab}
          onValueChange={(value) => {
            if (
              value === "following" ||
              value === "trending" ||
              value === "trenches"
            ) {
              setTab(value);
            }
          }}
        >
          <div className="sticky top-[calc(env(safe-area-inset-top,0px))] z-10 hidden w-full bg-[rgba(20,20,20,0.88)] backdrop-blur-[9px] sm:block">
            <TabsList className="mt-3 w-full">
              <TabsTrigger value="following" className="flex-1">
                Following
              </TabsTrigger>
              <TabsTrigger value="trenches" className="flex-1">
                Trenches
              </TabsTrigger>
              <TabsTrigger value="trending" className="flex-1">
                Trending
              </TabsTrigger>
            </TabsList>
          </div>
          <Header>
            {showStages ? <LiveStagesHeader /> : null}
            <TabsList className="w-full">
              <TabsTrigger value="following" className="flex-1">
                Following
              </TabsTrigger>
              <TabsTrigger value="trenches" className="flex-1">
                Trenches
              </TabsTrigger>
              <TabsTrigger value="trending" className="flex-1">
                Trending
              </TabsTrigger>
            </TabsList>
          </Header>
          <div className="hidden bg-[rgb(20_20_20/0.88)] sm:block">
            <PostEditor />
          </div>
          <TabsContent value="following" className="h-[calc(100%-139px)] pb-2">
            <FollowingTimeline />
          </TabsContent>
          <TabsContent value="trenches" className="h-[calc(100%-139px)] pb-2">
            <TrenchesTimeline />
          </TabsContent>
          <TabsContent value="trending" className="h-[calc(100%-139px)] pb-2">
            <TrendingTimeline />
          </TabsContent>
        </Tabs>
      )}
      <ComposePost reset={reset} />
    </div>
  );
}

const ComposePost = ({ reset }: { reset: () => void }) => {
  const isStageOn = useStageStore((state) => Boolean(state.token));
  const isLivestreamOn = useLivestreamStore((state) => Boolean(state.token));
  const isPlayerOn = useStageRecordingPlayerStore((state) =>
    Boolean(state.url),
  );

  return (
    <Button
      className={cn(
        "fixed right-5 z-50 h-auto rounded-full bg-[linear-gradient(98deg,#FF7817_-10.93%,#D05700_-10.93%,#DD3C09_57.47%)] p-[10px] shadow-[0px_4px_20px_0px_rgba(0,0,0,0.10)] sm:hidden",
        isStageOn || isPlayerOn || isLivestreamOn
          ? "bottom-[calc(143px+env(safe-area-inset-bottom))]"
          : "bottom-[calc(70px+env(safe-area-inset-bottom))]",
      )}
      asChild
    >
      <ProgressBarLink
        href="/compose/post"
        onClick={() => {
          reset();
        }}
      >
        <AddOutlineIcon className="h-8 w-8 text-white" />
      </ProgressBarLink>
    </Button>
  );
};

export default HomePage;
