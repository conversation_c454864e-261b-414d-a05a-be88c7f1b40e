import { useQuery } from "@tanstack/react-query";

import {
  getAvaxPrice,
  getSupportedCurrencies,
  getSystemCurrencies,
  getTippableCurrencies,
} from "@/api/client/currency";

export const useAvaxPriceQuery = () => {
  return useQuery({
    queryKey: ["currency", "avax"],
    queryFn: () => getAvaxPrice(),
  });
};

export const useSystemCurrenciesQuery = () => {
  return useQuery({
    queryKey: ["currency", "system"],
    queryFn: () => getSystemCurrencies(),
  });
};

export const useSupportedCurrenciesQuery = () => {
  return useQuery({
    queryKey: ["currency", "supported"],
    queryFn: () => getSupportedCurrencies(),
  });
};

export const useTippableCurrenciesQuery = () => {
  return useQuery({
    queryKey: ["currency", "tippable"],
    queryFn: () => getTippableCurrencies(),
  });
};
