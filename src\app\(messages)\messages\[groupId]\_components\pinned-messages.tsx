"use client";

import { useEffect, useMemo, useState } from "react";
import { useParams } from "next/navigation";

import { PinOutlineIcon } from "@/components/icons";
import { usePinnedMessagesQuery, useUserByIdQuery } from "@/queries";
import { useUser } from "@/stores";
import { UserFlaggedEnum } from "@/types";

import { useGroup, useGroupStore } from "../context/group-context";

export const PinnedMessages = () => {
  const params = useParams() as { groupId: string };
  const { data } = useGroup();
  const { user } = useUser();
  const owner = useUserByIdQuery(data?.group?.ownerUserId || "");
  const chatMate = useUserByIdQuery(data?.group?.chatMateId || "");
  const { data: pinnedMessagesData } = usePinnedMessagesQuery({
    groupId: params.groupId,
  });
  const [messageIndex, setMessageIndex] = useState<null | number>(null);

  const messageId = useGroupStore((state) => state.messageId);
  const setMessageId = useGroupStore((state) => state.actions.setMessageId);
  const setPinnedMessagesCount = useGroupStore(
    (state) => state.actions.setPinnedMessagesCount,
  );

  const pinnedMessages = useMemo(() => {
    if (!pinnedMessagesData) return [];

    return pinnedMessagesData.messages.reverse();
  }, [pinnedMessagesData]);

  const [activeMessage, activeMessageIndex] = useMemo(() => {
    if (pinnedMessages.length === 0) return [null, null];

    let index = 0;

    if (messageIndex !== null) {
      index = messageIndex;
    } else if (messageId && pinnedMessages.some((m) => m.id === messageId)) {
      const currentIndex =
        pinnedMessages.findIndex((m) => m.id === messageId) + 1;

      if (pinnedMessages.length > currentIndex) {
        index = currentIndex;
      }
    }

    const activeMessage = pinnedMessages[index];

    return [activeMessage, index];
  }, [messageIndex, messageId, pinnedMessages]);

  const _message = activeMessage
    ? activeMessage.message.replace(/<br>/g, " ")
    : "";
  const parser = new DOMParser();
  const html = parser.parseFromString(_message, "text/html");
  let text = html.body.textContent;
  const attachment = activeMessage ? activeMessage.attachments?.[0] : null;
  const isImage = attachment?.messageType === 2;
  const isVideo = attachment?.messageType === 3;

  const handleClick = () => {
    if (!activeMessage) return;

    const element = document.getElementById(`message-${activeMessage.id}`);

    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });

      // Create an Intersection Observer
      const observer = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting) {
            element.classList.add("animate-highlight-bg");
            // Remove the highlight class after the animation
            setTimeout(() => {
              element.classList.remove("animate-highlight-bg");
            }, 1500); // 1500ms matches our animation duration

            // Disconnect the observer after triggering the animation
            observer.disconnect();
          }
        },
        { threshold: 1 },
      ); // Trigger when 100% of the element is visible

      // Start observing the element
      observer.observe(element);
      const newMessageIndex = activeMessageIndex + 1;

      setMessageIndex(
        pinnedMessages.length > newMessageIndex ? newMessageIndex : 0,
      );
    } else {
      setMessageId(activeMessage.id);
      setMessageIndex(null);
    }
  };

  useEffect(() => {
    setPinnedMessagesCount(pinnedMessages.length ?? 0);
  }, [pinnedMessages, setPinnedMessagesCount]);

  const isMe = user !== null && user?.id === data?.group?.ownerUserId;
  let isSuspended = false;
  if (data?.group?.isDirect) {
    isSuspended = isMe
      ? chatMate?.data?.user?.flag === UserFlaggedEnum.SUSPENDED
      : owner?.data?.user?.flag === UserFlaggedEnum.SUSPENDED;
  } else {
    isSuspended = owner?.data?.user?.flag === UserFlaggedEnum.SUSPENDED;
  }

  if (isSuspended || pinnedMessages.length === 0) return null;

  return (
    <button
      className="flex h-11 w-full select-none items-center gap-3 bg-[#141414] bg-opacity-[0.88] px-3 outline-none"
      onClick={handleClick}
    >
      <div className="flex h-full w-1 flex-shrink-0 flex-col gap-0.5 py-2">
        {pinnedMessages.map((message, index) => {
          const indexPlace = pinnedMessages.length - index - 1;

          return (
            <div
              className="w-0.5 rounded-full bg-brand-orange"
              style={{
                height: 100 / pinnedMessages.length + "%",
                opacity: indexPlace === activeMessageIndex ? 1 : 0.3,
              }}
              key={indexPlace + "-pin-line"}
            />
          );
        })}
      </div>
      <div className="flex flex-grow flex-col gap-0.5 overflow-hidden text-left">
        <h4 className="text-xs font-semibold leading-none text-brand-orange">
          Pinned Message {(activeMessageIndex ?? 0) + 1}
        </h4>
        <p className="truncate whitespace-nowrap text-xs leading-4 text-gray-text">
          {text?.trim() !== ""
            ? text
            : attachment && isImage
              ? "Photo"
              : attachment && isVideo
                ? "Video"
                : ""}
        </p>
      </div>
      <div className="flex-shrink-0">
        <PinOutlineIcon className="size-5 text-gray-text " />
      </div>
    </button>
  );
};
