"use client";

import { useEffect, useRef } from "react";

import { useGesture } from "@use-gesture/react";
import { animate, motion, useMotionValue } from "framer-motion";

interface ImagePreviewProps {
  url: string;
}

export const ImagePreview = ({ url }: ImagePreviewProps) => {
  const imageRef = useRef<HTMLImageElement>(null);
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const scale = useMotionValue(1);

  useGesture(
    {
      onDrag: ({ offset: [dx, dy] }) => {
        if (!imageRef.current || !imageContainerRef.current) return;

        x.stop();
        y.stop();

        const imageBounds = imageRef.current.getBoundingClientRect();
        const containerBounds =
          imageContainerRef.current.getBoundingClientRect();
        const originalWidth = imageRef.current.clientWidth;
        const widthOverhang = (imageBounds.width - originalWidth) / 2;
        const originalHeight = imageRef.current.clientHeight;
        const heightOverhang = (imageBounds.height - originalHeight) / 2;
        const maxX = widthOverhang;
        const minX =
          -(imageBounds.width - containerBounds.width) + widthOverhang;
        const maxY = heightOverhang;
        const minY =
          -(imageBounds.height - containerBounds.height) + heightOverhang;

        x.set(dampen(dx, [minX, maxX]));
        y.set(dampen(dy, [minY, maxY]));
      },
      onPinch: ({
        memo,
        origin: [pinchOriginX, pinchOriginY],
        offset: [d],
      }) => {
        x.stop();
        y.stop();
        if (!imageRef.current) return;

        memo ??= {
          bounds: imageRef.current.getBoundingClientRect(),
          crop: { x: x.get(), y: y.get(), scale: scale.get() },
        };

        const transformedOriginX = memo.bounds.x + memo.bounds.width / 2;
        const transformedOriginY = memo.bounds.y + memo.bounds.height / 2;

        const displacementX =
          transformedOriginX - pinchOriginX / memo.crop.scale;
        const displacementY =
          transformedOriginY - pinchOriginY / memo.crop.scale;

        const initialOffsetDistance = memo.crop.scale;
        const movementDistance = d - initialOffsetDistance;

        x.set(memo.crop.x + (displacementX * movementDistance) / 2);
        y.set(memo.crop.y + (displacementY * movementDistance) / 2);
        scale.set(d);

        return memo;
      },
      onDragEnd: maybeAdjustImage,
      onPinchEnd: maybeAdjustImage,
    },
    {
      target: imageRef,
      drag: {
        from: () => [x.get(), y.get()],
      },
      pinch: {
        scaleBounds: {
          min: 1,
          max: 3,
        },
      },
      eventOptions: {
        passive: false,
      },
    },
  );

  function maybeAdjustImage() {
    if (!imageRef.current || !imageContainerRef.current) return;

    const newCrop = { x: x.get(), y: y.get(), scale: scale.get() };
    const imageBounds = imageRef.current.getBoundingClientRect();
    const containerBounds = imageContainerRef.current.getBoundingClientRect();
    const originalWidth = imageRef.current.clientWidth;
    const widthOverhang = (imageBounds.width - originalWidth) / 2;
    const originalHeight = imageRef.current.clientHeight;
    const heightOverhang = (imageBounds.height - originalHeight) / 2;

    if (imageBounds.left > containerBounds.left) {
      newCrop.x = widthOverhang;
    } else if (imageBounds.right < containerBounds.right) {
      newCrop.x = -(imageBounds.width - containerBounds.width) + widthOverhang;
    }

    if (imageBounds.top > containerBounds.top) {
      newCrop.y = heightOverhang;
    } else if (imageBounds.bottom < containerBounds.bottom) {
      newCrop.y =
        -(imageBounds.height - containerBounds.height) + heightOverhang;
    }

    animate(x, newCrop.x, {
      type: "tween",
      duration: 0.4,
      ease: [0.25, 1, 0.5, 1],
    });
    animate(y, newCrop.y, {
      type: "tween",
      duration: 0.4,
      ease: [0.25, 1, 0.5, 1],
    });
  }

  useEffect(() => {
    function calculateImageSize() {
      if (imageRef.current) {
        const image = imageRef.current;
        const originalWidth = image.naturalWidth;
        const originalHeight = image.naturalHeight;

        const minRatio = Math.min(
          window.innerWidth / originalWidth,
          window.innerHeight / originalHeight,
        );

        imageRef.current.style.width = `${originalWidth * minRatio}px`;
        imageRef.current.style.height = `${originalHeight * minRatio}px`;
      }
    }

    imageRef.current?.addEventListener("load", calculateImageSize);

    window.addEventListener("resize", calculateImageSize);
    return () => window.removeEventListener("resize", calculateImageSize);
  }, []);

  return (
    <div className="relative flex h-full w-full flex-grow items-center justify-center overflow-hidden bg-transparent">
      <div ref={imageContainerRef} className="mx-auto">
        <motion.img
          ref={imageRef}
          src={url}
          alt=""
          className="relative h-0 max-h-none w-0 max-w-none touch-none object-contain "
          style={{
            x,
            y,
            scale,
            userSelect: "none",
            MozUserSelect: "none",
            // @ts-ignore
            WebkitUserDrag: "none",
          }}
        />
      </div>
    </div>
  );
};

function dampen(val: number, [min, max]: [number, number]) {
  if (val > max) {
    const extra = val - max;
    const dampenedExtra = extra > 0 ? Math.sqrt(extra) : -Math.sqrt(-extra);
    return max + dampenedExtra * 2;
  } else if (val < min) {
    const extra = val - min;
    const dampenedExtra = extra > 0 ? Math.sqrt(extra) : -Math.sqrt(-extra);
    return min + dampenedExtra * 2;
  } else {
    return val;
  }
}
