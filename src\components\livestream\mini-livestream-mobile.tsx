import { useRouter } from "next/navigation";

import { useQuery, useQueryClient } from "@tanstack/react-query";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { livestreamQueries } from "@/queries";
import { useUser } from "@/stores";
import { useLivestreamStore } from "@/stores/livestream";

import { CloseOutlineIcon } from "../icons";
import { MiniVideoPlayer } from "./mini-video-player";
import { VideoPlayer } from "./video-player-new";

export const MinifiedLivestream = () => {
  const { user } = useUser();
  const router = useRouter();
  const queryClient = useQueryClient();
  const isLivestreamOn = useLivestreamStore((state) => Boolean(state.token));
  const actions = useLivestreamStore((state) => state.actions);
  const id = useLivestreamStore((state) => state.id!);
  const twitterHandle = useLivestreamStore((state) => state.twitterHandle!);
  const { data, isLoading } = useQuery({
    ...livestreamQueries.livestreamSimpleInfo(id),
    enabled: isLivestreamOn,
  });
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  // const { mutateAsync: endStage } = useEndStageMutation({
  //   onSuccess: () => {
  //     actions.reset();
  //   },
  // });

  // const handleLeave = async () => {
  //   if (data?.host?.userId === user?.id) {
  //     await endStage({
  //       stageId: id,
  //     });
  //   } else {
  //     actions.reset();
  //     setTimeout(() => {
  //       queryClient.invalidateQueries({
  //         queryKey: stageQueries.currentlyListeningKey(),
  //       });
  //       actions.setCanShowCurrentlyListening(true);
  //     }, 3000);
  //   }
  // };

  if (isLivestreamOn && data && !isLoading && !isTablet) {
    return (
      <div
        className="flex items-center gap-4 border-t-2 border-brand-orange bg-light-background "
        onClick={() => {
          router.push(`/live/${twitterHandle}`);
        }}
      >
        <div className="h-[70px]">
          {/* <MiniVideoPlayer type="mobile" /> */}
          <VideoPlayer type="mini" device="mobile" />
        </div>
        <div className="flex min-w-0 flex-col py-3">
          <div className="flex items-center gap-1">
            <div className="rounded-md bg-danger px-1.5 py-1 text-xs font-semibold leading-none text-off-white md:px-2 md:py-1.5 md:text-sm">
              LIVE
            </div>
            <span className="mt-0.5 text-xs text-off-white">
              {data.host.user.twitterName}
            </span>
          </div>
          <div className="mt-0.5 truncate text-sm font-semibold text-off-white">
            {data.livestream.name}
          </div>
        </div>
        <div
          onClick={(e) => {
            e.stopPropagation();
          }}
          className="ml-auto"
        >
          {/* {data?.host?.userId === user?.id ? (
            <EndStageConfirmationModal onConfirm={handleLeave}>
              <button className="rounded-full bg-dark-gray p-2">
                <CloseOutlineIconclassName="size-5 text-off-white" />
              </button>
            </EndStageConfirmationModal>
          ) : (
          )} */}
          <button
            className="mr-4 rounded-full bg-dark-gray p-1"
            onClick={() => {
              actions.reset();
            }}
          >
            <CloseOutlineIcon className="size-6 text-off-white" />
          </button>
        </div>
      </div>
    );
  }

  return null;
};
