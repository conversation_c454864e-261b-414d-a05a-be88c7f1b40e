import { useMemo } from "react";
import { useParams } from "next/navigation";

import { Virtuoso } from "react-virtuoso";

import { PostLoadingSkeleton } from "@/components/post-loading-skeleton";
import {
  useThreadsByUserIdInfiniteQuery,
  useUserByHandleQuery,
} from "@/queries";
import { Thread } from "@/types";

import { ProfileTimelinePost } from "./profile-timeline-post";

export const ThreadsTab = ({ showReposts }: { showReposts: boolean }) => {
  const params = useParams() as { userHandle: string };
  const { data: userData } = useUserByHandleQuery(params.userHandle);

  const { data, fetchNextPage, isLoading, isFetchingNextPage, hasNextPage } =
    useThreadsByUserIdInfiniteQuery(userData?.user?.id || "");

  const threads = useMemo(() => {
    if (!data) return [];

    const threads = data.pages.reduce((prev, current) => {
      return [...prev, ...current.threads];
    }, [] as Thread[]);

    if (showReposts) {
      return threads;
    }

    return threads.filter((x) => !x.repost);
  }, [data, showReposts]);

  return (
    <Virtuoso
      useWindowScroll
      data={threads}
      endReached={() => {
        if (hasNextPage) {
          fetchNextPage();
        }
      }}
      increaseViewportBy={3000}
      overscan={2000}
      itemContent={(index, thread) => {
        return (
          <ProfileTimelinePost
            thread={thread}
            userId={userData?.user.id || ""}
          />
        );
      }}
      components={{
        Footer: () => {
          if (isLoading || isFetchingNextPage) {
            return (
              <>
                {Array.from({ length: 5 }).map((_, i) => (
                  <PostLoadingSkeleton key={i} />
                ))}
              </>
            );
          }
          return null;
        },
      }}
    />
  );
};
