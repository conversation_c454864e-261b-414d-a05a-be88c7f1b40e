"use client";

import { useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";

import { InfiniteData, useQueryClient } from "@tanstack/react-query";
import FileHandler from "@tiptap-pro/extension-file-handler";
import Document from "@tiptap/extension-document";
import HardBreak from "@tiptap/extension-hard-break";
import History from "@tiptap/extension-history";
import Mention from "@tiptap/extension-mention";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, nodePasteRule, useEditor } from "@tiptap/react";
import { AnimatePresence, motion, useMotionTemplate } from "framer-motion";
import DOMPurify from "isomorphic-dompurify";
import { v4 } from "uuid";

import { proxyFile } from "@/actions/proxy-file";
import EmojiPickerComponent from "@/components/EmojiPicker/emoji-picker";
import {
  AddCircleOutlineIcon,
  ArrowBackOutlineIcon,
  GIFOutlineIcon,
  ImageOutlineIcon,
  SmileOutlineIcon,
  TwitterIcon,
  XCircleOutlineIcon,
} from "@/components/icons";
import { GroupIcon, OfficialGroupIcon } from "@/components/icons-v2/group-logo";
import { TicketOutlineIconV2 } from "@/components/icons-v2/ticket-outline-v2";
import { suggestion } from "@/components/mention/suggestion";
import { ReplyPostUI } from "@/components/post";
import { QuotePost } from "@/components/quote-post";
import { toast } from "@/components/toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import {
  usePostQuoteMutation,
  usePostThreadAnswerMutation,
  usePostThreadMutation,
  useThreadNestedAnswersInfiniteQuery,
  useTotalUploadedQuery,
} from "@/queries";
import { ThreadsResponse } from "@/queries/types";
import { useHomeStore, usePostStore, useUser } from "@/stores";
import { useTutorialStore } from "@/stores/tutorial";
import {
  FileType,
  Thread,
  ThreadPrivacyTypeEnum,
  ThreadResponse,
} from "@/types";
import { cn, upload, uploadFileToXAPI } from "@/utils";
import { compressImageToJpeg } from "@/utils/compress-jpeg";
import { getOAuthAccessToken } from "@/utils/get-x-token";
import { removeMentionSpans } from "@/utils/mention";
import { IS_ANDROID, IS_IOS } from "@/utils/window-environment";

import { GIFsModal } from "./gifs-modal";
import { CommunitySelectionPage } from "./select-community";
import { UrlHighlighter } from "./url-highlighter";

const posters = [
  {
    name: "Home Feed",
    communityId: "",
  },
];

export const PostEditor = () => {
  const [poster, setPoster] = useState<{
    name: string;
    communityId: string;
    isOfficial?: boolean;
  }>(posters[0]);
  const [isCommunitySelectionVisible, setIsCommunitySelectionVisible] =
    useState(false);

  const queryClient = useQueryClient();
  const router = useRouter();
  const { user } = useUser();

  //TODO: save different data for each feed type?
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const type = usePostStore((state) => state.type);
  const thread = usePostStore((state) => state.thread);
  const reset = usePostStore((state) => state.reset);
  const { community, setCommunity } = usePostStore();
  const referrer = usePostStore((state) => state.referrer);
  const setFollowingSnapshot = useHomeStore(
    (state) => state.setFollowingSnapshot,
  );
  const setTrendingSnapshot = useHomeStore(
    (state) => state.setTrendingSnapshot,
  );
  const setTrenchesSnapshot = useHomeStore(
    (state) => state.setTrenchesSnapshot,
  );
  const { data: nestedAnswersData, isLoading: isAnswersLoading } =
    useThreadNestedAnswersInfiniteQuery(
      type === "reply" && thread ? thread?.id : undefined,
    );

  const usersHandles = useMemo(() => {
    if (!nestedAnswersData || isAnswersLoading) return null;

    return Array.from(
      new Set(
        nestedAnswersData.pages
          .reduce((prev, current) => {
            return [...prev, ...(current?.threads ?? [])];
          }, [] as Thread[])
          .reverse()
          .map((thread) => thread?.user?.twitterHandle),
      ),
    );
  }, [nestedAnswersData, isAnswersLoading]);

  const { data: totalUploaded, isLoading: isUploadDataLoading } =
    useTotalUploadedQuery();

  const [mentionCountExceeded, setMentionCountExceeded] = useState(false);
  const [mentionWarningShown, setMentionWarningShown] = useState(false);

  const POST_DRAFT_KEY =
    type && thread
      ? type === "reply"
        ? `reply-draft-${thread.id}`
        : `quote-draft-${thread.id}`
      : "editor-draft";

  const FILE_DRAFT_KEY =
    type && thread
      ? type === "reply"
        ? `reply-file-draft-${thread.id}`
        : `quote-file-draft-${thread.id}`
      : "file-draft";

  const loadDraft = () => {
    const savedContent = localStorage.getItem(POST_DRAFT_KEY) || "";
    editor?.commands.setContent(savedContent);

    if (!localStorage.getItem(FILE_DRAFT_KEY)) {
      return;
    }
    const savedFile = JSON.parse(localStorage.getItem(FILE_DRAFT_KEY) || "");

    if (savedFile && savedFile.url) {
      setFiles([
        {
          id: savedFile.id,
          isLoading: false,
          previewUrl: savedFile.previewUrl,
          url: savedFile.url,
          fileType: savedFile.fileType,
          size: savedFile.size,
        },
      ]);

      setPreview({
        url: savedFile.previewUrl,
        type: savedFile.fileType.includes("image") ? "image" : "video",
      });
    }
  };

  const editor = useEditor({
    extensions: [
      Document,
      Text,
      Paragraph,
      HardBreak.extend({
        addPasteRules() {
          return [
            nodePasteRule({
              find: /<br>$/g,
              type: this.type,
            }),
          ];
        },
      }),
      History,
      Placeholder.configure({
        placeholder: ({ editor }) => {
          return (
            editor.options.element.getAttribute("data-placeholder") ??
            "What's happening?"
          );
        },
      }),
      Mention.configure({
        HTMLAttributes: {
          class: "text-brand-orange",
        },
        suggestion,
      }),
      FileHandler.configure({
        allowedMimeTypes: [
          "image/png",
          "image/jpeg",
          "image/gif",
          "image/webp",
          "video/mp4",
          "video/quicktime",
        ],

        onDrop: (_, files) => {
          setIsDragging(false);
          files.forEach((file) => {
            handleUpload(file, isPostToXRef.current);
          });
        },
        onPaste: (_, files, htmlContent) => {
          if (htmlContent) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlContent, "text/html");
            const imgElement = doc.querySelector("img");

            if (imgElement && imgElement.src) {
              handleUpload(imgElement.src, isPostToXRef.current);
              return false; // Prevent default paste behavior
            }
          }

          files.forEach((file) => handleUpload(file, isPostToXRef.current));
        },
      }),
      UrlHighlighter,
    ],
    editorProps: {
      attributes: {
        class:
          "focus:outline-none min-h-[40px] px-1 w-full select-text py-3 text-base leading-5 text-off-white",
      },
    },
    onCreate: ({ editor }) => {
      loadDraft();

      let mentionCount = 0;
      editor.state.doc.descendants((node) => {
        if (node.type.name === "mention") {
          mentionCount++;
        }
      });
      if (mentionCount && mentionCount > 20) {
        setMentionCountExceeded(true);
        if (!mentionWarningShown) {
          setMentionWarningShown(true);
          toast.danger("This post exceeds the number of @mentions allowed.");
        }
      } else {
        setMentionWarningShown(false);
        setMentionCountExceeded(false);
      }
    },
    onUpdate: ({ editor }) => {
      const content = editor.getHTML();
      localStorage.setItem(POST_DRAFT_KEY, content);

      let mentionCount = 0;
      editor.state.doc.descendants((node) => {
        if (node.type.name === "mention") {
          mentionCount++;
        }
      });
      if (mentionCount && mentionCount > 20) {
        setMentionCountExceeded(true);
        if (!mentionWarningShown) {
          setMentionWarningShown(true);
          toast.danger("This post exceeds the number of @mentions allowed.");
        }
      } else {
        setMentionWarningShown(false);
        setMentionCountExceeded(false);
      }
    },
  });

  const [isEmojiPickerOpen, setEmojiPickerOpen] = useState(false);
  const emojiPickerRef = useRef<HTMLDivElement | null>(null);
  const emojiPickerButtonRef = useRef<HTMLDivElement | null>(null);
  const postButtonRef = useRef<HTMLButtonElement | null>(null);
  const editorRef = useRef<HTMLDivElement>(null);
  const isTutorialOpen = useTutorialStore((state) => state.isTutorialOpen);

  const [files, setFiles] = useState<FileType[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [xMediaIds, setXMediaIds] = useState<string[]>([]);

  const inputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isUploaded, setIsUploaded] = useState(false);
  const [isUploadingToX, setIsUploadingToX] = useState(false);
  const [preview, setPreview] = useState<{
    url: string;
    type: "video" | "image";
  } | null>(null);
  const [progress, setProgress] = useState(0);
  const width = useMotionTemplate`${progress}%`;

  const [isPrivate, setIsPrivate] = useState(false);
  const [isPostToX, setIsPostToX] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const isPostToXRef = useRef(isPostToX);
  const { mutateAsync: postThread, isPending: isPostPending } =
    usePostThreadMutation({
      onMutate: async (variables) => {
        toast.green("You created a new post!");
        localStorage.removeItem(POST_DRAFT_KEY);
        localStorage.removeItem(FILE_DRAFT_KEY);
        await queryClient.cancelQueries({
          queryKey: ["home", "threads", "my-feed"],
        });
        await queryClient.cancelQueries({
          queryKey: ["home", "threads", "trending-feed"],
        });
        if (variables.communityId) {
          await queryClient.cancelQueries({
            queryKey: ["threads", "community", variables.communityId],
          });
          await queryClient.cancelQueries({
            queryKey: ["home", "threads", "trenches-feed"],
          });
        }

        const previousMyFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = queryClient.getQueryData([
          "home",
          "threads",
          "my-feed",
        ]);
        const previousTrendingFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = queryClient.getQueryData([
          "home",
          "threads",
          "trending-feed",
        ]);
        const previousGroupFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = variables.communityId
          ? queryClient.getQueryData([
              "threads",
              "community",
              variables.communityId,
            ])
          : undefined;
        const previousTrenchesFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = queryClient.getQueryData([
          "home",
          "threads",
          "trenches-feed",
        ]);

        const threadId = v4();
        const images = files
          .filter((f) => f.fileType === "image")
          .map((file) => ({
            id: v4(),
            url: file.url,
            threadId,
            size: file.size,
          }));

        const videos = files
          .filter((f) => f.fileType === "video")
          .map((file) => ({
            id: v4(),
            url: file.url,
            threadId,
            size: file.size,
          }));

        const newThread: Thread = {
          displayStatus: 0,
          id: threadId,
          content: variables.content,
          contentUrl: "",
          threadType:
            images.length > 0 ? "image" : videos.length > 0 ? "video" : "text",
          userId: user?.id ?? "",
          userName: user?.twitterName ?? "",
          userHandle: user?.twitterHandle ?? "",
          userPicture: user?.twitterPicture ?? "",
          createdDate: new Date().toISOString(),
          answerCount: 0,
          likeCount: 0,
          bookmarkCount: 0,
          repostCount: 0,
          repostId: null,
          answerId: null,
          isDeleted: false,
          privacyType: 0,
          answerPrivacyType: 0,
          language: "en",
          isPinned: false,
          pinnedInCommunity: false,
          paywall: false,
          price: "0",
          tipAmount: 0,
          tipCount: 0,
          currency: "AVAX",
          currencyAddress: null,
          currencyDecimals: 18,
          like: null,
          bookmark: null,
          reposted: null,
          images,
          videos,
          user: {
            threadCount: user?.threadCount ?? 0,
            followerCount: user?.followerCount ?? 0,
            followingsCount: user?.followingsCount ?? 0,
            twitterFollowers: user?.twitterFollowers ?? 0,
            id: user?.id ?? "",
            createdOn: user?.createdOn ?? "",
            twitterId: user?.twitterId ?? "",
            twitterHandle: user?.twitterHandle ?? "",
            twitterName: user?.twitterName ?? "",
            twitterPicture: user?.twitterPicture ?? "",
            lastLoginTwitterPicture: user?.twitterPicture ?? "",
            bannerUrl: user?.bannerUrl ?? "",
            address: user?.address ?? "",
            ethereumAddress: user?.ethereumAddress ?? "",
            solanaAddress: user?.solanaAddress ?? "",
            prevAddress: user?.prevAddress ?? "",
            addressConfirmed: user?.addressConfirmed ?? false,
            twitterDescription: user?.twitterDescription ?? "",
            signedUp: user?.signedUp ?? false,
            subscriptionCurrency: user?.subscriptionCurrency ?? "",
            subscriptionCurrencyAddress:
              user?.subscriptionCurrencyAddress ?? "",
            subscriptionPrice: user?.subscriptionPrice ?? "",
            keyPrice: user?.keyPrice ?? "",
            subscriptionsEnabled: user?.subscriptionsEnabled ?? false,
            userConfirmed: user?.userConfirmed ?? false,
            twitterConfirmed: user?.twitterConfirmed ?? false,
            flag: user?.flag ?? 0,
            ixHandle: user?.ixHandle ?? "",
            handle: user?.twitterHandle ?? "",
          },
          communityId: poster.communityId,
          community: community ?? undefined,
        };

        if (!variables.communityId) {
          queryClient.setQueryData(
            ["home", "threads", "my-feed"],
            (old: InfiniteData<ThreadsResponse, unknown>) => {
              if (!old) return old;

              return {
                ...old,
                pages: old.pages.map((page, index) => {
                  if (index === 0) {
                    return {
                      ...page,
                      threads: [newThread, ...page.threads],
                    };
                  }
                  return page;
                }),
              };
            },
          );
          queryClient.setQueryData(
            ["home", "threads", "trending-feed"],
            (old: InfiniteData<ThreadsResponse, unknown>) => {
              if (!old) return old;

              return {
                ...old,
                pages: old.pages.map((page, index) => {
                  if (index === 0) {
                    return {
                      ...page,
                      threads: [newThread, ...page.threads],
                    };
                  }
                  return page;
                }),
              };
            },
          );
        } else {
          if (
            user?.id === community?.ownerId ||
            Boolean(community?.following)
          ) {
            queryClient.setQueryData(
              ["home", "threads", "trenches-feed"],
              (old: InfiniteData<ThreadsResponse, unknown>) => {
                if (!old) return old;

                return {
                  ...old,
                  pages: old.pages.map((page, index) => {
                    if (index === 0) {
                      return {
                        ...page,
                        threads: [newThread, ...page.threads],
                      };
                    }
                    return page;
                  }),
                };
              },
            );
          }
          queryClient.setQueryData(
            ["threads", "community", variables.communityId],
            (old: InfiniteData<ThreadsResponse, unknown>) => {
              if (!old) return old;

              return {
                ...old,
                pages: old.pages.map((page, index) => {
                  if (index === 0) {
                    return {
                      ...page,
                      threads: [newThread, ...page.threads],
                    };
                  }
                  return page;
                }),
              };
            },
          );
        }

        if (referrer) {
          router.replace(referrer);
        } else {
          router.replace("/home");
        }
        return {
          previousMyFeed,
          previousTrendingFeed,
          previousGroupFeed,
          previousTrenchesFeed,
          tempThreadId: threadId,
        };
      },
      onError(err, variables, context) {
        queryClient.setQueryData(
          ["home", "threads", "my-feed"],
          context?.previousMyFeed,
        );
        queryClient.setQueryData(
          ["home", "threads", "trending-feed"],
          context?.previousTrendingFeed,
        );
        queryClient.setQueryData(
          ["home", "threads", "trenches-feed"],
          context?.previousTrenchesFeed,
        );
        if (variables.communityId) {
          queryClient.setQueryData(
            ["threads", "community", variables.communityId],
            context?.previousGroupFeed,
          );
        }
      },
      onSuccess: (data, variables, context) => {
        if (data.xTweetResult && !data.xTweetResult.success) {
          toast.danger(data.xTweetResult.error || "Failed to post on X");
        }
        if (!variables.communityId) {
          queryClient.setQueryData(
            ["home", "threads", "my-feed"],
            (old: InfiniteData<ThreadsResponse, unknown>) => {
              if (!old) return old;

              return {
                ...old,
                pages: old.pages.map((page, index) => {
                  if (index === 0) {
                    return {
                      ...page,
                      threads: page.threads.map((t) =>
                        t.id === context.tempThreadId ? data.thread : t,
                      ),
                    };
                  }
                  return page;
                }),
              };
            },
          );
          queryClient.setQueryData(
            ["home", "threads", "trending-feed"],
            (old: InfiniteData<ThreadsResponse, unknown>) => {
              if (!old) return old;

              return {
                ...old,
                pages: old.pages.map((page, index) => {
                  if (index === 0) {
                    return {
                      ...page,
                      threads: page.threads.map((t) =>
                        t.id === context.tempThreadId ? data.thread : t,
                      ),
                    };
                  }
                  return page;
                }),
              };
            },
          );
        } else {
          queryClient.setQueryData(
            ["home", "threads", "trenches-feed"],
            (old: InfiniteData<ThreadsResponse, unknown>) => {
              if (!old) return old;

              return {
                ...old,
                pages: old.pages.map((page, index) => {
                  if (index === 0) {
                    return {
                      ...page,
                      threads: page.threads.map((t) =>
                        t.id === context.tempThreadId ? data.thread : t,
                      ),
                    };
                  }
                  return page;
                }),
              };
            },
          );
          queryClient.setQueryData(
            ["threads", "community", variables.communityId],
            (old: InfiniteData<ThreadsResponse, unknown>) => {
              if (!old) return old;

              return {
                ...old,
                pages: old.pages.map((page, index) => {
                  if (index === 0) {
                    return {
                      ...page,
                      threads: page.threads.map((t) =>
                        t.id === context.tempThreadId ? data.thread : t,
                      ),
                    };
                  }
                  return page;
                }),
              };
            },
          );
        }
        queryClient.invalidateQueries({
          queryKey: ["threads", "user", user?.id],
        });
        queryClient.invalidateQueries({
          queryKey: ["threads", "uploads"],
        });
        setFollowingSnapshot(undefined);
        setTrendingSnapshot(undefined);
        setTrenchesSnapshot(undefined);
        editor?.commands.clearContent();
        setFiles([]);
        setXMediaIds([]);
        resetUploading();
        setIsPostToX(false);
        setIsPrivate(false);
      },
    });
  const { mutateAsync: postReply, isPending: isReplyPending } =
    usePostThreadAnswerMutation({
      onSuccess: (data) => {
        toast.green("Your post was sent");
        localStorage.removeItem(POST_DRAFT_KEY);
        localStorage.removeItem(FILE_DRAFT_KEY);
        queryClient.setQueryData(
          ["threads", thread?.id],
          (old: ThreadResponse) => {
            if (!old) return old;

            return {
              ...old,
              thread: {
                ...old.thread,
                answerCount: old.thread.answerCount + 1,
              },
            };
          },
        );

        queryClient.setQueryData(
          ["threads", "answers", thread?.id],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) {
              queryClient.invalidateQueries({
                queryKey: ["threads", "answers", thread?.id],
              });
              return old;
            }

            return {
              ...old,
              pages: old.pages.map((page, index) => {
                if (index === 0) {
                  return {
                    ...page,
                    threads: [data.thread, ...page.threads],
                  };
                }
                return page;
              }),
            };
          },
        );
        router.replace(`/${thread?.user?.twitterHandle}/nested/${thread?.id}`);

        editor?.commands.clearContent();
        setFiles([]);
        resetUploading();
      },
    });
  const { mutateAsync: postQuote, isPending: isQuotePending } =
    usePostQuoteMutation({
      onSuccess: (data, variables) => {
        if (!variables.communityId && !data.repostingThread.communityId) {
          queryClient.setQueryData(
            ["home", "threads", "my-feed"],
            (old: InfiniteData<ThreadsResponse, unknown>) => {
              if (!old) return old;

              return {
                ...old,
                pages: old.pages.map((page, index) => {
                  if (index === 0) {
                    return {
                      ...page,
                      threads: [
                        {
                          ...data.thread,
                          repost: {
                            ...data.repostingThread,
                            user: data.repostingThread?.user
                              ? data.repostingThread?.user
                              : thread?.user,
                          },
                        },
                        ...page.threads,
                      ],
                    };
                  }
                  return page;
                }),
              };
            },
          );
          queryClient.setQueryData(
            ["home", "threads", "trending-feed"],
            (old: InfiniteData<ThreadsResponse, unknown>) => {
              if (!old) return old;

              return {
                ...old,
                pages: old.pages.map((page, index) => {
                  if (index === 0) {
                    return {
                      ...page,
                      threads: [
                        {
                          ...data.thread,
                          repost: {
                            ...data.repostingThread,
                            user: data.repostingThread?.user
                              ? data.repostingThread?.user
                              : thread?.user,
                          },
                        },
                        ...page.threads,
                      ],
                    };
                  }
                  return page;
                }),
              };
            },
          );
        } else {
          if (variables.communityId) {
            queryClient.setQueryData(
              ["threads", "community", variables.communityId],
              (old: InfiniteData<ThreadsResponse, unknown>) => {
                if (!old) return old;

                return {
                  ...old,
                  pages: old.pages.map((page, index) => {
                    if (index === 0) {
                      return {
                        ...page,
                        threads: [
                          {
                            ...data.thread,
                            repost: {
                              ...data.repostingThread,
                              user: data.repostingThread?.user
                                ? data.repostingThread?.user
                                : thread?.user,
                            },
                          },
                          ...page.threads,
                        ],
                      };
                    }
                    return page;
                  }),
                };
              },
            );
          }
          queryClient.setQueryData(
            ["home", "threads", "trenches-feed"],
            (old: InfiniteData<ThreadsResponse, unknown>) => {
              if (!old) return old;

              return {
                ...old,
                pages: old.pages.map((page, index) => {
                  if (index === 0) {
                    return {
                      ...page,
                      threads: [
                        {
                          ...data.thread,
                          repost: {
                            ...data.repostingThread,
                            user: data.repostingThread?.user
                              ? data.repostingThread?.user
                              : thread?.user,
                          },
                        },
                        ...page.threads,
                      ],
                    };
                  }
                  return page;
                }),
              };
            },
          );
        }

        setFollowingSnapshot(undefined);
        setTrendingSnapshot(undefined);
        setTrenchesSnapshot(undefined);
        toast.green("You created a new post!");
        localStorage.removeItem(POST_DRAFT_KEY);
        localStorage.removeItem(FILE_DRAFT_KEY);
        router.replace("/home");

        editor?.commands.clearContent();
        setFiles([]);
        resetUploading();
      },
    });

  useEffect(() => {
    if (files.length > 0) {
      setIsUploaded(true);
      localStorage.setItem(FILE_DRAFT_KEY, JSON.stringify(files[0]));
    } else if (isUploaded && files.length === 0) {
      setIsUploaded(false);
      localStorage.removeItem(FILE_DRAFT_KEY);
    }
  }, [files]);

  const handlePost = async () => {
    if (!editor || (editor?.getText().trim() === "" && files.length === 0))
      return;

    const text = editor.getHTML();
    const plainText = editor.getText();

    editor.chain().focus().clearContent().run();

    let formattedContent = "";
    if (plainText.trim().length > 0) {
      const updatedContent = DOMPurify.sanitize(text);
      formattedContent = removeMentionSpans(updatedContent);
    }

    let accessToken: string | undefined;
    if (isPostToX) {
      accessToken = await getOAuthAccessToken();
    }
    await postThread({
      content: formattedContent,
      privacyType: isPrivate
        ? ThreadPrivacyTypeEnum.SHAREHOLDERS
        : ThreadPrivacyTypeEnum.PUBLIC,
      files,
      communityId: poster.communityId,
      ...(isPostToX &&
        accessToken && {
          xPostData: {
            isPostToX,
            accessToken,
            mediaIds: xMediaIds.length > 0 ? [xMediaIds[0]] : [],
          },
        }),
    });
  };

  const handleReply = async () => {
    if (!thread) return;
    if (!editor || (editor?.getText().trim() === "" && files.length === 0))
      return;

    const text = editor.getHTML();
    const plainText = editor.getText();

    editor.chain().focus().clearContent().run();

    let formattedContent = "";
    if (plainText.trim().length > 0) {
      const updatedContent = DOMPurify.sanitize(text);
      formattedContent = removeMentionSpans(updatedContent);
    }

    await postReply({
      content: formattedContent,
      threadId: thread?.id,
      userId: thread?.userId,
      files,
    });
  };

  const handleQuote = async () => {
    if (!thread) return;
    if (!editor || (editor?.getText().trim() === "" && files.length === 0))
      return;

    const text = editor.getHTML();
    const plainText = editor.getText();

    editor.chain().focus().clearContent().run();

    let formattedContent = "";
    if (plainText.trim().length > 0) {
      const updatedContent = DOMPurify.sanitize(text);
      formattedContent = removeMentionSpans(updatedContent);
    }

    await postQuote({
      threadId: thread.id,
      content: formattedContent,
      files,
      communityId: poster.communityId,
    });
  };

  const resetUploading = () => {
    setIsUploading(false);
    setIsUploadingToX(false);
    setProgress(0);
    setPreview(null);
  };

  const handleUpload = async (file: File | string, postToX: boolean) => {
    let fileToUpload: File;
    let previewURL: string;

    if (typeof file === "string") {
      try {
        const { data, contentType } = await proxyFile(file);
        const blob = new Blob([Buffer.from(data, "base64")], {
          type: contentType,
        });
        const fileName = file.split("/").pop() || "";
        fileToUpload = new File([blob], fileName, { type: contentType });
        previewURL = URL.createObjectURL(blob);
      } catch (error) {
        resetUploading();
        return;
      }
    } else {
      fileToUpload = file;
      previewURL = URL.createObjectURL(file);
    }

    if (fileToUpload.type.includes("video/ogg")) {
      toast.danger("Please upload video in either mp4 or webm format");
      return;
    }
    if (
      fileToUpload.type.includes("image") &&
      !(
        fileToUpload.type.includes("image/jpeg") ||
        fileToUpload.type.includes("image/gif") ||
        fileToUpload.type.includes("image/png")
      )
    ) {
      toast.danger("Please upload image in JPEG, JPG, PNG or GIF format");
      return;
    }
    if (
      fileToUpload.type.includes("image") &&
      fileToUpload.size > 10 * 1024 * 1024
    ) {
      toast.danger("Uploaded image file cannot exceed 10 MB");
      return;
    }
    if (
      fileToUpload.type.includes("video") &&
      fileToUpload.size > 300 * 1024 * 1024
    ) {
      toast.danger("Uploaded video file cannot exceed 300 MB");
      return;
    }
    if (
      fileToUpload.type.includes("video") &&
      fileToUpload.size + (totalUploaded || 0) > 600 * 1024 * 1024
    ) {
      toast.danger("You have reached your daily video upload limit of 600 MB.");
      resetUploading();
      return;
    }

    if (fileToUpload.type.includes("video")) {
      previewURL += "#t=0.001";
    }
    setPreview({
      url: previewURL,
      type: fileToUpload.type.includes("image") ? "image" : "video",
    });
    setIsUploading(true);
    setProgress(0);

    if (
      fileToUpload.type.includes("image") &&
      (fileToUpload.type.includes("image/jpeg") ||
        fileToUpload.type.includes("image/png")) &&
      fileToUpload.size > 500 * 1024
    ) {
      const compressed = await compressImageToJpeg(fileToUpload, (progress) => {
        setProgress(progress);
      });
      if (compressed) {
        fileToUpload = compressed;
      }
    }

    try {
      let gcpProgress = 0;
      let xProgress = 0;

      const updateCombinedProgress = () => {
        const combinedProgress = postToX
          ? gcpProgress * 0.75 + xProgress * 0.25
          : gcpProgress;
        setProgress(combinedProgress);
      };

      const gcpUploadPromise = upload({
        file: fileToUpload,
        onProgressChange: (progress) => {
          gcpProgress = progress;
          updateCombinedProgress();
        },
      });

      let xApiUploadPromise: Promise<any> | undefined;
      if (postToX) {
        xApiUploadPromise = uploadFileToXAPI(fileToUpload, (progress) => {
          xProgress = progress;
          updateCombinedProgress();
        });
      }

      const [gcpResult, xApiResult] = await Promise.all([
        gcpUploadPromise,
        xApiUploadPromise ?? Promise.resolve(null),
      ]);

      if (postToX && !xApiResult?.success) {
        toast.danger(xApiResult?.error || "Failed to upload on X");
        setIsPostToX(false);
      }

      setFiles([
        {
          id: gcpResult.id,
          isLoading: false,
          previewUrl: gcpResult.previewUrl,
          url: gcpResult.url,
          fileType: fileToUpload.type.includes("image") ? "image" : "video",
          size: fileToUpload.size,
        },
      ]);

      if (postToX && xApiResult?.mediaId) setXMediaIds([xApiResult.mediaId]);
    } catch (error) {
      toast.danger("File upload failed");
      resetUploading();
    } finally {
      setIsUploading(false);
    }
  };

  useEffect(() => {
    const handleClickOutside = async (event: MouseEvent | TouchEvent) => {
      const target = event.target as Node;
      if (
        (postButtonRef.current && postButtonRef.current.contains(target)) ||
        IS_ANDROID ||
        IS_IOS
      ) {
        return;
      }
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(target) &&
        emojiPickerButtonRef.current &&
        !emojiPickerButtonRef.current.contains(target)
      ) {
        setEmojiPickerOpen(false);
      }
      if (editorRef.current && editorRef.current.contains(target)) {
        setEmojiPickerOpen(false);
        editor?.chain().focus().run();
      }
    };
    document.addEventListener("pointerdown", handleClickOutside);

    return () => {
      document.removeEventListener("pointerdown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    isPostToXRef.current = isPostToX;
  }, [isPostToX]);

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    await handleUpload(file, isPostToX);
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  useEffect(() => {
    if (community) {
      setPoster({
        name: community.ticker,
        communityId: community.id,
        isOfficial: community.isOfficial,
      });
    } else {
      setPoster(posters[0]);
    }
  }, []);

  const handlePostToXToggle = async (checked: boolean) => {
    if (isUploading || isUploadingToX) return;

    setIsPostToX(checked);
    if (checked) {
      setIsPrivate(false);

      if (files.length > 0 && !xMediaIds.length) {
        try {
          setIsUploadingToX(true);
          const { data, contentType } = await proxyFile(files[0].url);
          const blob = new Blob([Buffer.from(data, "base64")], {
            type: contentType,
          });
          const fileName = files[0].url.split("/").pop() || "";
          const fileToUpload = new File([blob], fileName, {
            type: contentType,
          });

          const res = await uploadFileToXAPI(fileToUpload, (progress) => {
            setProgress(progress);
          });

          if (res.success) {
            setXMediaIds([res.mediaId]);
          } else {
            toast.danger(res.error || "Failed to upload media on X");
            setIsPostToX(false);
          }
        } catch (error) {
          console.error(error);
          toast.danger("Failed to upload media on X");
          setIsUploadingToX(false);
          setIsPostToX(false);
        } finally {
          setIsUploadingToX(false);
        }
      }
    }
  };

  const handleGifUploadToX = async (url: string) => {
    let fileToUpload: File;
    setIsUploadingToX(true);
    try {
      const { data, contentType } = await proxyFile(url);
      const blob = new Blob([Buffer.from(data, "base64")], {
        type: contentType,
      });
      const fileName = url.split("/").pop() || "";
      fileToUpload = new File([blob], fileName, { type: contentType });

      if (fileToUpload.size > 15 * 1024 * 1024) {
        toast.danger("X upload error: Uploaded gif cannot exceed 15 MB");
        setIsPostToX(false);
        return;
      }

      const res = await uploadFileToXAPI(fileToUpload, (progress) => {
        setProgress(progress);
      });

      if (res.success) {
        setXMediaIds([res.mediaId]);
      } else {
        toast.danger(res.error || "Failed to upload on X");
        setIsPostToX(false);
      }
    } catch (error) {
      toast.danger("Failed to upload media on X");
      setIsUploadingToX(false);
      setIsPostToX(false);
      return;
    } finally {
      setIsUploadingToX(false);
    }
  };

  return (
    <>
      {isCommunitySelectionVisible ? (
        <CommunitySelectionPage
          onSelect={(community) => {
            setPoster({
              name: community.ticker,
              communityId: community.id,
              isOfficial: community.isOfficial,
            });
            setCommunity(community);
            setIsPrivate(false);
            setIsCommunitySelectionVisible(false);
          }}
          onClose={() => setIsCommunitySelectionVisible(false)}
        />
      ) : (
        <>
          {isTutorialOpen && (
            <div className="absolute inset-0 z-40 bg-dark-bk/65" />
          )}
          <div
            className={cn(
              "flex items-center border-b border-dark-gray px-6 py-4",
              isTutorialOpen &&
                "relative z-50 shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)]",
            )}
          >
            <div className="flex flex-1 justify-start">
              <button
                onClick={() => {
                  reset();
                  if (window.history.length > 1) {
                    router.back();
                  } else {
                    router.push("/home");
                  }
                }}
              >
                <ArrowBackOutlineIcon className="size-5 text-off-white" />
              </button>
            </div>
            <h3 className="text-base font-semibold leading-5 text-white">
              {type && type === "reply"
                ? "Reply"
                : type === "quote"
                  ? "Quote Repost"
                  : "New post"}
            </h3>
            <div className="flex-1" />
          </div>
          <div className="flex flex-grow flex-col sm:flex-grow-0 sm:border-b sm:border-dark-gray">
            {type && thread && type === "reply" && (
              <ReplyPostUI thread={thread} usersHandles={usersHandles} />
            )}

            <div className="mt-2 flex items-start justify-start px-6">
              {type !== "reply" && (
                <Select
                  value={poster.communityId === "" ? "home" : "community"}
                  onValueChange={(value) => {
                    if (value === "home") {
                      setPoster({
                        name: "Home Feed",
                        communityId: "",
                      });
                      setCommunity(null);
                    } else {
                      setIsCommunitySelectionVisible(true);
                    }
                  }}
                >
                  <SelectTrigger className="flex w-auto items-center gap-1.5 rounded border border-dark-gray bg-[#2a2a2a] px-[2px] py-[3px] focus:ring-0 focus:ring-offset-0">
                    {poster.communityId && (
                      <>
                        {poster.isOfficial ? (
                          <OfficialGroupIcon />
                        ) : (
                          <GroupIcon />
                        )}
                      </>
                    )}
                    <span className="text-base font-medium leading-5 text-off-white">
                      {poster.communityId === ""
                        ? "Home Feed"
                        : `$${poster.name}`}
                    </span>
                  </SelectTrigger>
                  <SelectContent
                    className="z-50 max-h-48 w-[calc(80vw-40px)] sm:w-[15rem]"
                    align="end"
                    sideOffset={20}
                    alignOffset={-16}
                  >
                    <SelectItem value="home">Home Feed</SelectItem>
                    <SelectItem value="community">Token Feeds</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>

            <div
              className={cn(
                "flex justify-start gap-3 px-6 pt-[22px]",
                type === "reply" && "pt-1",
              )}
            >
              <Avatar className="size-[42px] flex-shrink-0">
                <AvatarImage src={user?.twitterPicture} />
                <AvatarFallback />
              </Avatar>
              <div className="flex min-w-0 flex-grow flex-col gap-3">
                <div
                  ref={editorRef}
                  className={cn(
                    "relative rounded-[10px] border border-dashed",
                    isDragging ? "border-brand-orange" : "border-transparent",
                  )}
                  onDragEnter={handleDragEnter}
                  onDragLeave={handleDragLeave}
                  onDragOver={handleDragOver}
                  onDrop={() => {
                    setIsDragging(false);
                  }}
                >
                  <EditorContent
                    editor={editor}
                    className="post-content w-full"
                  />
                </div>
                {!(IS_ANDROID || IS_IOS) && (
                  <EmojiPickerComponent
                    emojiPickerRef={emojiPickerRef}
                    editor={editor}
                    isEmojiPickerOpen={isEmojiPickerOpen}
                  />
                )}
                {(preview || files.length > 0) && (
                  <div className="relative">
                    {preview && preview.type === "video" && (
                      <video
                        src={
                          isUploading && !files[0]?.url
                            ? preview.url
                            : files[0]?.url
                        }
                        className="w-full rounded-[10px]"
                        autoPlay
                        loop
                        muted
                        controls
                        playsInline
                        webkit-playsinline="true"
                      />
                    )}
                    {preview && preview.type === "image" && (
                      <img
                        src={
                          isUploading && !files[0]?.url
                            ? preview.url
                            : files[0]?.url
                        }
                        alt="preview"
                        className="max-h-[510px] w-full rounded-[10px] object-cover"
                      />
                    )}
                    {!preview && files.length > 0 && (
                      <img
                        src={files[0].url}
                        alt="preview"
                        className="max-h-[510px] w-full rounded-[10px] object-cover"
                      />
                    )}
                    <button
                      className="absolute right-2 top-2 z-10"
                      onClick={() => {
                        setFiles([]);
                        setXMediaIds([]);
                        resetUploading();
                      }}
                    >
                      <XCircleOutlineIcon className="size-6 fill-dark-bk/75 text-off-white" />
                    </button>
                    <AnimatePresence>
                      {(isUploading || isUploadingToX) && (
                        <>
                          {isUploading && (!isPostToX || progress < 75) && (
                            <div className="absolute inset-0 overflow-hidden rounded-[10px]">
                              <motion.div
                                style={{ width }}
                                exit={{ opacity: 0 }}
                                className="absolute bottom-0 left-0 h-1 min-w-4 bg-brand-orange"
                              />
                            </div>
                          )}
                          {(isUploadingToX ||
                            (isPostToX && isUploading && progress >= 75)) && (
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              exit={{ opacity: 0 }}
                              className="absolute inset-0 flex flex-col items-center justify-center gap-2 rounded-[10px] bg-[#0F0F0F]/40  text-off-white"
                            >
                              <div className="flex flex-col items-center justify-center gap-1 rounded-[10px]  bg-[#0F0F0F]/90 px-8 py-6">
                                <svg
                                  className="h-8 w-8 animate-spin text-white"
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                >
                                  <circle
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="3"
                                    strokeDasharray="60"
                                    strokeDashoffset="20"
                                  />
                                </svg>
                                <span className="text-sm font-medium">
                                  Processing media to X...
                                </span>
                              </div>
                            </motion.div>
                          )}
                        </>
                      )}
                    </AnimatePresence>
                  </div>
                )}
                {type === "quote" && thread && <QuotePost {...thread} />}
              </div>
            </div>
            <div className="mt-auto flex-shrink-0 sm:mt-0">
              <div className="sticky bottom-0 flex flex-col gap-6 border-t border-dark-gray p-6 pt-4 sm:flex-row sm:justify-between sm:border-none">
                <div className="flex items-center justify-between sm:w-full ">
                  <div className="flex items-center gap-4">
                    {!(IS_ANDROID || IS_IOS) && (
                      <div
                        ref={emojiPickerButtonRef}
                        className="bg-slate-500 text-black-700 left-0 flex cursor-pointer items-center rounded-full outline-none"
                        onClick={() => {
                          if (isEmojiPickerOpen) {
                            editor?.chain().focus().run();
                          }
                          setEmojiPickerOpen(!isEmojiPickerOpen);
                        }}
                      >
                        <span>
                          <SmileOutlineIcon
                            className="size-7 text-[#E0E0E0]"
                            strokeWidth={1.5}
                          />
                        </span>
                      </div>
                    )}
                    <div className="flex items-center">
                      <button
                        disabled={files.length > 0}
                        onClick={() => {
                          !isUploading &&
                            inputRef.current &&
                            inputRef.current.click();
                        }}
                      >
                        <ImageOutlineIcon
                          className={cn(
                            "flex size-8 items-center justify-center text-off-white",
                            files.length > 0 && "opacity-50",
                          )}
                        />
                      </button>
                      <input
                        ref={inputRef}
                        className="hidden"
                        type="file"
                        accept="image/*,video/*"
                        onChange={handleChange}
                        disabled={isUploadDataLoading}
                      />
                    </div>
                    <GIFsModal
                      onSelect={(gif) => {
                        setFiles([
                          {
                            id: v4(),
                            isLoading: false,
                            previewUrl: gif.media_formats.gifpreview.url,
                            url: gif.media_formats.gif.url,
                            fileType: "image",
                            size: 0,
                          },
                        ]);
                        if (isPostToX)
                          handleGifUploadToX(gif.media_formats.gif.url);
                      }}
                    >
                      <button disabled={files.length > 0}>
                        <GIFOutlineIcon
                          className={cn(
                            "size-8 text-off-white",
                            files.length > 0 && "opacity-50",
                          )}
                        />
                      </button>
                    </GIFsModal>
                  </div>
                  {type === null && (
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        {isPrivate && (
                          <TicketOutlineIconV2 className="size-8 text-brand-orange" />
                        )}
                        {isPostToX && (
                          <TwitterIcon className="size-8 text-brand-orange" />
                        )}
                        {isTablet && (
                          <DropdownMenu
                            open={menuOpen}
                            onOpenChange={setMenuOpen}
                          >
                            <DropdownMenuTrigger className="flex size-[32px] items-center justify-center outline-none">
                              <AddCircleOutlineIcon className="size-8 text-[#E0E0E0]" />
                            </DropdownMenuTrigger>
                            <DropdownMenuContent
                              align="center"
                              sideOffset={8}
                              className="w-[280px] p-2"
                            >
                              <div className="mb-2 flex items-center gap-2 px-2 py-3">
                                <button onClick={() => setMenuOpen(false)}>
                                  <ArrowBackOutlineIcon className="size-5" />
                                </button>
                                <span className="font-semibold text-[#f3f3f3]">
                                  Posting Options
                                </span>
                              </div>
                              <DropdownMenuLabel className="gap-4">
                                <Label
                                  className={cn(
                                    "flex w-full items-center justify-between rounded-[10px] border border-[#636363] p-4",
                                    !!poster.communityId && "opacity-50",
                                  )}
                                >
                                  <div className="flex items-center gap-1">
                                    <TicketOutlineIconV2 className="size-6 text-off-white" />
                                    <span className="text-sm font-semibold normal-case leading-5 text-off-white">
                                      Make post private
                                    </span>
                                  </div>
                                  <Switch
                                    checked={isPrivate}
                                    disabled={!!poster.communityId}
                                    onCheckedChange={(checked) => {
                                      setIsPrivate(checked);
                                      if (checked) {
                                        setIsPostToX(false);
                                      }
                                    }}
                                  />
                                </Label>
                              </DropdownMenuLabel>
                              <DropdownMenuLabel className="gap-4">
                                <Label
                                  className={cn(
                                    "flex w-full items-center justify-between rounded-[10px] border border-[#636363] p-4",
                                    (isUploading || isUploadingToX) &&
                                      "opacity-50",
                                  )}
                                >
                                  <div className="flex items-center gap-1">
                                    <TwitterIcon className="size-6 text-off-white" />
                                    <span className="text-sm font-semibold normal-case leading-5 text-off-white">
                                      Cross post on X
                                    </span>
                                  </div>
                                  <Switch
                                    checked={isPostToX}
                                    disabled={isUploading || isUploadingToX}
                                    onCheckedChange={handlePostToXToggle}
                                  />
                                </Label>
                              </DropdownMenuLabel>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                        {!isTablet && (
                          <Drawer open={menuOpen} onOpenChange={setMenuOpen}>
                            <DrawerTrigger className="flex size-[32px] items-center justify-center outline-none">
                              <AddCircleOutlineIcon className="size-8 text-[#E0E0E0]" />
                            </DrawerTrigger>
                            <DrawerContent className="gap-4 px-4 pt-4">
                              <div className="mb-4 flex items-center gap-2">
                                <button onClick={() => setMenuOpen(false)}>
                                  <ArrowBackOutlineIcon className="size-5" />
                                </button>
                                <span className="font-semibold text-[#f3f3f3]">
                                  Posting Options
                                </span>
                              </div>
                              <Label
                                className={cn(
                                  "flex w-full items-center justify-between rounded-[10px] border border-[#636363] p-4",
                                  !!poster.communityId && "opacity-50",
                                )}
                              >
                                <div className="flex items-center gap-1">
                                  <TicketOutlineIconV2 className="h-6 w-6 text-off-white" />
                                  <span className="text-sm font-semibold normal-case leading-5 text-off-white">
                                    Make post private
                                  </span>
                                </div>
                                <Switch
                                  checked={isPrivate}
                                  disabled={!!poster.communityId}
                                  onCheckedChange={(checked) => {
                                    setIsPrivate(checked);
                                    if (checked) {
                                      setIsPostToX(false);
                                    }
                                  }}
                                />
                              </Label>
                              <Label
                                className={cn(
                                  "flex w-full items-center justify-between rounded-[10px] border border-[#636363] p-4",
                                  isUploading && "opacity-50",
                                )}
                              >
                                <div className="flex items-center gap-1">
                                  <TwitterIcon className="size-6 text-off-white" />
                                  <span className="text-sm font-semibold normal-case leading-5 text-off-white">
                                    Cross post on X
                                  </span>
                                </div>
                                <Switch
                                  checked={isPostToX}
                                  disabled={isUploading || isUploadingToX}
                                  onCheckedChange={handlePostToXToggle}
                                />
                              </Label>
                            </DrawerContent>
                          </Drawer>
                        )}
                      </div>
                      <Button
                        ref={postButtonRef}
                        className="hidden w-full sm:block sm:w-auto sm:px-8 sm:py-2"
                        onClick={handlePost}
                        disabled={
                          mentionCountExceeded ||
                          isPostPending ||
                          isUploading ||
                          !editor ||
                          (editor?.getText().trim() === "" &&
                            files.length === 0) ||
                          isUploadingToX
                        }
                      >
                        Post Now
                      </Button>
                    </div>
                  )}
                </div>
                {type === null && (
                  <Button
                    ref={postButtonRef}
                    className="w-full sm:hidden"
                    onClick={handlePost}
                    disabled={
                      mentionCountExceeded ||
                      isPostPending ||
                      isUploading ||
                      !editor ||
                      (editor?.getText().trim() === "" && files.length === 0) ||
                      isUploadingToX
                    }
                  >
                    Post Now
                  </Button>
                )}
                {type && type === "reply" && (
                  <Button
                    ref={postButtonRef}
                    className="w-full sm:w-auto sm:px-8 sm:py-2"
                    onClick={handleReply}
                    disabled={
                      mentionCountExceeded ||
                      isReplyPending ||
                      isUploading ||
                      !thread ||
                      !editor ||
                      (editor?.getText().trim() === "" && files.length === 0)
                    }
                  >
                    Reply
                  </Button>
                )}
                {type && type === "quote" && (
                  <Button
                    ref={postButtonRef}
                    className="w-full sm:w-auto sm:px-8 sm:py-2"
                    onClick={handleQuote}
                    disabled={
                      mentionCountExceeded ||
                      isQuotePending ||
                      isUploading ||
                      !thread ||
                      !editor ||
                      (editor?.getText().trim() === "" && files.length === 0)
                    }
                  >
                    Reply
                  </Button>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};
