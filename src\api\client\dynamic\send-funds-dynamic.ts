import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { Wallet } from "@dynamic-labs/sdk-react-core";
import { MaxUint256 } from "ethers";
import { Address, encodeFunctionData, Hex, publicActions } from "viem";

import { ERC20_CONTRACT_ABI } from "@/environments/BACKEND_FRIENDS_CONTRACT";
import {
  MULTI_SEND_CONTRACT_ABI,
  MULTI_SEND_CONTRACT_ADDRESS,
} from "@/environments/MULTI_SEND_CONTRACT";

export const sendFundsDynamic = async (
  primaryWallet: Wallet | null,
  toAddress: string,
  amount: bigint,
  currency: string,
  tokenContractAddress: string | undefined,
): Promise<{
  txHash: string;
  txData: string;
}> => {
  if (!primaryWallet) {
    throw new Error("Dynamic wallet wasn't initialized");
  }
  if (!isEthereumWallet(primaryWallet)) {
    throw new Error("This wallet is not a Ethereum wallet");
  }

  const walletClient = await primaryWallet.getWalletClient();
  let txHash;
  let txData;

  if (currency === "AVAX") {
    txData = encodeFunctionData({
      abi: ERC20_CONTRACT_ABI,
      functionName: "transfer",
      args: [toAddress as Hex, amount],
    });

    txHash = await walletClient.sendTransaction({
      account: primaryWallet.address as Hex,
      chain: walletClient.chain,
      to: toAddress as Hex,
      value: amount,
    });
  } else {
    const contractAddress = tokenContractAddress;

    if (!contractAddress) {
      throw new Error("Invalid currency selected");
    }

    txData = encodeFunctionData({
      abi: ERC20_CONTRACT_ABI,
      functionName: "transfer",
      args: [toAddress as Hex, amount],
    });

    txHash = await walletClient.sendTransaction({
      account: primaryWallet.address as Hex,
      chain: walletClient.chain,
      to: contractAddress as Hex,
      data: txData,
    });
  }
  return { txHash, txData };
};

export const multiSendDynamic = async (
  primaryWallet: Wallet | null,
  toAddresses: string[],
  amounts: bigint[],
  currency: string,
  tokenContractAddress: string | undefined,
): Promise<{
  txHash: string;
  txData: string;
}> => {
  if (!primaryWallet) {
    throw new Error("Wallet not found!");
  }
  if (!isEthereumWallet(primaryWallet)) {
    throw new Error("This wallet is not an Ethereum wallet");
  }

  const publicClient = await primaryWallet.getPublicClient();

  const walletClient = (await primaryWallet.getWalletClient()).extend(
    publicActions,
  );

  let contractAddress;
  let txHash;
  let txData;

  try {
    if (currency === "AVAX") {
      const totalAmount = amounts.reduce((acc, curr) => acc + curr, 0n);

      const feePercentage = await publicClient.readContract({
        address: MULTI_SEND_CONTRACT_ADDRESS as Address,
        abi: MULTI_SEND_CONTRACT_ABI,
        functionName: "feePercentage",
      });

      const amountsMinusFee = amounts.map(
        (amount) => amount - (amount * feePercentage) / BigInt(100),
      );

      txData = encodeFunctionData({
        abi: MULTI_SEND_CONTRACT_ABI,
        functionName: "multiSend",
        args: [
          toAddresses.map((toAddress) => toAddress as Hex),
          amountsMinusFee,
        ],
      });

      txHash = await walletClient.sendTransaction({
        account: primaryWallet.address as Hex,
        chain: walletClient.chain,
        to: MULTI_SEND_CONTRACT_ADDRESS as Hex,
        value: totalAmount,
        data: txData,
      });
    } else {
      contractAddress = tokenContractAddress;

      if (!contractAddress) {
        throw new Error("Invalid currency selected");
      }

      const feePercentage = await publicClient.readContract({
        address: MULTI_SEND_CONTRACT_ADDRESS as Address,
        abi: MULTI_SEND_CONTRACT_ABI,
        functionName: "feePercentage",
      });

      const amountsMinusFee = amounts.map((amount) => {
        let percentage = (amount * feePercentage) / BigInt(100);
        const isRounded = (amount * feePercentage) % BigInt(100) === 0n;
        if (!amount.toString().endsWith("00") && !isRounded) percentage += 2n;
        return amount - percentage;
      });

      txData = encodeFunctionData({
        abi: MULTI_SEND_CONTRACT_ABI,
        functionName: "multiTransferERC20",
        args: [
          contractAddress as Hex,
          toAddresses.map((toAddress) => toAddress as Hex),
          amountsMinusFee,
        ],
      });

      txHash = await walletClient.sendTransaction({
        account: primaryWallet.address as Hex,
        chain: walletClient.chain,
        to: MULTI_SEND_CONTRACT_ADDRESS as Hex,
        data: txData,
      });
    }
    return { txHash, txData };
  } catch (error) {
    console.error("Transaction error:", error);
    throw new Error("Transaction failed");
  }
};

export const getAllowance = async (
  wallet: Wallet,
  tokenAddress: Address,
  spender: Address,
): Promise<bigint> => {
  if (!isEthereumWallet(wallet)) {
    throw new Error("This wallet is not an Ethereum wallet");
  }

  const publicClient = await wallet.getPublicClient();
  const allowance = await publicClient.readContract({
    address: tokenAddress,
    abi: ERC20_CONTRACT_ABI,
    functionName: "allowance",
    args: [wallet.address as Address, spender],
  });
  return allowance;
};

export const batchMultiSendDynamic = async (
  primaryWallet: Wallet | null,
  toAddresses: string[],
  amounts: bigint[],
  currency: string,
  isToken: boolean | undefined,
  tokenContractAddress: string | undefined,
): Promise<{ txHash: string[]; txData: string[] }> => {
  if (!primaryWallet) {
    throw new Error("Wallet not found!");
  }
  if (!isEthereumWallet(primaryWallet)) {
    throw new Error("This wallet is not an Ethereum wallet");
  }
  const walletClient = (await primaryWallet.getWalletClient()).extend(
    publicActions,
  );

  if (currency !== "AVAX") {
    const contractAddress = tokenContractAddress;

    if (!contractAddress) {
      throw new Error("Invalid currency selected");
    }

    const totalAmount = amounts.reduce((acc, curr) => acc + curr, 0n);

    const allowance = await getAllowance(
      primaryWallet,
      contractAddress as Address,
      MULTI_SEND_CONTRACT_ADDRESS,
    );

    if (allowance < totalAmount) {
      const approvalTx = await walletClient.writeContract({
        address: contractAddress as Address,
        abi: ERC20_CONTRACT_ABI,
        functionName: "approve",
        args: [MULTI_SEND_CONTRACT_ADDRESS, MaxUint256],
        account: primaryWallet.address as Address,
      });
      console.log("Approval transaction:", approvalTx);

      const approvalReceipt = await walletClient.waitForTransactionReceipt({
        hash: approvalTx,
        confirmations: 1,
      });
      console.log("Approval transaction completed:", approvalReceipt);
    } else {
      console.log("Allowance sufficient, skip approve!");
    }
  }

  if (toAddresses.length !== amounts.length) {
    throw new Error("toAddresses and amounts arrays must have the same length");
  }

  const txHash: string[] = [];
  const txData: string[] = [];

  const batchSize: number = 300;

  for (let i = 0; i < toAddresses.length; i += batchSize) {
    const batchToAddresses = toAddresses.slice(i, i + batchSize);
    const batchAmounts = amounts.slice(i, i + batchSize);

    try {
      const { txHash: batchTxHash, txData: batchTxData } =
        await multiSendDynamic(
          primaryWallet,
          batchToAddresses,
          batchAmounts,
          currency,
          tokenContractAddress,
        );

      if (i + batchSize < toAddresses.length) {
        await walletClient.waitForTransactionReceipt({
          hash: batchTxHash as Hex,
          confirmations: 1,
        });
      }

      txHash.push(batchTxHash);
      txData.push(batchTxData);
    } catch (error) {
      console.error(`Batch ${i / batchSize + 1} failed:`, error);
      throw new Error(`Batch ${i / batchSize + 1} failed`);
    }
  }

  return { txHash, txData };
};
