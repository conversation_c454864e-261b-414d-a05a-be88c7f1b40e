"use client";

import { ReplyEditor } from "@/components/editor/reply-editor";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

import { Comments } from "../../_post-components/comments";
import { Header } from "../../_post-components/header";
import { MainPost } from "./_components/main-post";

function PostPage() {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  return (
    <div>
      <Header />
      <MainPost />
      {isTablet ? <ReplyEditor /> : null}
      <Comments />
    </div>
  );
}

export default PostPage;
