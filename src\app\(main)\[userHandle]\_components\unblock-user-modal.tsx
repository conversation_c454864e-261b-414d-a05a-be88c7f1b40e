"use client";

import { useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useUnblockUserMutation } from "@/queries/user-mutations";
import { User } from "@/types";

interface UnblockUserModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: User;
}

export const UnblockUserModal = ({
  open,
  setOpen,
  user,
}: UnblockUserModalProps) => {
  const queryClient = useQueryClient();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  const { mutateAsync: unblockUser, isPending } = useUnblockUserMutation({
    onMutate: async () => {
      toast.green(`${user.twitterName} is now unblocked!`);

      await queryClient.cancelQueries({
        queryKey: ["user", "isBlocked", user.id],
      });

      const previousIsBlocked = queryClient.getQueryData([
        "user",
        "isBlocked",
        user.id,
      ]);

      queryClient.setQueryData(["user", "isBlocked", user.id], false);

      return { previousIsBlocked };
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to unblock ${user.twitterName}.`);
      queryClient.setQueryData(
        ["user", "isBlocked", user.id],
        context?.previousIsBlocked,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["threads", "user", user.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
      queryClient.invalidateQueries({
        queryKey: ["chat", "group", "profile", user.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["chat", "conversations"],
      });
    },
  });

  const handleUnblock = async () => {
    setOpen(false);
    await unblockUser({ userId: user.id });
  };

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-sm">
          <UnblockUserModalContent
            user={user}
            setOpen={setOpen}
            handleUnblock={handleUnblock}
            isPending={isPending}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="justify-start gap-6 text-left">
        <UnblockUserModalContent
          user={user}
          setOpen={setOpen}
          handleUnblock={handleUnblock}
          isPending={isPending}
        />
      </DrawerContent>
    </Drawer>
  );
};

interface UnblockUserModalContentProps {
  user: User;
  setOpen: (open: boolean) => void;
  handleUnblock: () => void;
  isPending: boolean;
}

const UnblockUserModalContent = ({
  user,
  setOpen,
  handleUnblock,
  isPending,
}: UnblockUserModalContentProps) => {
  return (
    <>
      <DialogHeader className="space-y-4">
        <DialogTitle>Unblock @{user.twitterHandle}?</DialogTitle>
        <DialogDescription className="text-gray-text">
          @{user.twitterHandle} will be able to follow you and view your posts.
          Are you sure you want to proceed?
        </DialogDescription>
      </DialogHeader>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => {
            setOpen(false);
          }}
        >
          Cancel
        </Button>
        <Button
          variant="secondary"
          className="flex-1"
          onClick={handleUnblock}
          disabled={isPending}
        >
          Unblock
        </Button>
      </div>
    </>
  );
};
