import { Dispatch, RefObject, SetStateAction, useEffect, useRef } from "react";

import { Trigger } from "@radix-ui/react-tabs";
import { motion, useMotionValue } from "framer-motion";

import { Tabs, TabsContent, TabsList } from "@/components/ui/tabs";
import { User } from "@/queries/types/postreactions";
import { cn } from "@/utils";

import { ClaimTab } from "./claim-tab";
import { StakeTab } from "./stake-tab";
import { VoteTab } from "./vote-tab";

interface Proposal {
  id: string;
  title: string;
  body: string;
  state: string;
  end: number;
}

interface TabsSectionProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  user: User;
  proposals: Proposal[];
  router: any;
  videoRef: RefObject<HTMLVideoElement>;
  play: boolean;
  setPlay: (play: boolean) => void;
}

export const TabsSection = ({
  activeTab,
  setActiveTab,
  user,
  proposals,
  router,
  videoRef,
  play,
  setPlay,
}: TabsSectionProps) => {
  const tabRef = useRef<HTMLDivElement>(null);
  const backgroundColor = useMotionValue("rgb(0 0 0 / 0)");

  useEffect(() => {
    const handleScroll = () => {
      if (tabRef.current && tabRef.current.getBoundingClientRect().top <= 0) {
        backgroundColor.set("rgb(15 15 15 / 0.9)");
      } else {
        backgroundColor.set("rgb(0 0 0 / 0)");
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [backgroundColor]);

  return (
    <Tabs
      value={activeTab}
      className="flex flex-grow flex-col"
      onValueChange={setActiveTab}
      ref={tabRef}
    >
      <motion.div
        className={cn(
          "sticky top-0 z-10 mt-[calc(-0px-env(safe-area-inset-top))] w-full pt-[calc(13px+env(safe-area-inset-top))] backdrop-blur-[9px]",
        )}
        style={{ backgroundColor }}
      >
        <TabsList className="sticky top-[60px] z-10 mb-3 mt-1 h-auto w-full">
          <Trigger
            value="arena-claim"
            className="flex-1 p-2 pb-3 text-sm opacity-50 data-[state=active]:border-b-2 data-[state=active]:border-brand-orange data-[state=active]:font-medium data-[state=active]:text-off-white data-[state=active]:opacity-100"
          >
            Claim & Buy
          </Trigger>
          <Trigger
            value="vote"
            className="mx-3 flex-1 p-2 pb-3 text-sm opacity-50 data-[state=active]:border-b-2 data-[state=active]:border-brand-orange data-[state=active]:font-medium data-[state=active]:text-off-white data-[state=active]:opacity-100"
          >
            Vote
          </Trigger>
          <Trigger
            value="stake"
            className="flex-1 p-2 pb-3 text-sm opacity-50 data-[state=active]:border-b-2 data-[state=active]:border-brand-orange data-[state=active]:font-medium data-[state=active]:text-off-white data-[state=active]:opacity-100"
          >
            Stake
          </Trigger>
        </TabsList>
      </motion.div>
      <TabsContent
        value="arena-claim"
        className="flex flex-col px-4 py-3 data-[state=active]:flex-grow"
      >
        <ClaimTab
          user={user}
          videoRef={videoRef}
          play={play}
          setPlay={setPlay}
          router={router}
        />
      </TabsContent>
      <TabsContent
        value="vote"
        className="flex flex-col px-5 data-[state=active]:flex-grow"
        style={{ marginTop: 0, paddingTop: 0 }}
      >
        <VoteTab proposals={proposals} router={router} />
      </TabsContent>
      <TabsContent
        value="stake"
        className="flex flex-col px-5 data-[state=active]:flex-grow"
        style={{ marginTop: 0, paddingTop: 0 }}
      >
        <StakeTab />
      </TabsContent>
    </Tabs>
  );
};
