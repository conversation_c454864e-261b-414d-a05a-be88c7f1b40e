import React, { useEffect, useState } from "react";
import { NextRouter } from "next/router";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import Markdown from "react-markdown";
import gfm from "remark-gfm";
import { Address, formatEther, Hex } from "viem";

import { LogoIcon } from "@/components/icons";
import { LocatedNumber } from "@/components/located-number";
import { Button } from "@/components/ui/button";
import {
  votingPowerContractABI,
  votingPowerContractAddress,
} from "@/environments/stakingABI";

interface Proposal {
  id: string;
  title: string;
  body: string;
  state: string;
  end: number;
}

interface VoteTabProps {
  proposals: Proposal[];
  router: NextRouter;
}

export const VoteTab = ({ proposals, router }: VoteTabProps) => {
  const { primaryWallet } = useDynamicContext();
  const [votingPower, setVotingPower] = useState("0");

  const getVotingPower = async () => {
    if (!primaryWallet) return;
    if (!isEthereumWallet(primaryWallet)) {
      throw new Error("This wallet is not a Ethereum wallet");
    }

    const publicClient = await primaryWallet.getPublicClient();

    const contractVotingPower = await publicClient.readContract({
      address: votingPowerContractAddress as Address,
      abi: votingPowerContractABI,
      functionName: "getVotingPower",
      args: [primaryWallet.address as Hex],
    });

    setVotingPower(formatEther(contractVotingPower));
  };

  useEffect(() => {
    void getVotingPower();
  }, [primaryWallet]);

  return (
    <div className="flex flex-col gap-4 pb-[calc(70px+env(safe-area-inset-bottom))]">
      <h1 className="text-2xl font-semibold text-off-white">Voting</h1>
      <div className="text-sm text-light-gray-text">
        In order to vote, you&apos;ll need to stake your $ARENA. Each staked
        token will count for your voting power.
      </div>
      <div className="flex items-center justify-between rounded-[10px] border border-gray-text p-4 text-sm font-semibold text-white">
        <span>Your Voting Power</span>
        <span className="font-normal text-off-white">
          {LocatedNumber(votingPower)}
        </span>
      </div>
      <Button
        className="text-14 mb-3 mt-5 bg-brand-orange px-20 py-4 text-off-white"
        onClick={() => router.push("/wallet/token-portal?tab=stake")}
      >
        Stake ARENA
      </Button>
      <h1 className="text-2xl font-semibold text-off-white">Proposals</h1>
      {proposals &&
        proposals.map((proposal) => (
          <div
            key={proposal.id}
            className="cursor-pointer rounded-[10px] border border-gray-text p-4"
            onClick={() => {
              router.push(`/wallet/token-portal/vote-info/${proposal.id}`);
            }}
          >
            <div className="flex w-full justify-between">
              <div className="flex items-center">
                <LogoIcon className="mr-2 w-3 text-brand-orange" />
                <span className="text-xs">The Arena Team</span>
              </div>
              <Button
                variant={proposal.state === "active" ? "active" : undefined}
                className={`text-xs ${
                  proposal.state !== "active" ? "bg-purple-gradient" : ""
                } ml-4 self-start px-[10.6px] py-[7.07px] text-[10.61px] font-semibold leading-3 text-white`}
              >
                {proposal.state === "active" ? "Active" : "Closed"}
              </Button>
            </div>
            <div className="flex w-full items-start justify-between">
              <div className="flex flex-col gap-1 font-semibold text-off-white">
                <span className="mt-2 text-base">{proposal.title}</span>
                <span className="line-clamp-2 text-xs font-normal text-off-white">
                  <Markdown remarkPlugins={[gfm]}>{proposal.body}</Markdown>
                </span>
              </div>
            </div>
            <div className="mt-3 text-xs font-semibold text-light-gray-text">
              {proposal.state === "active"
                ? `Ends in ${Math.ceil(
                    (proposal.end - Date.now() / 1000) / 86400,
                  )} days`
                : `Ended ${Math.ceil(
                    (Date.now() / 1000 - proposal.end) / 86400,
                  )} days ago`}
            </div>
          </div>
        ))}
    </div>
  );
};
