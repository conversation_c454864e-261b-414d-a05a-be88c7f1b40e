"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import CopyToClipboard from "react-copy-to-clipboard";

import {
  livestreamQueries,
  useGenerateLivestreamIngressMutation,
} from "@/queries";
import { useLivestreamStore } from "@/stores/livestream";

import { CopyOutlineIcon } from "../icons";
import { toast } from "../toast";
import { Button } from "../ui/button";
import { Label } from "../ui/label";
import { TextInput } from "../ui/text-input";

export function IngressControls() {
  const id = useLivestreamStore((state) => state.id!);
  const queryClient = useQueryClient();

  const { data: livestreamIngress } = useQuery(
    livestreamQueries.livestreamIngress(),
  );

  const { mutateAsync: generateIngress, isPending } =
    useGenerateLivestreamIngressMutation({
      onSuccess: () => {
        toast.green("Ingress generated");
        queryClient.invalidateQueries({
          queryKey: livestreamQueries.livestreamIngressKey(),
        });
      },
    });

  const address = livestreamIngress?.server ?? "";
  const streamKey = livestreamIngress?.streamKey ?? "";

  const handleGenerateIngress = async () => {
    await generateIngress({ livestreamId: id });
  };

  if (!livestreamIngress) {
    return (
      <div className="mt-2 flex flex-col gap-2">
        <Button
          variant="outline"
          className="w-full"
          onClick={handleGenerateIngress}
          disabled={isPending}
        >
          Generate Streaming Key
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="mt-2 flex flex-col gap-2">
        <Label className="text-off-white">SERVER</Label>
        <div className="relative">
          <TextInput value={address} onChange={() => {}} disabled />
          <CopyToClipboard
            text={address}
            onCopy={() => {
              toast.green("Copied to clipboard");
            }}
          >
            <button className="absolute right-4 top-1/2 -translate-y-1/2">
              <CopyOutlineIcon className="size-5 text-[#9E9E9E]" />
            </button>
          </CopyToClipboard>
        </div>
      </div>
      <div className="mt-2 flex flex-col gap-2">
        <Label className="text-off-white">STREAMING KEY</Label>
        <div className="relative">
          <TextInput
            value={streamKey}
            type="password"
            onChange={() => {}}
            disabled
          />
          <CopyToClipboard
            text={streamKey}
            onCopy={() => {
              toast.green("Copied to clipboard");
            }}
          >
            <button className="absolute right-4 top-1/2 -translate-y-1/2">
              <CopyOutlineIcon className="size-5 text-[#9E9E9E]" />
            </button>
          </CopyToClipboard>
        </div>
        <p className="text-xs text-brand-orange">
          Warning: Never show your streaming key to anyone.
        </p>
      </div>
      <div className="mt-2 flex flex-col gap-2">
        <Button
          variant="outline"
          className="w-full"
          onClick={handleGenerateIngress}
          disabled={isPending}
        >
          Reset Streaming Key
        </Button>
      </div>
    </div>
  );
}
