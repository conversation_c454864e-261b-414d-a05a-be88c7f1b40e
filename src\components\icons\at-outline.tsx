import { ComponentProps } from "react";

export const AtOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      d="M12.6599 21.9999C10.934 21.9999 9.40835 21.7658 8.08291 21.2976C6.75747 20.8351 5.64354 20.1523 4.74112 19.2495C3.83869 18.3466 3.15623 17.2403 2.69374 15.9306C2.23125 14.6209 2 13.1217 2 11.433C2 9.80004 2.23407 8.33985 2.7022 7.05243C3.17597 5.76501 3.86125 4.67265 4.75804 3.77536C5.66046 2.87249 6.75465 2.18419 8.04061 1.71047C9.33221 1.23674 10.793 0.999878 12.423 0.999878C14.0079 0.999878 15.3954 1.25625 16.5855 1.76899C17.7812 2.27615 18.7795 2.96445 19.5804 3.83388C20.3869 4.69773 20.9904 5.67026 21.3909 6.75147C21.797 7.83268 22 8.94733 22 10.0954C22 10.9035 21.9605 11.7228 21.8816 12.5532C21.8026 13.3836 21.6362 14.1472 21.3824 14.8438C21.1286 15.5349 20.7366 16.0922 20.2064 16.5158C19.6819 16.9394 18.9712 17.1512 18.0745 17.1512C17.6796 17.1512 17.2453 17.0898 16.7716 16.9672C16.2978 16.8446 15.8776 16.6412 15.511 16.357C15.1444 16.0727 14.9272 15.691 14.8596 15.2117H14.758C14.6227 15.5349 14.414 15.8414 14.132 16.1312C13.8556 16.4211 13.4918 16.6523 13.0406 16.8251C12.595 16.9979 12.0508 17.0731 11.4078 17.0508C10.6746 17.023 10.0288 16.8613 9.47039 16.566C8.91201 16.265 8.44388 15.8582 8.06599 15.3454C7.69374 14.8271 7.41173 14.228 7.21997 13.548C7.03384 12.8625 6.94078 12.1241 6.94078 11.3327C6.94078 10.5803 7.05358 9.892 7.27919 9.26779C7.50479 8.64359 7.81782 8.09741 8.21828 7.62926C8.62437 7.1611 9.09814 6.7877 9.6396 6.50903C10.1867 6.2248 10.7761 6.04924 11.4078 5.98236C11.9718 5.92663 12.4851 5.95171 12.9475 6.0576C13.41 6.15792 13.7908 6.31118 14.0897 6.51739C14.3886 6.71803 14.5776 6.94096 14.6565 7.18618H14.758V6.24988H16.5516V13.8072C16.5516 14.2754 16.6842 14.6878 16.9492 15.0445C17.2143 15.4012 17.6007 15.5795 18.1083 15.5795C18.6836 15.5795 19.1235 15.3844 19.4281 14.9943C19.7383 14.6042 19.9498 14.0023 20.0626 13.1886C20.1811 12.3749 20.2403 11.3327 20.2403 10.062C20.2403 9.31516 20.1359 8.5795 19.9272 7.85497C19.7242 7.12488 19.414 6.44216 18.9966 5.80681C18.5849 5.17145 18.0632 4.61134 17.4315 4.12647C16.7998 3.6416 16.0581 3.26262 15.2064 2.98953C14.3604 2.71087 13.3988 2.57153 12.3215 2.57153C10.9961 2.57153 9.8088 2.77496 8.75973 3.18181C7.7163 3.58308 6.82798 4.17106 6.09476 4.94574C5.36717 5.71485 4.81162 6.65115 4.42809 7.75466C4.0502 8.85259 3.86125 10.101 3.86125 11.4999C3.86125 12.9211 4.0502 14.1834 4.42809 15.2869C4.81162 16.3904 5.37563 17.3211 6.12014 18.0791C6.87028 18.8371 7.79808 19.4111 8.90355 19.8012C10.009 20.1969 11.2837 20.3948 12.7276 20.3948C13.348 20.3948 13.96 20.3363 14.5635 20.2192C15.167 20.1022 15.6999 19.974 16.1624 19.8347C16.6249 19.6953 16.9577 19.5922 17.1607 19.5254L17.6345 21.0636C17.2848 21.2085 16.828 21.3534 16.264 21.4983C15.7056 21.6432 15.1077 21.763 14.4704 21.8578C13.8387 21.9525 13.2352 21.9999 12.6599 21.9999ZM11.6785 15.3454C12.4343 15.3454 13.0463 15.1949 13.5144 14.894C13.9825 14.593 14.3237 14.1388 14.5381 13.5313C14.7524 12.9238 14.8596 12.1575 14.8596 11.2324C14.8596 10.2961 14.7411 9.56596 14.5042 9.04208C14.2673 8.51819 13.9177 8.15036 13.4552 7.93857C12.9927 7.72679 12.423 7.6209 11.7462 7.6209C11.1032 7.6209 10.5533 7.78809 10.0964 8.12249C9.64524 8.45131 9.29837 8.8916 9.05584 9.44335C8.81895 9.98953 8.70051 10.5859 8.70051 11.2324C8.70051 11.9457 8.79639 12.6173 8.98816 13.2471C9.17992 13.8713 9.49295 14.3785 9.92724 14.7686C10.3615 15.1531 10.9453 15.3454 11.6785 15.3454Z"
      fill="currentColor"
    />
  </svg>
);
