import { useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import { UserCardUI } from "@/components/user-card";
import { useFollowMutation, useUnfollowMutation } from "@/queries";
import { TradesUsersTrendingResponse, User } from "@/queries/types";

interface TrendingUserCardProps {
  user: User;
}

export function TrendingUserCard({ user }: TrendingUserCardProps) {
  const queryClient = useQueryClient();

  const { mutateAsync: follow } = useFollowMutation({
    onMutate: async () => {
      toast.green(`You're now following ${user.twitterName}!`);

      await queryClient.cancelQueries({
        queryKey: ["trade", "users", "trending"],
      });
      await queryClient.cancelQueries({
        queryKey: ["user", "handle", user.twitterHandle],
      });

      const previousTrendingUsers = queryClient.getQueryData([
        "trade",
        "users",
        "trending",
      ]);
      const previousUser = queryClient.getQueryData([
        "user",
        "handle",
        user.twitterHandle,
      ]);

      queryClient.setQueryData(
        ["trade", "users", "trending"],
        (old: TradesUsersTrendingResponse) => {
          if (!old) return old;

          return {
            ...old,
            users: old.users.map((u) => {
              if (u.id === user.id) {
                return {
                  ...u,
                  following: true,
                };
              }
              return u;
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["user", "handle", user.twitterHandle],
        (
          old:
            | {
                user: User;
              }
            | undefined,
        ) => {
          if (!old) {
            return old;
          }

          return {
            ...old,
            user: {
              ...old.user,
              following: true,
            },
          };
        },
      );

      return { previousTrendingUsers, previousUser };
    },
    onError(err, variables, context) {
      toast.danger(`Failed to follow ${user.twitterName}.`);
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        context?.previousTrendingUsers,
      );
      queryClient.setQueryData(
        ["user", "handle", user.twitterHandle],
        context.previousUser,
      );
    },
  });

  const { mutateAsync: unfollow } = useUnfollowMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["trade", "users", "trending"],
      });
      await queryClient.cancelQueries({
        queryKey: ["user", "handle", user.twitterHandle],
      });

      const previousTrendingUsers = queryClient.getQueryData([
        "trade",
        "users",
        "trending",
      ]);
      const previousUser = queryClient.getQueryData([
        "user",
        "handle",
        user.twitterHandle,
      ]);

      queryClient.setQueryData(
        ["trade", "users", "trending"],
        (old: TradesUsersTrendingResponse) => {
          if (!old) return old;

          return {
            ...old,
            users: old.users.map((u) => {
              if (u.id === user.id) {
                return {
                  ...u,
                  following: false,
                };
              }
              return u;
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["user", "handle", user.twitterHandle],
        (
          old:
            | {
                user: User;
              }
            | undefined,
        ) => {
          if (!old) {
            return old;
          }

          return {
            ...old,
            user: {
              ...old.user,
              following: false,
            },
          };
        },
      );

      return { previousTrendingUsers, previousUser };
    },
    onError(err, variables, context) {
      toast.danger(`Failed to unfollow ${user.twitterName}.`);
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        context?.previousTrendingUsers,
      );
      queryClient.setQueryData(
        ["user", "handle", user.twitterHandle],
        context.previousUser,
      );
    },
  });

  function handleFollow() {
    if (user.following) {
      unfollow({ userId: user.id });
    } else {
      follow({ userId: user.id });
    }
  }

  return <UserCardUI user={user} handleFollow={handleFollow} />;
}
