import React, { useState } from "react";

import { Toaster } from "sonner";

import { mockCurrencies } from "@/components/tipping/tip-currency-selector.stories";
import { But<PERSON> } from "@/components/ui/button";

import { CommunityTippingPartyModal } from "./community-tipping-party-modal";

export default {
  title: "Modals/CommunityTippingPartyModal",
  component: CommunityTippingPartyModal,
  decorators: [
    (Story: any) => (
      <>
        <Story />
        <Toaster position="top-center" />
      </>
    ),
  ],
};

export const Default = () => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Button onClick={() => setOpen(true)}>
        Open Community Tipping Party Modal
      </Button>
      <CommunityTippingPartyModal
        open={open}
        setOpen={setOpen}
        contractAddress="0xMockCommunity"
        // Mocks for tipReceivers and sortedCurrencies will be used inside the component
      />
    </>
  );
};
