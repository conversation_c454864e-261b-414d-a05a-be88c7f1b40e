import { formatEther, Hex, parseEther } from "viem";

import {
  GetAvaxAmountReturnType,
  GetTokenAmountReturnType,
  TradeERC20Context,
} from "@/app/_components/trade-erc20/trade-erc20";
import { ERC20_CONTRACT_ABI } from "@/environments/BACKEND_FRIENDS_CONTRACT";
import { MinTokenData, swapTokens } from "@/environments/tokens";
import { useUser } from "@/stores";

import { useSwap } from "./use-swap";

const PARASWAP_ROUTER_ADDRESS = "******************************************";

function findOriginalNumber(target: bigint): bigint {
  const multiplier = 1005n;
  const base = 1000n;
  return (target * base) / multiplier;
}

export const useTradeERC20 = () => {
  const { user } = useUser();
  const { getRate, buildSwapWithCommission, getBuyRate } = useSwap();

  const subtractPlatformCommision = (amount: bigint): bigint =>
    amount - amount / 200n;

  const swapAvaxToToken = async (
    ctx: Pick<
      TradeERC20Context,
      "token" | "dynamicWallet" | "publicClient" | "walletClient" | "postAuthor"
    >,
    avaxAmount: string,
    commission: bigint | null,
    slippage: number,
  ) => {
    const srcAmount = findOriginalNumber(parseEther(avaxAmount)).toString();

    const sellRate = await getRate({
      srcToken: swapTokens["AVAX"],
      destToken: ctx.token,
      srcAmount,
    });

    const buyRate = await getBuyRate({
      srcToken: swapTokens["AVAX"],
      destToken: ctx.token,
      destAmount: sellRate.destAmount,
    });

    const res = await buildSwapWithCommission({
      srcToken: swapTokens["AVAX"],
      destToken: ctx.token,
      srcAmount,
      priceRoute: buyRate,
      userAddress: ctx.dynamicWallet.address,
      slippage,
    });

    const swapHash = await ctx.walletClient.sendTransaction({
      account: ctx.dynamicWallet.address as Hex,
      chain: ctx.walletClient.chain,
      to: res.to as Hex,
      value: res.value as any,
      data: res.data as any,
    });

    const receipt = await ctx.publicClient.waitForTransactionReceipt({
      hash: swapHash as Hex,
      confirmations: 1,
    });

    if (receipt.status === "reverted") throw new Error("transaction reverted");

    try {
      if (commission && ctx.postAuthor) {
        const commisoinHash = await ctx.walletClient.sendTransaction({
          account: ctx.dynamicWallet.address as Hex,
          chain: ctx.walletClient.chain,
          to: ctx.postAuthor.address as Hex,
          value: commission,
        });

        await ctx.publicClient.waitForTransactionReceipt({
          hash: commisoinHash as Hex,
          confirmations: 1,
        });
      }
    } catch (e) {
      console.log(e);
    }
  };

  const getTokenAmount = async (
    ctx: Pick<TradeERC20Context, "token" | "postAuthor">,
    amount: string,
    calculateCommission: (amount: string) => bigint,
  ): Promise<GetTokenAmountReturnType> => {
    let commission: bigint | null = null;
    if (ctx.postAuthor && user && ctx.postAuthor.address !== user.address) {
      commission = calculateCommission(amount);
    }

    const fixedAmount = findOriginalNumber(parseEther(amount));

    const srcAmount = commission
      ? (fixedAmount - commission).toString()
      : fixedAmount.toString();

    if (BigInt(srcAmount) <= 0n) {
      return { tokenAmount: "0" };
    }

    const rate = await getRate({
      srcToken: swapTokens["AVAX"],
      destToken: ctx.token,
      srcAmount,
    });

    const tokenAmountBigInt = BigInt(rate.destAmount);

    return {
      tokenAmount: formatEther(tokenAmountBigInt),
      tokenDisplayAmount: formatEther(tokenAmountBigInt),
      commission,
    };
  };

  const swapTokenToAvax = async (
    ctx: Pick<
      TradeERC20Context,
      | "token"
      | "dynamicWallet"
      | "publicClient"
      | "walletClient"
      | "postAuthor"
      | "userTokenBalance"
    >,
    tokenAmount: string,
    commission: bigint | null,
    slippage: number,
  ) => {
    const srcAmount = parseEther(tokenAmount).toString();

    const rate = await getRate({
      srcToken: ctx.token,
      destToken: swapTokens["AVAX"],
      srcAmount,
    });

    const allowance = await ctx.publicClient.readContract({
      address: ctx.token.address as Hex,
      abi: ERC20_CONTRACT_ABI,
      functionName: "allowance",
      args: [ctx.dynamicWallet.address as Hex, PARASWAP_ROUTER_ADDRESS as Hex],
    });

    const approvalAmount = parseEther(tokenAmount);
    if (allowance < approvalAmount) {
      const approvalTx = await ctx.walletClient.writeContract({
        address: ctx.token.address as Hex,
        abi: ERC20_CONTRACT_ABI,
        functionName: "approve",
        args: [PARASWAP_ROUTER_ADDRESS as Hex, ctx.userTokenBalance],
        account: ctx.dynamicWallet.address as Hex,
      });

      await ctx.publicClient.waitForTransactionReceipt({
        hash: approvalTx,
        confirmations: 1,
      });
    }

    const res = await buildSwapWithCommission({
      srcToken: ctx.token,
      destToken: swapTokens["AVAX"],
      srcAmount,
      priceRoute: rate,
      userAddress: ctx.dynamicWallet.address,
      slippage,
    });

    const swapHash = await ctx.walletClient.sendTransaction({
      account: ctx.dynamicWallet.address as Hex,
      chain: ctx.walletClient.chain,
      to: res.to as Hex,
      value: res.value as any,
      data: res.data as any,
    });

    const receipt = await ctx.publicClient.waitForTransactionReceipt({
      hash: swapHash as Hex,
      confirmations: 1,
    });

    if (receipt.status === "reverted") throw new Error("transaction reverted");

    try {
      if (commission && ctx.postAuthor) {
        const commisoinHash = await ctx.walletClient.sendTransaction({
          account: ctx.dynamicWallet.address as Hex,
          chain: ctx.walletClient.chain,
          to: ctx.postAuthor.address as Hex,
          value: commission,
        });

        await ctx.publicClient.waitForTransactionReceipt({
          hash: commisoinHash as Hex,
          confirmations: 1,
        });
      }
    } catch (e) {
      console.log(e);
    }
  };

  const getAvaxAmount = async (
    ctx: Pick<TradeERC20Context, "token" | "postAuthor">,
    amount: bigint,
    calculateCommission: (destAmount: string) => bigint,
  ): Promise<GetAvaxAmountReturnType> => {
    if (amount <= 0n) return { avaxAmount: "0" };

    const srcAmount = amount.toString();

    const rate = await getRate({
      srcToken: ctx.token,
      destToken: swapTokens["AVAX"],
      srcAmount,
    });

    const avaxAmountBigInt = subtractPlatformCommision(BigInt(rate.destAmount));

    let commission: bigint | null = null;
    if (ctx.postAuthor && user && ctx.postAuthor.address !== user.address) {
      commission = calculateCommission(rate.destAmount);
      return {
        avaxAmount: formatEther(avaxAmountBigInt - commission),
        commission,
      };
    }

    return { avaxAmount: formatEther(avaxAmountBigInt) };
  };

  const getConversionRate = async (token: MinTokenData) => {
    const srcAmount = parseEther("1").toString();

    const rate = await getRate({
      srcToken: swapTokens["AVAX"],
      destToken: token,
      srcAmount,
    });

    return BigInt(rate.destAmount);
  };

  return {
    swapAvaxToToken,
    getTokenAmount,
    swapTokenToAvax,
    getAvaxAmount,
    getConversionRate,
    subtractPlatformCommision,
  };
};
