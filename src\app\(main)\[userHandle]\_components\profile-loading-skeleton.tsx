"use client";

import { useRouter } from "next/navigation";

import Skeleton from "react-loading-skeleton";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { PostLoadingSkeleton } from "@/components/post-loading-skeleton";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export const ProfileLoadingSkeleton = () => {
  const router = useRouter();

  const goBack = () => {
    if (typeof window !== "undefined" && window.history.length > 1) {
      router.back();
    } else {
      router.push("/home");
    }
  };

  return (
    <>
      <div className="pt-pwa relative z-20">
        <button
          className="absolute left-5 top-[calc(2rem+env(safe-area-inset-top))] flex size-8 items-center justify-center rounded-full bg-dark-bk"
          onClick={goBack}
        >
          <ArrowBackOutlineIcon className="size-6 text-white" />
        </button>
        <div className="aspect-[3/1] w-full bg-cover bg-center bg-no-repeat" />
        <div className="px-5">
          <div className="-mt-10 flex items-end gap-5 leading-none">
            <Skeleton circle className="size-[92px] border border-[#E8E8E8]" />
            <div className="flex gap-6 sm:hidden">
              <div className="flex flex-col gap-0.5 font-medium text-off-white">
                <span className="text-sm text-gray-text">Followers</span>
                <span className="text-base leading-5">
                  <Skeleton className="h-4 w-10" />
                </span>
              </div>
              <div className="flex flex-col gap-0.5 font-medium text-off-white">
                <span className="text-sm text-gray-text">Following</span>
                <span className="text-base leading-5">
                  <Skeleton className="h-4 w-10" />
                </span>
              </div>
            </div>
          </div>
          <div className="mt-4 flex flex-col gap-0.5">
            <h1 className="flex items-center gap-1.5 text-base font-medium leading-5 text-off-white">
              <Skeleton className="h-4 w-24" />
            </h1>
            <span className="text-xs text-gray-text">
              <Skeleton className="h-3 w-32" />
            </span>
          </div>
          <div className="mt-5 text-sm text-off-white">
            <Skeleton className="h-[14px] w-full" />
            <Skeleton className="h-[14px] w-5/6" />
          </div>
          <div className="mt-5 hidden items-center gap-5 sm:flex">
            <div className="flex gap-2 font-medium text-off-white">
              <span className="text-base leading-5">
                <Skeleton className="h-4 w-10" />
              </span>
              <span className="text-sm text-gray-text">Followers</span>
            </div>
            <div className="flex gap-2 font-medium text-off-white">
              <span className="text-base leading-5">
                <Skeleton className="h-4 w-10" />
              </span>
              <span className="text-sm text-gray-text">Following</span>
            </div>
          </div>
        </div>
      </div>
      <Tabs value="threads" className="flex-grow">
        <div className="sticky top-0 z-10 w-full pt-[calc(14px+env(safe-area-inset-top))] shadow-[0px_-5px_14px_20px_rgba(0,0,0,0.25)] backdrop-blur-[9px]">
          <TabsList className="mt-2 w-full">
            <TabsTrigger value="threads">Threads</TabsTrigger>
            <TabsTrigger value="ticket-holders">Ticket Holders</TabsTrigger>
            <TabsTrigger value="stats">Stats</TabsTrigger>
          </TabsList>
        </div>
        <TabsContent value="threads" className="h-full">
          {Array.from({ length: 10 }).map((_, i) => (
            <PostLoadingSkeleton key={i} />
          ))}
        </TabsContent>
      </Tabs>
    </>
  );
};
