import { AutoResizingTextarea } from "@/components/ui/auto-resizing-textarea";

import { reportTypes } from "./reason-page";

export const DetailsPage = ({
  reason,
  description,
  setDescription,
  maxDescriptionLength,
}: {
  reason: string | null;
  description: string;
  setDescription: (value: string) => void;
  maxDescriptionLength: number;
}) => (
  <div className="px-6">
    <div className="flex flex-col gap-2 rounded-lg bg-chat-bubble p-4">
      <h4 className="text-sm font-semibold leading-none text-off-white">
        {reason}
      </h4>
      <p className="text-sm text-gray-text">
        {reportTypes.find((t) => t.reason === reason)?.description}
      </p>
    </div>
    <div className="mt-8">
      <h5 className="mb-4 text-sm font-semibold text-white">DESCRIPTION</h5>
      <AutoResizingTextarea
        value={description}
        onChange={(e) => setDescription(e.target.value)}
        placeholder="Do you want to add more context?"
        maxLength={maxDescriptionLength}
      />
    </div>
  </div>
);
