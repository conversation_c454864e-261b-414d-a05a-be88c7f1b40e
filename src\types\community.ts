import { Group } from "@/queries/types/chats";
import { User } from "@/types/user";

export interface CommunityExtended {
  id: string;
  contractAddress: string;
  pairAddress: string;
  owner: User;
  ownerId: string;
  group: Group;
  groupId: string;
  name: string;
  type: "open" | "exclusive";
  photoURL: string;
  description: string | null;
  tokenName: string;
  bannerURL: string | null;
  ticker: string;
  followerCount: number;
  tokenPhase: number;
  buys: number;
  sells: number;
  transactionHash: string;
  bcGroupId: string;
  isLP: boolean;
  createdOn: Date;
  isOfficial: boolean;
  isOwnerExternal: boolean;
  postingThreshold: string;
  following:
    | {
        id: number;
        createdOn: string;
        modifiedOn: string;
        followerId: string;
        communitId: string;
        followingActionTs: string | null;
        confirmed: string | null;
      }
    | boolean
    | null;
  stats?: CommunityStats;
  memberLink?: CommunityMember | null;
}

export interface CommunityStats {
  id: string;
  communityId: string;
  buys: number;
  sells: number;
  buyVolume: string;
  sellVolume: string;
  price: string;
  lastPrice: string;
  liquidity: string;
  marketCap: string;
  lastMarketCap: string;
  totalSupply: string;
  createdOn: Date;
}

export type CommunityMemberRole = "owner" | "moderator" | "leader" | "member";

export interface CommunityMember {
  id: string;
  userId: string;
  communityId: string;
  role: CommunityMemberRole;
  isMuted: boolean;
  votingLeaderCounter: number;
  lastSeen: string | null;
  createdOn: string;
}

export interface GroupStatsResponse {
  stats?: CommunityStats;
}

export interface PricesResponse {
  buyPrice: string;
  sellPrice: string;
}

export interface GetTokenHoldersData {
  page: number;
  pageSize: number;
  contractAddress: string;
}

export interface GroupTokenHolder {
  address: string;
  tokenRatio: number;
  user: User;
}

export interface GetFeedEligibleGroups {
  page: number;
  pageSize: number;
  searchString: string;
}

export interface GroupTokenHoldersResponse {
  holders: GroupTokenHolder[];
  numberOfPages: number;
  numberOfResults: number;
  pageSize: number;
}

export interface CommunityTrade {
  id: string;
  bcGroupId: string;
  address: string;
  tokenAmount: string;
  referrerAddress: string;
  referralFee: string;
  isBuy: boolean;
  block: number;
  index: number;
  transactionHash: string;
  amount: string;
  createdOn: Date;
  community?: CommunityExtended;
  trader?: User;
}

export interface CommunityTradesRecentResponse {
  trades: CommunityTrade[];
}
