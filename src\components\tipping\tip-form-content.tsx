"use client";

import React, { useEffect, useMemo, useState } from "react";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQueryClient } from "@tanstack/react-query";
import { Controller, useForm } from "react-hook-form";
import { Address, parseUnits } from "viem";
import { z } from "zod";

import { batchMultiSendDynamic } from "@/api/client/dynamic/send-funds-dynamic";
import {
  MULTI_SEND_CONTRACT_ABI,
  MULTI_SEND_CONTRACT_ADDRESS,
} from "@/environments/MULTI_SEND_CONTRACT";
import { fallbackAvax } from "@/environments/tokens";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import { useTipMutation } from "@/queries";
import { useTippableCurrenciesQuery } from "@/queries/currency-queries";
import { useUser } from "@/stores";
import { cn } from "@/utils/cn";
import { formatPrice } from "@/utils/format-token-price";
import { formatNumericValue } from "@/utils/number";

import { filterCurrencies } from "../currecency-items";
import { XCircleOutlineIcon } from "../icons";
import {
  TippingRecipient,
  TippingRecipients,
} from "../tipping/tipping-recipients";
import { toast } from "../toast";
import { Button } from "../ui/button";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger } from "../ui/select";
import { TipAmountInputV2 } from "./tip-amount-input-v2";
import { TipCurrencySelector } from "./tip-currency-selector";

// mock functions for testing
// const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// const useTipMutation = (options: { onSuccess: () => void }) => {
//   const mutateAsync = async (data: any) => {
//     console.log("Mock useTipMutation called with data:", data);
//     await delay(1500);
//     options.onSuccess();
//     return { success: true };
//   };

//   return {
//     mutateAsync,
//     isPending: false,
//   };
// };

// const batchMultiSendDynamic = async (
//   wallet: any,
//   addresses: string[],
//   amounts: any[],
//   currency: string,
//   isToken: boolean,
//   contractAddress?: string,
// ) => {
//   console.log("Mock batchMultiSendDynamic called with:", {
//     wallet,
//     addresses,
//     amounts,
//     currency,
//     isToken,
//     contractAddress,
//   });
//   await delay(10000);
//   return {
//     txHash: ["mocked-tx-hash"],
//     txData: ["mocked-tx-data"],
//   };
// };

export interface TipFormContentProps {
  recepients: TippingRecipient[];
  threadId?: string;
  setOpen: any;
  sortedCurrencies: any;
  setResetForm?: (fn: () => void) => void;
  distributionMode?: "equal" | "byTickets";
  onSubmit?: (args: any) => Promise<void>;
  onMaxClick?: (currency: string) => void;
  buttonLabel?: string;
  customRecipientsSelector?: React.ReactNode;
  onPartyNotify?: (args: {
    currency: string;
    txHash: string;
    txData: string;
    recipient: TippingRecipient;
  }) => Promise<void>;
  onPartyMessage?: (args: { message: any }) => Promise<void>;
  onAfterTip?: (args: any) => void;
  className?: string;
}

export const TipFormContent = ({
  recepients,
  threadId,
  setOpen,
  sortedCurrencies: sortedCurrenciesProp,
  setResetForm,
  distributionMode = "equal",
  onSubmit: customOnSubmit,
  onMaxClick: customOnMaxClick,
  buttonLabel,
  customRecipientsSelector,
  onPartyNotify,
  onPartyMessage,
  onAfterTip,
  className,
}: TipFormContentProps) => {
  const queryClient = useQueryClient();
  const [tipAmount, setTipAmount] = useState("1");
  const [tipAccurateAmount, setTipAccurateAmount] = useState("0");
  const [totalTipUSD, setTotalTipUSD] = useState("0");
  const [isTippingDisabled, setIsTippingDisabled] = useState(false);
  const [feePercentage, setFeePercentage] = useState(2);

  const { user } = useUser();

  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useTippableCurrenciesQuery();

  const { sortedCurrencies: sortedCurrenciesHook, avaxPrice } =
    useWalletCurrencies({
      user,
      currenciesData,
      isCurrenciesLoading,
    });
  const sortedCurrencies = sortedCurrenciesProp || sortedCurrenciesHook;

  const [symbol, setSymbol] = useState("AVAX");
  const token = useMemo(
    () =>
      sortedCurrencies.find((t: any) => t.symbol === symbol) ?? fallbackAvax,
    [sortedCurrencies, symbol],
  );

  // solana logic ->
  const wallets = {
    ARENA: "The Arena",
    PHANTOM: "Phantom",
  };

  const [wallet, setWallet] = useState(wallets.ARENA);

  const provider =
    typeof window !== "undefined" && (window as any).phantom?.solana;

  const isSolanaCurrency = [
    "SOL",
    "Bonk",
    "$WIF",
    "USEDCAR",
    "Moutai",
    "HARAMBE",
  ].includes(token.symbol);

  const showWarning =
    isSolanaCurrency && !provider && wallet === wallets.PHANTOM;
  // <- end solana logic

  const TipInput = z.object({
    currency: z.string(),
    tipAmount: z
      .string()
      .min(1, {
        message: "Tip amount is required",
      })
      .refine((v) => !isNaN(parseFloat(v.replace(/,/g, ""))), {
        message: "Tip amount must be a number",
      })
      .refine(
        (v) =>
          isSolanaCurrency
            ? true
            : parseFloat(v.replace(/,/g, "")) <=
              parseFloat(
                token.isToken
                  ? formatPrice(token.balance || "0").toString()
                  : (token.balance || "0").toString().replace(/,/g, ""),
              ),
        {
          message: "Insufficient balance",
        },
      ),
  });
  type TipInputType = z.infer<typeof TipInput>;

  const form = useForm<TipInputType>({
    defaultValues: {
      currency: "AVAX",
      tipAmount: "",
    },
    resolver: zodResolver(TipInput),
    mode: "all",
  });

  const [isPendingDynamic, setIsPendingDynamic] = useState(false);

  const { mutateAsync: tip, isPending } = useTipMutation({
    onSuccess: () => {
      toast.green("Tips sent!");
      setOpen(false);
      form.reset();
      setTipAmount("");
      setTotalTipUSD("0");
      setWallet(wallets.ARENA);
      queryClient.invalidateQueries({
        queryKey: ["wallet", "balance", user?.address, token.symbol],
      });
      setSymbol("AVAX");
      queryClient.invalidateQueries({
        queryKey: ["currency", "system"],
      });
    },
  });

  const { primaryWallet } = useDynamicContext();

  const isMulti = recepients.length > 1;

  const defaultOnSubmit = async (values: TipInputType) => {
    setOpen(false);
    let transactionHash = "";
    let transactionData = "";
    let isDynamic = false;
    let isToken = false;
    let tokenContractAddress = "";
    const usdAmount = parseFloat(values.tipAmount.replace(/,/g, ""));
    let tokenAmount = usdAmount;
    if (token.balanceUsd && Number(token.balanceUsd) > 0 && token.balance) {
      tokenAmount =
        (usdAmount / Number(token.balanceUsd)) * Number(token.balance);
    }
    let addresses = recepients.map((r) => r.address);
    let amounts: any[] = [];
    if (isMulti) {
      if (distributionMode === "equal") {
        amounts = addresses.map(() => {
          return token.isToken
            ? BigInt(Math.floor(tokenAmount * 10 ** (token.decimals || 18)))
            : parseUnits(tokenAmount.toString(), token.decimals || 18);
        });
      } else if (distributionMode === "byTickets") {
        amounts = recepients.map((r: any) => {
          const count = r.ticketsCount || 1;
          return token.isToken
            ? BigInt(
                Math.floor(tokenAmount * count * 10 ** (token.decimals || 18)),
              )
            : parseUnits(
                (tokenAmount * count).toString(),
                token.decimals || 18,
              );
        });
      }
    } else {
      addresses = [recepients[0].address];
      amounts = [
        token.isToken
          ? BigInt(Math.floor(tokenAmount * 10 ** (token.decimals || 18)))
          : parseUnits(tokenAmount.toString(), token.decimals || 18),
      ];
    }
    let txHashArr: string[] = [];
    let txDataArr: string[] = [];
    if (!isSolanaCurrency) {
      if (user && user.address === user.dynamicAddress?.toLowerCase()) {
        const decimals = values.currency === "MEAT" ? 6 : 18;
        const tippedToken = sortedCurrencies.find(
          (t: any) => t.symbol === values.currency,
        );
        isToken = tippedToken?.isToken || false;
        tokenContractAddress = tippedToken?.contractAddress || "";
        isDynamic = true;
        setIsPendingDynamic(true);
        const loadingToastId = toast.loading("Sending tip");
        try {
          const { txHash, txData } = await batchMultiSendDynamic(
            primaryWallet,
            addresses,
            amounts,
            values.currency,
            isToken,
            tokenContractAddress,
          );
          txHashArr = Array.isArray(txHash) ? txHash : [txHash];
          txDataArr = Array.isArray(txData) ? txData : [txData];
          // Party notify and tip for each recipient
          if (isMulti) {
            for (let i = 0; i < recepients.length; i++) {
              if (onPartyNotify) {
                await onPartyNotify({
                  currency: values.currency,
                  txHash: txHashArr[i],
                  txData: txDataArr[i],
                  recipient: recepients[i],
                });
              }
              await tip({
                currency: values.currency,
                tipAmount: (
                  parseFloat(values.tipAmount.replace(/,/g, "")) -
                  (parseFloat(values.tipAmount.replace(/,/g, "")) *
                    feePercentage) /
                    100
                ).toString(),
                userId: recepients[i].id,
                threadId: threadId,
                wallet: isSolanaCurrency ? wallet : wallets.ARENA,
                isDynamic,
                txHash: txHashArr[i] || "",
                txData: txDataArr[i] || "",
                isToken,
                tokenContractAddress,
              });
              if (onAfterTip) onAfterTip({ values, recipient: recepients[i] });
            }
          }
        } catch (e) {
          toast.danger("Tipping failed! Please retry later.");
          console.log("Something went wrong with sendFundsDynamic:", e);
        } finally {
          setIsPendingDynamic(false);
          toast.dismiss(loadingToastId);
        }
      }
    }
    // Party message (data channel)
    if (isMulti && onPartyMessage) {
      await onPartyMessage({
        message: {
          type: "tip-party",
          data: {
            amount: Number(values.tipAmount.replace(/,/g, "")),
            currency: values.currency,
            recipients: recepients.map((r) => r.id),
          },
        },
      });
    }
    if (!isMulti) {
      const loadingToastId = toast.loading(
        "Almost ready, sending notifications",
      );
      try {
        await tip({
          currency: values.currency,
          tipAmount: (
            parseFloat(values.tipAmount.replace(/,/g, "")) -
            (parseFloat(values.tipAmount.replace(/,/g, "")) * feePercentage) /
              100
          ).toString(),
          userId: recepients[0].id,
          threadId: threadId,
          wallet: isSolanaCurrency ? wallet : wallets.ARENA,
          isDynamic,
          txHash: txHashArr[0] || "",
          txData: txDataArr[0] || "",
          isToken,
          tokenContractAddress,
        });
        if (onAfterTip) onAfterTip({ values, recipient: recepients[0] });
      } catch (e) {
      } finally {
        toast.dismiss(loadingToastId);
      }
    }
  };

  const tipValue = async (price: string, value: string) => {
    setTotalTipUSD(
      formatNumericValue(
        Number((Number(value) * Number(price)).toFixed(2)).toString(),
      ),
    );
  };

  const onMaxClick = async (currency: string) => {
    let maxTip = token.isToken
      ? formatPrice(token.balance || "0")
      : (token.balance || "0").toString().replace(/,/g, "");
    if (token.isToken) {
      setTipAmount(formatNumericValue(Number(maxTip).toString()));
      setTipAccurateAmount(token.balance || "0");
    }
    let maxTipWithoutGas;
    if (currency === "AVAX") {
      maxTipWithoutGas = Number(maxTip) - 0.005;
      if (maxTipWithoutGas < 0) {
        maxTipWithoutGas = 0;
      }
      maxTip = maxTipWithoutGas.toString();
    }
    if (Number(maxTip) < 1) {
      let numMaxTip = Number(
        Number(maxTip)
          .toFixed(4)
          .replace(/\.?0+$/, ""),
      );
      if (numMaxTip <= Number(maxTip)) {
        maxTip = numMaxTip.toString();
      } else {
        maxTip = (numMaxTip - 0.0001).toFixed(4).toString();
      }
    } else {
      let numMaxTip = Number(
        Number(maxTip)
          .toFixed(2)
          .replace(/\.?0+$/, ""),
      );
      if (numMaxTip <= Number(maxTip)) {
        maxTip = numMaxTip.toString();
      } else {
        maxTip = (numMaxTip - 0.01).toFixed(2).toString();
      }
    }
    if (!token.isToken) {
      setTipAmount(formatNumericValue(Number(maxTip).toString()));
    }
    form.setValue("tipAmount", formatNumericValue(Number(maxTip).toString()));
  };

  useEffect(() => {
    const sanitizedValue = tipAmount.replace(/,/g, "");
    if (
      Number(sanitizedValue) >
        parseFloat(
          token.isToken
            ? formatPrice(token.balance || "0").toString()
            : (token.balance || "0").toString().replace(/,/g, ""),
        ) ||
      Number(sanitizedValue) === 0
    ) {
      setIsTippingDisabled(true);
    } else {
      setIsTippingDisabled(false);
    }
    tipValue(
      token.isToken
        ? (Number(formatPrice(token.systemRate)) * avaxPrice).toString()
        : token.systemRate,
      sanitizedValue,
    );
  }, [token, tipAmount]);

  useEffect(() => {
    async function fetchFeePercentage() {
      if (!primaryWallet) {
        return;
      }
      if (!isEthereumWallet(primaryWallet)) {
        toast.danger("This wallet is not an Ethereum wallet");
        setIsPendingDynamic(false);
        return;
      }

      const publicClient = await primaryWallet.getPublicClient();

      const percentage = await publicClient.readContract({
        address: MULTI_SEND_CONTRACT_ADDRESS as Address,
        abi: MULTI_SEND_CONTRACT_ABI,
        functionName: "feePercentage",
      });

      setFeePercentage(Number(percentage));
    }

    void fetchFeePercentage();
  }, []);

  const resetForm = () => {
    form.reset();
    setWallet(wallets.ARENA);
    setSymbol("AVAX");
    setTotalTipUSD("0");
    setTipAmount("");
    setTipAccurateAmount("0");
  };

  useEffect(() => {
    if (setResetForm) setResetForm(resetForm);
  }, [
    setResetForm,
    form,
    setWallet,
    setSymbol,
    setTotalTipUSD,
    setTipAmount,
    setTipAccurateAmount,
  ]);

  const handlePresetClick = (amount: string) => {
    if (amount === "max") {
      onMaxClick(token.symbol);
    } else {
      form.setValue("tipAmount", amount, { shouldValidate: true });
      setTipAmount(amount);
    }
  };

  return (
    <div className={cn("flex h-full flex-1 flex-col", className)}>
      <TippingRecipients recipients={recepients} />
      <form
        onSubmit={form.handleSubmit(customOnSubmit || defaultOnSubmit)}
        className="flex flex-1 flex-col"
      >
        <div className="flex flex-1 flex-col justify-center sm:pt-14">
          <Controller
            name="tipAmount"
            control={form.control}
            render={({ field: { name, onChange, value, onBlur, ref } }) => (
              <TipAmountInputV2
                name={name}
                token={token}
                value={tipAmount}
                onValueChange={(value: string) => {
                  onChange(value);
                  setTipAmount(value);
                  setTipAccurateAmount("0"); // stub
                  form.setValue("tipAmount", value, { shouldValidate: true });
                }}
                onBlur={onBlur}
                ref={ref}
                errorMessage={form.formState.errors.tipAmount?.message}
                onPresetClick={handlePresetClick}
              />
            )}
          />
          {isSolanaCurrency && (
            <div className="mt-2 flex flex-col gap-2">
              <Label htmlFor="form-item-wallet">Wallet</Label>
              <Select
                name="form-item-wallet"
                value={wallet}
                onValueChange={(value) => {
                  setWallet(value);
                  setTipAmount("");
                  setTipAccurateAmount("0");
                }}
                key={"form-item-wallet" + wallet}
              >
                <SelectTrigger
                  className="w-full text-base"
                  id={`form-item-${name}`}
                >
                  {wallet}
                </SelectTrigger>
                <SelectContent className="max-h-48">
                  <SelectItem value={wallets.ARENA} className="text-base">
                    {wallets.ARENA}
                  </SelectItem>
                  <SelectItem value={wallets.PHANTOM} className="text-base">
                    {wallets.PHANTOM}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
        <div className="mt-auto flex flex-col gap-8 pb-4 sm:gap-0 sm:pb-0">
          <div className=" sm:pb-6 sm:pt-14 ">
            <Controller
              name="currency"
              control={form.control}
              render={({ field: { onChange } }) => (
                <TipCurrencySelector
                  token={token}
                  sortedCurrencies={filterCurrencies(sortedCurrencies)}
                  onValueChange={(value) => {
                    setSymbol(value);
                    onChange(value);
                    setTipAmount("");
                    setTipAccurateAmount("0");
                    form.setValue("tipAmount", "");
                  }}
                />
              )}
            />
          </div>
          {!showWarning && (
            <Button
              className="w-full"
              type="submit"
              loading={isPending || isPendingDynamic}
              disabled={isTippingDisabled}
            >
              {buttonLabel || (isMulti ? "Send tips" : "Send tip")}
            </Button>
          )}
          {showWarning && (
            <div className="flex w-full items-center gap-[10px] rounded-lg border border-[#BE5D5D] bg-[#D14848] px-4 py-3 sm:py-0">
              <XCircleOutlineIcon className="size-6 flex-shrink-0 text-off-white" />
              <span className="text-xs leading-5 text-off-white">
                No Phantom wallet detected. Please install the Phantom wallet
                extension or use Phantom wallet browser on mobile
              </span>
            </div>
          )}
        </div>
        {customRecipientsSelector}
      </form>
    </div>
  );
};
