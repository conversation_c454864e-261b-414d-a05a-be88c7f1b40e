import { Extension } from "@tiptap/core";
import { Node as ProseMirrorNode } from "prosemirror-model";
import { EditorState, Plugin, PluginKey, Transaction } from "prosemirror-state";
import { Decoration, DecorationSet } from "prosemirror-view";

const urlRegex =
  /(^|[\s:])(((https?:\/\/)|(www\.))[^<\s]+[\w\/?&=#.%+\-]*|arena\.social(?:\/[^\s<]*)?|((www\.)|([a-zA-Z0-9\-]+?\.)+)(com|ch|de|net|org|it|at|eu|biz|io|xyz|tech|tr)(\/[^\s,<.\s@]*)?(\?[^\s<]*)?(#[^\s<]*)?)/gi;

export const UrlHighlighter = Extension.create({
  name: "urlHighlighter",

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey("urlHighlighter"),
        state: {
          init(_config, { doc }): DecorationSet {
            return DecorationSet.empty;
          },
          apply(
            transaction: Transaction,
            oldDecorationSet: DecorationSet,
            oldState: EditorState,
            newState: EditorState,
          ): DecorationSet {
            if (!transaction.docChanged) return oldDecorationSet;
            return getUrlDecorations(newState.doc);
          },
        },
        props: {
          decorations(state: EditorState): DecorationSet {
            return this.getState(state) as DecorationSet;
          },
        },
      }),
    ];
  },
});

// Extracts URLs and returns the corresponding decorations.
function getUrlDecorations(doc: ProseMirrorNode): DecorationSet {
  const decorations: Decoration[] = [];

  doc.descendants((node, pos) => {
    if (!node.isText) return;

    const text: string = node.text ?? "";

    urlRegex.lastIndex = 0; // Reset regex state before matching

    let match: RegExpExecArray | null;
    while ((match = urlRegex.exec(text)) !== null) {
      const [fullMatch, leadingSpaceOrStart, urlPortion] = match;
      const startIndex: number = match.index + leadingSpaceOrStart.length;
      const start: number = pos + startIndex;
      const end: number = start + urlPortion.length;

      let urlStr: string = urlPortion;
      if (!/^http/i.test(urlStr)) {
        urlStr = "https://" + urlStr;
      }

      try {
        new URL(urlStr);
        decorations.push(
          Decoration.inline(start, end, {
            class: "text-brand-orange",
          }),
        );
      } catch {
        // Ignore invalid URLs
      }
    }
  });

  return DecorationSet.create(doc, decorations);
}
