import { ComponentProps } from "react";

export const AccountSecurityIcon = (props: ComponentProps<"svg">) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 12L11 14L15 9.99997M20.618 5.98397C17.4561 6.15189 14.3567 5.05858 12 2.94397C9.64327 5.05858 6.5439 6.15189 3.382 5.98397C3.12754 6.96908 2.99918 7.98252 3 8.99997C3 14.591 6.824 19.29 12 20.622C17.176 19.29 21 14.592 21 8.99997C21 7.95797 20.867 6.94797 20.618 5.98397Z"
      stroke="#F4F4F4"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PlusCircleIcon = (props: ComponentProps<"svg">) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M12 9V12M12 12V15M12 12H15M12 12H9M21 12C21 13.1819 20.7672 14.3522 20.3149 15.4442C19.8626 16.5361 19.1997 17.5282 18.364 18.364C17.5282 19.1997 16.5361 19.8626 15.4442 20.3149C14.3522 20.7672 13.1819 21 12 21C10.8181 21 9.64778 20.7672 8.55585 20.3149C7.46392 19.8626 6.47177 19.1997 5.63604 18.364C4.80031 17.5282 4.13738 16.5361 3.68508 15.4442C3.23279 14.3522 3 13.1819 3 12C3 9.61305 3.94821 7.32387 5.63604 5.63604C7.32387 3.94821 9.61305 3 12 3C14.3869 3 16.6761 3.94821 18.364 5.63604C20.0518 7.32387 21 9.61305 21 12Z"
      stroke="#F4F4F4"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const MailIcon = (props: ComponentProps<"svg">) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3 8.00043L10.89 13.2604C11.2187 13.4797 11.6049 13.5967 12 13.5967C12.3951 13.5967 12.7813 13.4797 13.11 13.2604L21 8.00043M5 19.0004H19C19.5304 19.0004 20.0391 18.7897 20.4142 18.4146C20.7893 18.0396 21 17.5309 21 17.0004V7.00043C21 6.46999 20.7893 5.96129 20.4142 5.58621C20.0391 5.21114 19.5304 5.00043 19 5.00043H5C4.46957 5.00043 3.96086 5.21114 3.58579 5.58621C3.21071 5.96129 3 6.46999 3 7.00043V17.0004C3 17.5309 3.21071 18.0396 3.58579 18.4146C3.96086 18.7897 4.46957 19.0004 5 19.0004Z"
      stroke="#F4F4F4"
      strokeWidth="1.8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const AuthenticatorIcon = (props: ComponentProps<"svg">) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 12L11 14L15 9.99997M20.618 5.98397C17.4561 6.15189 14.3567 5.05858 12 2.94397C9.64327 5.05858 6.5439 6.15189 3.382 5.98397C3.12754 6.96908 2.99918 7.98252 3 8.99997C3 14.591 6.824 19.29 12 20.622C17.176 19.29 21 14.592 21 8.99997C21 7.95797 20.867 6.94797 20.618 5.98397Z"
      stroke="#F4F4F4"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const UnlinkIcon = (props: ComponentProps<"svg">) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21 20.951L3.049 3M11.2133 18.4276L9.85843 19.7821C9.49045 20.1631 9.05027 20.467 8.56358 20.6761C8.0769 20.8851 7.55345 20.9952 7.02377 20.9998C6.4941 21.0044 5.96882 20.9035 5.47857 20.7029C4.98832 20.5023 4.54293 20.2061 4.16838 19.8316C3.79383 19.457 3.49763 19.0116 3.29706 18.5214C3.09648 18.0311 2.99555 17.5058 3.00015 16.9762C3.00475 16.4465 3.1148 15.923 3.32386 15.4364C3.53293 14.9497 3.83683 14.5095 4.21783 14.1415L5.5068 12.8525M12.8582 5.5011L14.1906 4.16873C14.943 3.44208 15.9506 3.04 16.9966 3.04909C18.0425 3.05818 19.043 3.47771 19.7826 4.21732C20.5222 4.95694 20.9418 5.95745 20.9508 7.00339C20.9599 8.04932 20.5579 9.05698 19.8312 9.80933L18.4988 11.1417"
      stroke="#808080"
      strokeWidth="1.49592"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ShieldIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="23"
    viewBox="0 0 24 23"
    fill="none"
  >
    <path
      d="M9.24598 11.0463L11.0819 12.8822L14.7537 9.21044M19.9107 5.52396C17.0082 5.6781 14.1632 4.6745 11.9998 2.7334C9.83646 4.6745 6.9914 5.6781 4.08894 5.52396C3.85535 6.42825 3.73753 7.35853 3.73828 8.2925C3.73828 13.4247 7.24852 17.7382 11.9998 18.9609C16.7511 17.7382 20.2614 13.4257 20.2614 8.2925C20.2614 7.33599 20.1393 6.40887 19.9107 5.52396Z"
      stroke="#F4F4F4"
      strokeWidth="1.37692"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CopyIcon = (props: ComponentProps<"svg">) => (
  <svg
    width="17"
    height="18"
    viewBox="0 0 17 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.5137 4.75H6.14258C5.09734 4.75 4.25 5.59734 4.25 6.64258V14.0137C4.25 15.0589 5.09734 15.9062 6.14258 15.9062H13.5137C14.5589 15.9062 15.4062 15.0589 15.4062 14.0137V6.64258C15.4062 5.59734 14.5589 4.75 13.5137 4.75Z"
      stroke="#B5B5B5"
      strokeWidth="1.0625"
      strokeLinejoin="round"
    />
    <path
      d="M12.7334 4.75L12.75 3.95312C12.7486 3.46042 12.5522 2.98829 12.2039 2.6399C11.8555 2.2915 11.3833 2.09515 10.8906 2.09375H3.71875C3.15568 2.09541 2.61614 2.31983 2.21799 2.71799C1.81983 3.11614 1.59541 3.65568 1.59375 4.21875V11.3906C1.59515 11.8833 1.7915 12.3555 2.1399 12.7039C2.48829 13.0522 2.96042 13.2486 3.45312 13.25H4.25"
      stroke="#B5B5B5"
      strokeWidth="1.0625"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
