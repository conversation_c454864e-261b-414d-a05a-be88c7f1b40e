import {
  createAssociatedTokenAccountInstruction,
  createTransferInstruction,
  getAccount,
  getAssociatedTokenAddress,
} from "@solana/spl-token";
import {
  Commitment,
  Connection,
  LAMPORTS_PER_SOL,
  PublicKey,
  SystemProgram,
  Transaction,
} from "@solana/web3.js";
import axios from "axios";
import { ethers, JsonRpcProvider } from "ethers";
import { formatEther } from "viem";

import { env } from "@/env";
import {
  BACKEND_FRIENDS_CONTRACT,
  BACKEND_FRIENDS_CONTRACT_ABI,
  ERC20_CONTRACT_ABI,
} from "@/environments/BACKEND_FRIENDS_CONTRACT";

import { getOrCreateSolanaWallet } from "./client/chain";

const provider = new JsonRpcProvider(env.NEXT_PUBLIC_MAINNET_RPC_URL);
const contract = new ethers.Contract(
  BACKEND_FRIENDS_CONTRACT.addressMainnet,
  BACKEND_FRIENDS_CONTRACT_ABI,
  provider,
);

const SOLANA_RPC_NODE =
  "https://rpc.hellomoon.io/93c67b6d-228d-4520-bcee-74fd41c4fa3b";
export const SOLANA_SPL_TOKENS: {
  [token: string]: { publicKey: PublicKey; decimalsMultiplier: number };
} = {
  Bonk: {
    publicKey: new PublicKey("DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"),
    decimalsMultiplier: 100_000,
  },
  $WIF: {
    publicKey: new PublicKey("EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm"),
    decimalsMultiplier: 1_000_000,
  },
  USEDCAR: {
    publicKey: new PublicKey("9gwTegFJJErDpWJKjPfLr2g2zrE3nL1v5zpwbtsk3c6P"),
    decimalsMultiplier: 1_000_000_000,
  },
  Moutai: {
    publicKey: new PublicKey("45EgCwcPXYagBC7KqBin4nCFgEZWN7f3Y6nACwxqMCWX"),
    decimalsMultiplier: 1_000_000,
  },
  HARAMBE: {
    publicKey: new PublicKey("Fch1oixTPri8zxBnmdCEADoJW2toyFHxqDZacQkwdvSP"),
    decimalsMultiplier: 1_000_000_000,
  },
};

export const getBalance = async (address: string) => {
  return provider.getBalance(address);
};

export const getUserToSigner = async (address: string): Promise<string> => {
  const contract = await new ethers.Contract(
    BACKEND_FRIENDS_CONTRACT.addressMainnet,
    BACKEND_FRIENDS_CONTRACT_ABI,
    provider,
  );
  const signerAddress = await contract.userToSigner(address);
  return signerAddress;
};

export const fetchTokenBalance = async (
  address: string,
  currency: { symbol: string; contractAddress?: string },
): Promise<{ symbol: string; balance: bigint }> => {
  if (currency.symbol === "AVAX") {
    return { symbol: currency.symbol, balance: await getBalance(address) };
  } else if (currency.contractAddress) {
    const contract = new ethers.Contract(
      currency.contractAddress,
      ERC20_CONTRACT_ABI,
      provider,
    );
    return {
      symbol: currency.symbol,
      balance: await contract.balanceOf(address),
    };
  } else {
    return { symbol: currency.symbol, balance: BigInt(0) };
  }
};

export const getSolanaBalance = async (
  address: string,
): Promise<{
  SOL: number;
  splTokens: {
    Bonk?: number;
    $WIF?: number;
    USEDCAR?: number;
    Moutai?: number;
    HARAMBE?: number;
  };
}> => {
  // Create a new connection to the Solana blockchain
  const connection = new Connection(SOLANA_RPC_NODE, "confirmed");

  // Convert the provided Solana address into a PublicKey
  const publicKey = new PublicKey(address);

  // Get the balance
  const balance = await connection.getBalance(publicKey);

  // The balance is returned in lamports (the smallest unit of SOL), so you might want to convert it to SOL
  const SOL = balance / 1e9; // 1 SOL = 1,000,000,000 lamports

  const splTokens: any = {};
  const splTokensList = Object.keys(SOLANA_SPL_TOKENS);
  for (let i = 0, l = splTokensList.length; i < l; i++) {
    const splToken = splTokensList[i];
    const associatedTokenAddress = await getAssociatedTokenAddress(
      SOLANA_SPL_TOKENS[splToken].publicKey,
      new PublicKey(address),
    );

    try {
      const tokenAccountInfo = await getAccount(
        connection,
        associatedTokenAddress,
      );

      // The balance is returned in the smallest unit of the token, which is usually equivalent to lamports for SOL
      const tokenBalance = tokenAccountInfo.amount;
      const tokenBalanceNumber =
        parseInt(tokenBalance.toString()) /
        SOLANA_SPL_TOKENS[splToken].decimalsMultiplier;
      splTokens[splToken] = tokenBalanceNumber;
    } catch (e: unknown) {
      if (e instanceof Error && e.name === "TokenAccountNotFoundError") {
        // user doesn't have the splTokensList[i]
      } else {
        console.log(e);
      }
    }
  }

  return { SOL, splTokens };
};

export const depositSolana = async (
  provider: any,
  amount: number,
  solanaCurrency = "SOL",
  commitment: Commitment = "confirmed",
): Promise<any> => {
  const { response } = await getOrCreateSolanaWallet();
  const solanaAddress = response.solanaAddress;

  const providerConnection = await provider.connect();
  const { publicKey } = providerConnection;
  const connection = new Connection(SOLANA_RPC_NODE, commitment);
  const blockhash = await connection.getLatestBlockhash();

  let transaction = new Transaction();
  if (solanaCurrency === "SOL") {
    transaction.add(
      SystemProgram.transfer({
        fromPubkey: publicKey,
        toPubkey: new PublicKey(solanaAddress),
        lamports: LAMPORTS_PER_SOL * amount,
      }),
    );
  } else if (Object.keys(SOLANA_SPL_TOKENS).includes(solanaCurrency)) {
    const destinationAssociatedTokenAddress = await getAssociatedTokenAddress(
      SOLANA_SPL_TOKENS[solanaCurrency].publicKey,
      new PublicKey(solanaAddress),
    );
    const accountInfo = await connection.getAccountInfo(
      destinationAssociatedTokenAddress,
    );
    if (!accountInfo) {
      transaction.add(
        createAssociatedTokenAccountInstruction(
          publicKey,
          destinationAssociatedTokenAddress,
          new PublicKey(solanaAddress),
          SOLANA_SPL_TOKENS[solanaCurrency].publicKey,
        ),
      );
    }
    transaction.add(
      createTransferInstruction(
        await getAssociatedTokenAddress(
          SOLANA_SPL_TOKENS[solanaCurrency].publicKey,
          publicKey,
        ),
        destinationAssociatedTokenAddress,
        publicKey,
        SOLANA_SPL_TOKENS[solanaCurrency].decimalsMultiplier * amount,
      ),
    );
  } else {
    throw new Error("Invalid currency");
  }

  transaction.recentBlockhash = blockhash.blockhash;
  transaction.feePayer = publicKey;

  const resp = await provider.signAndSendTransaction(transaction);
  return resp;
};

export const getPrice = async (address: string, amount: bigint) => {
  // Uncomment when these methods are fixed in contract
  // return Promise.all([
  //   contract.getBuyPriceForFractionalSharesAfterFee(address, amount),
  //   contract.getSellPriceForFractionalSharesAfterFee(address, amount),
  // ]);

  const supplies = await Promise.all([
    contract.sharesSupply(address),
    contract.fractionalSharesSupply(address),
  ]);

  const totalSupply = supplies[0] * 100n + supplies[1];

  const buyPriceWithoutFee = await contract.getPriceForFractionalShares(
    totalSupply,
    amount,
  );

  const sellPriceWithoutFee =
    amount === 0n || amount > totalSupply
      ? 0n
      : await contract.getPriceForFractionalShares(
          totalSupply - amount,
          amount,
        );

  return [buyPriceWithoutFee, sellPriceWithoutFee];
};

export const getTokenPrice = async () => {
  try {
    const response = await axios.get(env.NEXT_COINGECKO_API);
    return response.data;
  } catch (error) {
    return null;
  }
};

export const getFeePercent = async (
  address: string,
): Promise<{
  subjectFee: number;
  protocolFee: number;
  referralFee: number;
  totalFee: number;
  rawTotalFee: bigint;
}> => {
  const fees = await Promise.all([
    contract.getSubjectFeePercent(address),
    contract.protocolFeePercent(),
    contract.referralFeePercent(),
  ]);

  const subjectFee = Number(formatEther(fees[0] * 100n));
  const protocolFee = Number(formatEther(fees[1] * 100n));
  const referralFee = Number(formatEther(fees[2] * 100n));
  const rawTotalFee = fees[0] + fees[1] + fees[2];
  const totalFee = Number(formatEther(rawTotalFee * 100n));

  return { subjectFee, protocolFee, referralFee, totalFee, rawTotalFee };
};
