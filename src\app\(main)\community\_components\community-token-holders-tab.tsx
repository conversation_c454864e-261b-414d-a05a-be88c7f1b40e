"use client";

import { useMemo } from "react";

import { Virtuoso } from "react-virtuoso";
import { Address, formatEther } from "viem";

import { useCommunityByIdQuery } from "@/queries";
import { useAvaxPriceQuery } from "@/queries/currency-queries";
import {
  useCommunityStatsQuery,
  useTokenHoldersQuery,
} from "@/queries/groups-queries";

import { CommunityListItemLoadingSkeleton } from "./community-list-item-loading-skeleton";
import { TokenHolderItem } from "./community-token-holder-item";

interface TokenHoldersTabProps {
  communityId: string;
  feeWalletAddress: Address | null;
}

export const TokenHoldersTab = ({
  communityId,
  feeWalletAddress,
}: TokenHoldersTabProps) => {
  const { data } = useCommunityByIdQuery(communityId);
  const { data: statsData } = useCommunityStatsQuery(
    data?.community.contractAddress || "",
  );
  const {
    data: holdersData,
    isLoading: isLoadingHolders,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useTokenHoldersQuery(data?.community.contractAddress || "");
  const { data: avaxPrice, isLoading: isFetchingAvaxPrice } =
    useAvaxPriceQuery();

  const usdOfToken =
    Number(formatEther(BigInt(statsData?.stats?.price ?? "0"))) *
    (avaxPrice?.avax || 0);

  const holders = useMemo(() => {
    if (!holdersData) return [];
    return holdersData.pages.flatMap((page) => page.holders);
  }, [holdersData]);

  if (isLoadingHolders || isFetchingAvaxPrice || !feeWalletAddress) {
    return (
      <>
        {Array.from({ length: 10 }).map((_, i) => (
          <CommunityListItemLoadingSkeleton key={i} />
        ))}
      </>
    );
  }

  if (!isLoadingHolders && holders.length === 0) {
    return (
      <div className="my-auto mt-8 text-center">
        <p className="mb-2 text-sm text-off-white">No holders yet!</p>
      </div>
    );
  }

  return (
    <Virtuoso
      useWindowScroll
      data={holders}
      overscan={200}
      itemContent={(_, holder) => (
        <TokenHolderItem
          holder={holder}
          ownerAddress={
            data?.community?.isOwnerExternal
              ? null
              : data?.community?.owner?.address || ""
          }
          usdPrice={usdOfToken || 0}
          lpPairAddress={data?.community?.pairAddress || ""}
          feeWalletAddress={feeWalletAddress}
        />
      )}
      endReached={() => {
        if (hasNextPage) fetchNextPage();
      }}
      components={{
        Footer: () => {
          if (isLoadingHolders || isFetchingNextPage) {
            return (
              <>
                {Array.from({ length: 5 }).map((_, i) => (
                  <CommunityListItemLoadingSkeleton key={i} />
                ))}
              </>
            );
          }
          return null;
        },
      }}
    />
  );
};
