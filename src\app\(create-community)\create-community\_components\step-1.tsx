import { useEffect } from "react";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

import { getCommunityNameCheck } from "@/api/client/group";
import {
  HANDLE_PHASE_MARKET_CAP_THRESHOLD,
  MAX_NAME_LENGTH,
  MAX_TICKER_LENGTH,
} from "@/app/(main)/community/_components/consts";
import { Button } from "@/components/ui/button";
import { TextInput } from "@/components/ui/text-input";
import { abbreviateNumber } from "@/utils";

import { CommunityPicture } from "./community-picture";
import {
  CreateCommunityFormState,
  Step1FormInput,
  Step1FormInputType,
} from "./create-community-form-input";

interface Props {
  onNext: () => void;
  onUpdateState: (params: Partial<CreateCommunityFormState>) => void;
  formState: CreateCommunityFormState;
}

export const CreateCommunityStep1 = ({
  onNext: handleNext,
  onUpdateState: handleUpdateState,
  formState,
}: Props) => {
  const { primaryWallet } = useDynamicContext();

  const form = useForm<Step1FormInputType>({
    resolver: zodResolver(Step1FormInput),
    mode: "onChange",
    reValidateMode: "onChange",
    shouldFocusError: true,
  });

  const validateCommunityName = async (name: string) => {
    const nameAvailable = await getCommunityNameCheck({ name });
    if (!nameAvailable.isAvailable) {
      form.setError("name", {
        type: "manual",
        message: "Profile page handle taken. Please try another one.",
      });
    }
  };

  useEffect(() => {
    form.setValue("name", formState.name);
    form.setValue("ticker", formState.ticker);
    form.setValue("photoURL", formState.photoURL);
    form.setValue("tokenName", formState.tokenName);
  }, []);

  const handleSubmit = async (data: any) => {
    handleUpdateState(data);
    handleNext();
  };

  const handlePictureUpload = (pictureUrl: string) => {
    form.setValue("photoURL", pictureUrl, { shouldValidate: true });
  };

  const handleNameChange = async (value: string) => {
    const name = value.trim();
    const isBasicValidationPass = await form.trigger("name");
    if (isBasicValidationPass) {
      validateCommunityName(name);
    }
  };

  return (
    <>
      <div>
        <div className="mb-4 inline-flex rounded-[20px] bg-brand-orange">
          <p className="px-2">Step 1/2</p>
        </div>
        <p className="mb-4 text-xl font-semibold text-off-white">
          Basic information
        </p>
        <p className="mb-4 text-sm text-off-white">
          Provide some information for the token you wish to create!
        </p>
      </div>
      <div>
        <CommunityPicture
          initialPictureUrl={formState.photoURL}
          onPictureUpload={handlePictureUpload}
        />
        {form.formState.errors.photoURL && (
          <p className="mb-4 text-xs text-danger">
            Please set up token profile picture
          </p>
        )}
      </div>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="flex flex-1 flex-col gap-6"
      >
        <div className="relative flex flex-col gap-2">
          <label className="text-xs text-[#f3f3f3]">CHOOSE A TICKER</label>
          <TextInput
            placeholder="$TOKEN"
            maxLength={MAX_TICKER_LENGTH}
            {...form.register("ticker", {
              onChange: (e) => {
                e.target.value = e.target.value.toUpperCase();
              },
            })}
            errorMessage={form.formState.errors.ticker?.message}
          />
          <div className="absolute right-8 top-10 text-[#808080]">
            {MAX_TICKER_LENGTH - (form.watch("ticker") ?? "").length}
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <label className="text-xs text-[#f3f3f3]">CHOOSE A NAME</label>
          <div className="relative flex flex-col gap-1">
            <TextInput
              placeholder="token name"
              maxLength={MAX_NAME_LENGTH}
              {...form.register("tokenName")}
              errorMessage={form.formState.errors.tokenName?.message}
            />
            <div className="absolute right-8 top-4 text-[#808080]">
              {MAX_NAME_LENGTH - (form.watch("tokenName") ?? "").length}
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <label className="text-xs text-[#f3f3f3]">CHOOSE A HANDLE *</label>
          <div className="relative flex flex-col gap-1">
            <TextInput
              placeholder="profile_page_handle"
              maxLength={MAX_NAME_LENGTH}
              {...form.register("name", {
                onChange: (e) => {
                  const value = e.target.value;
                  handleNameChange(value);
                },
              })}
              errorMessage={form.formState.errors.name?.message}
            />
            <div className="pl-1 text-xs text-[#808080]">
              {`* Handles are granted upon reaching $${abbreviateNumber(HANDLE_PHASE_MARKET_CAP_THRESHOLD)} market cap`}
            </div>
            <div className="absolute right-8 top-4 text-[#808080]">
              {MAX_NAME_LENGTH - (form.watch("name") ?? "").length}
            </div>
          </div>
        </div>
        <div className="mt-auto flex flex-col gap-4 pb-4">
          <Button
            className="w-full"
            type="submit"
            disabled={
              !primaryWallet ||
              !isEthereumWallet(primaryWallet) ||
              Object.keys(form.formState.errors).length > 0
            }
          >
            Continue
          </Button>
        </div>
      </form>
    </>
  );
};
