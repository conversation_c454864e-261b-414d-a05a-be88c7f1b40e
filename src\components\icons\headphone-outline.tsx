import { ComponentProps } from "react";

export const HeadphoneOutlineIcon = (props: ComponentProps<"svg">) => (
  <svg
    viewBox="0 0 16 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      id="Vector"
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8 2.33268C4.76963 2.33268 2.16667 4.92022 2.16667 8.09279V10.1217C2.54269 9.9369 2.96541 9.83268 3.41667 9.83268C5.06534 9.83268 6.33333 11.2239 6.33333 12.8536V14.3118C6.33333 15.9415 5.06534 17.3327 3.41667 17.3327C1.76799 17.3327 0.5 15.9415 0.5 14.3118V13.9993V12.8536V8.09279C0.5 3.98243 3.86657 0.666016 8 0.666016C12.1334 0.666016 15.5 3.98243 15.5 8.09279V12.8519V12.8536V13.9993V14.3118C15.5 15.9415 14.232 17.3327 12.5833 17.3327C10.9347 17.3327 9.66667 15.9415 9.66667 14.3118V12.8536C9.66667 11.2239 10.9347 9.83268 12.5833 9.83268C13.0346 9.83268 13.4573 9.9369 13.8333 10.1217V8.09279C13.8333 4.92022 11.2303 2.33268 8 2.33268ZM13.8333 12.8523C13.8328 12.0663 13.2356 11.4993 12.5833 11.4993C11.9308 11.4993 11.3333 12.0668 11.3333 12.8536V14.3118C11.3333 15.0986 11.9308 15.666 12.5833 15.666C13.2359 15.666 13.8333 15.0986 13.8333 14.3118V13.9993V12.8536V12.8523ZM2.16667 13.9993V12.8536C2.16667 12.0668 2.76416 11.4993 3.41667 11.4993C4.06918 11.4993 4.66667 12.0668 4.66667 12.8536V14.3118C4.66667 15.0986 4.06918 15.666 3.41667 15.666C2.76416 15.666 2.16667 15.0986 2.16667 14.3118V13.9993Z"
      fill="currentColor"
    />
  </svg>
);
