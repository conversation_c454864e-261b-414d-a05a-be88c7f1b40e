export type LoginButtonProps = React.ComponentProps<"button"> & {
  children: React.ReactNode;
  badge?: React.ReactNode;
};

export const LoginButton: React.FC<LoginButtonProps> = ({
  children,
  badge,
  ...props
}): React.ReactNode => {
  return (
    <button
      className="relative h-11 w-[295px] rounded-full bg-[linear-gradient(97deg,#F6F6F6_-0.98%,#E4E4E4_-0.97%,#C1C1C1_84.13%)] px-10 text-[#1B1B1B] shadow-[-1px_2px_20px_0px_rgba(0,0,0,0.14),_0px_0px_4px_0px_rgba(0,0,0,0.12)]"
      {...props}
    >
      <p className="font-inter text-1b text-lg font-semibold leading-5">
        {children}
      </p>
      {badge && (
        <div className="absolute left-6 top-1/2 -translate-y-1/2 transform">
          {badge}
        </div>
      )}
    </button>
  );
};
