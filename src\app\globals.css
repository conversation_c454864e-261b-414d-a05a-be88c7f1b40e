@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Infer";
  src: url("/fonts/Inter-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Infer";
  src: url("/fonts/Inter-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "Infer";
  src: url("/fonts/Inter-SemiBold.ttf") format("truetype");
  font-weight: 600;
  font-style: normal;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  --sat: env(safe-area-inset-top);
  --sar: env(safe-area-inset-right);
  --sab: env(safe-area-inset-bottom);
  --sal: env(safe-area-inset-left);
}

@layer utilities {
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield; /* Firefox */
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

html {
  min-height: calc(100% + env(safe-area-inset-top));
}

body {
  @apply bg-dark-bk text-white;
  scrollbar-width: thin;
  scrollbar-color: #2e2e2e #1a1a1a;
}

body::-webkit-scrollbar {
  width: 12px;
}

body::-webkit-scrollbar-track {
  background: #1a1a1a;
}

body::-webkit-scrollbar-thumb {
  background-color: #2e2e2e;
  border-radius: 10px;
  border: 3px solid #1a1a1a;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .disable-select {
    user-select: none;
    -moz-user-select: none;
    -webkit-user-drag: none;
  }

  .undo-disable-select {
    user-select: auto;
    -moz-user-select: auto;
    -webkit-user-drag: auto;
  }

  .bottom-pwa {
    bottom: env(safe-area-inset-bottom);
  }

  .top-pwa {
    top: env(safe-area-inset-top);
  }

  .tbottom-pwa {
    top: env(safe-area-inset-bottom);
  }

  .tbottom-stage-pwa {
    top: calc(env(safe-area-inset-bottom) + 165px);
  }

  .left-pwa {
    left: env(safe-area-inset-left);
  }

  .right-pwa {
    right: env(safe-area-inset-right);
  }

  .pb-pwa {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .pt-pwa {
    padding-top: env(safe-area-inset-top);
  }

  .pl-pwa {
    padding-left: env(safe-area-inset-left);
  }

  .pr-pwa {
    padding-right: env(safe-area-inset-right);
  }
}

.scroll-behavior-y-none {
  overscroll-behavior-y: none;
}

.post-content {
  word-break: break-word;
}

.post-content a {
  color: #ea540a;
}

.post-content p:not(:first-child) {
  margin-top: 1rem;
}

.hide-scrollbar {
  scrollbar-width: none; /* For Firefox */
  -ms-overflow-style: none; /* For Internet Explorer and Edge */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* For Chrome, Safari and Opera */
}

/* React Loading Skeleton */
@keyframes react-loading-skeleton {
  100% {
    transform: translateX(100%);
  }
}

.react-loading-skeleton {
  --base-color: #ebebeb;
  --highlight-color: #f5f5f5;
  --animation-duration: 1.5s;
  --animation-direction: normal;
  --pseudo-element-display: block; /* Enable animation */

  background-color: var(--base-color);

  border-radius: 0.25rem;
  display: inline-flex;
  line-height: 1;

  position: relative;
  user-select: none;
  overflow: hidden;
}

.react-loading-skeleton::after {
  content: " ";
  display: var(--pseudo-element-display);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background-repeat: no-repeat;
  background-image: linear-gradient(
    90deg,
    var(--base-color),
    var(--highlight-color),
    var(--base-color)
  );
  transform: translateX(-100%);

  animation-name: react-loading-skeleton;
  animation-direction: var(--animation-direction);
  animation-duration: var(--animation-duration);
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
}

@media (prefers-reduced-motion) {
  .react-loading-skeleton {
    --pseudo-element-display: none; /* Disable animation */
  }
}

.tiptap p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
  @apply text-gray-text;
}

.uprising-gradient {
  background-image: linear-gradient(210deg, #2c1637, #000000);
}

.overflow-hidden-container {
  overflow-y: hidden;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.overflow-hidden-container::-webkit-scrollbar {
  display: none;
}

.reactionMask {
  mask-image: linear-gradient(to top, black, transparent);
  -webkit-mask-image: linear-gradient(to top, black, transparent);
}

.reaction {
  position: absolute;
  bottom: 0;
  font-size: 2rem;
  animation: float-up 4s ease-out forwards;
  opacity: 0;
}

.stage-chat-mask {
  mask-image: linear-gradient(to bottom, transparent, black 30px);
}

.message-tag {
  text-decoration-line: none !important;
  font-weight: bold;
}

input[type="time"].time-input {
  position: relative;
  text-align: left;

  &::-webkit-calendar-picker-indicator {
    filter: invert(1);
    cursor: pointer;
    width: 24px;
    height: 24px;
    position: absolute;
    top: 10px;
    left: 4px;
  }
}

input[type="date"].time-input {
  position: relative;
  text-align: left;

  &::-webkit-calendar-picker-indicator {
    filter: invert(1);
    cursor: pointer;
    width: 24px;
    height: 24px;
    position: absolute;
    top: 10px;
    left: 13px;
  }
}

input[type="time"].time-input::-moz-calendar-picker-indicator,
input[type="date"].time-input::-moz-calendar-picker-indicator {
  filter: invert(1);
  cursor: pointer;
}

input[type="date"].time-input-mobile::-webkit-inner-spin-button,
input[type="date"].time-input-mobile::-webkit-outer-spin-button,
input[type="time"].time-input-mobile::-webkit-inner-spin-button,
input[type="time"].time-input-mobile::-webkit-outer-spin-button {
  -webkit-appearance: none;
}

input[type="date"].time-input-mobile::-webkit-calendar-picker-indicator,
input[type="time"].time-input-mobile::-webkit-calendar-picker-indicator {
  display: none;
}

/* For Firefox */
input[type="date"].time-input-mobile,
input[type="time"].time-input-mobile {
  appearance: textfield;
  -moz-appearance: textfield;
}

/* General reset for custom styling */
input[type="date"].time-input-mobile,
input[type="time"].time-input-mobile {
  appearance: none;
  -webkit-appearance: none;
}

.mask-top-to-bottom {
  mask-image: linear-gradient(to bottom, transparent, black);
}

@keyframes float-up {
  0% {
    transform: translateY(0) translateX(var(--tx)) rotate(var(--rotate))
      scale(0.5);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(-150px) translateX(var(--tx)) rotate(var(--rotate))
      scale(1.5);
    opacity: 0;
  }
}

.clip-card-image {
  clip-path: polygon(25% 0, 100% 0, 100% 100%, 0% 100%);
}

.mask-card-image {
  mask-image: linear-gradient(
    90deg,
    rgba(217, 217, 217, 0) -3.48%,
    rgba(171, 171, 171, 0.6) 60%,
    #737373 99.98%
  );
}
