"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

import { InfiniteData, useQueryClient } from "@tanstack/react-query";
import Skeleton from "react-loading-skeleton";
import { useLongPress } from "use-long-press";

import { leaveChat } from "@/api/client/chat";
import {
  EllipsisHorizontalFilledIcon,
  PinOutlineIcon,
  UnpinOutlineIcon,
} from "@/components/icons";
import { GroupIcon, OfficialGroupIcon } from "@/components/icons-v2/group-logo";
import { DMRequestDeleteIcon } from "@/components/icons/dm-request-delete-outline";
import { SuspendedUserChatPreviewOutlineIcon } from "@/components/icons/suspended-user";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { usePinConversation, useUserByIdQuery } from "@/queries";
import { Group, GroupsResponse } from "@/queries/types/chats";
import { useUser } from "@/stores";
import { UserFlaggedEnum } from "@/types";
import { CommunityExtended } from "@/types/community";
import { cn, formatTimeDistance } from "@/utils";

interface MessageItemProps {
  group: Group;
  isSelected: boolean;
  typingUser?: string;
  setSeen: () => void;
  selectGroup: () => void;
  isConversationOpen: boolean;
  isCommunity?: boolean;
  community?: CommunityExtended;
  search: string;
  isProjectChat?: boolean;
}

export const MessageItem = ({
  group,
  isSelected,
  typingUser,
  selectGroup,
  isConversationOpen,
  search,
  isCommunity,
  community,
  isProjectChat,
}: MessageItemProps) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { user } = useUser();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const owner = useUserByIdQuery(group?.ownerUserId);

  const { mutateAsync: pinConversation } = usePinConversation({
    onMutate: async ({ groupId, isPinned }) => {
      const previousData = queryClient.getQueryData(["chat", "conversations"]);
      const previousSearchData = queryClient.getQueryData([
        "chat",
        "search-room-conversations",
        search,
      ]);

      queryClient.setQueryData(
        ["chat", "search-room-conversations", search],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === groupId) {
                    return {
                      ...g,
                      memberLink: {
                        ...g.memberLink,
                        isPinned,
                      },
                    };
                  }
                  return g;
                }),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["chat", "conversations"],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === groupId) {
                    return {
                      ...g,
                      memberLink: {
                        ...g.memberLink,
                        isPinned,
                      },
                    };
                  }
                  return g;
                }),
              };
            }),
          };
        },
      );
      return { previousData, previousSearchData };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(["chat", "conversations"], context.previousData);
      queryClient.setQueryData(
        ["chat", "search-room-conversations", search],
        context.previousSearchData,
      );
    },
  });

  const { mutateAsync: pinProjectConversation } = usePinConversation({
    onMutate: async ({ groupId, isPinned }) => {
      const previousData = queryClient.getQueryData([
        "chat",
        "group-conversations",
      ]);
      const previousSearchData = queryClient.getQueryData([
        "chat",
        "search-project-chats",
        search,
      ]);

      queryClient.setQueryData(
        ["chat", "search-project-chats", search],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === groupId) {
                    return {
                      ...g,
                      memberLink: {
                        ...g.memberLink,
                        isPinned,
                      },
                    };
                  }
                  return g;
                }),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["chat", "group-conversations"],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                communities: page.groups.map((g) => {
                  if (g.id === groupId) {
                    return {
                      ...g,
                      memberLink: {
                        ...g.memberLink,
                        isPinned,
                      },
                    };
                  }
                  return g;
                }),
              };
            }),
          };
        },
      );
      return { previousData, previousSearchData };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(
        ["chat", "group-conversations"],
        context.previousData,
      );
      queryClient.setQueryData(
        ["chat", "search-project-chats", search],
        context.previousSearchData,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["chat", "group-conversations"],
      });
      queryClient.invalidateQueries({
        queryKey: ["chat", "search-project-chats", search],
      });
    },
  });

  const bind = useLongPress(selectGroup, {
    onCancel: () => {
      if (isSelected) return;
    },
    threshold: 700,
  });
  const isLastMessageMine = group.lastUserId === user?.id;

  const lastMessageDate = group.lastMessageOn
    ? new Date(+group.lastMessageOn)
    : null;
  const lastSeenDate = group?.memberLink?.lastSeen
    ? new Date(+group?.memberLink?.lastSeen)
    : null;

  const isNotSeen = isLastMessageMine
    ? false
    : lastMessageDate && lastSeenDate
      ? lastMessageDate > lastSeenDate
      : false;

  const lastName = isLastMessageMine ? "You" : group.lastName;
  const _message = (group.lastMessage || "").replace(/<br>/g, " ");
  const parser = new DOMParser();
  const html = parser.parseFromString(_message, "text/html");
  const lastMessage = html.body.textContent;
  const getAvatarImageSrc = (groupName: string) => {
    if (groupName === "OG Badge Arena") {
      return "/assets/badges/badge-type-1.png";
    }
    if (groupName === "Dokyo Badge Arena") {
      return "/assets/badges/badge-type-3.png";
    }
    if (groupName === "DeGods Badge Arena") {
      return "/assets/badges/badge-type-2.png";
    }
    if (groupName === "SappySeals Badge Arena") {
      return "/assets/badges/badge-type-4.png";
    }
    if (groupName === "Steady Badge Arena") {
      return "/assets/badges/badge-type-8.png";
    }
    if (groupName === "Gurs Badge Arena") {
      return "/assets/coins/gurs.png";
    }
    return group.profilePictureUrl;
  };
  return (
    <div
      className={cn(
        "group relative flex cursor-pointer select-none items-center gap-[10px] overflow-hidden rounded-[10px] py-4 pl-6 pr-2 transition-colors",
        (group.memberLink?.isPinned || isSelected) &&
          "bg-[#212121] bg-opacity-[0.88]",
        isConversationOpen
          ? "bg-[#111]"
          : "hover:bg-[#212121] hover:bg-opacity-[0.2]",
      )}
      {...(isTablet ? {} : bind())}
      onClick={() => {
        router.push(
          `/messages/${isCommunity ? `community/${group.communityId}` : `${group.id}`}`,
        );
      }}
    >
      {owner?.data?.user?.flag === UserFlaggedEnum.SUSPENDED ? (
        <SuspendedUserChatPreviewOutlineIcon />
      ) : (
        <Avatar className="size-[42px]">
          <AvatarImage src={getAvatarImageSrc(group.name)} />
          <AvatarFallback />
        </Avatar>
      )}
      <div className="flex-grow overflow-hidden">
        <div className="flex items-center gap-2">
          <h4 className="justfiy-center flex items-center gap-1.5 truncate text-sm font-semibold leading-4 text-off-white">
            {isCommunity &&
              (community?.isOfficial ? <OfficialGroupIcon /> : <GroupIcon />)}
            {isCommunity ? `$${community?.ticker}` : group.name}
          </h4>
          <div className="hidden whitespace-nowrap text-xs text-gray-text sm:block">
            {group.lastMessageOn ? (
              <MessageTime time={group.lastMessageOn} />
            ) : (
              ""
            )}
          </div>
        </div>
        <p className="mt-1 truncate text-sm leading-4 text-gray-text">
          {owner?.data?.user?.flag === UserFlaggedEnum.SUSPENDED ? (
            <span>Account suspended.</span>
          ) : typingUser ? (
            <span>{typingUser} is typing...</span>
          ) : !lastName && !lastMessage ? null : (
            <>
              <span>{lastName}: </span>
              {lastMessage}
            </>
          )}
        </p>
      </div>
      <div className="ml-2 flex w-12 flex-shrink-0 flex-col items-end gap-1 text-gray-text sm:flex-row sm:items-center sm:justify-end">
        <div className="whitespace-nowrap text-xs sm:hidden">
          {group.lastMessageOn ? (
            <MessageTime time={group.lastMessageOn} />
          ) : (
            ""
          )}
        </div>
        {group.memberLink?.isPinned && (
          <div className="flex-shrink-0">
            <PinOutlineIcon className="size-4" />
          </div>
        )}
        {isTablet && (
          <DropdownMenu>
            <DropdownMenuTrigger
              className="flex size-8 flex-shrink-0 items-center justify-center opacity-0 outline-none transition-opacity group-hover:opacity-100"
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <EllipsisHorizontalFilledIcon className="size-5  fill-off-white" />
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className=""
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              {group.memberLink?.isPinned && (
                <DropdownMenuItem
                  onSelect={async () => {
                    if (isProjectChat) {
                      await pinProjectConversation({
                        groupId: group.id,
                        isPinned: false,
                      });
                    } else {
                      await pinConversation({
                        groupId: group.id,
                        isPinned: false,
                      });
                    }
                  }}
                  className="flex flex-shrink-0 items-center gap-4"
                >
                  <UnpinOutlineIcon className="size-[18px] flex-shrink-0 text-gray-text" />
                  <div className="text-sm font-semibold text-off-white">
                    Unpin
                  </div>
                </DropdownMenuItem>
              )}
              {!group.memberLink?.isPinned && (
                <DropdownMenuItem
                  onSelect={async () => {
                    if (isProjectChat) {
                      await pinProjectConversation({
                        groupId: group.id,
                        isPinned: true,
                      });
                    } else {
                      await pinConversation({
                        groupId: group.id,
                        isPinned: true,
                      });
                    }
                  }}
                  className="flex items-center gap-4"
                >
                  <PinOutlineIcon className="size-[18px] flex-shrink-0 text-gray-text" />
                  <div className="text-sm font-semibold text-off-white">
                    Pin
                  </div>
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
      {isNotSeen && (
        <div className="absolute left-[10px] top-1/2 h-1 w-1 -translate-y-1/2 rounded-full bg-brand-orange" />
      )}
      {isConversationOpen && (
        <div className="absolute inset-y-0 right-0 w-1.5 bg-brand-orange" />
      )}
    </div>
  );
};

export const MessageItemLoadingSkeleton = () => {
  return (
    <div className="relative flex cursor-pointer select-none items-center gap-[10px] overflow-hidden rounded-[10px] py-4 pl-6 pr-2 transition-colors">
      <div className="flex items-center gap-[10px] leading-none">
        <Skeleton circle className="size-[42px]" />
        <div className="flex w-full flex-col gap-1 leading-4">
          <Skeleton className="mt-1 h-[14px] w-28" />
          <Skeleton className="h-[12px] w-20" />
        </div>
      </div>
    </div>
  );
};

const MessageTime = ({ time }: { time: number | string | Date }) => {
  const [timeDistance, setTimeDistance] = useState(() => {
    return formatTimeDistance(time);
  });

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeDistance(formatTimeDistance(time));
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  });

  return timeDistance;
};

export const DirectMessageItem = ({
  group,
  isSelected,
  typingUser,
  selectGroup,
  isConversationOpen,
  search,
  isCommunity = false,
}: MessageItemProps) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { user } = useUser();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const chatMate = useUserByIdQuery(group?.chatMateId || "");
  const owner = useUserByIdQuery(group?.ownerUserId);

  const { mutateAsync: pinConversation } = usePinConversation({
    onMutate: async ({ groupId, isPinned }) => {
      const previousData = queryClient.getQueryData([
        "chat",
        "direct-messages",
      ]);
      const previousSearchData = queryClient.getQueryData([
        "chat",
        "search-dm-conversations",
        search,
      ]);

      queryClient.setQueryData(
        ["chat", "search-dm-conversations", search],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === groupId) {
                    return {
                      ...g,
                      memberLink: {
                        ...g.memberLink,
                        isPinned,
                      },
                    };
                  }
                  return g;
                }),
              };
            }),
          };
        },
      );
      queryClient.setQueryData(
        ["chat", "direct-messages"],
        (oldData: InfiniteData<GroupsResponse>) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => {
              return {
                ...page,
                groups: page.groups.map((g) => {
                  if (g.id === groupId) {
                    return {
                      ...g,
                      memberLink: {
                        ...g.memberLink,
                        isPinned,
                      },
                    };
                  }
                  return g;
                }),
              };
            }),
          };
        },
      );
      return { previousData, previousSearchData };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(
        ["chat", "direct-messages"],
        context.previousData,
      );
      queryClient.setQueryData(
        ["chat", "search-dm-conversations", search],
        context.previousSearchData,
      );
    },
  });

  const bind = useLongPress(selectGroup, {
    onCancel: () => {
      if (isSelected) return;
    },
    threshold: 700,
  });
  const isLastMessageMine = group.lastUserId === user?.id;

  const lastMessageDate = group.lastMessageOn
    ? new Date(+group.lastMessageOn)
    : null;
  const lastSeenDate = group?.memberLink?.lastSeen
    ? new Date(+group?.memberLink?.lastSeen)
    : null;

  const isNotSeen = isLastMessageMine
    ? false
    : lastMessageDate && lastSeenDate
      ? lastMessageDate > lastSeenDate
      : false;

  const lastName = isLastMessageMine ? "You" : "";
  const _message = (group.lastMessage || "").replace(/<br>/g, " ");
  const parser = new DOMParser();
  const html = parser.parseFromString(_message, "text/html");
  const lastMessage = html.body.textContent;

  let updatedGroup: Group;
  let isSuspended = false;
  if (!user?.id) {
    updatedGroup = group;
  } else if (group?.ownerUserId === user?.id) {
    const name = group?.chatMateName || "";
    isSuspended = chatMate?.data?.user?.flag === UserFlaggedEnum.SUSPENDED;
    const profilePictureUrl = chatMate?.data?.user?.twitterPicture || "";
    updatedGroup = { ...group, name, profilePictureUrl };
  } else {
    updatedGroup = group;
    isSuspended = owner?.data?.user?.flag === UserFlaggedEnum.SUSPENDED;
  }
  group = updatedGroup;

  return (
    <div
      className={cn(
        "group relative flex cursor-pointer select-none items-center gap-[10px] overflow-hidden rounded-[10px] py-4 pl-6 pr-2 transition-colors",
        (group.memberLink?.isPinned || isSelected) &&
          "bg-[#212121] bg-opacity-[0.88]",
        isConversationOpen
          ? "bg-[#111]"
          : "hover:bg-[#212121] hover:bg-opacity-[0.2]",
      )}
      {...(isTablet ? {} : bind())}
      onClick={() => {
        router.push(`/messages/${group.id}`);
      }}
    >
      {isSuspended ? (
        <SuspendedUserChatPreviewOutlineIcon />
      ) : (
        <Avatar className="size-[42px]">
          <AvatarImage src={group.profilePictureUrl} />
          <AvatarFallback />
        </Avatar>
      )}
      <div className="flex flex-grow items-center justify-between overflow-hidden">
        <div>
          <div className="flex w-[200px] items-center gap-2">
            <h4 className="truncate text-sm font-semibold leading-4 text-off-white">
              {isSuspended ? "Suspended Account" : group.name}
            </h4>
            <div className="mr-4 whitespace-nowrap text-xs text-gray-text sm:block">
              <MessageTime time={group.lastMessageOn} />
            </div>
          </div>
          <p className="mt-1 w-[200px] truncate text-sm leading-4 text-gray-text">
            {typingUser ? (
              <span>typing...</span>
            ) : !lastMessage ? null : lastName ? (
              <>
                <span>{lastName}: </span>
                {lastMessage}
              </>
            ) : (
              <>{lastMessage}</>
            )}
          </p>
        </div>
      </div>
      {group.memberLink?.isPinned && (
        <div className="flex-shrink-0">
          <PinOutlineIcon className="right-1 size-4 opacity-50" />
        </div>
      )}
      {isTablet && (
        <DropdownMenu>
          <DropdownMenuTrigger
            className="flex size-8 flex-shrink-0 items-center justify-center opacity-0 outline-none transition-opacity group-hover:opacity-100"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <EllipsisHorizontalFilledIcon className="size-5  fill-off-white" />
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className=""
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            {group.memberLink?.isPinned && (
              <DropdownMenuItem
                onSelect={async () => {
                  await pinConversation({
                    groupId: group.id,
                    isPinned: false,
                  });
                }}
                className="flex flex-shrink-0 items-center gap-4"
              >
                <UnpinOutlineIcon className="size-[18px] flex-shrink-0 text-gray-text" />
                <div className="text-sm font-semibold text-off-white">
                  Unpin
                </div>
              </DropdownMenuItem>
            )}
            {!group.memberLink?.isPinned && (
              <DropdownMenuItem
                onSelect={async () => {
                  await pinConversation({
                    groupId: group.id,
                    isPinned: true,
                  });
                }}
                className="flex items-center gap-4"
              >
                <PinOutlineIcon className="size-[18px] flex-shrink-0 text-gray-text" />
                <div className="text-sm font-semibold text-off-white">Pin</div>
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      {isNotSeen && (
        <div className="absolute left-[10px] top-1/2 h-1 w-1 -translate-y-1/2 rounded-full bg-brand-orange" />
      )}
      {isConversationOpen && (
        <div className="absolute inset-y-0 right-0 w-1.5 bg-brand-orange" />
      )}
    </div>
  );
};

export const DirectMessageRequestItem = ({
  group,
  isSelected,
  typingUser,
  selectGroup,
  isConversationOpen,
}: MessageItemProps) => {
  const router = useRouter();
  const { user } = useUser();
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const chatMate = useUserByIdQuery(group?.chatMateId || "");
  const owner = useUserByIdQuery(group?.ownerUserId || "");

  const handleDeleteGroup = () => leaveChat({ groupId: group?.id || "" });

  const bind = useLongPress(selectGroup, {
    onCancel: () => {
      if (isSelected) return;
    },
    threshold: 700,
  });

  let updatedGroup: Group;
  const userHandle =
    group?.ownerUserId === user?.id
      ? chatMate?.data?.user?.twitterHandle
      : owner?.data?.user?.twitterHandle;
  if (!user?.id) {
    updatedGroup = group;
  } else if (group?.ownerUserId === user?.id) {
    const name = group?.chatMateName || "";
    const profilePictureUrl = chatMate?.data?.user?.twitterPicture || "";
    updatedGroup = { ...group, name, profilePictureUrl };
  } else {
    updatedGroup = group;
  }
  group = updatedGroup;

  const _message = (group.lastMessage || "").replace(/<br>/g, " ");
  const parser = new DOMParser();
  const html = parser.parseFromString(_message, "text/html");
  const lastMessage = html.body.textContent;

  return (
    <div
      className={cn(
        "group relative flex cursor-pointer select-none items-center gap-[10px] overflow-hidden rounded-[10px] py-4 pl-6 pr-2 transition-colors",
        (group.memberLink?.isPinned || isSelected) &&
          "bg-[#212121] bg-opacity-[0.88]",
        isConversationOpen
          ? "bg-[#111]"
          : "hover:bg-[#212121] hover:bg-opacity-[0.2]",
      )}
      {...(isTablet ? {} : bind())}
      onClick={() => {
        router.push(`/messages/${group.id}`);
      }}
    >
      <Avatar className="size-[42px]">
        <AvatarImage src={group.profilePictureUrl} />
        <AvatarFallback />
      </Avatar>
      <div className="flex flex-grow items-center justify-between overflow-hidden">
        <div>
          <div className="flex w-[260px] items-center gap-1">
            <h4 className="truncate text-sm font-semibold leading-4 text-off-white">
              {group.name}
            </h4>
            <div className="mb-2 text-sm text-gray-text">
              <span className="self-center">.</span>
            </div>
            <div className="mr-4 flex truncate text-sm text-gray-text sm:block">
              @{userHandle}
            </div>
          </div>
          <p className="truncate text-sm leading-4 text-gray-text">
            {typingUser ? <span>typing...</span> : <>{lastMessage}</>}
          </p>
        </div>
        <DMRequestDeleteIcon className="size-5" onClick={handleDeleteGroup} />
      </div>
      {isConversationOpen && (
        <div className="absolute inset-y-0 right-0 w-1.5 bg-brand-orange" />
      )}
    </div>
  );
};
