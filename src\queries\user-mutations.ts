import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import {
  banUser,
  blockUser,
  connectExternalWallet,
  postConfirmReferrer,
  postNotifiedClick,
  unbanUser,
  unblockUser,
  updateUserPreferences,
} from "@/api/client/user";

import { UpdateUserPreferencesVariables } from "./types/user";

type BlockUserMutationType = MutationOptions<
  unknown,
  DefaultError,
  {
    userId: string;
  },
  any
>;

export const useBlockUserMutation = (options?: BlockUserMutationType) => {
  return useMutation({
    mutationFn: blockUser,
    ...options,
  });
};

type UnblockUserMutationType = MutationOptions<
  unknown,
  DefaultError,
  {
    userId: string;
  },
  any
>;

export const useUnblockUserMutation = (options?: UnblockUserMutationType) => {
  return useMutation({
    mutationFn: unblockUser,
    ...options,
  });
};

type BanUserMutationType = MutationOptions<
  unknown,
  DefaultError,
  {
    userId: string;
  },
  any
>;

export const useBanUserMutation = (options?: BanUserMutationType) => {
  return useMutation({
    mutationFn: banUser,
    ...options,
  });
};

export const useUnbanUserMutation = (options?: BanUserMutationType) => {
  return useMutation({
    mutationFn: unbanUser,
    ...options,
  });
};

type ConfirmReferrerMutationType = MutationOptions<
  unknown,
  DefaultError,
  {
    referralId: string;
    referrerId: string;
    answer: boolean;
  },
  any
>;

export const useConfirmReferrerMutation = (
  options?: ConfirmReferrerMutationType,
) => {
  return useMutation({
    mutationFn: postConfirmReferrer,
    ...options,
  });
};

type UpdateUserPreferencesMutationType = MutationOptions<
  unknown,
  DefaultError,
  UpdateUserPreferencesVariables,
  any
>;

export const useUpdateUserPreferencesMutation = (
  options?: UpdateUserPreferencesMutationType,
) => {
  return useMutation({
    mutationFn: updateUserPreferences,
    ...options,
  });
};

type ConnectExternalWalletMutationType = MutationOptions<
  unknown,
  DefaultError,
  {
    connectedWalletAddress: string;
  },
  any
>;

export const useConnectExternalWalletMutation = (
  options?: ConnectExternalWalletMutationType,
) => {
  return useMutation({
    mutationFn: connectExternalWallet,
    ...options,
  });
};

type UpdateNotifiedClickedType = MutationOptions<
  boolean,
  DefaultError,
  void,
  any
>;
export const userNotifiedMutation = (options?: UpdateNotifiedClickedType) => {
  return useMutation({
    mutationFn: postNotifiedClick,
    ...options,
  });
};
