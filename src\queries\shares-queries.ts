import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import {
  getSharesHolders,
  getSharesHoldings,
  getSharesStats,
} from "@/api/client/shares";

import { SharesStatsResponse } from "./types";

export const useSharesStatsQuery = ({ userId }: { userId?: string }) => {
  return useQuery<SharesStatsResponse>({
    queryKey: ["shares", "stats", userId],
    queryFn: async () => {
      if (!userId) return null;

      return await getSharesStats({ userId });
    },
  });
};

export const useSharesHoldersInfiniteQuery = (
  data: {
    userId?: string;
    enabled?: boolean;
  } = {
    enabled: true,
  },
) => {
  return useInfiniteQuery({
    queryKey: ["shares", "holders", data?.userId],
    queryFn: ({ pageParam }) => {
      return getSharesHolders({
        userId: data?.userId,
        page: pageParam.page,
        pageSize: pageParam.pageSize,
      });
    },
    enabled: data?.enabled || true,
    initialPageParam: {
      page: 1,
      pageSize: 20,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (!lastPage || lastPage.page >= lastPage.numberOfPages) {
        return undefined;
      }
      return {
        ...lastPageParam,
        page: lastPageParam.page + 1,
      };
    },
  });
};

export const useSharesHoldingsInfiniteQuery = () => {
  return useInfiniteQuery({
    queryKey: ["shares", "holdings"],
    queryFn: ({ pageParam }) => {
      return getSharesHoldings({
        page: pageParam.page,
        pageSize: pageParam.pageSize,
      });
    },
    initialPageParam: {
      page: 1,
      pageSize: 20,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (!lastPage || lastPage.page >= lastPage.numberOfPages) {
        return undefined;
      }
      return {
        ...lastPageParam,
        page: lastPageParam.page + 1,
      };
    },
  });
};
