import { StateSnapshot, VirtuosoHandle } from "react-virtuoso";
import { create } from "zustand";

interface Store {
  livestreamsSnapshot?: StateSnapshot;
  livestreamsTimelineRef: { current: VirtuosoHandle | null };
}

interface Actions {
  setLivestreamsSnapshot: (livestreamsSnapshot?: StateSnapshot) => void;
}

export const useLivestreamsTimelineStore = create<Store & Actions>((set) => ({
  livestreamsSnapshot: undefined,
  livestreamsTimelineRef: {
    current: null,
  },
  setLivestreamsSnapshot: (livestreamsSnapshot) => set({ livestreamsSnapshot }),
}));
