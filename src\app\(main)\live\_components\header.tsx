"use client";

import { useEffect, useState } from "react";

import { motion, useTransform } from "framer-motion";

import { LogoIcon } from "@/components/icons";
import { useBoundedScroll } from "@/hooks";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

import { MenuModal } from "../../home/<USER>/menu-modal";
import { StageSettingsModal } from "./settings-modal";

export const Header = ({
  children,
  tab,
}: {
  children?: React.ReactNode;
  tab: "stages" | "livestreams";
}) => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const { scrollYBoundedProgress } = useBoundedScroll(300);
  const [isPageTop, setIsPageTop] = useState(true);
  const scrollYBoundedProgressDelayed = useTransform(
    scrollYBoundedProgress,
    [0, 0.5, 1],
    [0, 1, 1],
  );

  const y = useTransform(
    scrollYBoundedProgressDelayed,
    [0, 1],
    ["0%", "-100%"],
  );

  useEffect(() => {
    const handleScroll = () => {
      setIsPageTop(window.scrollY <= 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  if (isTablet)
    return (
      <motion.div
        className="sticky top-[52px] z-40 hidden items-center justify-between bg-dark-bk bg-opacity-65 px-6 py-5 backdrop-blur-md sm:flex"
        style={{
          y: isPageTop ? 0 : y,
        }}
      >
        <h1 className="text-[26px] font-semibold leading-[30px] text-white">
          {tab === "stages" ? "Stages" : "Livestreams"}
        </h1>
        <StageSettingsModal />
      </motion.div>
    );

  return (
    <motion.div
      className="sticky top-0 z-40 flex flex-col bg-dark-bk bg-opacity-65 pt-[calc(22px+env(safe-area-inset-top))]  backdrop-blur-md sm:hidden"
      style={{
        y: isPageTop ? 0 : y,
      }}
    >
      <div className="flex items-center justify-between px-5 pb-[18px]">
        <div className="flex flex-1 items-center">
          <MenuModal />
        </div>
        <LogoIcon className="h-[42px] text-brand-orange" />
        <div className="flex flex-1 items-center justify-end">
          <StageSettingsModal />
        </div>
      </div>
      {children}
    </motion.div>
  );
};
