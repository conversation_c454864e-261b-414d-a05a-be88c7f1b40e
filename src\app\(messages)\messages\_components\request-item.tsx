import { FC } from "react";
import { useRouter } from "next/navigation";

import Skeleton from "react-loading-skeleton";
import { v4 as uuid } from "uuid";

import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UserBadges } from "@/components/user-badges";
import { User } from "@/queries/types/top-users-response";

interface RequestItemProps {
  user: User & { groupId?: string };
}

export const RequestItem: FC<RequestItemProps> = ({ user }) => {
  const router = useRouter();

  const handleClick = () => {
    const newPathname =
      user.groupId || `${uuid()}?twitterHandle=${user.twitterHandle}`;
    router.push(`/messages/${newPathname}`);
  };

  return (
    <div
      onClick={handleClick}
      className="flex w-full cursor-pointer justify-between gap-4 px-6 py-4"
    >
      <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
        <Avatar className="size-[42px]">
          <AvatarImage src={user.twitterPicture} />
          <AvatarFallback />
        </Avatar>
        <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
          <div className="flex gap-1.5">
            <h4 className="truncate text-[#F4F4F4]">{user.twitterName}</h4>
            {user.badges && <UserBadges badges={user.badges} />}
          </div>
          <div className="truncate text-[#808080]">@{user.twitterHandle}</div>
        </div>
      </div>
    </div>
  );
};

const RequestItemSkeleton: FC = () => {
  return (
    <div className="flex w-full justify-between gap-4 px-6 py-4">
      <div className="flex items-center gap-[10px]">
        <Skeleton circle className="size-[42px]" />
        <div className="flex w-full flex-col gap-1 leading-4">
          <Skeleton className="mt-1 h-[14px] w-28" />
          <Skeleton className="h-[12px] w-20" />
        </div>
      </div>
    </div>
  );
};

export const RequestItemSkeletons: FC = () => (
  <div className="mt-2">
    {Array(9)
      .fill(null)
      .map((_, index) => (
        <RequestItemSkeleton key={index} />
      ))}
  </div>
);
