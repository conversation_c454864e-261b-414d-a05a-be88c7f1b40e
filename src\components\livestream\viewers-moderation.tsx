"use client";

import { ChangeEvent, useCallback, useMemo, useState } from "react";

import { useParticipants } from "@livekit/components-react";
import { useQuery } from "@tanstack/react-query";
import { RoomEvent } from "livekit-client";

import useThrottle from "@/hooks/use-throttle";
import { livestreamQueries } from "@/queries/livestream-queries";
import { useLivestreamStore } from "@/stores/livestream";

import {
  ArrowBackOutlineIcon,
  CloseOutlineIcon,
  SearchFilledIcon,
} from "../icons";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "../ui/dialog";
import { Input } from "../ui/input";
import { ROLES } from "./constants";
import { ModerateViewerListItem } from "./moderate-viewer-list-item";

export const ViewersModeration = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const throttledSearchValue = useThrottle(searchValue);
  const id = useLivestreamStore((state) => state.id!);

  const { data } = useQuery({
    ...livestreamQueries.blockedViewers(id),
  });

  const participants = useParticipants({
    updateOnlyOn: [
      RoomEvent.ParticipantConnected,
      RoomEvent.ParticipantDisconnected,
      RoomEvent.ParticipantAttributesChanged,
    ],
  });

  const formattedParticipants = useMemo(() => {
    const formattedBlockedViewers =
      data?.blockedViewers.map((viewer) => ({
        id: viewer.id,
        name: viewer.user.twitterName,
        avatar: viewer.user.twitterPicture,
        username: viewer.user.twitterHandle,
        role: viewer.role,
        blocked: true,
      })) ?? [];

    const formattedParticipants = participants
      .map((participant) => ({
        id: participant.identity,
        name: participant.attributes?.name,
        avatar: participant.attributes?.avatar,
        username: participant.attributes?.username,
        role: participant.attributes?.role,
        blocked: false,
      }))
      .filter(
        (participant) =>
          !formattedBlockedViewers.some((blockedViewer) =>
            blockedViewer.id.includes(participant.id),
          ),
      );

    return [...formattedBlockedViewers, ...formattedParticipants].filter(
      (participant) => {
        if (participant.role === ROLES.HOST) return false;

        if (
          participant?.name
            ?.toLowerCase()
            ?.includes(throttledSearchValue?.toLowerCase()) ||
          participant?.username
            ?.toLowerCase()
            ?.includes(throttledSearchValue?.toLowerCase())
        ) {
          return true;
        }
        return false;
      },
    );
  }, [participants, data?.blockedViewers, throttledSearchValue]);

  const handleSearch = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  }, []);

  const callbackRef = useCallback((inputElement: HTMLInputElement) => {
    if (inputElement) {
      setTimeout(() => {
        inputElement.focus();
      }, 200);
    }
  }, []);

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
      }}
    >
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="z-50 flex h-full w-full flex-col gap-0 overflow-y-auto bg-dark-bk px-0 pb-16 pt-0 sm:bg-dark-bk md:max-h-[700px]">
        <div className="sticky top-0 z-20 bg-dark-bk px-6 pb-4">
          <div className="flex items-center gap-2  pb-4 pt-[calc(1rem+env(safe-area-inset-top))]">
            <div className="flex-1">
              <DialogClose className="flex flex-shrink-0">
                <ArrowBackOutlineIcon className="size-5 text-off-white" />
              </DialogClose>
            </div>
            <h2 className="text-base font-semibold leading-[22px] text-off-white">
              Moderate Viewers
            </h2>
            <div className="flex-1" />
          </div>
          <div className="relative">
            <Input
              value={searchValue}
              onChange={handleSearch}
              placeholder="Search for people and groups"
              className="h-[47px] rounded-full border border-dark-gray bg-transparent py-2 pl-11 pr-10 ring-0  placeholder:text-gray-text"
              ref={callbackRef}
            />
            <SearchFilledIcon className="pointer-events-none absolute left-[20px] top-1/2 size-[14px] -translate-y-1/2 select-none fill-gray-text" />
            {searchValue && (
              <button className="absolute right-[10px] top-1/2 -translate-y-1/2 rounded-full border border-dark-gray bg-[rgba(20,20,20,0.88)] p-1">
                <CloseOutlineIcon
                  className="pointer-events-auto size-[14px] select-none text-off-white"
                  onClick={() => setSearchValue("")}
                />
              </button>
            )}
          </div>
        </div>
        {formattedParticipants && formattedParticipants.length > 0 && (
          <div className="flex flex-col gap-[32px] px-6 py-5">
            {formattedParticipants.map(({ blocked, ...user }) => (
              <ModerateViewerListItem
                key={user.id + blocked ? "blocked" : "not-blocked"}
                // @ts-ignore
                user={user}
                blocked={blocked}
              />
            ))}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
