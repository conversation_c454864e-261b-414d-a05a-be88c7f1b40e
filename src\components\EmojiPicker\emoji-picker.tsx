import React, { useEffect, useRef } from "react";

import { ReactRenderer } from "@tiptap/react";
import EmojiPicker, { Theme } from "emoji-picker-react";
import tippy from "tippy.js";

import { cn } from "@/utils";

interface EmojiPickerProps {
  editor: any;
  emojiPickerRef: React.RefObject<HTMLDivElement>;
  isEmojiPickerOpen: boolean;
}

const EmojiPickerComponent: React.FC<EmojiPickerProps> = ({
  editor,
  emojiPickerRef,
  isEmojiPickerOpen,
}) => {
  const popupRef = useRef<any>(null);
  const reactRendererRef = useRef<any>(null);

  useEffect(() => {
    const updatePopupPosition = () => {
      if (editor) {
        const cursorRect = editor.view.coordsAtPos(
          editor.state.selection.$from.pos,
        );
        const referenceRect = new DOMRect(
          cursorRect?.left,
          cursorRect?.top,
          1,
          1,
        );

        if (popupRef.current) {
          popupRef.current.setProps({
            getReferenceClientRect: () => referenceRect,
          });
        }
      }
    };

    if (editor) {
      const cursorRect = editor.view.coordsAtPos(
        editor.state.selection.$from.pos,
      );
      const referenceRect = new DOMRect(
        cursorRect?.left,
        cursorRect?.top,
        1,
        1,
      );

      if (!reactRendererRef.current) {
        reactRendererRef.current = new ReactRenderer(
          () => (
            <div
              ref={emojiPickerRef}
              className={cn(
                "mr-10 mt-2 flex max-h-[420px] w-full flex-col overflow-y-auto rounded-lg bg-chat-bubble text-left shadow-[4px_4px_4px_0px_rgba(0,0,0,0.50)] sm:w-[420px]",
              )}
            >
              <EmojiPicker
                reactionsDefaultOpen={false}
                theme={Theme.DARK}
                height={300}
                width="100%"
                onEmojiClick={(emoji) => {
                  editor.chain().insertContent(emoji.emoji).run();
                }}
                previewConfig={{ showPreview: false }}
                className="!bg-chat-bubble [&_h2]:bg-chat-bubble/95"
              />
            </div>
          ),
          { editor: editor },
        );
      }

      if (!popupRef.current) {
        popupRef.current = tippy(document.body, {
          getReferenceClientRect: () => referenceRect,
          appendTo: () => document.body,
          content: reactRendererRef.current.element,
          showOnCreate: true,
          interactive: true,
          trigger: "manual",
          placement: "bottom-start",
        });
      }

      popupRef.current.setProps({
        getReferenceClientRect: () => referenceRect,
        content: reactRendererRef.current.element,
      });

      if (isEmojiPickerOpen) {
        popupRef.current.show();
      } else {
        popupRef.current.hide();
      }

      window.addEventListener("scroll", updatePopupPosition);
    }

    return () => {
      if (popupRef.current) {
        popupRef.current.destroy();
        reactRendererRef.current.destroy();
        popupRef.current = null;
        reactRendererRef.current = null;
      }
      window.removeEventListener("scroll", updatePopupPosition);
    };
  }, [isEmojiPickerOpen]);

  return null;
};

export default EmojiPickerComponent;
