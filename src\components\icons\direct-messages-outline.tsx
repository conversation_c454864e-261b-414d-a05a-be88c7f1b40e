import { ComponentProps } from "react";

interface DirectMessagesIconProps extends ComponentProps<"svg"> {
  number?: number;
}

export const DirectMessagesIcon = ({
  number,
  ...props
}: DirectMessagesIconProps) => (
  <svg
    viewBox="0 0 26 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M6.40858 25.8224C6.71212 26.6223 7.46079 27.1427 8.31647 27.1484C8.32094 27.1484 8.32542 27.1484 8.3303 27.1484C9.17988 27.1484 9.92896 26.639 10.241 25.8472L12.7051 19.5959L18.9565 17.1318C19.7524 16.8181 20.2631 16.0625 20.2578 15.2072C20.2521 14.3515 19.7317 13.6029 18.9321 13.2993L3.09449 7.28804C2.31449 6.99183 1.46409 7.17452 0.873702 7.7645C0.283717 8.35448 0.101432 9.20528 0.397238 9.98527L6.40858 25.8224ZM2.05205 8.94284C2.0964 8.89889 2.19893 8.81589 2.34622 8.81589C2.39383 8.81589 2.44632 8.82484 2.50288 8.84641L18.3409 14.8577C18.5651 14.9427 18.5908 15.138 18.5912 15.2178C18.5916 15.298 18.5688 15.4933 18.3454 15.5815L12.2655 17.9779L8.15534 13.8678C7.82983 13.5422 7.30251 13.5422 6.977 13.8678C6.65149 14.1933 6.65149 14.7206 6.977 15.0461L11.0872 19.1562L8.6908 25.2361C8.60251 25.4603 8.37425 25.4831 8.32705 25.4818C8.2473 25.4814 8.05199 25.4558 7.96695 25.2316L1.95561 9.39366C1.86773 9.16215 1.99386 9.00143 2.05205 8.94284Z"
      fill="#F4F4F4"
    />
    {number && (
      <>
        <path
          d="M25.752 7.46973C25.752 11.1248 22.7889 14.0879 19.1338 14.0879C15.4787 14.0879 12.5156 11.1248 12.5156 7.46973C12.5156 3.81462 15.4787 0.851562 19.1338 0.851562C22.7889 0.851562 25.752 3.81462 25.752 7.46973Z"
          fill="#EB540A"
        />
        <text
          x="19"
          y="8.5"
          fill="white"
          fontSize="10"
          textAnchor="middle"
          alignmentBaseline="middle"
        >
          {number}
        </text>
      </>
    )}
  </svg>
);
