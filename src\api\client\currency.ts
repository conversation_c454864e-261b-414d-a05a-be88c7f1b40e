import { axios } from "@/lib/axios";

export interface AvaxPrice {
  avax: number;
}

export interface SystemCurrency {
  symbol: string;
  systemRate: string;
  name: string;
  tokenName?: string;
  balance?: string;
  balanceUsd?: string;
  isToken: boolean;
  photoURL?: string;
  contractAddress?: string;
  tokenPhase?: number;
  isStakedArena?: boolean;
  official?: boolean;
  decimals: number;
  blockchain: string;
}

export interface SystemCurrencies {
  currencies: SystemCurrency[];
}

export const getAvaxPrice = async () => {
  const response = await axios.get<AvaxPrice>(`/currency/avax`);

  return response.data;
};

export const getSystemCurrencies = async () => {
  const response = await axios.get<SystemCurrencies>(`/currency/system`);
  return response.data;
};

export const getSupportedCurrencies = async () => {
  const response = await axios.get<SystemCurrencies>(`/currency/supported`);
  return response.data;
};

export const getTippableCurrencies = async () => {
  const response = await axios.get<SystemCurrencies>(`/currency/tippable`);

  return response.data;
};

export const getExchangeCurrencies = async () => {
  const response = await axios.get<SystemCurrency[]>(`/currency/exchange`);
  return response.data;
};
