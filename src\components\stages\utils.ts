type Timestamp = number;
type FormattedTime = string;

interface TimeDuration {
  hours: number;
  minutes: number;
  seconds: number;
}

export const formatTimeDistance = (
  startDate: Timestamp,
  endDate: Timestamp,
) => {
  const durationInMs = endDate - startDate;
  return msToTime(durationInMs);
};

function msToTime(duration: number): FormattedTime {
  const seconds = Math.floor((duration / 1000) % 60);
  const minutes = Math.floor((duration / (1000 * 60)) % 60);
  const hours = Math.floor((duration / (1000 * 60 * 60)) % 24);

  const timeDuration: TimeDuration = { hours, minutes, seconds };

  return formatTime(timeDuration);
}

function formatTime(duration: TimeDuration): FormattedTime {
  const { hours, minutes, seconds } = duration;

  const formattedHours = hours.toString().padStart(2, "0");
  const formattedMinutes = minutes.toString().padStart(2, "0");
  const formattedSeconds = seconds.toString().padStart(2, "0");

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
}
