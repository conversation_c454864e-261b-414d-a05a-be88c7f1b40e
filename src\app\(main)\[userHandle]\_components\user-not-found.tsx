import { useParams, useRouter } from "next/navigation";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

export const UserNotFound = () => {
  const params = useParams() as { userHandle: string };
  const router = useRouter();

  const goBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push("/home");
    }
  };

  return (
    <div className="pt-pwa relative z-20">
      <div className="flex aspect-[3/0.27] w-full flex-col justify-center bg-dark-bk bg-cover bg-no-repeat ">
        <button
          className="absolute left-4 flex size-5 items-center justify-center rounded-full bg-dark-bk"
          onClick={goBack}
        >
          <ArrowBackOutlineIcon className="size-6 text-white" />
        </button>
        <div className="leading-1 absolute left-10 ml-8 flex text-base font-medium tracking-wider text-off-white">
          Profile
        </div>
      </div>
      <div className="aspect-[3/1] w-full bg-[#5E5E5E] bg-cover bg-center bg-no-repeat" />
      <div className="-mt-11 ml-5 flex items-end gap-5">
        <Avatar className="size-[92px] border">
          <AvatarFallback className="bg-gray-text" />
        </Avatar>
      </div>
      <div className="mt-4 flex flex-col gap-0.5">
        <h1 className="ml-4 flex items-center  text-base font-medium leading-5 text-off-white">
          @{params.userHandle}
        </h1>
      </div>
      <div className="flex flex-col items-center">
        <div className="mt-20 text-lg font-semibold text-off-white">
          This account doesn&apos;t exist
        </div>
        <div className="text-sm text-gray-text">Try searching for another.</div>
      </div>
    </div>
  );
};
