import React, { useState } from "react";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import { BadgeChatsTippingPartyModal } from "./badges-tipping-party-modal";

export default {
  title: "Modals/BadgeChatsTippingPartyModal",
  component: BadgeChatsTippingPartyModal,
};

const mockUser = {
  id: "user-1",
  address: "0x123",
  dynamicAddress: "0x123",
};

const mockBadgeHolders = [
  { id: "user-2", twitterName: "Alice", address: "0x234" },
  { id: "user-3", twitterName: "Bob", address: "0x345" },
];

const mockCurrencies = [
  {
    symbol: "AVAX",
    name: "Avalanche",
    balance: "100",
    isToken: false,
    photoURL: "",
  },
  {
    symbol: "MEAT",
    name: "Meat Token",
    balance: "50",
    isToken: true,
    photoURL: "",
  },
];

const queryClient = new QueryClient();

export const Default = () => {
  const [open, setOpen] = useState(true);
  const [tipReceivers, setTipReceivers] = useState(
    mockBadgeHolders.map((user) => ({ isChecked: true, user, amount: "1" })),
  );
  return (
    <QueryClientProvider client={queryClient}>
      <BadgeChatsTippingPartyModal
        open={open}
        setOpen={setOpen}
        groupId="mock-group"
      />
    </QueryClientProvider>
  );
};
