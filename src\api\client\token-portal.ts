import { gql, request } from "graphql-request";

import { axios } from "@/lib/axios";
import {
  APYResponse,
  RestakeAgreedResponse,
  RewardsResponse,
  StakersLeaderboardRankResponse,
  TokenPortalClaimableBalanceResponse,
  TokenPortalGetClaimTxParamsResponse,
  TokenPortalRankResponse,
  TopStakersLeaderboardResponse,
} from "@/queries/types/token-portal";

const endpoint = "https://hub.snapshot.org/graphql";

export const getTokenPortlaClaimableBalance = async () => {
  const response = await axios.get<TokenPortalClaimableBalanceResponse>(
    `/token-portal/claimable_balance`,
  );
  return response.data;
};

export const getClaimTxParams = async (address: string) => {
  const response = await axios.get<TokenPortalGetClaimTxParamsResponse>(
    `/token-portal/getClaimTxParams`,
    {
      params: { address },
    },
  );
  return response.data;
};

export const getTokenPortalRank = async () => {
  const response =
    await axios.get<TokenPortalRankResponse>(`/token-portal/rank`);
  return response.data;
};

export const getTopStakersLeaderboard = async ({
  page,
  pageSize,
}: {
  page: number;
  pageSize: number;
}) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<TopStakersLeaderboardResponse>(
    `/token-portal/top-stakers-leaderboard?${searchParams.toString()}`,
  );
  return response.data;
};

export const getStakersLeaderboardRank = async () => {
  const response = await axios.get<StakersLeaderboardRankResponse>(
    `/token-portal/get-stakers-leaderboard-rank`,
  );
  return response.data;
};

export const postTokenPortalClaim = async () => {
  const response = await axios.post(`/token-portal/claim`);
  return response.data;
};

export const fetchProposals = async (space: string) => {
  const query = gql`
    {
      proposals(
        first: 10,
        where: {
          space_in: ["${space}"]
        },
        orderBy: "created",
        orderDirection: desc
      ) {
        id
        title
        scores
        body
        choices
        start
        end
        snapshot
        state
        votes
        author
      }
    }
  `;
  try {
    const data: { proposals: any[] } = await request(endpoint, query);
    return data.proposals;
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const getAPY = async () => {
  const response = await axios.get<APYResponse>(`/token-portal/apy`);
  return response.data;
};

export const postAgreeRestake = async () => {
  const response = await axios.post<boolean>("/token-portal/agreement/restake");
  return response.data;
};

export const getRestakeAgreed = async () => {
  const response = await axios.get<RestakeAgreedResponse>(
    "/token-portal/agreement/restake",
  );
  return response.data;
};

export const getRewards = async (): Promise<RewardsResponse> => {
  const response = await axios.get<RewardsResponse>(`/token-portal/rewards`);
  return response.data;
};
