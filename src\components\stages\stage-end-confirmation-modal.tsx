"use client";

import { useState } from "react";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { Drawer, DrawerContent, DrawerTrigger } from "../ui/drawer";

export function EndStageConfirmationModal({
  onConfirm,
  children,
}: {
  onConfirm: () => void;
  children: React.ReactNode;
}) {
  const [open, setOpen] = useState(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <DialogContent className="max-w-sm">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold">
              Are you sure you want to end the stage?
            </DialogTitle>
            <DialogDescription className="text-sm text-gray-text">
              This will end the stage and all participants will be disconnected.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              className="flex-[1]"
              onClick={() => {
                setOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button
              className="flex-[2]"
              onClick={() => {
                onConfirm();
                setOpen(false);
              }}
            >
              End Stage
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>{children}</DrawerTrigger>
      <DrawerContent className="justify-start gap-6 text-left">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">
            Are you sure you want to end the stage?
          </DialogTitle>
          <DialogDescription className="text-sm text-gray-text">
            This will end the stage and all participants will be disconnected.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex flex-row items-center gap-2">
          <Button
            variant="outline"
            className="flex-[1]"
            onClick={() => {
              setOpen(false);
            }}
          >
            Cancel
          </Button>
          <Button
            className="flex-[2]"
            onClick={() => {
              onConfirm();
              setOpen(false);
            }}
          >
            End Stage
          </Button>
        </DialogFooter>
      </DrawerContent>
    </Drawer>
  );
}
