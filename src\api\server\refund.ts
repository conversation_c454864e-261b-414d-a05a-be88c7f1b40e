import { getAxios } from "@/lib/axios-server";

export const getIsRefundAvailable = async () => {
  const axios = getAxios();

  const response = await axios.get("/refund/isAvailable");
  return response.data;
};

export const postRefund = async (data: {
  addresses: string;
  transactions: string;
  amount: string;
}) => {
  const axios = getAxios();

  const response = await axios.post("/refund", data);
  return response.data;
};
