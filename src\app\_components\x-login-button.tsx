"use client";

import { useRouter, useSearchParams } from "next/navigation";

import { getAuthorizationTokens } from "@/actions/get-authorization-tokens";
import { Button } from "@/components/ui/button";

export const XLoginButton = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const ref = searchParams.get("ref");

  return (
    <Button
      className="w-full gap-2 bg-gray-gradient sm:px-12"
      variant="secondary"
      onClick={async () => {
        const data = await getAuthorizationTokens(ref);
        if (!data) return;

        router.push(data.url);
      }}
    >
      <span>Enter The Arena With</span>
      <img
        src={"/icons/twitter.svg"}
        alt="X logo"
        width={28}
        height={24}
        className="sm:h-auto sm:w-5"
      />
    </Button>
  );
};
