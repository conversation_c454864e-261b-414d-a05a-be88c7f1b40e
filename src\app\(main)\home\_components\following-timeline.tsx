"use client";

import { useEffect, useMemo, useRef, useState } from "react";

import { useQueryClient } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { StateSnapshot, Virtuoso, VirtuosoHandle } from "react-virtuoso";

import { ArrowDownFilledIcon } from "@/components/icons";
import { PostLoadingSkeleton } from "@/components/post-loading-skeleton";
import { Button } from "@/components/ui/button";
import { usePullToRefresh } from "@/hooks/use-pull-to-refresh";
import { useThreadsInfiniteQuery, useTopUsersQuery } from "@/queries";
import { useHomeStore } from "@/stores";
import { Thread } from "@/types";
import { cn } from "@/utils";

import { TimelinePost } from "./timeline-post";
import { UserListItem, UserListItemLoadingSkeleton } from "./user-lits-item";

const MotionButton = motion(Button);

export const FollowingTimeline = () => {
  const queryClient = useQueryClient();
  const timelineState = useRef<StateSnapshot>();
  const containerRef = useRef<HTMLDivElement>(null);
  const virtuoso = useRef<VirtuosoHandle | null>(null);
  const timelineRef = useHomeStore((state) => state.followingTimelineRef);
  const [followsCount, setFollowsCount] = useState(0);
  const [startReached, setStartReached] = useState(true);
  const state = usePullToRefresh(
    containerRef,
    () => {
      queryClient.resetQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
    },
    startReached,
  );

  const { snapshot, setSnapshot } = useHomeStore((state) => ({
    snapshot: state.followingSnapshot,
    setSnapshot: state.setFollowingSnapshot,
  }));

  const { data, fetchNextPage, hasNextPage, isLoading, isFetchingNextPage } =
    useThreadsInfiniteQuery();

  const { data: topUsersData, isLoading: isTopUsersLoading } =
    useTopUsersQuery();

  const threads = useMemo(() => {
    if (!data) return [];

    return data.pages.reduce((prev, current) => {
      return [...prev, ...current.threads];
    }, [] as Thread[]);
  }, [data]);

  useEffect(() => {
    return () => {
      if (timelineState.current) {
        setSnapshot(timelineState.current);
        timelineRef.current = null;
      }
    };
  }, []);

  if (threads.length === 0 && !isLoading) {
    return (
      <div className="relative mt-9 pb-6 shadow-[0px_1px_1px_0px_#323232]">
        <div className="px-6 text-center">
          <h2 className="text-base font-semibold leading-5 text-white">
            Welcome to the Arena!
          </h2>
          <p className="mt-1 text-sm text-[#808080]">
            Follow some of the popular accounts below to get started
          </p>
        </div>
        <div className="mt-4 pb-10">
          {isTopUsersLoading && (
            <>
              {Array.from({ length: 10 }).map((_, i) => (
                <UserListItemLoadingSkeleton key={i} />
              ))}
            </>
          )}
          {!isTopUsersLoading &&
            topUsersData &&
            topUsersData.users.map((user) => {
              return (
                <UserListItem
                  key={user.id}
                  user={user}
                  onFollow={() => {
                    setFollowsCount((count) => count + 1);
                  }}
                  onUnfollow={() => {
                    setFollowsCount((count) => count - 1);
                  }}
                />
              );
            })}
        </div>
        {followsCount > 0 && (
          <MotionButton
            initial={{
              opacity: 0,
              y: 50,
              x: "-50%",
            }}
            animate={{
              opacity: 1,
              y: 0,
              x: "-50%",
            }}
            className="fixed bottom-[calc(70px+env(safe-area-inset-bottom))] left-1/2 px-10 sm:hidden"
            transition={{
              ease: "easeIn",
            }}
            onClick={() => {
              queryClient.resetQueries({
                queryKey: ["home", "threads", "my-feed"],
              });
            }}
          >
            Reload
          </MotionButton>
        )}
      </div>
    );
  }

  return (
    <>
      {state !== "idle" && (
        <div className="absolute left-1/2 top-[calc(8.75rem+env(safe-area-inset-top))] -translate-x-1/2">
          <ArrowDownFilledIcon
            className={cn(
              "h-6 w-6 text-gray-text transition-transform",
              state === "release" && "rotate-180",
            )}
          />
        </div>
      )}
      <div ref={containerRef}>
        <Virtuoso
          ref={(node) => {
            virtuoso.current = node;
            timelineRef.current = node;
          }}
          useWindowScroll
          data={threads}
          endReached={() => {
            if (hasNextPage) {
              fetchNextPage();
            }
          }}
          atTopStateChange={(state) => {
            setStartReached(state);
          }}
          increaseViewportBy={3000}
          overscan={2000}
          restoreStateFrom={snapshot}
          itemContent={(index, thread) => {
            return <TimelinePost thread={thread} />;
          }}
          isScrolling={() => {
            virtuoso.current?.getState((state) => {
              timelineState.current = state;
            });
          }}
          components={{
            Footer: () => {
              if (isLoading || isFetchingNextPage) {
                return (
                  <>
                    {Array.from({ length: 7 }).map((_, i) => (
                      <PostLoadingSkeleton key={i} />
                    ))}
                  </>
                );
              }
              return null;
            },
          }}
        />
      </div>
    </>
  );
};
