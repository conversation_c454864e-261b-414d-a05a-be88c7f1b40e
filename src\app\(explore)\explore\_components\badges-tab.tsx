"use client";

import { memo, startTransition, useCallback, useState } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

import { Trigger } from "@radix-ui/react-tabs";

import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList } from "@/components/ui/tabs";

import { BadgeContent } from "./badge-content";

export const BadgesTab = memo(function BadgesTab() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [currentTab, setCurrentTab] = useState<string>(
    () => searchParams.get("badge-tab") || "arena-ogs",
  );

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set(name, value);

      return params.toString();
    },
    [searchParams],
  );

  const deleteQueryString = useCallback(
    (name: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.delete(name);

      return params.toString();
    },
    [searchParams],
  );

  const handleTabChange = (value: string) => {
    startTransition(() => {
      if (value === "arena-ogs") {
        router.replace(`${pathname}?${deleteQueryString("badge-tab")}`);
      } else {
        router.replace(`${pathname}?${createQueryString("badge-tab", value)}`);
      }
    });
    setCurrentTab(value);
  };

  return (
    <Tabs value={currentTab} onValueChange={handleTabChange}>
      <TabsList className="mb-3 h-auto w-full space-x-[14px] border-none">
        <Trigger value="arena-ogs" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-1.png"
                className="h-[18px] w-auto"
                alt="OGs logo"
              />
            </div>
            <span>Arena OGs</span>
          </Button>
        </Trigger>
        <Trigger value="arena-champions" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/icons/arena-champion-user.svg"
                className="h-[18px] w-auto"
                alt="Champions logo"
              />
            </div>
            <span>Arena Champions</span>
          </Button>
        </Trigger>
        <Trigger value="degods" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 px-3 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-2.png"
                className="h-[18px] w-auto"
                alt="DeGods logo"
              />
            </div>
            <span>DeGods</span>
          </Button>
        </Trigger>
        <Trigger value="dokyo" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-3.png"
                className="h-[18px] w-auto"
                alt="Dokyo logo"
              />
            </div>
            <span>Dokyo</span>
          </Button>
        </Trigger>
        <Trigger value="sappy-seals" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-4.png"
                className="h-[18px] w-auto"
                alt="Sappy Seals logo"
              />
            </div>
            <span>Sappy Seals</span>
          </Button>
        </Trigger>
        <Trigger value="gurs" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-5.png"
                className="h-[18px] w-auto"
                alt="GURS logo"
              />
            </div>
            <span>GURS</span>
          </Button>
        </Trigger>
        <Trigger value="nochill" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-6.png"
                className="h-[18px] w-auto"
                alt="Nochill logo"
              />
            </div>
            <span>Nochill</span>
          </Button>
        </Trigger>
        <Trigger value="steady" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-8.png"
                className="h-[18px] w-auto"
                alt="Steady logo"
              />
            </div>
            <span>Steady</span>
          </Button>
        </Trigger>
        <Trigger value="smoljoe" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-9.png"
                className="h-[18px] w-auto"
                alt="Smol Joe logo"
              />
            </div>
            <span>OG Smol Joe</span>
          </Button>
        </Trigger>
        {/* <Trigger value="diamond" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-10.png"
                className="h-[18px] w-auto"
                alt="Diamond Handers logo"
              />
            </div>
            <span>Diamond Handers</span>
          </Button>
        </Trigger> */}
        <Trigger value="gogonauts" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-11.png"
                className="h-[18px] w-auto"
                alt="Gogonauts logo"
              />
            </div>
            <span>Gogonauts</span>
          </Button>
        </Trigger>
        <Trigger value="bodoggos" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-12.png"
                className="h-[18px] w-auto"
                alt="Bodoggos logo"
              />
            </div>
            <span>Bodoggos</span>
          </Button>
        </Trigger>
        <Trigger value="pudgy" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-13.png"
                className="h-[18px] w-auto"
                alt="Pudgy logo"
              />
            </div>
            <span>Pudgy Penguins</span>
          </Button>
        </Trigger>
        <Trigger value="coq" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-14.png"
                className="h-[18px] w-auto"
                alt="COQ logo"
              />
            </div>
            <span>COQ</span>
          </Button>
        </Trigger>
        <Trigger value="mog" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-15.png"
                className="h-[18px] w-auto"
                alt="MOG logo"
              />
            </div>
            <span>MOG</span>
          </Button>
        </Trigger>
        <Trigger value="madlads" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-16.png"
                className="h-[18px] w-auto"
                alt="Mad Lads logo"
              />
            </div>
            <span>Mad Lads</span>
          </Button>
        </Trigger>
        {/* <Trigger value="techfriend" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-17.png"
                className="h-[18px] w-auto"
                alt="tech friend logo"
              />
            </div>
            <span>TechFriends</span>
          </Button>
        </Trigger> */}
        <Trigger value="nochillio" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-18.png"
                className="h-[18px] w-auto"
                alt="Nochill logo"
              />
            </div>
            <span>Nochillio</span>
          </Button>
        </Trigger>
        <Trigger value="friendtech" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-17.png"
                className="h-[18px] w-auto"
                alt="friendtech logo"
              />
            </div>
            <span>FT</span>
          </Button>
        </Trigger>
        <Trigger value="abc" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-20.png"
                className="h-[18px] w-auto"
                alt="abc logo"
              />
            </div>
            <span>AuTistiC BoYs CLub</span>
          </Button>
        </Trigger>
        <Trigger value="mu-tier-1" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-21.png"
                className="h-[18px] w-auto"
                alt="mu logo 1"
              />
            </div>
            <span>MU Doggy</span>
          </Button>
        </Trigger>
        <Trigger value="mu-tier-2" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-22.png"
                className="h-[18px] w-auto"
                alt="mu logo 2"
              />
            </div>
            <span>MU</span>
          </Button>
        </Trigger>
        <Trigger value="bad-bois" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-23.png"
                className="h-[18px] w-auto"
                alt="bad bois logo"
              />
            </div>
            <span>Bad Bois</span>
          </Button>
        </Trigger>
        <Trigger value="boi" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-24.png"
                className="h-[18px] w-auto"
                alt="bois club logo"
              />
            </div>
            <span>Boi</span>
          </Button>
        </Trigger>
        <Trigger value="lok" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-25.png"
                className="h-[18px] w-auto"
                alt="LOK logo"
              />
            </div>
            <span>LOK</span>
          </Button>
        </Trigger>
        <Trigger value="erol" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-26.png"
                className="h-[18px] w-auto"
                alt="EROL logo"
              />
            </div>
            <span>EROL</span>
          </Button>
        </Trigger>
        <Trigger value="doomercorp" asChild>
          <Button
            variant="outline"
            className="flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5 data-[state=active]:border-brand-orange"
          >
            <div className="w-max">
              <img
                src="/assets/badges/badge-type-27.png"
                className="h-[18px] w-auto"
                alt="Doomercorp logo"
              />
            </div>
            <span>Doomercorp</span>
          </Button>
        </Trigger>
      </TabsList>
      <TabsContent value="arena-ogs">
        <BadgeContent type={1} />
      </TabsContent>
      <TabsContent value="arena-champions">
        <BadgeContent type={19} />
      </TabsContent>
      <TabsContent value="degods">
        <BadgeContent type={2} />
      </TabsContent>
      <TabsContent value="dokyo">
        <BadgeContent type={3} />
      </TabsContent>
      <TabsContent value="sappy-seals">
        <BadgeContent type={4} />
      </TabsContent>
      <TabsContent value="gurs">
        <BadgeContent type={5} />
      </TabsContent>
      <TabsContent value="nochill">
        <BadgeContent type={6} />
      </TabsContent>
      <TabsContent value="steady">
        <BadgeContent type={8} />
      </TabsContent>
      <TabsContent value="smoljoe">
        <BadgeContent type={9} />
      </TabsContent>
      {/* <TabsContent value="diamond">
        <BadgeContent type={10} />
      </TabsContent> */}
      <TabsContent value="gogonauts">
        <BadgeContent type={11} />
      </TabsContent>
      <TabsContent value="bodoggos">
        <BadgeContent type={12} />
      </TabsContent>
      <TabsContent value="pudgy">
        <BadgeContent type={13} />
      </TabsContent>
      <TabsContent value="coq">
        <BadgeContent type={14} />
      </TabsContent>
      <TabsContent value="mog">
        <BadgeContent type={15} />
      </TabsContent>
      <TabsContent value="madlads">
        <BadgeContent type={16} />
      </TabsContent>
      <TabsContent value="friendtech">
        <BadgeContent type={17} />
      </TabsContent>
      <TabsContent value="nochillio">
        <BadgeContent type={18} />
      </TabsContent>
      <TabsContent value="abc">
        <BadgeContent type={20} />
      </TabsContent>
      <TabsContent value="mu-tier-1">
        <BadgeContent type={21} />
      </TabsContent>
      <TabsContent value="mu-tier-2">
        <BadgeContent type={22} />
      </TabsContent>
      <TabsContent value="bad-bois">
        <BadgeContent type={23} />
      </TabsContent>
      <TabsContent value="boi">
        <BadgeContent type={24} />
      </TabsContent>
      <TabsContent value="lok">
        <BadgeContent type={25} />
      </TabsContent>
      <TabsContent value="erol">
        <BadgeContent type={26} />
      </TabsContent>
      <TabsContent value="doomercorp">
        <BadgeContent type={27} />
      </TabsContent>
    </Tabs>
  );
});
