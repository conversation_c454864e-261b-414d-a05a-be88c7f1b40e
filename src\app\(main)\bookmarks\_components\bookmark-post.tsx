import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { PostUI } from "@/components/post";
import {
  useBookmarkThreadMutation,
  useDeleteRepostMutation,
  useLikeThreadMutation,
  useRepostThreadMutation,
  useUnbookmarkThreadMutation,
  useUnlikeThreadMutation,
} from "@/queries";
import { ThreadsResponse } from "@/queries/types";
import { Thread } from "@/types";

interface BookmarkPostProps {
  thread: Thread;
}

export const BookmarkPost = ({ thread }: BookmarkPostProps) => {
  const isRepost = thread.threadType === "repost";
  const activePost = isRepost && thread.repost ? thread.repost : thread;
  const queryClient = useQueryClient();

  const { mutateAsync: repost } = useRepostThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({ queryKey: ["threads", "bookmarks"] });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "bookmarks"]);

      queryClient.setQueryData(
        ["threads", "bookmarks"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.reposted) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount + 1,
                          reposted: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount + 1,
                      reposted: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "bookmarks"],
        context?.previousMyFeed,
      );
    },
  });
  const { mutateAsync: deleteRepost } = useDeleteRepostMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({ queryKey: ["threads", "bookmarks"] });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "bookmarks"]);

      queryClient.setQueryData(
        ["threads", "bookmarks"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.repost) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount - 1,
                          reposted: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount - 1,
                      reposted: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "bookmarks"],
        context?.previousMyFeed,
      );
    },
  });
  const { mutateAsync: like } = useLikeThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({ queryKey: ["threads", "bookmarks"] });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "bookmarks"]);

      queryClient.setQueryData(
        ["threads", "bookmarks"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.like) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount + 1,
                          like: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && !t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount + 1,
                      like: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "bookmarks"],
        context?.previousMyFeed,
      );
    },
  });
  const { mutateAsync: unlike } = useUnlikeThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({ queryKey: ["threads", "bookmarks"] });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "bookmarks"]);

      queryClient.setQueryData(
        ["threads", "bookmarks"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.like) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount - 1,
                          like: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount - 1,
                      like: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "bookmarks"],
        context?.previousMyFeed,
      );
    },
  });
  const { mutateAsync: bookmark } = useBookmarkThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({ queryKey: ["threads", "bookmarks"] });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "bookmarks"]);

      queryClient.setQueryData(
        ["threads", "bookmarks"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.bookmark) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount + 1,
                          bookmark: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && !t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount + 1,
                      bookmark: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "bookmarks"],
        context?.previousMyFeed,
      );
    },
  });
  const { mutateAsync: unbookmark } = useUnbookmarkThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({ queryKey: ["threads", "bookmarks"] });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["threads", "bookmarks"]);

      queryClient.setQueryData(
        ["threads", "bookmarks"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.bookmark) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount - 1,
                          bookmark: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount - 1,
                      bookmark: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        },
      );

      return { previousMyFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "bookmarks"],
        context?.previousMyFeed,
      );
    },
  });

  const handleLike = async ({ threadId }: { threadId: string }) => {
    if (activePost.like) {
      await unlike({ threadId });
    } else {
      await like({ threadId });
    }
  };

  const handleBookmark = async ({ threadId }: { threadId: string }) => {
    if (activePost.bookmark) {
      await unbookmark({ threadId });
    } else {
      await bookmark({ threadId });
    }
  };

  const handleRepost = async ({ threadId }: { threadId: string }) => {
    if (activePost.reposted) {
      await deleteRepost({ threadId });
    } else {
      await repost({ threadId });
    }
  };

  return (
    <PostUI
      thread={thread}
      handleLike={handleLike}
      handleBookmark={handleBookmark}
      handleRepost={handleRepost}
    />
  );
};
