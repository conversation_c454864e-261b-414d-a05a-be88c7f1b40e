"use client";

import { memo, useEffect, useMemo, useState } from "react";

import {
  useLocalParticipant,
  useParticipantAttribute,
  useParticipantAttributes,
  useParticipants,
} from "@livekit/components-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { RoomEvent } from "livekit-client";
import { ErrorBoundary } from "react-error-boundary";

import { postUpdateAttributes } from "@/api/client/stage";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useEndStageMutation } from "@/queries";
import { stageQueries } from "@/queries/stage-queries";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";

import {
  AnnotationOutlineIcon,
  BanOutlineIcon,
  ChevronDownFilled,
  EllipsisHorizontalFilledIcon,
  HeadphoneOutlineIcon,
  LogoIcon,
} from "../icons";
import { Button } from "../ui/button";
import { Drawer, DrawerContent, DrawerTrigger } from "../ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Role, ROLE_ORDER } from "./constants";
import { DropDownToListenerModal } from "./drop-down-to-listener-modal";
import { GuestsContent } from "./guests-content";
import { useLogSpeakingDuration } from "./hooks/use-log-speaking-duration";
import { StageBottom } from "./stage-bottom";
import { StageChatContainer } from "./stage-chat";
import { EndStageConfirmationModal } from "./stage-end-confirmation-modal";
import { StageInvitationModal } from "./stage-invitation-modal";
import { StageSharedPosts } from "./stage-shared-posts";
import { StageUser } from "./stage-user";
import { StartAudioButton } from "./start-audio-button";
import { TipPartyConfetti } from "./tip-party-confetti";

export const StageContent = () => {
  const local = useLocalParticipant();
  const { attributes } = useParticipantAttributes({
    participant: local.localParticipant,
  });
  const id = useStageStore((state) => state.id!);
  const isGuestsModalOpen = useStageStore((state) => state.isGuestsModalOpen);
  const actions = useStageStore((state) => state.actions);

  useQuery({
    queryKey: ["stage-participant-attributes", id, attributes],
    queryFn: async () => {
      if (
        !attributes?.id ||
        !attributes?.name ||
        !attributes?.avatar ||
        !attributes?.username ||
        !attributes?.role
      ) {
        await postUpdateAttributes({
          stageId: id,
        });
      }
      return true;
    },
  });

  const role = useMemo(() => {
    return attributes?.role as Role;
  }, [attributes?.role]);

  useEffect(() => {
    actions.setMyRole(role);
    actions.setIsRequestedToSpeak(false);
    if (role !== "HOST" && role !== "COHOST") {
      actions.setIsGuestsModalOpen(false);
    }
  }, [role]);

  if (isGuestsModalOpen) {
    return <GuestsContent />;
  }

  return <StageMainContent />;
};

const MAX_PARTICIPANTS = 100;

const StageMainContent = memo(() => {
  const queryClient = useQueryClient();
  const { user } = useUser();
  const id = useStageStore((state) => state.id!);
  const actions = useStageStore((state) => state.actions);
  const { data, isLoading } = useQuery({
    ...stageQueries.stageInfo(id),
  });
  const logSpeakingDuration = useLogSpeakingDuration();

  const { mutateAsync: endStage } = useEndStageMutation({
    onSuccess: () => {
      actions.reset();
    },
  });

  const participants = useParticipants({
    updateOnlyOn: [RoomEvent.ParticipantAttributesChanged],
  });

  const [sortedParticipants, otherListenersCount] = useMemo(() => {
    const sortedParticipants = participants
      .sort((a, b) => {
        const aRole = a.attributes.role;
        const bRole = b.attributes.role;

        if (!aRole && !bRole) return 0;
        if (!aRole) return 1;
        if (!bRole) return -1;

        const roleAIndex = ROLE_ORDER.indexOf(aRole as Role);
        const roleBIndex = ROLE_ORDER.indexOf(bRole as Role);
        return roleAIndex - roleBIndex;
      })
      .slice(0, MAX_PARTICIPANTS);

    const otherListenersCount =
      participants.length > MAX_PARTICIPANTS
        ? participants.length - MAX_PARTICIPANTS
        : 0;

    return [sortedParticipants, otherListenersCount];
  }, [participants]);

  const handleLeave = async () => {
    logSpeakingDuration();
    if (data?.host?.userId === user?.id) {
      await endStage({
        stageId: id,
      });
    } else {
      actions.reset();
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: stageQueries.currentlyListeningKey(),
        });
        actions.setCanShowCurrentlyListening(true);
      }, 3000);
    }
  };

  if (isLoading) return null;

  return (
    <>
      <div className="relative flex w-full flex-shrink-0 flex-col overflow-hidden bg-gray-bg p-6">
        <LogoIcon className="pointer-events-none absolute -right-6 top-4 w-[120px] select-none text-light-gray-text opacity-5" />
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            className="-ml-2 -mt-2 bg-none p-2 hover:bg-none"
            onClick={() => {
              actions.setFullScreen(false);
            }}
          >
            <ChevronDownFilled className="size-6 text-off-white" />
          </Button>
          <div className="flex items-center gap-2">
            {data?.host?.userId === user?.id ? (
              <EndStageConfirmationModal onConfirm={handleLeave}>
                <Button
                  variant="ghost"
                  className="-mr-3 -mt-1 bg-none px-3 py-1 text-base text-danger hover:bg-none"
                >
                  End
                </Button>
              </EndStageConfirmationModal>
            ) : (
              <Button
                variant="ghost"
                className="-mr-3 -mt-1 bg-none px-3 py-1 text-base text-danger hover:bg-none"
                onClick={handleLeave}
              >
                Leave
              </Button>
            )}
            <ToggleChat />
          </div>
        </div>
        <div className="mt-5">
          {data?.stage.isRecorded ? (
            <div className="mr-2 inline-flex items-center gap-1 self-start rounded-md border border-light-gray-text px-1.5 py-0.5">
              <div className="size-3 rounded-full bg-[#E74141]" />
              <div className="text-sm font-semibold uppercase text-off-white">
                REC
              </div>
            </div>
          ) : null}
          <h3 className="inline text-lg font-semibold leading-[22px] text-off-white">
            {data?.stage.name}
          </h3>
        </div>
      </div>
      <StageSharedPosts />
      <div className="relative flex flex-grow flex-col overflow-y-auto">
        <div className="relative flex flex-grow flex-col overflow-y-auto">
          <div className="grid w-full grid-cols-4 content-start gap-4 p-6">
            {sortedParticipants.map((participant, index) => {
              return (
                <ErrorBoundary fallback={null} key={participant.sid + index}>
                  <StageUser participant={participant} />
                </ErrorBoundary>
              );
            })}
          </div>
          {otherListenersCount > 0 && (
            <div className="flex w-full items-center justify-center px-6 pb-8 pt-2">
              <span className="w-fit whitespace-nowrap text-center text-sm text-gray-text">
                {otherListenersCount} more listeners
              </span>
            </div>
          )}
        </div>
        <StageChatContainer />
        <TipPartyConfetti />
        <StartAudioButton />
      </div>
      <div className="pb-pwa flex flex-shrink-0 flex-col border-t border-gray-border">
        <StageBottom />
      </div>
      <StageInvitationModal />
    </>
  );
});
StageMainContent.displayName = "StageMainContent";

const ToggleChat = () => {
  const local = useLocalParticipant();
  const role = useParticipantAttribute("role", {
    participant: local.localParticipant,
  }) as Role;
  const [open, setOpen] = useState(false);
  const [isSwitchOpen, setIsSwitchOpen] = useState(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const isChatOpen = useStageStore((state) => state.chat.isOpen);
  const isChatFullScreen = useStageStore((state) => state.chat.isFullScreen);
  const actions = useStageStore((state) => state.actions);

  if (role === "HOST" || role === "LISTENER") return null;

  return (
    <>
      {isTablet ? (
        <DropdownMenu open={open} onOpenChange={setOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="-mr-2 -mt-2 bg-none p-2 hover:bg-none"
            >
              <EllipsisHorizontalFilledIcon className="size-6 text-off-white" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[220px]">
            {role === "COHOST" || role === "SPEAKER" ? (
              <DropdownMenuItem className="w-full gap-4" asChild>
                <button
                  onClick={() => {
                    setIsSwitchOpen(true);
                    setOpen(false);
                  }}
                >
                  <HeadphoneOutlineIcon className="h-4 w-5 flex-shrink-0 text-gray-text" />
                  <span>Switch to Listening</span>
                </button>
              </DropdownMenuItem>
            ) : null}
            {/* {isChatOpen ? (
              <>
                {isChatFullScreen ? (
                  <DropdownMenuItem className="w-full gap-4" asChild>
                    <button
                      onClick={() => {
                        actions.setChatFullScreen(false);
                        setOpen(false);
                      }}
                    >
                      <AnnotationOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Minimize Chat</span>
                    </button>
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem className="w-full gap-4" asChild>
                    <button
                      onClick={() => {
                        actions.setChatFullScreen(true);
                        setOpen(false);
                      }}
                    >
                      <AnnotationOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                      <span>Expand Chat</span>
                    </button>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem className="w-full gap-4" asChild>
                  <button
                    onClick={() => {
                      actions.setChatOpen(false);
                      setOpen(false);
                    }}
                  >
                    <BanOutlineIcon className="size-5 flex-shrink-0 text-gray-text" />
                    <span>Disable Chat</span>
                  </button>
                </DropdownMenuItem>
              </>
            ) : null} */}
          </DropdownMenuContent>
        </DropdownMenu>
      ) : (
        <Drawer open={open} onOpenChange={setOpen}>
          <DrawerTrigger asChild>
            <Button
              variant="ghost"
              className="-mr-2 -mt-2 translate-y-0.5 bg-none p-2 hover:bg-none"
            >
              <EllipsisHorizontalFilledIcon className="size-6 text-off-white" />
            </Button>
          </DrawerTrigger>
          <DrawerContent className="px-4 pt-4">
            {role === "COHOST" || role === "SPEAKER" ? (
              <button
                className="flex items-center gap-2 p-2 text-base leading-5"
                onClick={() => {
                  setIsSwitchOpen(true);
                  setOpen(false);
                }}
              >
                <HeadphoneOutlineIcon className="h-5 w-6 text-gray-text" />
                Switch to Listening
              </button>
            ) : null}
            {/* {isChatOpen ? (
              <>
                {isChatFullScreen ? (
                  <button
                    className="flex items-center gap-2 p-2 text-base leading-5"
                    onClick={() => {
                      actions.setChatFullScreen(false);
                      setOpen(false);
                    }}
                  >
                    <AnnotationOutlineIcon className="size-6 text-gray-text" />
                    Minimize Chat
                  </button>
                ) : (
                  <button
                    className="flex items-center gap-2 p-2 text-base leading-5"
                    onClick={() => {
                      actions.setChatFullScreen(true);
                      setOpen(false);
                    }}
                  >
                    <AnnotationOutlineIcon className="size-6 text-gray-text" />
                    Expand Chat
                  </button>
                )}

                <button
                  className="flex items-center gap-2 p-2 text-base leading-5"
                  onClick={() => {
                    actions.setChatOpen(false);
                    setOpen(false);
                  }}
                >
                  <BanOutlineIcon
                    className="size-6 text-gray-text"
                    strokeWidth={42}
                  />
                  Disable Chat
                </button>
              </>
            ) : null} */}
          </DrawerContent>
        </Drawer>
      )}
      <DropDownToListenerModal open={isSwitchOpen} setOpen={setIsSwitchOpen} />
    </>
  );
};
