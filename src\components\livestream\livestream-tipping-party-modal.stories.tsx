import React, { useState } from "react";

import { LiveKitRoom } from "@livekit/components-react";
import { Toaster } from "sonner";

import { Button } from "@/components/ui/button";

import { LivestreamTippingPartyModal } from "./livestream-tipping-party-modal";

const mockReceivedChatMessage = {
  id: "mock-id",
  timestamp: Date.now(),
  message: "mock message",
};

const mockDataChannelsContext = {
  chat: {
    send: async () => mockReceivedChatMessage,
    update: async () => ({
      message: "mock message",
      editTimestamp: Date.now(),
      id: "mock-id",
      timestamp: Date.now(),
    }),
    chatMessages: [mockReceivedChatMessage],
    isSending: false,
  },
  sendInvalidateTippingStats: () => {},
};

export default {
  title: "Modals/LivestreamTippingPartyModal",
  component: LivestreamTippingPartyModal,
  decorators: [
    (Story: any) => (
      <LiveKitRoom
        token="dummy-token"
        serverUrl="wss://dummy-url"
        connect={false}
      >
        <Story />
        <Toaster position="top-center" />
      </LiveKitRoom>
    ),
  ],
};

export const Default = () => {
  const [open, setOpen] = useState(false);
  return (
    <LivestreamTippingPartyModal
      livestreamId="mock-livestream-id"
      dataChannelsContext={mockDataChannelsContext}
    >
      <Button onClick={() => setOpen(true)}>
        Open Livestream Tipping Party Modal
      </Button>
    </LivestreamTippingPartyModal>
  );
};
