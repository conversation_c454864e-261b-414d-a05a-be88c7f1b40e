import { NextRequest, NextResponse } from "next/server";

import { env } from "@/env";

import {
  allowedRoutesForSuspendedUser,
  authRoutes,
  DEFAULT_LOGIN_REDIRECT,
  publicRoutes,
} from "./routes";
import { UserFlaggedEnum } from "./types";

async function isAuthenticated(req: NextRequest) {
  const authHeader =
    req.headers.get("authorization") || req.headers.get("Authorization");

  if (!authHeader) {
    return false;
  }

  const auth = Buffer.from(authHeader.split(" ")[1], "base64")
    .toString()
    .split(":");
  const username = auth[0];
  const password = auth[1];

  if (env.NEXT_PUBLIC_HTTP_BASIC_AUTH) {
    const [AUTH_USER, AUTH_PASS] = (
      env.NEXT_PUBLIC_HTTP_BASIC_AUTH || ":"
    ).split(":");

    if (username == AUTH_USER && password == AUTH_PASS) {
      return true;
    }
  }

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/auth/basic-auth`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ username, password }),
    },
  );

  return response.status === 200;
}
export async function middleware(request: NextRequest) {
  const isHelthcheckRoute = request.nextUrl.pathname === "/api/healthcheck";
  if (env.NEXT_PUBLIC_HTTP_BASIC_AUTH_ENABLED && !isHelthcheckRoute) {
    if (!(await isAuthenticated(request))) {
      return new NextResponse("Authentication required", {
        status: 401,
        headers: { "WWW-Authenticate": "Basic" },
      });
    }
  }

  const startTime = new Date();
  const start = startTime.getTime();
  const startTimeISO = startTime.toISOString();

  try {
    const token = request.cookies.get("token")?.value;
    const userCookie = request.cookies.get("user");
    const user = userCookie ? JSON.parse(userCookie.value || "{}") : null;
    const isBanned = user?.flag === UserFlaggedEnum.SUSPENDED;
    const isLoggedIn = !!token;
    const { nextUrl } = request;

    const ip = request.ip;
    const userAgent = request.headers.get("user-agent");

    let response = NextResponse.next();

    const isPublicRoute = publicRoutes.includes(nextUrl.pathname);
    const isAuthRoute = authRoutes.includes(nextUrl.pathname);
    const isAllowedToSuspended = allowedRoutesForSuspendedUser.includes(
      nextUrl.pathname,
    );

    if (isAuthRoute) {
      if (token) {
        response = NextResponse.redirect(
          new URL(DEFAULT_LOGIN_REDIRECT, nextUrl),
        );
        console.log(
          `${startTimeISO} Redirect from ${nextUrl.pathname} to ${DEFAULT_LOGIN_REDIRECT} for an already authenticated user.`,
        );
      }
    } else if (!isLoggedIn && !isPublicRoute) {
      let callbackUrl = nextUrl.pathname;
      if (nextUrl.search) {
        callbackUrl += nextUrl.search;
      }
      const encodedCallbackUrl = encodeURIComponent(callbackUrl);
      response = NextResponse.redirect(
        new URL(`/?callbackUrl=${encodedCallbackUrl}`, nextUrl),
      );
      console.log(
        `${startTimeISO} Unauthenticated access to ${nextUrl.pathname}, redirecting to login page.`,
      );
    }

    if (isBanned && isLoggedIn && !isAllowedToSuspended) {
      response = NextResponse.redirect(
        new URL(DEFAULT_LOGIN_REDIRECT, nextUrl),
      );
    }

    const duration = Date.now() - start;
    console.log(
      `${startTimeISO} Request to ${nextUrl.pathname} from IP ${ip} using ${userAgent}, Authentication Status: ${isLoggedIn ? "authenticated" : "not authenticated"}, Duration: ${duration}ms`,
    );

    return response;
  } catch (error: unknown) {
    const duration = Date.now() - start;
    console.error(
      `${startTimeISO} An error occurred while processing the request:`,
      error,
      `Duration: ${duration}ms`,
    );
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export const config = {
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
};
