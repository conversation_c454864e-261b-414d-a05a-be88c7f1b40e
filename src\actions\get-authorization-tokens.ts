"use server";

import { cookies } from "next/headers";

import { env } from "@/env";
import { axios } from "@/lib/axios";

interface AuthorizeUrlResponse {
  authUrl: string;
}

export async function getAuthorizationTokens(ref: string | null) {
  try {
    if (ref) {
      cookies().set("__ref", ref, { httpOnly: true });
    }

    const { data } = await axios<AuthorizeUrlResponse>("/twitter/login", {
      params: {
        callbackUrl: `${env.NEXT_PUBLIC_APP_DOMAIN}/twitter/auth`,
      },
    });

    return {
      url: data.authUrl,
    };
  } catch (error) {
    console.error("error", error);
  }
}
