"use client";

import { ReplyEditor } from "@/components/editor/reply-editor";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

import { Comments } from "../../_post-components/comments";
import { Header } from "../../_post-components/header";
import { NestedPosts } from "./_components/nested-posts";

function NestedPostPage() {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  return (
    <>
      <Header />
      <NestedPosts />
      {isTablet ? <ReplyEditor /> : null}
      <Comments />
    </>
  );
}

export default NestedPostPage;
