"use client";

import { useEffect, useRef } from "react";
import { useRouter } from "next/navigation";

import {
  isTrackReference,
  TrackReference,
  useTrackMutedIndicator,
  useTracks,
  VideoTrack,
} from "@livekit/components-react";
import { useQuery } from "@tanstack/react-query";
import { Track } from "livekit-client";

import { livestreamQueries } from "@/queries";
import { useLivestreamStore } from "@/stores/livestream";
import { cn } from "@/utils";

import { CloseOutlineIcon, ExternalLinkOutlineIcon } from "../icons";

export function MiniVideoPlayer({
  type = "desktop",
}: {
  type?: "desktop" | "mobile";
}) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const id = useLivestreamStore((state) => state.id!);
  const twitterHandle = useLivestreamStore((state) => state.twitterHandle!);

  const actions = useLivestreamStore((state) => state.actions);
  const { data } = useQuery(livestreamQueries.livestreamSimpleInfo(id));

  const router = useRouter();

  const screenShareTracks = useTracks(
    [{ source: Track.Source.ScreenShare, withPlaceholder: false }],
    {
      onlySubscribed: false,
    },
  )
    .filter(isTrackReference)
    .filter(
      (track) =>
        track.publication.source === Track.Source.ScreenShare &&
        !track.publication.isMuted,
    );

  const cameraTracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: false },
      { source: Track.Source.Microphone, withPlaceholder: false },
    ],
    {
      onlySubscribed: false,
    },
  )
    .filter(isTrackReference)
    .filter((track) => !track.publication.isMuted);

  const micTracks = useTracks(
    [{ source: Track.Source.Microphone, withPlaceholder: false }],
    {
      onlySubscribed: false,
    },
  ).filter(isTrackReference);

  const handleClose = () => {
    actions.reset();
  };

  useEffect(() => {
    cameraTracks.forEach((track) => {
      if (videoRef.current) {
        track.publication.track?.attach(videoRef.current);
      }
    });

    micTracks.forEach((track) => {
      if (videoRef.current) {
        track.publication.track?.attach(videoRef.current);
      }
    });

    return () => {
      cameraTracks.forEach((track) => {
        track.publication.track?.detach();
      });

      micTracks.forEach((track) => {
        track.publication.track?.detach();
      });
    };
  }, [cameraTracks, micTracks]);

  if (screenShareTracks.length === 0 && cameraTracks.length === 0) {
    return (
      <div
        className={cn(
          "relative flex aspect-video w-full items-center justify-center border-off-white/30 bg-dark-bk lg:rounded-xl lg:border",
          type === "mobile" && "max-h-[70px]",
        )}
      >
        <span className="text-center text-[8px] text-off-white sm:text-xs md:text-sm">
          Waiting for screen share or camera
        </span>
        {type === "desktop" ? (
          <>
            <button
              className="absolute left-2 top-2 rounded-full bg-dark-bk/75 p-1 backdrop-blur-sm"
              onClick={() => {
                router.push(`/live/${twitterHandle}`);
              }}
            >
              <ExternalLinkOutlineIcon
                className="size-5 rotate-[270deg] text-off-white"
                strokeWidth={1.5}
              />
            </button>
            <button
              className="absolute right-2 top-2 rounded-full bg-dark-bk/75 p-1 backdrop-blur-sm"
              onClick={handleClose}
            >
              <CloseOutlineIcon className="size-5 text-off-white" />
            </button>
          </>
        ) : null}
      </div>
    );
  }

  if (
    screenShareTracks.length === 0 &&
    cameraTracks.length > 0 &&
    data?.livestream.type === "PRO"
  ) {
    return (
      <video
        ref={videoRef}
        className={cn(
          "w-full bg-dark-bk lg:rounded-xl",
          type === "mobile" ? "max-h-[70px]" : "max-h-[500px]",
        )}
      />
    );
  } else if (screenShareTracks.length === 0 && cameraTracks.length > 0) {
    return (
      <div className="relative bg-[rgba(2,2,2,0.75)] lg:rounded-xl">
        {cameraTracks.map((track, index) => (
          <CustomVideoTrack
            trackRef={track}
            key={"camera-share-" + index}
            className={cn(
              "w-full lg:rounded-xl",
              type === "mobile" ? "max-h-[70px]" : "max-h-[500px]",
            )}
          />
        ))}
        {type === "desktop" ? (
          <>
            <button
              className="absolute left-2 top-2 rounded-full bg-dark-bk/75 p-1 backdrop-blur-sm"
              onClick={() => {
                router.push(`/live/${twitterHandle}`);
              }}
            >
              <ExternalLinkOutlineIcon
                className="size-5 rotate-[270deg] text-off-white"
                strokeWidth={1.5}
              />
            </button>
            <button
              className="absolute right-2 top-2 rounded-full bg-dark-bk/75 p-1 backdrop-blur-sm"
              onClick={handleClose}
            >
              <CloseOutlineIcon className="size-5 text-off-white" />
            </button>
          </>
        ) : null}
      </div>
    );
  }

  return (
    <div className="relative">
      {screenShareTracks.map((track, index) => (
        <CustomVideoTrack
          trackRef={track}
          key={"screen-share-" + index}
          className={cn(
            "w-full",
            type === "mobile" ? "max-h-[70px]" : "max-h-[500px]",
          )}
        />
      ))}
      {cameraTracks.map((track, index) => (
        <CustomVideoTrack
          trackRef={track}
          key={"camera-share-" + index}
          className={cn(
            "absolute  bg-dark-bk",
            type === "mobile"
              ? "bottom-1 right-1 max-w-[40px] rounded"
              : "bottom-2 right-2 max-w-[150px] rounded-lg",
          )}
        />
      ))}
      {type === "desktop" ? (
        <>
          <button
            className="absolute left-2 top-2 rounded-full bg-dark-bk/75 p-1 backdrop-blur-sm"
            onClick={() => {
              router.push(`/live/${twitterHandle}`);
            }}
          >
            <ExternalLinkOutlineIcon
              className="size-5 rotate-[270deg] text-off-white"
              strokeWidth={1.5}
            />
          </button>
          <button
            className="absolute right-2 top-2 rounded-full bg-dark-bk/75 p-1 backdrop-blur-sm"
            onClick={handleClose}
          >
            <CloseOutlineIcon className="size-5 text-off-white" />
          </button>
        </>
      ) : null}
    </div>
  );
}

function CustomVideoTrack({
  trackRef,
  className,
}: {
  trackRef: TrackReference;
  className?: string;
}) {
  const { isMuted } = useTrackMutedIndicator(trackRef);

  if (isMuted) return null;

  return <VideoTrack trackRef={trackRef} className={className} />;
}
