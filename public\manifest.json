{"name": "The Arena", "short_name": "The Arena", "icons": [{"src": "/assets/pwa-logo/icon-36x36.png", "sizes": "36x36", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/pwa-logo/icon-48x48.png", "sizes": "48x48", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/pwa-logo/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/pwa-logo/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/pwa-logo/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/pwa-logo/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/pwa-logo/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/pwa-logo/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/pwa-logo/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/pwa-logo/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "theme_color": "#020202", "background_color": "#020202", "start_url": "/", "display": "standalone", "orientation": "portrait"}