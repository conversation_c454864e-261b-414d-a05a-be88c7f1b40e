// Give the service worker access to Firebase Messaging.
// Note that you can only use Firebase Messaging here. Other Firebase libraries
// are not available in the service worker.
importScripts("https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js");
importScripts(
  "https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js",
);

// Initialize the Firebase app in the service worker by passing in
// your app's Firebase config object.
// https://firebase.google.com/docs/web/setup#config-object
firebase.initializeApp({
  apiKey: "AIzaSyCN83XNCAEhZtViYjmRmVbML6FxIhIlJQA",
  authDomain: "imposing-yen-405409.firebaseapp.com",
  projectId: "imposing-yen-405409",
  storageBucket: "imposing-yen-405409.appspot.com",
  messagingSenderId: "277531163698",
  appId: "1:277531163698:web:814febc98e3d891494102b",
  measurementId: "G-C5C8N1GE1X",
});

// Retrieve an instance of Firebase Messaging so that it can handle background
// messages.
const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  console.log(
    "[firebase-messaging-sw.js] Received background message ",
    payload,
  );

  // Customize notification here
  const notificationTitle = payload.data.title;
  const notificationOptions = {
    body: payload.data.body,
    icon: "/assets/pwa-logo/icon-128x128.png",
    data: {
      url: payload.data.redirectTo,
    },
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

self.addEventListener("notificationclick", function (event) {
  event.notification.close(); // Android needs explicit close.
  const redirectUrl = event.notification.data?.url || "/notifications";

  event.waitUntil(
    clients
      .matchAll({ type: "window", includeUncontrolled: true })
      .then((windowClients) => {
        for (let client of windowClients) {
          if (
            client.url.startsWith(self.location.origin) &&
            "focus" in client
          ) {
            const clientPath = new URL(client.url).pathname;
            const targetPath = redirectUrl.startsWith("/")
              ? redirectUrl
              : `/${redirectUrl}`;

            if (clientPath === targetPath) {
              return client.focus();
            } else {
              client.focus();
              return client.postMessage({
                type: "PUSH_NOTIFICATION_REDIRECT",
                url: redirectUrl,
              });
            }
          }
        }
        if (clients.openWindow) {
          return clients.openWindow(redirectUrl);
        }
      })
      .catch((error) => {
        console.error("Error in notificationclick handler:", error);
      }),
  );
});

self.addEventListener("install", (event) => {
  self.skipWaiting();
  console.log("[firebase-messaging-sw.js] Service Worker installed");
});

self.addEventListener("activate", (event) => {
  event.waitUntil(clients.claim());
  console.log("[firebase-messaging-sw.js] Service Worker activated");
});
