{"name": "<PERSON><PERSON>na-frontend-react", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "build-storybook": "storybook build", "dev": "next dev -p 4400", "format:check": "prettier \"**/*\" --ignore-unknown --list-different", "format:write": "prettier \"**/*\" --ignore-unknown --list-different --write", "lint": "eslint .  --max-warnings 0", "prepare": "husky", "start": "next start -p 4400", "storybook": "storybook dev -p 6006"}, "lint-staged": {"*": ["prettier \"**/*\" --ignore-unknown --list-different --write"], "**/*.{ts,tsx,js,jsx,cjs,mjs}": ["eslint"]}, "dependencies": {"@date-fns/tz": "^1.2.0", "@ducanh2912/next-pwa": "^10.2.6", "@dynamic-labs/ethereum": "^3.6.0", "@dynamic-labs/global-wallet": "^3.6.0", "@dynamic-labs/sdk-react-core": "^3.6.0", "@emotion/is-prop-valid": "^1.3.1", "@headlessui/react": "^2.2.0", "@hookform/resolvers": "^3.3.4", "@livekit/components-react": "^2.6.7", "@lottiefiles/dotlottie-react": "^0.6.5", "@next/third-parties": "^14.2.3", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-visually-hidden": "^1.1.0", "@snapshot-labs/snapshot.js": "^0.12.1", "@solana/spl-token": "^0.4.3", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-react-ui": "^0.9.35", "@solana/web3.js": "^1.91.4", "@stripe/crypto": "^0.0.4", "@stripe/stripe-js": "^1.54.2", "@tanstack/react-query": "^5.18.1", "@tanstack/react-query-devtools": "^5.18.1", "@tanstack/react-virtual": "^3.8.3", "@tiptap-pro/extension-file-handler": "^2.11.1", "@tiptap/extension-character-count": "^2.9.1", "@tiptap/extension-hard-break": "^2.3.0", "@tiptap/extension-history": "^2.3.0", "@tiptap/extension-placeholder": "^2.3.0", "@tiptap/pm": "^2.3.0", "@tiptap/react": "^2.3.0", "@tiptap/starter-kit": "^2.3.0", "@tiptap/suggestion": "^2.6.4", "@typeform/embed-react": "^4.1.0", "@use-gesture/react": "^10.3.0", "@web3modal/ethers": "^4.1.7", "axios": "^1.6.7", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dexie": "^3.2.6", "dexie-react-hooks": "^1.1.7", "elliptic": "^6.5.5", "embla-carousel-react": "^8.5.1", "emoji-picker-react": "^4.12.0", "ethers": "^6.11.1", "firebase": "^10.10.0", "framer-motion": "^11.0.3", "graphql-request": "^7.1.0", "isomorphic-dompurify": "^2.3.0", "livekit-client": "^2.5.10", "lodash": "^4.17.21", "lucide-react": "^0.321.0", "next": "^14.2.29", "nuqs": "^2.2.3", "paraswap-core": "^1.0.2", "posthog-js": "^1.181.0", "posthog-node": "^4.2.1", "qr-code-styling": "^1.9.1", "react": "^18", "react-aria-components": "^1.5.0", "react-canvas-confetti": "^2.0.7", "react-cookie": "^7.1.4", "react-copy-to-clipboard": "^5.1.0", "react-day-picker": "^9.4.2", "react-dom": "^18", "react-easy-crop": "^5.0.5", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.50.1", "react-hotkeys-hook": "^4.6.1", "react-intersection-observer": "^9.8.0", "react-loading-skeleton": "^3.4.0", "react-markdown": "^9.0.1", "react-qr-code": "^2.0.12", "react-virtuoso": "^4.7.8", "remark-gfm": "^4.0.0", "remeda": "^2.6.0", "sha.js": "^2.4.11", "sharp": "^0.33.3", "socket.io-client": "^4.7.4", "sonner": "^1.4.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "tippy.js": "^6.3.7", "use-long-press": "^3.2.0", "uuid": "^9.0.1", "viem": "^2.17.4", "zod": "^3.22.4", "zustand": "^4.5.1"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.3.0", "@storybook/addon-onboarding": "^9.0.0", "@storybook/nextjs": "^9.0.0", "@tiptap/extension-mention": "^2.5.8", "@types/crypto-js": "^4.2.2", "@types/dompurify": "^3.0.5", "@types/elliptic": "^6.4.18", "@types/lodash": "^4.17.13", "@types/node": "^20", "@types/react": "^18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18", "@types/sha.js": "^2.4.4", "@types/uuid": "^9.0.8", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "eslint-plugin-storybook": "^9.0.0", "husky": "^9.0.11", "lint-staged": "^15.2.7", "msw-storybook-addon": "^2.0.5", "postcss": "^8", "prettier": "3.2.4", "prettier-plugin-packagejson": "^2.5.0", "prettier-plugin-tailwindcss": "^0.5.11", "storybook": "^9.0.0", "tailwindcss": "^3.3.0", "typescript": "^5"}, "msw": {"workerDirectory": ["public"]}}