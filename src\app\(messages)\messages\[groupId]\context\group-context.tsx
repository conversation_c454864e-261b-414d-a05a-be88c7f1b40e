"use client";

import { createContext, ReactNode, useContext, useState } from "react";
import { useParams, useSearchParams } from "next/navigation";

import { useQueryClient } from "@tanstack/react-query";
import { createStore, StoreApi, useStore } from "zustand";

import { useGroupByIdQuery } from "@/queries";
import { HeaderGroupResponse } from "@/queries/types/chats";

type GroupContextType = {
  data: HeaderGroupResponse | undefined;
  isLoading: boolean;
  groupId: string;
  twitterHandle: string | null;
  updateIsRequest: (isRequest: boolean) => void;
  store: StoreApi<GroupState>;
};

interface Store {
  messageId: string;
  initialLoad: boolean;
  pinnedMessagesCount: number | null;
}

interface Actions {
  actions: {
    setMessageId: (messageId: string) => void;
    setInitialLoad: (initialLoad: boolean) => void;
    resetMessageId: () => void;
    setPinnedMessagesCount: (pinnedMessagesCount: number) => void;
  };
}

type GroupState = Store & Actions;

const GroupContext = createContext<GroupContextType | undefined>(undefined);

export function GroupProvider({ children }: { children: ReactNode }) {
  const params = useParams() as { groupId: string };
  const searchParams = useSearchParams();
  const twitterHandle = searchParams.get("twitterHandle");
  const queryClient = useQueryClient();

  const [store] = useState(() =>
    createStore<GroupState>((set, get) => ({
      messageId: "",
      initialLoad: true,
      pinnedMessagesCount: null,
      actions: {
        setMessageId: (messageId) => {
          set({ messageId, initialLoad: true });
        },
        resetMessageId: () => {
          set({ messageId: "", initialLoad: true });
        },
        setInitialLoad: (initialLoad) => {
          set({ initialLoad });
        },
        setPinnedMessagesCount: (pinnedMessagesCount) => {
          set({ pinnedMessagesCount });
        },
      },
    })),
  );

  const { data, isLoading } = useGroupByIdQuery({
    groupId: params.groupId,
    twitterHandle: twitterHandle || "",
  });

  const updateIsRequest = (isRequest: boolean) => {
    queryClient.setQueryData<HeaderGroupResponse | undefined>(
      ["chat", "group", params.groupId],
      (oldData) => {
        if (oldData && oldData.group) {
          return {
            ...oldData,
            group: { ...oldData.group, isRequest },
          };
        }
        return oldData;
      },
    );
  };

  return (
    <GroupContext.Provider
      value={{
        data,
        isLoading,
        groupId: params.groupId,
        twitterHandle,
        updateIsRequest,
        store,
      }}
    >
      {children}
    </GroupContext.Provider>
  );
}

export function useGroup() {
  const context = useContext(GroupContext);
  const data = context && context.data;
  const isLoading = context ? context.isLoading : true;
  const groupId = context && context.groupId;
  const twitterHandle = context?.twitterHandle;
  const updateIsRequest = context?.updateIsRequest;
  return { data, isLoading, groupId, twitterHandle, updateIsRequest };
}

export function useGroupStore(): GroupState;
export function useGroupStore<T>(selector: (state: GroupState) => T): T;
export function useGroupStore<T>(selector?: (state: GroupState) => T) {
  const context = useContext(GroupContext);
  if (!context) {
    throw new Error("Missing GroupContextProvider");
  }
  const store = context.store;

  return useStore(store, selector!);
}
