"use client";

import { useRef, useState } from "react";
import { useParams } from "next/navigation";

import * as DialogPrimitive from "@radix-ui/react-dialog";
import { useQueryClient } from "@tanstack/react-query";
import { AnimatePresence, motion, useMotionTemplate } from "framer-motion";
import Cropper, { Area } from "react-easy-crop";
import { v4 as uuid } from "uuid";

import { ArrowBackOutlineIcon, PencilOutlineIcon } from "@/components/icons";
import { toast } from "@/components/toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogOverlay,
  DialogPortal,
} from "@/components/ui/dialog";
import { useUserByHandleQuery } from "@/queries";
import { useUpdateBannerMutation } from "@/queries/profile-mutations";
import { User } from "@/types";
import { upload } from "@/utils";
import getCroppedImg from "@/utils/crop-image";

export const BannerPicture = () => {
  const queryClient = useQueryClient();
  const params = useParams() as { userHandle: string };
  const inputRef = useRef<HTMLInputElement>(null);
  const [open, setOpen] = useState(false);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [previewURL, setPreviewURL] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const width = useMotionTemplate`${progress}%`;

  const { data, isLoading } = useUserByHandleQuery(params.userHandle);
  const { mutateAsync: updateBanner } = useUpdateBannerMutation({
    onSuccess: (data, { bannerUrl }) => {
      toast.green("Banner picture updated!");
      queryClient.setQueryData(
        ["user", "handle", params.userHandle],
        (old: { user: User }) => {
          return {
            ...old,
            user: {
              ...old.user,
              bannerUrl,
            },
          };
        },
      );
      resetUploading();
      setOpen(false);
    },
  });

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.danger("Uploaded image file cannot exceed 5 MB");
        return;
      }

      setOpen(true);
      const previewURL = URL.createObjectURL(file);
      setPreviewURL(previewURL);
    }
  };

  const resetUploading = () => {
    setIsUploading(false);
    setProgress(0);
    setPreviewURL(null);
  };

  const handleApply = async () => {
    if (!croppedAreaPixels || !previewURL) return;

    const blob = await getCroppedImg(previewURL, croppedAreaPixels);

    if (!blob) return;

    // @ts-expect-error
    blob.name = "image.jpeg";
    // @ts-expect-error
    blob.lastModified = new Date();

    const file = new File([blob], `${uuid()}.jpg`, { type: "image/jpeg" });

    setIsUploading(true);
    try {
      const res = await upload({
        file,
        onProgressChange: (progress) => {
          setProgress(progress);
        },
      });

      await updateBanner({ bannerUrl: res.url });
    } catch (error) {
      console.error(error);
      toast.danger("File upload failed");
      resetUploading();
      setOpen(false);
    }
  };

  return (
    <>
      <div className="relative">
        <div className="relative aspect-[3/1] w-full overflow-hidden">
          {!isLoading && data && (
            <img
              src={data?.user.bannerUrl}
              alt=""
              className="absolute inset-0 object-cover object-center"
            />
          )}
        </div>
        <div className="absolute -bottom-4 right-6">
          <Button
            className="size-[44px] items-center justify-center p-0"
            onClick={() => {
              !isUploading && inputRef.current && inputRef.current.click();
            }}
            disabled={isUploading || isLoading}
          >
            <PencilOutlineIcon className="size-6 text-off-white" />
          </Button>
          <input
            ref={inputRef}
            style={{ display: "none" }}
            type="file"
            accept="image/*"
            onChange={handleChange}
          />
        </div>
      </div>
      <div className="px-5">
        <Avatar className="mt-3 size-[92px] border border-[#E8E8E8]">
          <AvatarImage src={data?.user.twitterPicture} />
          <AvatarFallback />
        </Avatar>
      </div>
      <Dialog
        open={open}
        onOpenChange={(open) => {
          setOpen(open);
          !open && resetUploading();
        }}
      >
        <DialogPortal>
          <DialogOverlay />
          <DialogPrimitive.Content className="pt-pwa fixed left-[50%] top-[50%] z-50 grid h-full w-full max-w-[524px] translate-x-[-50%] translate-y-[-50%] flex-col gap-4 overflow-hidden bg-dark-bk px-0 pb-0 shadow-lg backdrop-blur-sm  duration-200  sm:rounded-[20px] sm:border sm:border-[rgba(59,59,59,0.30)] sm:bg-[rgba(15,15,15,0.90)] sm:backdrop-blur-sm lg:h-[500px]">
            <div className="top-pwa absolute inset-x-0 z-10 flex justify-between  py-1 pl-6 pr-3">
              <DialogClose>
                <ArrowBackOutlineIcon className="z-10 size-5 text-off-white" />
              </DialogClose>
              <Button
                variant="ghost"
                onClick={handleApply}
                disabled={isUploading}
              >
                Apply
              </Button>
            </div>
            <Cropper
              showGrid={false}
              maxZoom={2}
              image={previewURL || undefined}
              crop={crop}
              zoom={zoom}
              aspect={3 / 1}
              onCropChange={setCrop}
              onZoomChange={setZoom}
              onCropComplete={(croppedArea, croppedAreaPixels) => {
                setCroppedAreaPixels(croppedAreaPixels);
              }}
            />
            <AnimatePresence>
              {isUploading && (
                <motion.div
                  style={{ width }}
                  exit={{ opacity: 0 }}
                  className="absolute bottom-0 left-0 h-1 min-w-4 bg-brand-orange"
                />
              )}
            </AnimatePresence>
          </DialogPrimitive.Content>
        </DialogPortal>
      </Dialog>
    </>
  );
};
