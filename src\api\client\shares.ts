import { axios } from "@/lib/axios";
import { SharesHoldersResponse, SharesHoldingsResponse } from "@/queries/types";

export const getSharesStats = async ({ userId }: { userId: string }) => {
  const searchParams = new URLSearchParams({
    userId,
  });

  const response = await axios.get(`/shares/stats?${searchParams.toString()}`);
  return response.data;
};

interface GetSharesHoldersParams {
  page: number;
  pageSize: number;
  userId?: string;
}

export const getSharesHolders = async ({
  userId,
  page,
  pageSize,
}: GetSharesHoldersParams) => {
  const searchParams = userId
    ? new URLSearchParams({
        userId: userId,
        page: page.toString(),
        pageSize: pageSize.toString(),
      })
    : new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

  const response = await axios.get<SharesHoldersResponse>(
    `/shares/holders?${searchParams.toString()}`,
  );

  return response.data;
};

interface GetSharesHoldingsParams {
  page: number;
  pageSize: number;
}

export const getSharesHoldings = async ({
  page,
  pageSize,
}: GetSharesHoldingsParams) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<SharesHoldingsResponse>(
    `/shares/holdings?${searchParams.toString()}`,
  );
  return response.data;
};
