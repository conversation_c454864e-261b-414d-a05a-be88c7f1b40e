import { StateSnapshot, VirtuosoHandle } from "react-virtuoso";
import { create } from "zustand";

interface Store {
  followingSnapshot?: StateSnapshot;
  trendingSnapshot?: StateSnapshot;
  trenchesSnapshot?: StateSnapshot;
  followingTimelineRef: { current: VirtuosoHandle | null };
  trendingTimelineRef: { current: VirtuosoHandle | null };
  trenchesTimelineRef: { current: VirtuosoHandle | null };
}

interface Actions {
  setFollowingSnapshot: (followingSnapshot?: StateSnapshot) => void;
  setTrendingSnapshot: (trendingSnapshot?: StateSnapshot) => void;
  setTrenchesSnapshot: (trenchesSnapshot?: StateSnapshot) => void;
}

export const useHomeStore = create<Store & Actions>((set) => ({
  followingTimelineRef: {
    current: null,
  },
  trendingTimelineRef: {
    current: null,
  },
  trenchesTimelineRef: {
    current: null,
  },
  followingSnapshot: undefined,
  trendingSnapshot: undefined,
  trenchesSnapshot: undefined,
  setFollowingSnapshot: (followingSnapshot) => set({ followingSnapshot }),
  setTrendingSnapshot: (trendingSnapshot) => set({ trendingSnapshot }),
  setTrenchesSnapshot: (trenchesSnapshot) => set({ trenchesSnapshot }),
}));
