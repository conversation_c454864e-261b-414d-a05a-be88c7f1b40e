import { useMemo, useState } from "react";
import { useParams } from "next/navigation";

import { TipPartyContent } from "@/app/(messages)/messages/_components/TipPartyContent";
import { TippingInfoModal } from "@/components/tipping-info";
import { TipFormContent } from "@/components/tipping/tip-form-content";
import { ArenaDialogHeader } from "@/components/ui/arena-dialog-header";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import { useUserByHandleQuery } from "@/queries";
import { useTippableCurrenciesQuery } from "@/queries/currency-queries";
import { useUser } from "@/stores";
import { cn } from "@/utils/cn";

enum TipOption {
  TipTokens = "TipTokens",
  TipTickets = "TipTickets",
}

export const TipModal = ({
  userHandle: userHandleProp,
}: {
  userHandle?: string;
}) => {
  const [open, setOpen] = useState(false);
  const [option, setOption] = useState<TipOption>(TipOption.TipTokens);
  const [step, setStep] = useState<1 | 2>(1);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  const { user } = useUser();

  const params = useParams() as { userHandle?: string };
  const userHandle = userHandleProp ?? params.userHandle;
  const { data } = useUserByHandleQuery(userHandle!);

  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useTippableCurrenciesQuery();

  const { sortedCurrencies } = useWalletCurrencies({
    user,
    currenciesData,
    isCurrenciesLoading,
  });

  if (!data?.user) return null;

  const recepients = useMemo(
    () => [
      {
        id: data.user.id,
        name: data.user.twitterName,
        avatar: data.user.twitterPicture,
        username: data.user.twitterHandle,
        twitterHandle: data.user.twitterHandle,
        twitterPicture: data.user.twitterPicture,
        twitterName: data.user.twitterName,
        address: data.user.address,
      },
    ],
    [data.user],
  );

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
        if (!open) setStep(1);
      }}
    >
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          <span className="mr-2">
            {/* You can use a tip icon here if you want */}
          </span>
          Tip
        </Button>
      </DialogTrigger>
      <DialogContent
        className={cn(
          "flex h-full w-full flex-grow flex-col bg-dark-bk  px-6 pt-0 backdrop-blur-sm sm:h-fit sm:w-[420px] sm:gap-0 sm:bg-[#1A1A1A] sm:p-6",
          isKeyboardVisible
            ? "flex-grow-0 justify-end pb-[calc(142px+env(safe-area-inset-bottom))]"
            : "flex-grow justify-between",
        )}
      >
        {" "}
        <ArenaDialogHeader
          title="Send a Tip"
          showBack={true}
          onBack={step === 2 ? () => setStep(1) : () => setOpen(false)}
          className="mb-12 sm:mb-8"
        />
        {step === 1 && (
          <TipPartyContent
            options={[
              {
                value: TipOption.TipTokens,
                label: (
                  <>
                    Tip <br /> Tokens
                  </>
                ),
                emoji: "🪙",
              },
              {
                value: TipOption.TipTickets,
                label: (
                  <>
                    Tip <br /> Tickets
                  </>
                ),
                emoji: "🎟️",
              },
            ]}
            selected={option}
            setSelected={(v) => setOption(v as TipOption)}
            infoText={
              <div className="mt-2 text-center text-gray-text">
                <h4 className="text-sm">
                  {option === TipOption.TipTokens
                    ? "Tip using any token in your Arena Wallet"
                    : "Gift user's tickets you currently hold"}
                </h4>
              </div>
            }
            onContinue={() => setStep(2)}
            continueLabel="Continue"
          />
        )}
        {step === 2 && option === TipOption.TipTokens && (
          <TipFormContent
            recepients={recepients}
            sortedCurrencies={sortedCurrencies}
            setOpen={setOpen}
            buttonLabel="Send tip"
          />
        )}
        <TippingInfoModal />
      </DialogContent>
    </Dialog>
  );
};
