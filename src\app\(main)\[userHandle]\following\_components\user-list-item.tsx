"use client";

import { InfiniteD<PERSON>, Query<PERSON><PERSON>, useQueryClient } from "@tanstack/react-query";

import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useFollowMutation, useUnfollowMutation } from "@/queries";
import { Following, FollowingResponse } from "@/queries/types/follow";
import { useUser } from "@/stores";

export const UserListItem = ({
  following,
  followedByloggedInUser,
}: Following) => {
  const { user: me } = useUser();
  const queryClient = useQueryClient();
  const { mutateAsync: follow } = useFollowMutation({
    onMutate: async () => {
      toast.green(`You're now following ${following.twitterName}!`);

      const previousFollowersLists = queryClient.getQueriesData({
        queryKey: ["following", "list"],
      });

      queryClient.setQueriesData<InfiniteData<FollowingResponse>>(
        {
          queryKey: ["following", "list"],
        },
        (old) => {
          if (!old) return old;
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                followingsWithFollowedByloggedInUser:
                  page.followingsWithFollowedByloggedInUser.map((f) => {
                    if (f.following.id === following.id) {
                      return {
                        ...f,
                        followedByloggedInUser: true,
                      };
                    }
                    return f;
                  }),
              };
            }),
          };
        },
      );

      return { previousFollowersLists };
    },
    onError(err, variables, context) {
      toast.danger(`Failed to follow ${following.twitterName}.`);
      context?.previousFollowersLists?.map((key: QueryKey, value: unknown) => {
        queryClient.setQueryData(key, value);
      });
    },
  });

  const { mutateAsync: unfollow } = useUnfollowMutation({
    onMutate: async () => {
      const previousFollowersLists = queryClient.getQueriesData({
        queryKey: ["following", "list"],
      });

      queryClient.setQueriesData<InfiniteData<FollowingResponse>>(
        {
          queryKey: ["following", "list"],
        },
        (old) => {
          if (!old) return old;
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                followingsWithFollowedByloggedInUser:
                  page.followingsWithFollowedByloggedInUser.map((f) => {
                    if (f.following.id === following.id) {
                      return {
                        ...f,
                        followedByloggedInUser: false,
                      };
                    }
                    return f;
                  }),
              };
            }),
          };
        },
      );

      return { previousFollowersLists };
    },
    onError(err, variables, context) {
      toast.danger(`Failed to unfollow ${following.twitterName}.`);
      context?.previousFollowersLists?.map((key: QueryKey, value: unknown) => {
        queryClient.setQueryData(key, value);
      });
    },
  });

  function handleFollow() {
    if (followedByloggedInUser) {
      unfollow({ userId: following.id });
    } else {
      follow({ userId: following.id });
    }
  }

  return (
    <div className="flex w-full justify-between gap-4 px-6 py-4">
      <div className="flex flex-grow items-center gap-[10px] overflow-hidden">
        <ProgressBarLink href={`/${following.twitterHandle}`}>
          <Avatar className="size-[42px]">
            <AvatarImage src={following.twitterPicture} />
            <AvatarFallback />
          </Avatar>
        </ProgressBarLink>
        <div className="flex w-full min-w-0  flex-col gap-1 text-sm leading-4">
          <div className="flex gap-1.5">
            <ProgressBarLink
              href={`/${following.twitterHandle}`}
              className="truncate text-[#F4F4F4]"
            >
              {following.twitterName}
            </ProgressBarLink>
          </div>
          <ProgressBarLink
            href={`/${following.twitterHandle}`}
            className="truncate text-[#808080]"
          >
            @{following.twitterHandle}
          </ProgressBarLink>
        </div>
      </div>
      {me?.id !== following.id && (
        <div className="flex flex-shrink-0 flex-col items-end justify-center gap-[2px]">
          <Button
            variant={followedByloggedInUser ? "outline" : "secondary"}
            onClick={handleFollow}
            className="h-[34px] w-24"
          >
            {followedByloggedInUser ? "Unfollow" : "Follow"}
          </Button>
        </div>
      )}
    </div>
  );
};
