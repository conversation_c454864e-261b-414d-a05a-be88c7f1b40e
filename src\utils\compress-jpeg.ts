import imageCompression from "browser-image-compression";

export async function compressImageToJpeg(
  file: File,
  onProgress: (progress: any) => void | undefined,
) {
  const options = {
    maxSizeMB: 0.5,
    maxWidthOrHeight: 2000,
    quality: 0.85,
    useWebWorker: true,
    fileType: "image/jpeg",
    onProgress,
  };

  try {
    return await imageCompression(file, options);
  } catch (error) {
    console.error("Compression error:", error);
  }
}
