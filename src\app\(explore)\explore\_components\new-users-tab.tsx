"use client";

import { memo } from "react";

import { Virtuoso } from "react-virtuoso";

import { UserListItemLoadingSkeleton } from "@/components/user-list-item-loading-skeleton";
import { useNewUsersQuery } from "@/queries";

import { UserItem } from "./user-item";

export const NewUsersTab = memo(function NewUsersTab() {
  const { data, isLoading } = useNewUsersQuery();

  if (isLoading) {
    return (
      <>
        {Array.from({ length: 15 }).map((_, index) => {
          return <UserListItemLoadingSkeleton key={index} />;
        })}
      </>
    );
  }

  return (
    <Virtuoso
      useWindowScroll
      data={data?.users || []}
      increaseViewportBy={500}
      itemContent={(index, user) => {
        return <UserItem user={user} />;
      }}
    />
  );
});
