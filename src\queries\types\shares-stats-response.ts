export interface SharesStatsResponse {
  totalHoldings: string;
  totalHolders: string;
  holdingsByUser: number;
  portfolioValue: string;
  stats: {
    buys: number;
    sells: number;
    feesPaid: string;
    feesEarned: string;
    amountSpent: string;
    amountEarned: string;
    referralsEarned: string;
    distributedToShareholders: string;
    badgeType: number;
    id: string;
    userId: string;
    supply: string;
    volume: string;
    keyPrice: string;
  };
  badges: [];
  hasReferral: boolean;
}
