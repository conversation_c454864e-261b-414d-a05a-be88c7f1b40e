"use server";

import { cookies } from "next/headers";

import { env } from "@/env";
import { axios } from "@/lib/axios";
import { OAuth2SchemaType } from "@/schemas";

export async function login({ code, state }: OAuth2SchemaType) {
  try {
    const ref = cookies().get("__ref");

    const queryParams = new URLSearchParams({
      authCode: code,
      authState: state,
      referrer: ref?.value || "",
      callbackUrl: `${env.NEXT_PUBLIC_APP_DOMAIN}/twitter/auth`,
    });

    const { data } = await axios.get(
      `/twitter/callback?${queryParams.toString()}`,
    );

    if (data.errorCode) {
      throw data;
    }

    await axios.post(`/term-of-use/approve`, undefined, {
      headers: {
        Authorization: `Bearer ${data.token}`,
      },
    });

    // 30 days
    const expires = new Date(Date.now() + 30 * 1000 * 60 * 60 * 24);

    cookies().set("token", data.token, { httpOnly: true, expires });
    cookies().set(
      "user",
      JSON.stringify({
        ...data.user,
        loggedInAt: new Date(),
      }),
      {
        httpOnly: true,
        expires,
      },
    );
    cookies().set("twitterUser", JSON.stringify(data.twitterUser), {
      httpOnly: true,
      expires,
    });

    return { data };
  } catch (error: any) {
    return { error: error.message };
  }
}
