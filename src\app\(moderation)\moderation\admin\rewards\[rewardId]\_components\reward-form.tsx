"use client";

import { FC, memo, useCallback, useRef, useState } from "react";
import Image from "next/image";

import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import * as z from "zod";

import { RewardCurrencyStatusEnum } from "@/api/client/admin-rewards";
import { ImageOutlineIcon } from "@/components/icons/image-outline";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TextInput } from "@/components/ui/text-input";
import { compressImageToJpeg } from "@/utils/compress-jpeg";
import { upload } from "@/utils/upload";

export const rewardSchema = z.object({
  name: z.string().min(1, "Name is required"),
  symbol: z.string().min(1, "Symbol is required"),
  contractAddress: z.string().optional(),
  amount: z.number().min(0).optional(),
  decimals: z.number().min(0).max(18).default(18),
  image: z.string().default(""),
  status: z
    .nativeEnum(RewardCurrencyStatusEnum)
    .default(RewardCurrencyStatusEnum.UPCOMING),
});

export type RewardFormData = z.infer<typeof rewardSchema>;

export interface RewardFormProps {
  defaultValues?: Partial<RewardFormData>;
  onSubmit: (data: RewardFormData) => Promise<void>;
  isSubmitting?: boolean;
  existingImage?: string;
  children?: React.ReactNode;
}

export const RewardForm: FC<RewardFormProps> = memo(
  ({
    defaultValues = {},
    onSubmit,
    isSubmitting = false,
    existingImage,
    children,
  }) => {
    const [uploadProgress, setUploadProgress] = useState<number>(0);
    const [isUploading, setIsUploading] = useState<boolean>(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const initialValues: RewardFormData = {
      name: "",
      symbol: "",
      contractAddress: "",
      amount: 0,
      decimals: 18,
      image: "",
      status: RewardCurrencyStatusEnum.UPCOMING,
      ...defaultValues,
    };

    const {
      control,
      handleSubmit,
      formState: { errors },
      setValue,
      watch,
    } = useForm<RewardFormData>({
      resolver: zodResolver(rewardSchema),
      defaultValues: initialValues,
    });

    const imageUrl = watch("image");

    const handleImageClick = useCallback(() => {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    }, []);

    const handleImageUpload = useCallback(
      async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        setIsUploading(true);
        setUploadProgress(0);
        let fileToUpload = files[0];

        try {
          if (
            fileToUpload.type.includes("image") &&
            (fileToUpload.type.includes("image/jpeg") ||
              fileToUpload.type.includes("image/png")) &&
            fileToUpload.size > 500 * 1024
          ) {
            const compressed = await compressImageToJpeg(
              fileToUpload,
              (progress) => {
                setUploadProgress(progress * 0.5);
              },
            );
            if (compressed) {
              fileToUpload = compressed;
            }
          }

          const uploadResult = await upload({
            file: fileToUpload,
            onProgressChange: (progress) => {
              setUploadProgress(50 + progress * 0.5);
            },
          });

          setValue("image", uploadResult.url);
          toast.green("Image uploaded successfully");
        } catch (error) {
          toast.danger(
            error instanceof Error ? error.message : "Failed to upload image",
          );
        } finally {
          setIsUploading(false);
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        }
      },
      [setValue],
    );

    const handleFormSubmit = useCallback(
      async (data: RewardFormData) => {
        await onSubmit(data);
      },
      [onSubmit],
    );

    return (
      <form onSubmit={handleSubmit(handleFormSubmit)} className="flex flex-col">
        <div className="mb-8 flex flex-col items-start">
          <input
            type="file"
            accept="image/*"
            ref={fileInputRef}
            className="hidden"
            onChange={handleImageUpload}
          />
          <div
            className="bg-black relative flex h-[84px] w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-full border-[1.5px] border-gray-text"
            onClick={handleImageClick}
          >
            {imageUrl ? (
              <Image
                src={imageUrl}
                alt="Reward Logo"
                className="h-full w-full object-cover"
                width={84}
                height={84}
              />
            ) : existingImage ? (
              <Image
                src={existingImage}
                alt="Reward Logo"
                className="h-full w-full object-cover"
                width={84}
                height={84}
              />
            ) : (
              <ImageOutlineIcon className="h-8 w-8 text-gray-text" />
            )}
            {isUploading && (
              <div className="bg-black absolute inset-0 flex items-center justify-center bg-opacity-50">
                <div className="text-xs text-white">
                  {Math.round(uploadProgress)}%
                </div>
              </div>
            )}
          </div>
          <span className="text-gray-400 mt-4 text-sm font-medium uppercase">
            {existingImage ? "UPDATE PHOTO" : "ADD A PHOTO"}
          </span>
        </div>

        <div className="space-y-6">
          <div>
            <Label className="text-gray-400 text-xs font-medium uppercase">
              TICKER
            </Label>
            <Controller
              name="symbol"
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  errorMessage={errors.symbol?.message}
                  placeholder="$TOKEN"
                  className="mt-2 bg-[#111] text-off-white"
                />
              )}
            />
          </div>

          <div>
            <Label className="text-gray-400 text-xs font-medium uppercase">
              TOKEN NAME
            </Label>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  errorMessage={errors.name?.message}
                  placeholder="token name"
                  className="mt-2 bg-[#111] text-off-white"
                />
              )}
            />
          </div>

          <div>
            <Label className="text-gray-400 text-xs font-medium uppercase">
              AMOUNT TO BE DISTRIBUTED
            </Label>
            <Controller
              name="amount"
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  type="number"
                  value={field.value?.toString() || ""}
                  onChange={(e) =>
                    field.onChange(parseInt(e.target.value) || 0)
                  }
                  errorMessage={errors.amount?.message}
                  placeholder="Enter amount"
                  className="mt-2 bg-[#111] text-off-white"
                />
              )}
            />
          </div>

          <div>
            <Label className="text-gray-400 text-xs font-medium uppercase">
              REWARD PHASE
            </Label>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className="mt-2 bg-[#111] text-off-white">
                    <SelectValue placeholder="Select phase" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={RewardCurrencyStatusEnum.UPCOMING}>
                      Upcoming Rewards
                    </SelectItem>
                    <SelectItem value={RewardCurrencyStatusEnum.PREVIOUS}>
                      Previous Rewards
                    </SelectItem>
                    <SelectItem value={RewardCurrencyStatusEnum.STAKING}>
                      Staking
                    </SelectItem>
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {children}
        </div>

        <Button
          type="submit"
          variant="default"
          className="mt-8"
          disabled={
            isSubmitting || !imageUrl || !watch("name") || !watch("symbol")
          }
        >
          {isSubmitting
            ? "Saving..."
            : Object.keys(defaultValues).length > 0
              ? "Save"
              : "Add Reward"}
        </Button>
      </form>
    );
  },
);

RewardForm.displayName = "RewardForm";
