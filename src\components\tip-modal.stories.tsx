import React from "react";

import { Toaster } from "sonner";

import { Thread } from "@/types";

import { DynamicProvider } from "../mocks/dynamic-context-mock";
import { TipModal } from "./tip-modal";
import { mockCurrencies } from "./tipping/tip-currency-selector.stories";
import { Button } from "./ui/button";

const mockThread: Thread = {
  user: {
    twitterName: "Jason Desimone ⚔️",
    twitterHandle: "jasonmdesimone",
    twitterPicture:
      "https://static.starsarena.com/uploads/b4bebd39-ec9a-c996-2191-00334d0f30361745662820400.png",
  },
} as Thread;

export default {
  title: "Modals/TipModal (by thread)",
  component: TipModal,
  decorators: [
    (Story: any) => (
      <DynamicProvider>
        <Story />
        <Toaster position="top-center" />
      </DynamicProvider>
    ),
  ],
};

export const Default = () => {
  return (
    <TipModal thread={mockThread} sortedCurrencies={mockCurrencies}>
      <Button>Open Tip Modal</Button>
    </TipModal>
  );
};
