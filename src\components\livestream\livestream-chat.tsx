"use client";

import {
  memo,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { ReceivedChatMessage, useRoomInfo } from "@livekit/components-react";
import Document from "@tiptap/extension-document";
import HardBreak from "@tiptap/extension-hard-break";
import History from "@tiptap/extension-history";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import { EditorContent, useEditor } from "@tiptap/react";
import EmojiPicker, { Emoji, Theme } from "emoji-picker-react";
import DOMPurify from "isomorphic-dompurify";
import { ErrorBoundary } from "react-error-boundary";
import { Virtuoso, VirtuosoHandle } from "react-virtuoso";

import { TippingInfo } from "@/queries/types/livestream";
import { cn, numberFormatter } from "@/utils";
import { IS_ANDROID, IS_IOS } from "@/utils/window-environment";

import {
  ArrowDownFilledIcon,
  CrownFilledIcon,
  SendOutlineIcon,
  SmileOutlineIcon,
  TicketOutlineIcon,
  TipOutlineIcon,
  UserAddOutlineIcon,
} from "../icons";
import { ProgressBarLink } from "../progress-bar";
import { toast } from "../toast";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Role, ROLE_NAMES, ROLES } from "./constants";
import { LivestreamUserProfileModal } from "./livestream-user-profile-modal";
import { useDataChannelsContext } from "./stores/data-channels-context";

export const LivestreamChatContainer = memo(() => {
  return (
    <ErrorBoundary fallback={null}>
      <LivestreamChat />
    </ErrorBoundary>
  );
});

LivestreamChatContainer.displayName = "LivestreamChatContainer";

export const LivestreamChat = () => {
  const { send, chatMessages, isSending } = useDataChannelsContext().chat;
  const parentRef = useRef<HTMLDivElement>(null);
  const virtuoso = useRef<VirtuosoHandle | null>(null);
  const [showScrollToBottomButton, setShowScrollToBottomButton] =
    useState(false);

  const extensions = [
    Document.extend({
      addKeyboardShortcuts() {
        return {
          Enter: () => {
            if (IS_IOS || IS_ANDROID) {
              // If on mobile, insert a hard break instead of sending the message
              this.editor.commands.insertContent("<br/>");
              return true;
            }
            if (this.editor.getText().trim() === "") {
              return true;
            }
            const text = this.editor.getHTML();
            let _message = text.replace(/<p>/g, "").replace(/<\/p>/g, "");
            this.editor.chain().focus().clearContent().run();
            _message = DOMPurify.sanitize(_message);

            const message = {
              type: "text",
              message: _message,
            };
            virtuoso.current?.scrollToIndex({
              index: "LAST",
              behavior: "smooth",
            });
            send(JSON.stringify(message));
            return true;
          },
        };
      },
    }),
    Text,
    Paragraph,
    HardBreak,
    History,
    Placeholder.configure({
      placeholder: ({ editor }) => {
        return (
          editor.options.element.getAttribute("data-placeholder") ??
          "Type something..."
        );
      },
    }),
  ];

  const editor = useEditor({
    immediatelyRender: false,
    extensions,
    editorProps: {
      attributes: {
        class:
          "focus:outline-none h-auto max-h-20 w-full overflow-y-auto py-4 pl-5 pr-11 text-sm",
      },
    },
  });

  const handleSend = async () => {
    if (!editor || editor?.getText().trim() === "") return true;

    let originalHTML = "";
    let _message = "";

    try {
      const text = editor.getHTML();
      originalHTML = text;

      _message = text.replace(/<p>/g, "").replace(/<\/p>/g, "");
      editor.chain().focus().clearContent().run();

      _message = DOMPurify.sanitize(_message);

      const message = {
        type: "text",
        message: _message,
      };

      virtuoso.current?.scrollToIndex({
        index: "LAST",
        behavior: "smooth",
      });

      await send(JSON.stringify(message));
    } catch (error) {
      console.error("Failed to send chat message:", error);
      toast.danger("Failed to send message. Please try again.");
      if (editor && originalHTML) {
        editor.commands.setContent(originalHTML, false);
      }
    }
  };

  useEffect(() => {
    if ((!IS_IOS && !IS_ANDROID) || typeof window === "undefined") {
      return undefined;
    }

    const { visualViewport } = window;
    if (!visualViewport) {
      return undefined;
    }

    const handleResize = () => {
      const isFixNeeded =
        visualViewport.height < document.documentElement.clientHeight;

      parentRef.current?.parentElement?.parentElement?.parentElement?.parentElement?.classList.toggle(
        "tbottom-stage-pwa",
        isFixNeeded,
      );
    };

    visualViewport.addEventListener("resize", handleResize);

    return () => {
      visualViewport.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <div className={cn("flex h-full flex-grow flex-col")}>
      <Virtuoso
        ref={virtuoso}
        atBottomStateChange={(atBottom) => {
          setShowScrollToBottomButton(!atBottom);
        }}
        atBottomThreshold={100}
        className="h-full flex-grow overscroll-contain py-6"
        data={chatMessages}
        itemContent={(index, message) => (
          <ErrorBoundary fallback={null} key={message.id ?? index}>
            <Message message={message} />
          </ErrorBoundary>
        )}
        initialTopMostItemIndex={chatMessages.length - 1}
        alignToBottom
        followOutput="smooth"
        components={{
          Header,
          Footer,
        }}
      />
      <div className="relative flex flex-shrink-0 flex-col gap-4 bg-[#141414] bg-opacity-[0.88] py-1 pr-2 backdrop-blur-sm">
        <div className="flex items-end gap-1">
          <div className="relative flex min-w-0 flex-grow  items-center bg-transparent">
            {!(IS_ANDROID || IS_IOS) && (
              <>
                <Popover modal>
                  <PopoverTrigger className="ml-4">
                    <SmileOutlineIcon
                      className="size-7 text-[#E0E0E0]"
                      strokeWidth={1.5}
                    />
                  </PopoverTrigger>
                  <PopoverContent
                    align="center"
                    collisionPadding={{
                      right: 12,
                      left: 12,
                    }}
                    className="flex max-h-[420px] w-full flex-col overflow-y-auto rounded-lg border-none bg-chat-bubble p-0 text-left shadow-[4px_4px_4px_0px_rgba(0,0,0,0.50)] sm:w-[300px]"
                  >
                    <EmojiPicker
                      reactionsDefaultOpen={false}
                      theme={Theme.DARK}
                      height={300}
                      width="100%"
                      onEmojiClick={(emoji) => {
                        editor?.chain().insertContent(emoji.emoji).run();
                      }}
                      previewConfig={{ showPreview: false }}
                      className="!bg-chat-bubble [&_h2]:bg-chat-bubble/95"
                    />
                  </PopoverContent>
                </Popover>
              </>
            )}
            <EditorContent editor={editor} className="w-full" />
          </div>
          <button
            className="mb-1 flex size-[45px] flex-shrink-0 items-center justify-center"
            disabled={isSending}
            onClick={() => {
              handleSend();
            }}
          >
            <SendOutlineIcon className="size-6 text-off-white" />
          </button>
        </div>
        <Button
          onClick={() => {
            virtuoso.current?.scrollToIndex({
              index: "LAST",
            });
          }}
          variant="outline"
          className={cn(
            "absolute -top-14 left-1/2 size-10 -translate-x-1/2 border-dark-gray/50 bg-dark-bk p-0 transition-opacity duration-200 hover:bg-dark-bk",
            showScrollToBottomButton
              ? "opacity-100"
              : "pointer-events-none  opacity-0",
          )}
        >
          <ArrowDownFilledIcon className="size-5 text-off-white" />
        </Button>
      </div>
      {/* <div className="pointer-events-none absolute inset-x-0 top-0 z-20 h-[100px] bg-chat-overlay-gradient" /> */}
    </div>
  );
};

const Header = () => {
  return <div className="h-16" />;
};

const Footer = () => {
  return <div className="h-6" />;
};

const Message = ({ message }: { message: ReceivedChatMessage }) => {
  const [isOpen, setIsOpen] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);
  const [isMultiline, setIsMultiline] = useState(false);

  const roomInfo = useRoomInfo();
  const metadata = roomInfo.metadata
    ? JSON.parse(roomInfo.metadata)
    : { tippingInfo: {} };

  const currentUser = {
    id: message.from?.attributes?.id ?? "",
    name: message.from?.attributes?.name ?? "",
    avatar: message.from?.attributes?.avatar ?? "",
    username: message.from?.attributes?.username ?? "",
    role: message.from?.attributes?.role ?? "",
  };

  const supporterStatus = useMemo(() => {
    const tippingInfo = metadata.tippingInfo as TippingInfo;
    const topCreatorTippers = (tippingInfo?.topCreatorTippers ?? []).filter(
      (tipper) => tipper != null,
    );

    if (!topCreatorTippers?.length) return null;

    if (topCreatorTippers[0]?.user?.id === currentUser?.id) {
      return "king";
    }

    if (
      topCreatorTippers.some(
        (tipper) => tipper && tipper?.user?.id === currentUser?.id,
      )
    ) {
      return "supporter";
    }

    return null;
  }, [metadata, currentUser]);

  let messageData: ReceivedChatMessageType | null = null;
  // const messageData: ReceivedChatMessageType = {
  //   type: "tip",
  //   data: {
  //     amount: 1.34,
  //     currency: "AVAX",
  //     to: {
  //       id: "123",
  //       name: "Jason Desimone",
  //       avatar: "https://avatars.githubusercontent.com/u/123?v=4",
  //       username: "jasonmdesimone",
  //     },
  //   },
  // };
  try {
    messageData = JSON.parse(message.message) as ReceivedChatMessageType;
  } catch (error) {
    console.error(
      "Failed to parse chat message:",
      error,
      "Raw message:",
      message.message,
    );
    // Returning null will prevent this specific message from rendering
    return null;
  }

  const handleProfileClick = () => {
    setIsOpen(true);
  };

  useLayoutEffect(() => {
    const checkLineCount = () => {
      if (textRef.current) {
        const lineHeight = parseInt(
          window.getComputedStyle(textRef.current).lineHeight,
        );
        const height = textRef.current.offsetHeight;
        setIsMultiline(height > lineHeight * 1.5); // Use 1.5 to account for potential fractional values
      }
    };

    checkLineCount();
    window.addEventListener("resize", checkLineCount);

    return () => {
      window.removeEventListener("resize", checkLineCount);
    };
  }, [message.message]);

  if (!messageData) return null;

  return (
    <li className="flex flex-col gap-1 px-3 py-0.5">
      <div
        className={cn(
          "flex w-full gap-2 px-2 py-1.5",
          isMultiline || messageData.type === "tip-party"
            ? "items-start"
            : "items-center",
          (currentUser.role === ROLES.HOST ||
            currentUser.role === ROLES.MODERATOR) &&
            "items-start rounded-[10px] bg-chat-bubble",
          (messageData.type === "followed" ||
            messageData.type === "bought-ticket" ||
            messageData.type === "tip") &&
            "gap-3",
        )}
      >
        <LivestreamUserProfileModal
          user={currentUser}
          open={isOpen}
          setOpen={setIsOpen}
        >
          <span role="button" className="relative" onClick={handleProfileClick}>
            <Avatar className="size-[30px] flex-shrink-0">
              <AvatarImage src={currentUser.avatar} />
              <AvatarFallback />
            </Avatar>
            {messageData.type === "followed" && (
              <div className="absolute -right-2 bottom-0 flex size-[21px] items-center justify-center rounded-full bg-lighter-background">
                <UserAddOutlineIcon
                  className="size-4 scale-x-[-1] text-white"
                  strokeWidth={1.5}
                />
              </div>
            )}
            {messageData.type === "bought-ticket" && (
              <div className="absolute -right-2 bottom-0 flex size-[21px] items-center justify-center rounded-full bg-lighter-background">
                <TicketOutlineIcon
                  className="size-4 text-white"
                  strokeWidth={1.5}
                />
              </div>
            )}
            {messageData.type === "tip" && (
              <div className="absolute -right-2 bottom-0 flex size-[21px] items-center justify-center rounded-full bg-lighter-background">
                <TipOutlineIcon
                  className="size-4 text-white"
                  strokeWidth={1.5}
                />
              </div>
            )}
          </span>
        </LivestreamUserProfileModal>
        <div className="flex-grow text-sm" ref={textRef}>
          <span
            role="button"
            onClick={handleProfileClick}
            className="mr-2"
            style={{
              color: stringToColor(currentUser.id ?? "default"),
            }}
          >
            {currentUser.name}
          </span>
          {currentUser.role === ROLES.HOST ||
          currentUser.role === ROLES.MODERATOR ? (
            <span
              role="button"
              onClick={handleProfileClick}
              className="-mt-0.5 mr-2 inline-block rounded bg-brand-orange px-1 py-0.5 text-xs leading-none"
            >
              {ROLE_NAMES[currentUser.role as Role]}
            </span>
          ) : null}
          {currentUser.role !== ROLES.HOST && supporterStatus && (
            <>
              {supporterStatus === "king" && (
                <span
                  role="button"
                  onClick={handleProfileClick}
                  className="-mt-0.5 mr-2 inline-flex items-center gap-0.5 bg-purple px-1 py-0.5 text-[10px] leading-none"
                >
                  <CrownFilledIcon className="size-3 text-off-white" /> King
                  Tipper
                </span>
              )}
              {supporterStatus === "supporter" && (
                <span
                  role="button"
                  onClick={handleProfileClick}
                  className="-mt-0.5 mr-2 inline-flex items-center gap-0.5 bg-brand-orange px-1 py-0.5 text-[10px] leading-none"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="size-3 text-off-white"
                    viewBox="0 0 12 11"
                    fill="none"
                  >
                    <path
                      d="M0.928686 2.62728C1.06116 2.30746 1.25533 2.01686 1.50011 1.77209C1.74489 1.5273 2.03549 1.33313 2.3553 1.20066C2.67512 1.06818 3.0179 1 3.36408 1C3.71025 1 4.05303 1.06818 4.37285 1.20066C4.69267 1.33313 4.98326 1.5273 5.22804 1.77209L6.0001 2.54415L6.77216 1.77209C7.26652 1.27773 7.937 1.00001 8.63613 1.00001C9.33525 1.00001 10.0057 1.27773 10.5001 1.77209C10.9944 2.26644 11.2722 2.93693 11.2722 3.63605C11.2722 4.33517 10.9944 5.00566 10.5001 5.50001L6.0001 10L1.50011 5.50001C1.25533 5.25523 1.06116 4.96464 0.928686 4.64482C0.796211 4.325 0.728027 3.98222 0.728027 3.63605C0.728027 3.28988 0.796211 2.9471 0.928686 2.62728Z"
                      fill="currentColor"
                    />
                  </svg>
                  Top Supporter
                </span>
              )}
            </>
          )}
          <div
            className={cn(
              currentUser.role === ROLES.HOST ||
                currentUser.role === ROLES.MODERATOR
                ? "mt-1"
                : "inline",
            )}
          >
            <ErrorBoundary fallback={null}>
              <MessageSwitcher messageData={messageData} />
            </ErrorBoundary>
          </div>
        </div>
      </div>
    </li>
  );
};

type ReceivedChatMessageType =
  | {
      type: "text";
      message: string;
    }
  | {
      type: "emote";
      emote: string;
    }
  | {
      type: "tip";
      data: {
        amount: number;
        token: {
          name: string;
          icon: string;
        };
        to: {
          id: string;
          name: string;
          avatar: string;
          username: string;
        };
      };
    }
  | {
      type: "tip-party";
      data: {
        amount: number;
        token: {
          name: string;
          icon: string;
        };
        type: "EVERY_VIEWER" | "EVERY_TOP_10_SUPPORTERS";
      };
    }
  | {
      type: "followed";
      data: {
        followedUser: {
          id: string;
          name: string;
          username: string;
        };
      };
    }
  | {
      type: "bought-ticket";
      data: {
        user: {
          id: string;
          name: string;
          username: string;
        };
        amount: number;
      };
    };

const MessageSwitcher = ({
  messageData,
}: {
  messageData: ReceivedChatMessageType;
}) => {
  if (!messageData) return null;

  switch (messageData.type) {
    case "text":
      return <TextMessage message={messageData.message} />;
    case "emote":
      return <EmoteMessage emote={messageData.emote} />;
    case "tip":
      return (
        <TipMessage
          amount={messageData.data.amount}
          token={messageData.data.token}
          to={messageData.data.to}
        />
      );
    case "tip-party":
      return (
        <TipPartyMessage
          amount={messageData.data.amount}
          token={messageData.data.token}
          type={messageData.data.type}
        />
      );
    case "followed":
      return <FollowedMessage user={messageData.data.followedUser} />;
    case "bought-ticket":
      return (
        <BoughtTicketMessage
          user={messageData.data.user}
          amount={messageData.data.amount}
        />
      );
    default:
      return null;
  }
};

const TextMessage = ({ message }: { message: string }) => {
  return <span dangerouslySetInnerHTML={{ __html: message }} />;
};

const EmoteMessage = ({ emote }: { emote: string }) => {
  return (
    <div className="relative top-1 inline-block [&_img]:scale-125">
      <Emoji unified={emote} size={20} />
    </div>
  );
};

const TipMessage = ({
  amount,
  token,
  to,
}: {
  amount: number;
  token: {
    name: string;
    icon: string;
  };
  to: { id: string; name: string; avatar: string; username: string };
}) => {
  return (
    <span className="text-sm text-off-white">
      tipped <span className="font-semibold">{to.name}</span>{" "}
      <img
        src={token.icon}
        className="mx-1 inline-block size-5 rounded-full"
        alt={token.name + " logo"}
      />{" "}
      {numberFormatter.format(amount)} {token.name}
    </span>
  );
};

const TipPartyMessage = ({
  amount,
  token,
  type,
}: {
  amount: number;
  token: {
    name: string;
    icon: string;
  };
  type: "EVERY_VIEWER" | "EVERY_TOP_10_SUPPORTERS";
}) => {
  return (
    <div className="relative isolate mt-2 flex w-full flex-col gap-2 overflow-hidden rounded-lg border border-off-white/50 bg-purple-gradient p-4">
      <h5 className="text-sm font-semibold text-off-white">Tipping Party!</h5>
      <div className="flex flex-col text-off-white">
        <div className="text-xl font-semibold">
          {numberFormatter.format(amount)} {token.name}
        </div>
        <div className="text-sm">
          {type === "EVERY_VIEWER"
            ? "Sent to every current viewer."
            : "Sent to top 10 stream supporters."}
        </div>
      </div>
      <img
        src={token.icon}
        className="absolute -bottom-1 -right-4 -z-10 size-20 -rotate-12 rounded-full"
        alt={token.name + " logo"}
      />
      <img src="/assets/confetti-bg.png" className="absolute inset-0 -z-20" />
    </div>
  );
};

const FollowedMessage = ({
  user,
}: {
  user: { id: string; name: string; username: string };
}) => {
  return (
    <div className="inline">
      started following{" "}
      <ProgressBarLink href={`/${user.username}`}>{user.name}</ProgressBarLink>
    </div>
  );
};

const BoughtTicketMessage = ({
  user,
  amount,
}: {
  user: { id: string; name: string; username: string };
  amount: number;
}) => {
  return (
    <div className="inline">
      bought {amount}{" "}
      <ProgressBarLink href={`/${user.username}`}>{user.name}</ProgressBarLink>
      &apos;s tickets
    </div>
  );
};

const DEFAULT_COLOR_LIST = [
  "#D53535",
  "#EB540A",
  "#B6B840",
  "#40B877",
  "#40B8B8",
  "#0059E0",
  "#8340B8",
  "#B84081",
];

function stringToColor(str: string, colorList: string[] = DEFAULT_COLOR_LIST) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }

  const index = Math.abs(hash) % colorList.length;
  return colorList[index];
}
