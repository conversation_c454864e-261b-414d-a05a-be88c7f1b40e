"use client";

import { FC, useEffect } from "react";
import { useRouter } from "next/navigation";

import { useQueryClient } from "@tanstack/react-query";

import { markAsSeen } from "@/api/client/reports";
import {
  ArrowBackOutlineIcon,
  ArrowForwardOutlineIcon,
} from "@/components/icons";
import { ProgressBarLink } from "@/components/progress-bar";
import { toast } from "@/components/toast";
import {
  ModeratorAction,
  useProcessTicketMutation,
} from "@/queries/report-tickets-mutations";
import { useReportTicketQuery } from "@/queries/report-tickets-query";
import {
  ReportTicket,
  TicketStatus,
  TicketType,
} from "@/queries/types/report-tickets";
import { cn } from "@/utils";

import { useReportsContext } from "../context/reports-context";
import { ReportedPost } from "./reported-post";
import { TicketClosedInfo } from "./ticket-closed-info";
import { TicketActions } from "./ticket-details/ticket-actions";
import { TicketHeader } from "./ticket-header";
import { TicketInfoTable } from "./ticket-info-table";

interface ReportTicketDetailsProps {
  ticketId: number;
  group?: {
    tickets: { id: number }[];
  };
}

export const ReportTicketDetails: FC<ReportTicketDetailsProps> = ({
  ticketId,
  group,
}) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: ticket, isLoading } = useReportTicketQuery(ticketId);
  const { activeGroups, closedGroups, setActiveGroups, setClosedGroups } =
    useReportsContext();
  const { mutateAsync: processTicket, isPending } = useProcessTicketMutation({
    onSuccess: (_, variables) => {
      toast.green("Action completed successfully!");

      queryClient.invalidateQueries({
        queryKey: ["grouped-tickets"],
      });
      queryClient.invalidateQueries({
        queryKey: ["report-ticket", ticketId],
      });
    },
  });

  useEffect(() => {
    const markTicketAsSeen = async () => {
      try {
        if (ticket && !ticket.isSeen && ticket.status === "Active") {
          await markAsSeen(ticketId);

          queryClient.setQueryData<ReportTicket>(
            ["report-ticket", ticketId],
            (old) => (old ? { ...old, isSeen: true } : old),
          );

          const updatedGroups = activeGroups.map((group) => {
            const tickets = group.tickets.map((t) =>
              t.id === ticketId ? { ...t, isSeen: true } : t,
            );

            return {
              ...group,
              tickets,
              isSeen: tickets.every((t) => t.isSeen),
            };
          });

          setActiveGroups(updatedGroups);
        }
      } catch (error) {
        console.error("Failed to mark ticket as seen:", error);
      }
    };

    markTicketAsSeen();
  }, [ticket, ticketId, queryClient, setActiveGroups]);

  if (isLoading) return <div>Loading...</div>;
  if (!ticket) return <div>Select a ticket to view details</div>;

  const currentIndex = group?.tickets.findIndex((t) => t.id === ticketId) ?? -1;

  const handleNavigation = (direction: "prev" | "next") => {
    if (!group) return;

    const newIndex = direction === "prev" ? currentIndex - 1 : currentIndex + 1;
    if (newIndex >= 0 && newIndex < group.tickets.length) {
      router.push(`/moderation/reports/${group.tickets[newIndex].id}`);
    }
  };

  const handleVisitProfile = () => {
    router.push(`/${ticket.reportedUser.twitterHandle}`);
  };

  const handleAction = async (action: ModeratorAction) => {
    if (!ticket) return;

    try {
      await processTicket({
        ticketId: ticket.id,
        action,
      });
    } catch (error) {
      console.error("Failed to process ticket:", error);
    }
  };

  const handleDeletePost = () => handleAction(ModeratorAction.DELETE_POST);
  const handleSuspendAccount = () =>
    handleAction(ModeratorAction.SUSPEND_ACCOUNT);
  const handleCloseReopenTicket = () =>
    handleAction(
      ticket.status === TicketStatus.ACTIVE
        ? ModeratorAction.CLOSE_REPORT
        : ModeratorAction.REOPEN_TICKET,
    );

  return (
    <div className="flex h-screen flex-col">
      <TicketHeader ticket={ticket} />

      <div className="flex flex-grow flex-col justify-between overflow-y-auto p-8">
        <div className="flex flex-col text-sm">
          <TicketInfoTable ticket={ticket} />

          <div className="mt-4">
            <h3 className="mb-3 text-sm font-semibold">Context</h3>
            <div className="rounded-lg border border-off-white p-3">
              <p className="text-gray-300 text-sm">{ticket.content}</p>
            </div>
          </div>

          {ticket.type === TicketType.POST_REPORT && ticket.reportedThread && (
            <div className="mt-6">
              <h3 className="mb-3 text-sm font-semibold">Reported Post</h3>
              <ProgressBarLink
                href={`/${ticket.reportedThread.user?.twitterHandle}/status/${ticket.reportedThread.id}`}
              >
                <ReportedPost thread={ticket.reportedThread} />
              </ProgressBarLink>
            </div>
          )}
        </div>

        {ticket.status === TicketStatus.CLOSED && ticket.actionLogs && (
          <div className="mt-8">
            <TicketClosedInfo logs={ticket.actionLogs} />
          </div>
        )}

        {group && group.tickets.length > 1 && (
          <div className="my-8 flex items-center justify-between">
            <h3 className="text-sm font-semibold">Select Report</h3>
            <div className="flex items-center justify-between gap-6">
              <button
                className={cn(
                  "rounded-[20px] px-3 py-1",
                  currentIndex <= 0 ? "bg-[#2a2a2a]" : "bg-orange-gradient",
                )}
                onClick={() => handleNavigation("prev")}
                disabled={currentIndex <= 0}
              >
                <ArrowBackOutlineIcon className="size-5 text-off-white" />
              </button>
              <span className="text-sm text-light-gray-text">
                {currentIndex + 1} of {group.tickets.length}
              </span>
              <button
                className={cn(
                  "rounded-[20px] px-3 py-1",
                  currentIndex >= group.tickets.length - 1
                    ? "bg-[#2a2a2a]"
                    : "bg-orange-gradient",
                )}
                onClick={() => handleNavigation("next")}
                disabled={currentIndex >= group.tickets.length - 1}
              >
                <ArrowForwardOutlineIcon className="size-5 text-off-white" />
              </button>
            </div>
          </div>
        )}

        <TicketActions
          ticket={ticket}
          onDeletePost={handleDeletePost}
          onSuspendAccount={handleSuspendAccount}
          onVisitProfile={handleVisitProfile}
          onCloseReopen={handleCloseReopenTicket}
          isPending={isPending}
        />
      </div>
    </div>
  );
};
