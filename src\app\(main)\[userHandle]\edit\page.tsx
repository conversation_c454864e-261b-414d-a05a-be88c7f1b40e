import { cookies } from "next/headers";
import { redirect } from "next/navigation";

import { BannerPicture } from "./_components/banner-picture";
import { Header } from "./_components/header";
import { ProfileForm } from "./_components/profile-form";

function EditProfilePage({ params }: { params: { userHandle: string } }) {
  const userCookie = cookies().get("user");
  const user = userCookie ? JSON.parse(userCookie.value || "{}") : null;

  if (user && user.twitterHandle !== params.userHandle) {
    redirect(`/${params.userHandle}`);
  }

  return (
    <div className="pt-pwa flex h-full w-full flex-col">
      <Header />
      <BannerPicture />
      <ProfileForm />
    </div>
  );
}

export default EditProfilePage;
