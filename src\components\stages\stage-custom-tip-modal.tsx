"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQueryClient } from "@tanstack/react-query";
import { Controller, useForm } from "react-hook-form";
import { v4 } from "uuid";
import { Address, parseUnits } from "viem";
import { z } from "zod";

import { batchMultiSendDynamic } from "@/api/client/dynamic/send-funds-dynamic";
import { TippingInfoModal } from "@/components/tipping-info";
import { TipFormContent } from "@/components/tipping/tip-form-content";
import {
  MULTI_SEND_CONTRACT_ABI,
  MULTI_SEND_CONTRACT_ADDRESS,
} from "@/environments/MULTI_SEND_CONTRACT";
import { fallbackArena } from "@/environments/tokens";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import { useTipMutation } from "@/queries";
import {
  useAvaxPriceQuery,
  useTippableCurrenciesQuery,
} from "@/queries/currency-queries";
import { useUser } from "@/stores";
import { User } from "@/types";
import { cn } from "@/utils";
import { formatPrice } from "@/utils/format-token-price";
import { formatNumericValue, handleNumericInput } from "@/utils/number";

import { CurrencyItem, filterCurrencies } from "../currecency-items";
import { ArrowBackOutlineIcon, XCircleOutlineIcon } from "../icons";
import { toast } from "../toast";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { Dialog, DialogClose, DialogContent } from "../ui/dialog";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger } from "../ui/select";
import { TextInput } from "../ui/text-input";
import { useDataChannelsContext } from "./stores/data-channels-context";

interface TipModalProps {
  userToSend: User;
  open: boolean;
  setOpen: (open: boolean) => void;
  dataChannelsContext?: ReturnType<typeof useDataChannelsContext>;
}

const wallets = {
  ARENA: "The Arena",
  PHANTOM: "Phantom",
};

export const CustomTipModal = ({
  open,
  setOpen,
  userToSend,
  dataChannelsContext,
}: TipModalProps) => {
  const queryClient = useQueryClient();
  const context = dataChannelsContext ?? useDataChannelsContext();
  const {
    chat: { send },
    sendTip,
  } = context;
  const [tipAmount, setTipAmount] = useState("0");
  const [tipAccurateAmount, setTipAccurateAmount] = useState("0");
  const [totalTipUSD, setTotalTipUSD] = useState("0");
  const [isTippingDisabled, setIsTippingDisabled] = useState(false);
  const [wallet, setWallet] = useState(wallets.ARENA);
  const [feePercentage, setFeePercentage] = useState(2);

  const { user } = useUser();

  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useTippableCurrenciesQuery();
  const { data: avaxPrice } = useAvaxPriceQuery();

  const provider =
    typeof window !== "undefined" && (window as any).phantom?.solana;

  const { sortedCurrencies } = useWalletCurrencies({
    user,
    currenciesData,
    isCurrenciesLoading,
  });

  const [symbol, setSymbol] = useState("ARENA");
  const token = useMemo(
    () => sortedCurrencies.find((t) => t.symbol === symbol) ?? fallbackArena,
    [sortedCurrencies, symbol],
  );

  const isSolanaCurrency = [
    "SOL",
    "Bonk",
    "$WIF",
    "USEDCAR",
    "Moutai",
    "HARAMBE",
  ].includes(token.symbol);

  const showWarning =
    isSolanaCurrency && !provider && wallet === wallets.PHANTOM;

  const TipInput = z.object({
    currency: z.string(),
    tipAmount: z
      .string()
      .min(1, {
        message: "Tip amount is required",
      })
      .refine((v) => !isNaN(parseFloat(v.replace(/,/g, ""))), {
        message: "Tip amount must be a number",
      })
      .refine(
        (v) =>
          isSolanaCurrency
            ? true
            : parseFloat(v.replace(/,/g, "")) <=
              parseFloat(
                token.isToken
                  ? formatPrice(token.balance || "0").toString()
                  : (token.balance || "0").toString().replace(/,/g, ""),
              ),
        {
          message: "Insufficient balance",
        },
      ),
  });
  type TipInputType = z.infer<typeof TipInput>;

  const form = useForm<TipInputType>({
    defaultValues: {
      currency: "ARENA",
      tipAmount: "",
    },
    resolver: zodResolver(TipInput),
    mode: "all",
  });

  const [isPendingDynamic, setIsPendingDynamic] = useState(false);

  const { mutateAsync: tip, isPending } = useTipMutation({
    onSuccess: async (_data, variables) => {
      toast.green("Tip sent!");
      setOpen(false);
      form.reset();
      setTipAmount("0");
      setWallet(wallets.ARENA);
      queryClient.invalidateQueries({
        queryKey: ["wallet", "balance", user?.address, token.symbol],
      });
      setSymbol("ARENA");
      queryClient.invalidateQueries({
        queryKey: ["stages", "mostRecentTip"],
      });
      queryClient.invalidateQueries({
        queryKey: ["currency", "system"],
      });

      if (
        variables.tipAmount &&
        !isNaN(+variables.tipAmount) &&
        +variables.tipAmount > 0
      ) {
        const message = {
          type: "tip",
          data: {
            amount: +variables.tipAmount,
            currency: variables.currency,
            to: {
              id: userToSend.id,
              name: userToSend.twitterName,
              avatar: userToSend.twitterPicture,
              username: userToSend.twitterHandle,
            },
          },
        };
        await send(JSON.stringify(message));
        if (user) {
          await sendTip({
            currency: variables.currency,
            amount: +variables.tipAmount,
            to: userToSend.id,
            from: {
              name: user?.twitterName,
              avatar: user?.twitterPicture,
              username: user?.twitterHandle,
            },
            id: v4(),
          });
        }
      }
    },
  });

  const { primaryWallet } = useDynamicContext();

  const onSubmit = async (values: TipInputType) => {
    if (!userToSend) return;
    let transactionHash = "";
    let transactionData = "";
    let isDynamic = false;
    let isToken = false;
    if (!isSolanaCurrency) {
      if (user && user.address === user.dynamicAddress?.toLowerCase()) {
        const decimals = values.currency === "MEAT" ? 6 : 18;

        const tippedToken = sortedCurrencies.find(
          (token) => token.symbol === values.currency,
        );
        isToken = tippedToken?.isToken || false;

        const amountToTip = isToken
          ? BigInt(tipAccurateAmount)
          : parseUnits(tipAmount.replace(/,/g, ""), decimals);
        isDynamic = true;
        setIsPendingDynamic(true);
        try {
          const {
            txHash: [txHash],
            txData: [txData],
          } = await batchMultiSendDynamic(
            primaryWallet,
            [userToSend.address],
            [amountToTip],
            values.currency,
            tippedToken?.isToken || false,
            tippedToken?.contractAddress,
          );
          transactionHash = txHash;
          transactionData = txData;

          await tip({
            currency: values.currency,
            tipAmount: (
              parseFloat(values.tipAmount.replace(/,/g, "")) -
              (parseFloat(values.tipAmount.replace(/,/g, "")) * feePercentage) /
                100
            ).toString(),
            userId: userToSend.id,
            wallet: isSolanaCurrency ? wallet : wallets.ARENA,
            isDynamic,
            txHash: transactionHash,
            txData: transactionData,
            isToken,
          });
        } catch (e) {
          toast.danger(
            `Error while tipping ${userToSend.twitterName}, please try again!`,
          );
          console.log("Something went wrong with sendFundsDynamic:", e);
        } finally {
          setIsPendingDynamic(false);
        }
      }
    }
  };

  const tipValue = async (price: string, value: string) => {
    setTotalTipUSD(
      formatNumericValue((Number(value) * Number(price)).toFixed(2).toString()),
    );
  };

  const onMaxClick = async (currency: string) => {
    let maxTip = token.isToken
      ? formatPrice(token.balance || "0")
      : (token.balance || "0").toString().replace(/,/g, "");
    if (token.isToken) {
      setTipAmount(formatNumericValue(Number(maxTip).toString()));
      setTipAccurateAmount(token.balance || "0");
    }
    let maxTipWithoutGas;
    if (currency === "AVAX") {
      maxTipWithoutGas = Number(maxTip) - 0.005;
      if (maxTipWithoutGas < 0) {
        maxTipWithoutGas = 0;
      }
      maxTip = maxTipWithoutGas.toString();
    }
    if (Number(maxTip) < 1) {
      let numMaxTip = Number(
        Number(maxTip)
          .toFixed(4)
          .replace(/\.?0+$/, ""),
      );
      if (numMaxTip <= Number(maxTip)) {
        maxTip = numMaxTip.toString();
      } else {
        maxTip = (numMaxTip - 0.0001).toFixed(4).toString();
      }
    } else {
      let numMaxTip = Number(
        Number(maxTip)
          .toFixed(2)
          .replace(/\.?0+$/, ""),
      );
      if (numMaxTip <= Number(maxTip)) {
        maxTip = numMaxTip.toString();
      } else {
        maxTip = (numMaxTip - 0.01).toFixed(2).toString();
      }
    }
    if (!token.isToken) {
      setTipAmount(formatNumericValue(Number(maxTip).toString()));
    }
    form.setValue("tipAmount", formatNumericValue(Number(maxTip).toString()));
  };

  useEffect(() => {
    const sanitizedValue = tipAmount.replace(/,/g, "");
    if (
      Number(sanitizedValue) >
        parseFloat(
          token.isToken
            ? formatPrice(token.balance || "0").toString()
            : (token.balance || "0").toString().replace(/,/g, ""),
        ) ||
      Number(sanitizedValue) === 0
    ) {
      setIsTippingDisabled(true);
    } else {
      setIsTippingDisabled(false);
    }
    tipValue(
      token.isToken
        ? (
            Number(formatPrice(token.systemRate)) * (avaxPrice?.avax || 0)
          ).toString()
        : token.systemRate,
      sanitizedValue,
    );
  }, [token, tipAmount]);

  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  useEffect(() => {
    const handleResize = () => {
      const viewportHeight =
        window.visualViewport?.height || window.innerHeight;
      const windowHeight = window.innerHeight;
      if (viewportHeight < windowHeight) {
        setIsKeyboardVisible(true);
      } else {
        setIsKeyboardVisible(false);
      }
    };
    if (typeof visualViewport != "undefined") {
      window.visualViewport?.addEventListener("resize", handleResize);
    }
    return () => {
      if (typeof visualViewport != "undefined") {
        window.visualViewport?.removeEventListener("resize", handleResize);
      }
    };
  }, []);

  useEffect(() => {
    async function fetchFeePercentage() {
      if (!primaryWallet) {
        return;
      }
      if (!isEthereumWallet(primaryWallet)) {
        toast.danger("This wallet is not an Ethereum wallet");
        setIsPendingDynamic(false);
        return;
      }

      const publicClient = await primaryWallet.getPublicClient();

      const percentage = await publicClient.readContract({
        address: MULTI_SEND_CONTRACT_ADDRESS as Address,
        abi: MULTI_SEND_CONTRACT_ABI,
        functionName: "feePercentage",
      });

      setFeePercentage(Number(percentage));
    }

    void fetchFeePercentage();
  }, []);

  const handleOpenChange = useCallback(
    (open: boolean) => {
      setOpen(open);
      if (!open) {
        form.reset();
        setWallet(wallets.ARENA);
        setSymbol("ARENA");
        setTotalTipUSD("0");
        setTipAmount("0");
        setTipAccurateAmount("0");
      }
    },
    [form],
  );

  const recepients = useMemo(
    () => [
      {
        id: userToSend.id,
        name: userToSend.twitterName,
        avatar: userToSend.twitterPicture,
        username: userToSend.twitterHandle,
        twitterHandle: userToSend.twitterHandle,
        twitterPicture: userToSend.twitterPicture,
        twitterName: userToSend.twitterName,
        address: userToSend.address,
      },
    ],
    [userToSend],
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="flex h-full w-full flex-grow flex-col items-center justify-center p-0 md:h-auto md:w-[480px] md:rounded-2xl md:p-0">
        <div className="flex w-full flex-col items-center justify-center gap-6 p-6">
          <div className="flex items-center gap-3 rounded-xl border border-gray-text px-3 py-4">
            <Avatar className="size-9">
              <AvatarImage src={userToSend?.twitterPicture} />
              <AvatarFallback />
            </Avatar>
            <div className="flex flex-col gap-1">
              <div className="text-sm font-semibold text-white">
                {userToSend?.twitterName}
              </div>
              <div className="text-xs text-gray-text">
                @{userToSend?.twitterHandle}
              </div>
            </div>
          </div>
          <TipFormContent
            recepients={recepients}
            sortedCurrencies={sortedCurrencies}
            setOpen={setOpen}
            buttonLabel="Send tip"
          />
        </div>
        <TippingInfoModal />
      </DialogContent>
    </Dialog>
  );
};
