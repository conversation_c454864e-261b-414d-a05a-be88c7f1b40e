import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

import { Button } from "./ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "./ui/dialog";
import { Drawer, DrawerContent } from "./ui/drawer";

interface ConfirmationModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  title: string;
  confirmButtonLabel: string;
  children: React.ReactNode;
  onConfirm: () => void;
  destructive?: boolean;
}

export const ConfirmationModal = ({
  open,
  setOpen,
  title,
  confirmButtonLabel,
  children,
  onConfirm,
  destructive = false,
}: ConfirmationModalProps) => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-sm">
          <ConfirmationModalContent
            title={title}
            confirmButtonLabel={confirmButtonLabel}
            onConfirm={onConfirm}
            onCancel={() => {
              setOpen(false);
            }}
            destructive={destructive}
          >
            {children}
          </ConfirmationModalContent>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="justify-start gap-6 text-left">
        <ConfirmationModalContent
          title={title}
          confirmButtonLabel={confirmButtonLabel}
          onConfirm={onConfirm}
          onCancel={() => {
            setOpen(false);
          }}
          destructive={destructive}
        >
          {children}
        </ConfirmationModalContent>
      </DrawerContent>
    </Drawer>
  );
};

interface ConfirmationModalContentProps {
  title: string;
  children: React.ReactNode;
  confirmButtonLabel: string;
  onConfirm: () => void;
  onCancel: () => void;
  destructive?: boolean;
}

const ConfirmationModalContent = ({
  title,
  children,
  confirmButtonLabel,
  onConfirm,
  onCancel,
  destructive,
}: ConfirmationModalContentProps) => {
  return (
    <>
      <DialogHeader className="space-y-4">
        <DialogTitle>{title}</DialogTitle>
        <DialogDescription className="text-gray-text">
          {children}
        </DialogDescription>
      </DialogHeader>
      <div className="flex items-center gap-2">
        <Button variant="outline" className="flex-1" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          variant={destructive ? "destructive" : "secondary"}
          className="flex-1"
          onClick={onConfirm}
        >
          {confirmButtonLabel}
        </Button>
      </div>
    </>
  );
};
