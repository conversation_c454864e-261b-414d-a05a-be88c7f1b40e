"use client";

import {
  createContext,
  Dispatch,
  ReactNode,
  SetStateAction,
  useContext,
  useMemo,
  useState,
} from "react";

import { UseMutateFunction, useQuery } from "@tanstack/react-query";
import { v4 } from "uuid";

import { getSettings, SetSettingsRequest } from "@/api/client/chat";
import { toast } from "@/components/toast";
import { useChatSettingsMutation } from "@/queries/chat-settings-mutation";
import { useUser } from "@/stores";

type SettingsContextType = {
  isHolders: boolean;
  setIsHolders: Dispatch<SetStateAction<boolean>>;
  isFollowers: boolean;
  setIsFollowers: Dispatch<SetStateAction<boolean>>;
  isLoading: boolean;
  setSettings: UseMutateFunction<any, Error, SetSettingsRequest, any>;
  isPending: boolean;
  handleSetSettings: () => void;
};

const SettingsContext = createContext<SettingsContextType>({
  isHolders: true,
  setIsHolders: () => {},
  isFollowers: true,
  setIsFollowers: () => {},
  isLoading: true,
  setSettings: () => {},
  isPending: true,
  handleSetSettings: () => {},
});

export function SettingsProvider({ children }: { children: ReactNode }) {
  const [isHolders, setIsHolders] = useState(true);
  const [isFollowers, setIsFollowers] = useState(true);

  const user = useUser();
  const uuid = useMemo(v4, []);
  const userId = user?.user?.id || uuid;

  const { isLoading } = useQuery({
    queryKey: ["chat", "settings", { userId }],
    queryFn: async () => {
      try {
        const res = await getSettings();
        setIsHolders(res.holders ?? true);
        setIsFollowers(res.followers ?? true);
        return res;
      } catch (error) {
        console.error("Failed to fetch settings:", error);
        return { holders: true, followers: true };
      }
    },
  });

  const { mutate: setSettings, isPending } = useChatSettingsMutation({
    onSuccess: () => toast.green("The settings have been saved"),
    onError: () => toast.red("Something went wrong"),
  });

  const handleSetSettings = () =>
    setSettings({
      holders: isHolders,
      followers: isFollowers,
    });

  const data = {
    isHolders,
    setIsHolders,
    isFollowers,
    setIsFollowers,
    isLoading,
    setSettings,
    isPending,
    handleSetSettings,
  };
  return (
    <SettingsContext.Provider value={{ ...data }}>
      {children}
    </SettingsContext.Provider>
  );
}

export function useSettings() {
  const context = useContext(SettingsContext);
  return context;
}
