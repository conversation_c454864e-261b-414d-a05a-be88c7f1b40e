import { ComponentProps } from "react";

export const MicCircleFilledIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="39"
    height="38"
    viewBox="0 0 39 38"
    fill="none"
    {...props}
  >
    <path
      d="M25.3931 19.0918C25.3931 20.7658 24.7609 22.3712 23.6357 23.5548C22.5105 24.7385 20.9844 25.4035 19.3931 25.4035M19.3931 25.4035C17.8018 25.4035 16.2756 24.7385 15.1504 23.5548C14.0252 22.3712 13.3931 20.7658 13.3931 19.0918M19.3931 25.4035V28"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17.2717 21.2348C17.8344 21.8032 18.5974 22.1224 19.3931 22.1224C20.1887 22.1224 20.9518 21.8032 21.5144 21.2348C22.077 20.6665 22.3931 19.8956 22.3931 19.0918V13.0306C22.3931 12.2268 22.077 11.456 21.5144 10.8876C20.9518 10.3193 20.1887 10 19.3931 10C18.5974 10 17.8344 10.3193 17.2717 10.8876C16.7091 11.456 16.3931 12.2268 16.3931 13.0306V19.0918C16.3931 19.8956 16.7091 20.6665 17.2717 21.2348Z"
      fill="currentColor"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
