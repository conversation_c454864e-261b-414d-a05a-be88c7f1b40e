"use client";

import { useState } from "react";

import CopyToClipboard from "react-copy-to-clipboard";

import { CopyOutlineIcon } from "@/components/icons";
import { toast } from "@/components/toast";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { Label } from "@/components/ui/label";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

export const DynamicMigrationExecutedExternallyModal = ({
  dynamicAddress,
}: {
  dynamicAddress: string;
}) => {
  const [open, setOpen] = useState(true);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);

  if (isTablet) {
    return (
      <Dialog
        open={open}
        onOpenChange={(isOpen) => {
          setOpen(isOpen);
        }}
      >
        <DialogContent className="gap-6">
          <div className="flex items-center gap-3">
            <h3 className="text-base font-semibold leading-[22px] text-off-white">
              There is a problem with your Signer
            </h3>
          </div>

          <p className="text-sm text-[#B5B5B5]">
            Looks like you&apos;ve changed the Signer outside of the Arena!
            Please change your Signer on the Dynamic wallet address to get your
            account working normally.
          </p>

          <div className="mt-2 flex min-w-0 flex-col gap-2">
            <Label>Your Dynamic Wallet</Label>
            <div className="flex items-center justify-between gap-4 rounded-lg bg-[#313131] px-4 py-3 text-sm text-[#656565]">
              <span className="flex-grow truncate">{dynamicAddress}</span>
              <CopyToClipboard
                text={dynamicAddress}
                onCopy={() => {
                  toast.green("Copied to clipboard");
                }}
              >
                <button className="flex-shrink-0">
                  <CopyOutlineIcon className="size-5 text-[#9E9E9E]" />
                </button>
              </CopyToClipboard>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
      }}
    >
      <DrawerContent className="gap-4">
        <div className="flex items-center gap-3">
          <h3 className="text-base font-semibold leading-[22px] text-off-white">
            There is a problem with your Signer
          </h3>
        </div>

        <p className="text-sm text-[#B5B5B5]">
          Looks like you&apos;ve changed the Signer outside of the Arena! Please
          change your Signer on the Dynamic wallet address to get your account
          working normally.
        </p>

        <div className="mt-2 flex min-w-0 flex-col gap-2">
          <Label>Your Dynamic Wallet</Label>
          <div className="flex items-center justify-between gap-4 rounded-lg bg-[#313131] px-4 py-3 text-sm text-[#656565]">
            <span className="flex-grow truncate">{dynamicAddress}</span>
            <CopyToClipboard
              text={dynamicAddress}
              onCopy={() => {
                toast.green("Copied to clipboard");
              }}
            >
              <button className="flex-shrink-0">
                <CopyOutlineIcon className="size-5 text-[#9E9E9E]" />
              </button>
            </CopyToClipboard>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
};
