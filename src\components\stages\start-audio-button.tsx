"use client";

import { useRoomContext, useStartAudio } from "@livekit/components-react";

import { Button } from "../ui/button";

export function StartAudioButton() {
  const room = useRoomContext();
  const { mergedProps } = useStartAudio({
    room,
    props: {},
  });

  return (
    <Button
      {...mergedProps}
      className="absolute bottom-10 left-1/2 z-50 -translate-x-1/2"
    >
      Allow Audio
    </Button>
  );
}
