import { useQuery } from "@tanstack/react-query";

import {
  checkBadgesAvailable,
  getChainDegodsBadgeAvailable,
  getChainDokyoBadgeAvailable,
  getChainSappySealsBadgeAvailable,
  getSolanaAddress,
} from "@/api/client/chain";

export const useDegodsBadgeAvailableQuery = () => {
  return useQuery<boolean>({
    queryKey: ["chain", "degods_badge_available"],
    queryFn: getChainDegodsBadgeAvailable,
  });
};

export const useDokyoBadgeAvailableQuery = () => {
  return useQuery<boolean>({
    queryKey: ["chain", "dokyo_badge_available"],
    queryFn: getChainDokyoBadgeAvailable,
  });
};

export const useSappySealsBadgeAvailableQuery = () => {
  return useQuery<boolean>({
    queryKey: ["chain", "sappy_seals_badge_available"],
    queryFn: getChainSappySealsBadgeAvailable,
  });
};

export const useSolanaAddressQuery = () => {
  return useQuery({
    queryKey: ["chain", "getSolanaAddress"],
    queryFn: getSolanaAddress,
  });
};

export const useCheckBadgesAvailableQuery = (badgeType: number) => {
  return useQuery<boolean>({
    queryKey: ["chain", "badge_available", badgeType],
    queryFn: () => checkBadgesAvailable(badgeType),
  });
};
