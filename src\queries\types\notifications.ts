export enum NotificationTypeEnum {
  NoNotification = 0,
  PushNotification = 1,
  UNRECOGNIZED = -1,
}

export interface NotificationType {
  id: string;
  createdOn: string;
  userId: string;
  title: string;
  text: string;
  link: string;
  type: NotificationTypeEnum;
  isSeen: boolean;
}

export interface NotificationsResponse {
  notifications: NotificationType[];
  pageSize: number;
  numberOfPages: number;
  numberOfResults: number;
}

export interface UnseenNotificationsResponse {
  count: number;
}
