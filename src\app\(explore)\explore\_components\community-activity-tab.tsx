import { memo } from "react";

import { Virtuoso } from "react-virtuoso";

import { AvaxPrice } from "@/api/client/currency";
import { HANDLE_PHASE } from "@/app/(main)/community/_components/consts";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useRecentCommunityTradesQuery } from "@/queries";
import { useAvaxPriceQuery } from "@/queries/currency-queries";
import { CommunityTrade } from "@/types/community";
import { formatTimeDistance, numberFormatter } from "@/utils";
import { formatMarketCap } from "@/utils/format-market-cap";
import { formatAddress, formatPrice } from "@/utils/format-token-price";

import { NoContent } from "./no-content";

export const CommunityActivityTab = memo(function ActivityTab() {
  const { data } = useRecentCommunityTradesQuery();

  const { data: avaxPrice, isLoading: isFetchingAvaxPrice } =
    useAvaxPriceQuery();

  return (
    <>
      <p className="mt-[8px] px-6 text-sm text-[#808080]">
        Recent Trading Activity
      </p>
      {data && data.trades.length === 0 && (
        <NoContent message={"No activity to show!"} />
      )}
      {data && data.trades.length > 0 && !isFetchingAvaxPrice && (
        <Virtuoso
          useWindowScroll
          data={data?.trades || []}
          overscan={200}
          itemContent={(index, trade) => {
            return <TradeItem trade={trade} avaxPrice={avaxPrice} />;
          }}
        />
      )}
    </>
  );
});

interface TradeItemProps {
  trade: CommunityTrade;
  avaxPrice: AvaxPrice | undefined;
}

const TradeItem = ({ trade, avaxPrice }: TradeItemProps) => {
  const formattedAmount = formatPrice(trade.amount);
  const formattedTokenAmount = formatPrice(trade.tokenAmount.toString());

  return (
    <div className="flex  justify-between gap-2 px-6 py-4">
      <div className="flex items-center gap-3">
        <div className="flex -space-x-2">
          {trade.trader ? (
            <Avatar className="z-[1] size-[30px]" asChild>
              <ProgressBarLink href={`/${trade.trader.twitterHandle}`}>
                <AvatarImage src={trade.trader.twitterPicture} />
                <AvatarFallback />
              </ProgressBarLink>
            </Avatar>
          ) : (
            <Avatar className="z-[1] size-[30px]" asChild>
              <a
                href={`https://snowtrace.io/address/${trade.address}`}
                target="_blank"
                rel="noopener noreferrer"
              >
                <AvatarImage src={`https://effigy.im/a/${trade.address}.png`} />
                <AvatarFallback />
              </a>
            </Avatar>
          )}
          <Avatar className="size-[30px]" asChild>
            <ProgressBarLink
              href={
                trade.community && trade.community?.tokenPhase >= HANDLE_PHASE
                  ? `/community/${trade.community?.name}`
                  : `/community/${trade.community?.contractAddress}`
              }
            >
              <AvatarImage src={trade.community?.photoURL} />
              <AvatarFallback />
            </ProgressBarLink>
          </Avatar>
        </div>
        <div className="text-sm text-white">
          {trade.trader ? (
            <ProgressBarLink
              href={`/${trade.trader.twitterHandle}`}
              className="font-bold"
            >
              {trade.trader.twitterName}
            </ProgressBarLink>
          ) : (
            <a
              href={`https://snowtrace.io/address/${trade.address}`}
              target="_blank"
              rel="noopener noreferrer"
              className="font-bold"
            >
              {formatAddress(trade.address, 6, 4, 3)}
            </a>
          )}{" "}
          {trade.isBuy ? "bought" : "sold"}{" "}
          <span className="font-bold">
            {formatMarketCap(formattedTokenAmount)}
          </span>{" "}
          <ProgressBarLink
            href={
              trade.community && trade.community?.tokenPhase >= HANDLE_PHASE
                ? `/community/${trade.community?.name}`
                : `/community/${trade.community?.contractAddress}`
            }
            className="font-bold"
          >
            ${trade.community?.ticker}
          </ProgressBarLink>{" "}
          for{" "}
          {avaxPrice ? (
            <>
              <span className="font-bold">
                $
                {Number(
                  (avaxPrice.avax * formattedAmount).toFixed(
                    formattedAmount < 1 ? 4 : 2,
                  ),
                )}
                .
              </span>
            </>
          ) : (
            <>
              <span className="font-bold">
                {numberFormatter.format(
                  Number(formattedAmount.toFixed(formattedAmount < 1 ? 4 : 2)),
                )}
              </span>{" "}
              AVAX.
            </>
          )}
        </div>
      </div>
      <div className="flex-shrink-0 text-xs text-gray-text">
        {formatTimeDistance(trade.createdOn)}
      </div>
    </div>
  );
};
