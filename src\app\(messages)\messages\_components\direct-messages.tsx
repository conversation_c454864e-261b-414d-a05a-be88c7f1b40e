import { Dispatch, FC, SetStateAction, useEffect } from "react";

import { UseMutateAsyncFunction } from "@tanstack/react-query";
import { useInView } from "react-intersection-observer";

import {
  ArrowBackOutlineIcon,
  PinOutlineIcon,
  UnpinOutlineIcon,
} from "@/components/icons";
import { useSearchDMConversationsInfiniteQuery } from "@/queries";
import { Group, PinConversationRequest } from "@/queries/types/chats";
import { useUser } from "@/stores";
import { cn } from "@/utils";

import { ChatSearchRequest } from "./chat-search-request";
import { DirectMessageItem, MessageItemLoadingSkeleton } from "./message-item";
import { MessageRequests } from "./message-requests";
import { TypingsType } from "./messages";
import { NoDirectMessagesFound } from "./no-direct-messages-found";

interface DirectMessagesProps {
  groups: Group[] | undefined;
  isLoading: boolean;
  setSeen: (groupId: string, date: number) => void;
  setSelectedGroups: Dispatch<SetStateAction<Group[]>>;
  selectedGroups: Group[];
  typings: TypingsType;
  params: {
    groupId?: string | undefined;
  };
  isFetchingNextPage: boolean;
  setCurrentPage: Dispatch<
    SetStateAction<"messages" | "request" | "receivedRequest">
  >;
  fetchNextDirectMessagesPage: any;
  throttledSearch: string;
  pinConversationForDMs: UseMutateAsyncFunction<
    any,
    Error,
    PinConversationRequest,
    any
  >;
}

export const DirectMessages: FC<DirectMessagesProps> = ({
  groups,
  isLoading,
  setSeen,
  setSelectedGroups,
  selectedGroups,
  typings,
  params,
  isFetchingNextPage,
  fetchNextDirectMessagesPage,
  setCurrentPage,
  throttledSearch,
  pinConversationForDMs,
}) => {
  const { ref, inView } = useInView();
  const { user } = useUser();
  const {
    data: searchConversationsData,
    isLoading: isSearchConversationsLoading,
  } = useSearchDMConversationsInfiniteQuery(throttledSearch);

  if (searchConversationsData && throttledSearch) {
    groups = searchConversationsData?.pages.map((page) => page.groups).flat();
  }

  let directMessages: Group[] = [];
  groups?.forEach((group) => {
    if (!group.isTemporary && group.isDirect) {
      directMessages.push(group);
    }
  });

  useEffect(() => {
    if (inView) {
      fetchNextDirectMessagesPage();
    }
  }, [inView]);

  let countRequests = 0;
  directMessages?.forEach((group) => {
    if (group?.isRequest && group?.lastUserId !== user?.id) {
      countRequests += 1;
    }
  });

  return (
    <>
      <div className="mt-[15px] px-2">
        <div
          className={cn(
            "fixed z-50 flex w-full justify-end sm:w-[600px] lg:w-[364px]",
            "bottom-[calc(70px+env(safe-area-inset-bottom))] pr-5",
          )}
        >
          <ChatSearchRequest setCurrentPage={setCurrentPage} />
        </div>
        {countRequests > 0 &&
          !isSearchConversationsLoading &&
          throttledSearch === "" && (
            <MessageRequests
              count={countRequests}
              setCurrentPage={setCurrentPage}
            />
          )}
        {throttledSearch !== "" &&
          !isSearchConversationsLoading &&
          directMessages?.length === 0 && <NoDirectMessagesFound />}
        {!isLoading &&
          !isSearchConversationsLoading &&
          directMessages &&
          directMessages?.map((group) => {
            return group?.isRequest && group?.lastUserId !== user?.id ? null : (
              <DirectMessageItem
                key={group?.id}
                group={group}
                setSeen={() => {
                  setSeen(group?.id, Date.now());
                }}
                selectGroup={() => {
                  setSelectedGroups((prev) => {
                    if (prev.length === 1) {
                      return prev;
                    }

                    if (prev.find(({ id }) => id === group?.id)) {
                      return prev.filter((g) => g?.id !== group?.id);
                    }

                    return [...prev, group];
                  });
                }}
                isSelected={Boolean(
                  selectedGroups.find(({ id }) => id === group?.id),
                )}
                typingUser={
                  typings[group?.id] ? typings[group?.id].user.name : undefined
                }
                isConversationOpen={group?.id === params?.groupId}
                search={throttledSearch}
              />
            );
          })}
        {(isLoading ||
          isFetchingNextPage ||
          (throttledSearch !== "" && isSearchConversationsLoading)) && (
          <>
            {Array(9)
              .fill(null)
              .map((_, index) => (
                <MessageItemLoadingSkeleton key={index} />
              ))}
          </>
        )}
        {!isLoading && !isFetchingNextPage && throttledSearch === "" && (
          <div ref={ref} style={{ visibility: "hidden" }}>
            <p>.</p>
          </div>
        )}
        {selectedGroups.length > 0 && (
          <div className="top-pwa fixed inset-x-0 flex h-[54px] items-center justify-between bg-dark-bk px-6 pt-6 text-off-white">
            <div className="flex items-center gap-4">
              <button
                onClick={() => {
                  setSelectedGroups([]);
                }}
              >
                <ArrowBackOutlineIcon className="size-5" />
              </button>
              <div className="text-base leading-5">{selectedGroups.length}</div>
            </div>
            <div>
              <button
                onClick={async () => {
                  const group = selectedGroups[0];
                  setSelectedGroups([]);
                  await pinConversationForDMs({
                    groupId: group.id,
                    isPinned: !group.memberLink?.isPinned,
                  });
                }}
              >
                {selectedGroups[0].memberLink?.isPinned ? (
                  <UnpinOutlineIcon className="size-6" />
                ) : (
                  <PinOutlineIcon className="size-6" />
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
};
