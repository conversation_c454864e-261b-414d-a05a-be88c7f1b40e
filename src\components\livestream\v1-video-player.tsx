"use client";

import { useEffect, useMemo, useRef, useState } from "react";

import {
  AudioTrack,
  isTrackReference,
  TrackReference,
  useRoomContext,
  useStartAudio,
  useTrackMutedIndicator,
  useTracks,
  VideoTrack,
} from "@livekit/components-react";
import * as SliderPrimitive from "@radix-ui/react-slider";
import { useQuery } from "@tanstack/react-query";
import { Track } from "livekit-client";
import { useHotkeys } from "react-hotkeys-hook";

import { livestreamQueries } from "@/queries";
import { useLivestreamStore } from "@/stores/livestream";
import { cn } from "@/utils/cn";

import {
  ArrowsExpandOutlineIcon,
  VolumeOffOutlineIcon,
  VolumeUpOutlineIcon,
} from "../icons";

export function VideoPlayer() {
  const room = useRoomContext();
  const { mergedProps, canPlayAudio } = useStartAudio({
    room,
    props: {},
  });

  const videoRef = useRef<HTMLVideoElement>(null);
  const id = useLivestreamStore((state) => state.id!);
  const { data } = useQuery(livestreamQueries.livestreamSimpleInfo(id));
  const containerRef = useRef<HTMLDivElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [volume, setVolume] = useState(100);
  const [muted, setMuted] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  useHotkeys(["f", "F"], () => {
    if (isFullscreen) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  });

  const resetTimer = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setShowControls(true);
    timeoutRef.current = setTimeout(() => {
      setShowControls(false);
    }, 5000);
  };

  const handleMouseMove = () => {
    resetTimer();
  };

  const handleMouseLeave = () => {
    setShowControls(false);
  };

  const handleTouchStart = () => {
    resetTimer();
  };

  useEffect(() => {
    resetTimer();
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const screenShareTracks = useTracks(
    [{ source: Track.Source.ScreenShare, withPlaceholder: false }],
    {
      onlySubscribed: false,
    },
  )
    .filter(isTrackReference)
    .filter((track) => !track.publication.isMuted);

  const cameraTracks = useTracks(
    [{ source: Track.Source.Camera, withPlaceholder: false }],
    {
      onlySubscribed: false,
    },
  )
    .filter(isTrackReference)
    .filter((track) => !track.publication.isMuted);

  const micTracks = useTracks(
    [{ source: Track.Source.Microphone, withPlaceholder: false }],
    {
      onlySubscribed: false,
    },
  ).filter(isTrackReference);

  const enterFullscreen = async () => {
    try {
      if (containerRef.current) {
        await containerRef.current.requestFullscreen();
      }
    } catch (error) {
      console.error("Failed to enter fullscreen:", error);
    }
  };

  const exitFullscreen = async () => {
    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      }
    } catch (error) {
      console.error("Failed to exit fullscreen:", error);
    }
  };

  // useEffect(() => {
  //   if (data?.livestream.type === "PRO") {
  //     cameraTracks.forEach((track) => {
  //       if (videoRef.current) {
  //         track.publication.track?.attach(videoRef.current);
  //       }
  //     });

  //     // micTracks.forEach((track) => {
  //     //   if (videoRef.current) {
  //     //     track.publication.track?.attach(videoRef.current);
  //     //   }
  //     // });

  //     return () => {
  //       cameraTracks.forEach((track) => {
  //         track.publication.track?.detach();
  //       });

  //       // micTracks.forEach((track) => {
  //       //   track.publication.track?.detach();
  //       // });
  //     };
  //   }
  // }, [cameraTracks, data?.livestream.type]);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () =>
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, []);

  const renderVideo = useMemo(() => {
    if (screenShareTracks.length === 0 && cameraTracks.length === 0) {
      return (
        <div
          className={cn(
            "flex w-full items-center justify-center bg-dark-bk",
            isFullscreen ? "h-full" : "aspect-video lg:rounded-xl",
          )}
        >
          <span className="text-sm text-off-white">
            Waiting for screen share or camera
          </span>
        </div>
      );
    }
    // if (
    //   screenShareTracks.length === 0 &&
    //   cameraTracks.length > 0 &&
    //   data?.livestream.type === "PRO"
    // ) {
    //   return (
    //     <video
    //       ref={videoRef}
    //       className={cn(
    //         " w-full bg-dark-bk",
    //         isFullscreen ? "h-full" : "max-h-[500px] lg:rounded-xl",
    //       )}
    //       // controls
    //     />
    //   );
    // } else
    if (screenShareTracks.length === 0 && cameraTracks.length > 0) {
      return (
        <>
          {cameraTracks.map((track, index) => (
            <CustomVideoTrack
              trackRef={track}
              key={"camera-share-" + index}
              className={cn(
                "w-full bg-dark-bk",
                isFullscreen ? "h-full" : "max-h-[500px] lg:rounded-xl",
              )}
            />
          ))}
        </>
      );
    }

    return (
      <>
        {screenShareTracks.map((track, index) => (
          <CustomVideoTrack
            trackRef={track}
            key={"screen-share-" + index}
            className={cn(
              "w-full bg-dark-bk",
              isFullscreen ? "h-full" : "max-h-[700px] lg:rounded-xl",
            )}
          />
        ))}
        {cameraTracks.map((track, index) => (
          <CustomVideoTrack
            trackRef={track}
            key={"camera-share-" + index}
            className={cn(
              "absolute  max-w-[100px] rounded-lg bg-dark-bk ",
              isFullscreen
                ? "bottom-6 right-6 lg:max-w-[400px]"
                : "bottom-4 right-4 lg:max-w-[200px]",
            )}
          />
        ))}
      </>
    );
  }, [cameraTracks, data?.livestream.type, isFullscreen, screenShareTracks]);

  return (
    <div
      className={cn(
        "relative overflow-hidden lg:rounded-xl",
        screenShareTracks.length === 0 &&
          cameraTracks.length === 0 &&
          !isFullscreen &&
          "border-off-white/30 lg:border",
      )}
      ref={containerRef}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onTouchStart={handleTouchStart}
    >
      {renderVideo}

      <div
        className={cn(
          "pointer-events-none absolute inset-x-0 bottom-0 transition-opacity duration-150",
          showControls ? "opacity-100" : "opacity-0",
        )}
        style={{
          height: "99px",
          backgroundImage:
            "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAADGCAYAAAAT+OqFAAAAdklEQVQoz42QQQ7AIAgEF/T/D+kbq/RWAlnQyyazA4aoAB4FsBSA/bFjuF1EOL7VbrIrBuusmrt4ZZORfb6ehbWdnRHEIiITaEUKa5EJqUakRSaEYBJSCY2dEstQY7AuxahwXFrvZmWl2rh4JZ07z9dLtesfNj5q0FU3A5ObbwAAAABJRU5ErkJggg==)",
          backgroundRepeat: "repeat-x",
          backgroundPosition: "bottom",
        }}
      />
      <div
        className={cn(
          "absolute inset-x-0 bottom-0 z-10 flex items-center justify-end p-1 transition-opacity duration-150",
          showControls ? "opacity-100" : "pointer-events-none opacity-0",
        )}
      >
        {micTracks.length > 0 && (
          <>
            {canPlayAudio ? (
              <>
                {muted ? null : (
                  <SliderPrimitive.Root
                    className={cn(
                      "relative flex w-16 touch-none select-none items-center",
                    )}
                    defaultValue={[volume]}
                    max={100}
                    step={1}
                    onValueChange={(value) => setVolume(value[0])}
                  >
                    <SliderPrimitive.Track className="relative h-1 w-full grow overflow-hidden rounded-full bg-off-white/30">
                      <SliderPrimitive.Range className="absolute h-full bg-off-white" />
                    </SliderPrimitive.Track>
                    <SliderPrimitive.Thumb className="block size-3 rounded-full bg-off-white transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50" />
                  </SliderPrimitive.Root>
                )}
                <button
                  className="ml-1 flex items-center gap-2 p-2"
                  onClick={() => setMuted(!muted)}
                >
                  {muted ? (
                    <VolumeOffOutlineIcon className="size-6 text-off-white" />
                  ) : (
                    <VolumeUpOutlineIcon className="size-6 text-off-white" />
                  )}
                </button>
              </>
            ) : (
              <button
                {...mergedProps}
                className="ml-1 flex items-center gap-2 p-2"
              >
                <VolumeOffOutlineIcon className="size-6 text-off-white" />
              </button>
            )}
          </>
        )}
        <button
          className="mr-1 rounded-full p-2"
          onClick={isFullscreen ? exitFullscreen : enterFullscreen}
        >
          <ArrowsExpandOutlineIcon className="size-6 text-off-white" />
        </button>
      </div>
      {/* data?.livestream.type !== "PRO" && */}
      {micTracks.map((track, index) => (
        <AudioTrack
          trackRef={track}
          key={"mic-" + index}
          volume={volume / 100}
          muted={muted}
        />
      ))}
    </div>
  );
}

function CustomVideoTrack({
  trackRef,
  className,
}: {
  trackRef: TrackReference;
  className?: string;
}) {
  const { isMuted } = useTrackMutedIndicator(trackRef);

  if (isMuted) return null;

  return <VideoTrack trackRef={trackRef} className={className} />;
}
