import { StateSnapshot, VirtuosoHandle } from "react-virtuoso";
import { create } from "zustand";

interface Store {
  stagesSnapshot?: StateSnapshot;
  stagesTimelineRef: { current: VirtuosoHandle | null };
}

interface Actions {
  setStagesSnapshot: (stagesSnapshot?: StateSnapshot) => void;
}

export const useStagesTimelineStore = create<Store & Actions>((set) => ({
  stagesSnapshot: undefined,
  stagesTimelineRef: {
    current: null,
  },
  setStagesSnapshot: (stagesSnapshot) => set({ stagesSnapshot }),
}));
