import { getAxios } from "@/lib/axios-server";

export const getTrades = async () => {
  const axios = getAxios();

  const response = await axios.get("/trade/trades");
  return response.data;
};

export const getRecentTrades = async () => {
  const axios = getAxios();

  const response = await axios.get("/trade/recent");
  return response.data;
};

export const getTrendingUsers = async () => {
  const axios = getAxios();

  const response = await axios.get("/trade/users/trending");
  return response.data;
};
