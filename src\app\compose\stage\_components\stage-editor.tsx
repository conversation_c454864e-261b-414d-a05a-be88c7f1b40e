"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";

import {
  ArrowBackOutlineIcon,
  CalendarOutlineIcon,
  TrashOutlineIcon,
} from "@/components/icons";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useCreateStageMutation, useEditStageMutation } from "@/queries";
import { stageQueries } from "@/queries/stage-queries";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";
import { ThreadPrivacyTypeEnum } from "@/types";
import { cn } from "@/utils";

import { DeleteStageModal } from "./delete-stage-modal";
import { StageScheduleModal } from "./stage-schedule-modal";

const MAX_LENGTH = 60;

export const StageEditor = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { user } = useUser();
  const searchParams = useSearchParams();
  const stageId = searchParams.get("stageId");

  const { data: stageData, isLoading } = useQuery({
    ...stageQueries.stageInfoSimple(stageId ?? ""),
    enabled: !!stageId,
  });

  const [spaceTopic, setSpaceTopic] = useState(
    () => stageData?.stage.name ?? "",
  );
  const [record, setRecord] = useState(
    () => stageData?.stage.isRecorded ?? false,
  );
  const [isGated, setIsGated] = useState(() => {
    if (!stageData?.stage) return false;
    return (
      stageData.stage.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS ||
      stageData.stage.privacyType === ThreadPrivacyTypeEnum.BADGEHOLDERS
    );
  });
  const [gateType, setGateType] = useState<"ticket" | "badge">(() => {
    if (!stageData?.stage) return "ticket";
    return stageData.stage.privacyType === ThreadPrivacyTypeEnum.BADGEHOLDERS
      ? "badge"
      : "ticket";
  });
  const [selectedBadges, setSelectedBadges] = useState<number[]>(
    () => stageData?.stage.badgeTypes?.map((badge) => badge.badgeType) ?? [],
  );
  const [scheduledDate, setScheduledDate] = useState<string | null>(
    () => stageData?.stage.scheduledStartTime ?? null,
  );

  const actions = useStageStore((state) => state.actions);

  const { mutateAsync: createStage, isPending } = useCreateStageMutation({
    onSuccess: (data) => {
      toast.green("You created a new stage!");
      setSpaceTopic("");
      setScheduledDate(null);
      setIsGated(false);
      setRecord(false);
      setGateType("ticket");
      setSelectedBadges([]);
      queryClient.resetQueries({
        queryKey: ["home", "threads", "stages-feed"],
      });
      if (data.token && !data.stage.scheduledStartTime) {
        router.replace(`/live?streamTab=stages`);
        actions.setToken(data.token ?? null);
        actions.setId(data.stage.id);
      } else {
        router.replace(`/${user?.twitterHandle}/status/${data.stage.threadId}`);
      }
    },
  });

  const { mutateAsync: editStage, isPending: isEditing } = useEditStageMutation(
    {
      onSuccess: (data) => {
        toast.green("Stage updated successfully!");
        setSpaceTopic("");
        setScheduledDate(null);
        setIsGated(false);
        setRecord(false);
        setGateType("ticket");
        setSelectedBadges([]);
        queryClient.resetQueries({
          queryKey: ["home", "threads", "stages-feed"],
        });
        router.replace(`/${user?.twitterHandle}/status/${data.stage.threadId}`);
      },
    },
  );

  const isButtonDisabled =
    spaceTopic.trim().length === 0 ||
    (gateType === "badge" && selectedBadges.length === 0);

  const handleStartStage = async () => {
    if (spaceTopic.trim() === "") {
      toast.danger("Please enter a name before proceeding");
      return;
    }

    if (stageId) {
      editStage({
        stageId: stageId,
        name: spaceTopic,
        record: record,
        privacyType:
          isGated && gateType === "ticket"
            ? ThreadPrivacyTypeEnum.SHAREHOLDERS
            : isGated && gateType === "badge"
              ? ThreadPrivacyTypeEnum.BADGEHOLDERS
              : ThreadPrivacyTypeEnum.PUBLIC,
        badgeTypes:
          isGated && gateType === "badge" && selectedBadges.length > 0
            ? selectedBadges
            : undefined,
        scheduledStartTime: scheduledDate ?? undefined,
      });
    } else {
      createStage({
        name: spaceTopic,
        record: record,
        privacyType:
          isGated && gateType === "ticket"
            ? ThreadPrivacyTypeEnum.SHAREHOLDERS
            : isGated && gateType === "badge"
              ? ThreadPrivacyTypeEnum.BADGEHOLDERS
              : ThreadPrivacyTypeEnum.PUBLIC,
        badgeTypes:
          isGated && gateType === "badge" && selectedBadges.length > 0
            ? selectedBadges
            : undefined,
        scheduledStartTime: scheduledDate ?? undefined,
      });
    }
  };

  useEffect(() => {
    if (stageId && !isLoading) {
      if (stageData) {
        if (stageData.stage.hostId !== user?.id) {
          toast.danger("You are not the host of this stage");
          router.replace("/compose/stage");
          return;
        }

        setSpaceTopic(stageData.stage.name);
        setRecord(stageData.stage.isRecorded);
        setIsGated(
          stageData.stage.privacyType !== ThreadPrivacyTypeEnum.PUBLIC,
        );
        setGateType(
          stageData.stage.privacyType === ThreadPrivacyTypeEnum.SHAREHOLDERS
            ? "ticket"
            : stageData.stage.privacyType === ThreadPrivacyTypeEnum.BADGEHOLDERS
              ? "badge"
              : "ticket",
        );
        setSelectedBadges(
          stageData.stage.badgeTypes?.map((badge) => badge.badgeType) ?? [],
        );
        setScheduledDate(stageData.stage.scheduledStartTime ?? null);
      } else {
        router.replace("/compose/stage");
      }
    }
  }, [stageId, stageData?.stage.scheduledStartTime, isLoading]);

  return (
    <div className="pt-pwa pb-pwa flex min-h-[100dvh] flex-col">
      <div className="flex items-center p-6">
        <div className="flex flex-1 justify-start">
          <button
            onClick={() => {
              if (window.history.length > 1) {
                router.back();
              } else {
                router.push("/home");
              }
            }}
          >
            <ArrowBackOutlineIcon className="size-5 text-off-white" />
          </button>
        </div>
        <h3 className="text-base font-semibold leading-5 text-white">
          Create your Stage
        </h3>
        {stageId && stageData && stageData.stage.hostId === user?.id ? (
          <div className="flex flex-1 justify-end">
            <DeleteStageModal>
              <button>
                <TrashOutlineIcon className="size-5 text-brand-orange" />
              </button>
            </DeleteStageModal>
          </div>
        ) : (
          <div className="flex-1" />
        )}
      </div>
      <div className="flex flex-grow flex-col sm:flex-grow-0 sm:border-b sm:border-dark-gray">
        <div className="p-6">
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between gap-2">
              <Label htmlFor="stage-editor" className="text-off-white">
                Name
              </Label>
            </div>
            <Input
              id="stage-editor"
              type="text"
              placeholder="Name your Stage!"
              className="min-h-[50px] w-full bg-transparent p-4 pr-10 text-sm text-off-white placeholder:text-gray-text focus:outline-none"
              value={spaceTopic}
              onChange={(e) => setSpaceTopic(e.target.value)}
              maxLength={MAX_LENGTH}
            >
              <span className="pointer-events-none absolute right-4 top-4 text-sm text-gray-text">
                {MAX_LENGTH - spaceTopic.length}
              </span>
            </Input>
          </div>
        </div>
        <div className="flex w-full flex-col gap-6 px-6 sm:justify-start sm:gap-4">
          <Label className="flex w-full items-center justify-between gap-2">
            <div className="text-sm font-medium normal-case text-off-white">
              Record my Stage
            </div>
            <Switch checked={record} onCheckedChange={setRecord} />
          </Label>
          <Label className="flex w-full items-center justify-between gap-2">
            <div className="flex flex-col gap-1.5">
              <div className="text-sm font-medium normal-case text-off-white">
                Gate my Stage
              </div>
              <p className="max-w-48 text-xs font-normal normal-case text-gray-text">
                Select who can join your Stage
              </p>
            </div>
            <Switch checked={isGated} onCheckedChange={setIsGated} />
          </Label>
          {isGated ? (
            <div className="mt-6 flex flex-col gap-4">
              <div className="flex items-center justify-between gap-4">
                <Button
                  className="flex-1 justify-start rounded-[10px] p-4 text-left text-sm"
                  onClick={() => setGateType("ticket")}
                  variant={gateType === "ticket" ? "default" : "outline"}
                >
                  Ticket Gated <br /> Stage
                </Button>
                <Button
                  className="flex-1 justify-start rounded-[10px] p-4 text-left text-sm"
                  onClick={() => setGateType("badge")}
                  variant={gateType === "badge" ? "default" : "outline"}
                >
                  Badge Gated <br /> Stage
                </Button>
              </div>
              {gateType === "ticket" ? (
                <p className="text-xs text-off-white">
                  Only your ticket holders will be allowed to join your stage.
                </p>
              ) : null}
              {gateType === "badge" ? (
                <>
                  <p className="text-xs text-off-white">
                    Pick up to 3 badges to allow them to join your stage.
                  </p>
                  <div className="flex flex-wrap items-center justify-center gap-[14px] rounded-[10px] bg-gray-bg px-4 py-6">
                    {badges.map(({ name, image, type }) => {
                      return (
                        <Button
                          key={type}
                          variant="outline"
                          className={cn(
                            "flex-shrink-0 gap-1.5 py-1.5 text-base font-medium leading-5",
                            selectedBadges.includes(type) &&
                              "border-brand-orange",
                          )}
                          onClick={() => {
                            if (selectedBadges.includes(type)) {
                              setSelectedBadges((prev) =>
                                prev.filter((t) => t !== type),
                              );
                            } else {
                              if (selectedBadges.length >= 3) {
                                toast.danger(
                                  "You can only select up to 3 badges",
                                );
                              } else {
                                setSelectedBadges((prev) => [...prev, type]);
                              }
                            }
                          }}
                        >
                          <div className="w-max">
                            <img
                              src={image}
                              className="h-[18px] w-auto"
                              alt={`${name} logo`}
                            />
                          </div>
                          <span>{name}</span>
                        </Button>
                      );
                    })}
                  </div>
                </>
              ) : null}
            </div>
          ) : null}
        </div>
        <div className="sticky bottom-0 mt-auto flex flex-shrink-0 flex-col gap-4 bg-dark-bk/65 p-6 pt-4 backdrop-blur-md sm:mt-0 sm:items-end sm:justify-between sm:gap-4">
          <div className="flex w-full gap-2">
            <Button
              className="w-full flex-grow sm:w-auto sm:px-8 sm:py-2"
              onClick={handleStartStage}
              loading={isPending || isEditing}
              disabled={isButtonDisabled}
            >
              {scheduledDate ? "Save Scheduled Stage" : "Start Now"}
            </Button>
            <StageScheduleModal
              scheduledDate={scheduledDate}
              setScheduledDate={setScheduledDate}
            >
              <Button variant="outline" className="size-11">
                <CalendarOutlineIcon className="size-6 text-off-white" />
              </Button>
            </StageScheduleModal>
          </div>
          {scheduledDate ? (
            <div className="w-full rounded-[10px] bg-chat-bubble p-2 text-center text-xs text-gray-text">
              Starting on{" "}
              {format(new Date(scheduledDate), "EEEE, MMM d 'at' h:mm a")}
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};

const badges = [
  {
    name: "Arena OGs",
    type: 1,
    image: "/assets/badges/badge-type-1.png",
  },
  {
    name: "DeGods",
    type: 2,
    image: "/assets/badges/badge-type-2.png",
  },
  {
    name: "Dokyo",
    type: 3,
    image: "/assets/badges/badge-type-3.png",
  },
  {
    name: "Sappy Seals",
    type: 4,
    image: "/assets/badges/badge-type-4.png",
  },
  {
    name: "GURS",
    type: 5,
    image: "/assets/badges/badge-type-5.png",
  },
  {
    name: "Nochill",
    type: 6,
    image: "/assets/badges/badge-type-6.png",
  },
  {
    name: "Steady",
    type: 8,
    image: "/assets/badges/badge-type-8.png",
  },
  {
    name: "Smol Joe",
    type: 9,
    image: "/assets/badges/badge-type-9.png",
  },
  {
    name: "Gogonauts",
    type: 11,
    image: "/assets/badges/badge-type-11.png",
  },
  {
    name: "Bodoggos",
    type: 12,
    image: "/assets/badges/badge-type-12.png",
  },
  {
    name: "Pudgy Penguins",
    type: 13,
    image: "/assets/badges/badge-type-13.png",
  },
  {
    name: "COQ",
    type: 14,
    image: "/assets/badges/badge-type-14.png",
  },
  {
    name: "MOG",
    type: 15,
    image: "/assets/badges/badge-type-15.png",
  },
  {
    name: "Mad Lads",
    type: 16,
    image: "/assets/badges/badge-type-16.png",
  },
  {
    name: "Nochillio",
    type: 18,
    image: "/assets/badges/badge-type-18.png",
  },
];
