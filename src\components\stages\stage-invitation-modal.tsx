"use client";

import { useEffect, useState } from "react";

import { useQuery } from "@tanstack/react-query";

import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import {
  useAcceptInvitationMutation,
  useDeclineInvitationMutation,
} from "@/queries";
import { stageQueries } from "@/queries/stage-queries";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogTitle,
} from "../ui/alert-dialog";
import { Button } from "../ui/button";
import { DialogHeader, DialogTitle } from "../ui/dialog";
import { Drawer, DrawerContent } from "../ui/drawer";
import { InvitedRole, ROLE_NAMES } from "./constants";
import { useDataChannelsContext } from "./stores/data-channels-context";

export function StageInvitationModal() {
  const { user } = useUser();
  const id = useStageStore((state) => state.id!);
  const [open, setOpen] = useState(false);
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const { data: isUserInvitedData, refetch } = useQuery({
    ...stageQueries.isUserInvited({
      stageId: id,
      userId: user!.id,
    }),
  });
  const invitedRole = isUserInvitedData?.user?.invitedRole;

  const { sendInvalidateStageInfo } = useDataChannelsContext();

  const { mutateAsync: acceptInvitation, isPending: isAcceptPending } =
    useAcceptInvitationMutation({
      onSuccess: () => {
        setOpen(false);
        sendInvalidateStageInfo();
      },
    });

  const { mutateAsync: declineInvitation, isPending: isDeclinePending } =
    useDeclineInvitationMutation({
      onSuccess: () => {
        setOpen(false);
        sendInvalidateStageInfo();
      },
    });

  const isPending = isAcceptPending || isDeclinePending;

  const handleAccept = async () => {
    setOpen(false);
    await acceptInvitation({
      stageId: id,
      userId: user!.id,
    });
    refetch();
  };

  const handleDecline = async () => {
    setOpen(false);
    await declineInvitation({
      stageId: id,
      userId: user!.id,
    });
    refetch();
  };

  useEffect(() => {
    if (isUserInvitedData) {
      setOpen(true);
    } else {
      setOpen(false);
    }
  }, [isUserInvitedData]);

  if (!invitedRole) return null;

  if (isTablet) {
    return (
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent className="max-w-md">
          <StageInvitationModalContent
            invitedRole={invitedRole}
            isPending={isPending}
            handleAccept={handleAccept}
            handleDecline={handleDecline}
          />
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen} dismissible={false}>
      <DrawerContent className="justify-start gap-6 text-left">
        <StageInvitationModalContent
          invitedRole={invitedRole}
          isPending={isPending}
          handleAccept={handleAccept}
          handleDecline={handleDecline}
          isDrawer
        />
      </DrawerContent>
    </Drawer>
  );
}

interface StageInvitationModalContentProps {
  invitedRole: InvitedRole;
  isPending: boolean;
  handleAccept: () => void;
  handleDecline: () => void;
  isDrawer?: boolean;
}

const StageInvitationModalContent = ({
  invitedRole,
  isPending,
  handleAccept,
  handleDecline,
  isDrawer,
}: StageInvitationModalContentProps) => {
  return (
    <>
      <DialogHeader className="space-y-4">
        {isDrawer ? (
          <DialogTitle>
            You are invited to be a {ROLE_NAMES[invitedRole]}
          </DialogTitle>
        ) : (
          <AlertDialogTitle>
            You are invited to be a {ROLE_NAMES[invitedRole]}
          </AlertDialogTitle>
        )}
      </DialogHeader>
      {invitedRole === "COHOST" && (
        <ul className="flex list-inside list-disc flex-col gap-2 text-sm text-gray-text">
          <li>Co-hosts can speak in the Stage</li>
          <li>Co-hosts can invite, remove and mute speakers</li>
          {/* <li>Co-hosts can remove listeners from the Space</li> */}
          {/* <li>Co-hosts can’t invite or remove other co-hosts</li> */}
          {/* <li>Co-hosts can’t end the Space</li> */}
        </ul>
      )}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          className="flex-[1]"
          disabled={isPending}
          onClick={handleDecline}
        >
          Decline
        </Button>
        <Button
          className="flex-[2]"
          onClick={handleAccept}
          disabled={isPending}
        >
          Join as a {ROLE_NAMES[invitedRole]}
        </Button>
      </div>
    </>
  );
};
