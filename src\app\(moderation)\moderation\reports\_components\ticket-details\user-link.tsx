"use client";

import { FC } from "react";

import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface UserLinkProps {
  user: { twitterHandle: string; twitterPicture: string };
}

export const UserLink: FC<UserLinkProps> = ({ user }) => (
  <ProgressBarLink
    className="flex items-center gap-2 text-brand-orange"
    href={`/${user.twitterHandle}`}
  >
    <Avatar className="size-5">
      <AvatarImage src={user.twitterPicture} />
      <AvatarFallback />
    </Avatar>
    @{user.twitterHandle}
  </ProgressBarLink>
);
