"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import { InfiniteData, useQuery, useQueryClient } from "@tanstack/react-query";

import { TrashOutlineIcon } from "@/components/icons";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { livestreamQueries, useDeleteThreadMutation } from "@/queries";
import { ThreadsResponse } from "@/queries/types";

export function DeleteLivestreamModal({
  children,
}: {
  children: React.ReactNode;
}) {
  const [open, setOpen] = useState(false);
  const searchParams = useSearchParams();
  const livestreamId = searchParams.get("livestreamId");
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: livestreamData, isLoading } = useQuery({
    ...livestreamQueries.livestreamSimpleInfo(livestreamId ?? ""),
    enabled: !!livestreamId,
  });

  const { mutateAsync: deleteThread, isPending: isDeleting } =
    useDeleteThreadMutation({
      onMutate: async ({ threadId }) => {
        toast.red("Livestream deleted successfully");
        await queryClient.cancelQueries({
          queryKey: ["home", "threads", "my-feed"],
        });
        await queryClient.cancelQueries({
          queryKey: ["home", "threads", "trending-feed"],
        });

        const previousMyFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = queryClient.getQueryData([
          "home",
          "threads",
          "my-feed",
        ]);
        const previousTrendingFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = queryClient.getQueryData([
          "home",
          "threads",
          "trending-feed",
        ]);

        queryClient.setQueryData(
          ["home", "threads", "my-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.filter((t) => t.id !== threadId),
                };
              }),
            };
          },
        );
        queryClient.setQueryData(
          ["home", "threads", "trending-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page) => {
                return {
                  ...page,
                  threads: page.threads.filter((t) => t.id !== threadId),
                };
              }),
            };
          },
        );

        router.push("/live?streamTab=livestreams");
        queryClient.resetQueries({
          queryKey: ["home", "threads", "livestreams-feed"],
        });
        return { previousMyFeed, previousTrendingFeed };
      },
      onError(err, variables, context) {
        queryClient.setQueryData(
          ["home", "threads", "my-feed"],
          context?.previousMyFeed,
        );
        queryClient.setQueryData(
          ["home", "threads", "trending-feed"],
          context?.previousTrendingFeed,
        );
      },
    });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-xs gap-6 rounded-[10px] border border-[rgba(59,59,59,0.30)] bg-[rgba(15,15,15,0.90)] shadow-[0px_0px_10px_0px_rgba(255,255,255,0.25)] backdrop-blur-sm">
        <div className="flex flex-col items-center gap-2">
          <TrashOutlineIcon className="mx-auto size-6 text-brand-orange" />
          <h3 className="text-center text-base font-semibold text-off-white">
            Delete your scheduled Live Stream
          </h3>
        </div>
        <div className="flex flex-col gap-2 text-sm text-gray-text">
          <p>
            You can edit every detail of your scheduled Live Stream, are you
            sure you want to continue?
          </p>
        </div>
        <div className="flex gap-2">
          <DialogClose className="flex-[1]" asChild>
            <Button variant="outline">Close</Button>
          </DialogClose>
          <Button
            className="flex-[2]"
            loading={isDeleting}
            onClick={() => {
              if (!livestreamData || isLoading) return;

              deleteThread({
                threadId: livestreamData.livestream.threadId,
              });
            }}
          >
            Delete
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
