"use client";

import { TrendingCommunityProfiles } from "@/app/_components/trending-group-profiles";
import { TrendingProfiles } from "@/app/_components/trending-profiles";
import { LiveStagesSidebar } from "@/components/stages/live-stages-sidebar";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";
import { useTrendingUsersQuery } from "@/queries";
import { useTrendingGroupsQuery } from "@/queries/groups-queries";

export const RightSide = () => {
  const isLaptop = useMediaQuery(BREAKPOINTS.lg);
  const { data: trendingUsersData, isLoading: isTrendingUsersLoading } =
    useTrendingUsersQuery({
      // Enabled fetching trending users only on laptop screens
      enabled: isLaptop,
    });

  const { data: trendingGroupsData, isLoading: isTrendingGroupsLoading } =
    useTrendingGroupsQuery({
      // Enabled fetching trending users only on laptop screens
      enabled: isLaptop,
    });

  return (
    <div className="hide-scrollbar fixed top-0 flex h-full max-h-screen w-full flex-col overflow-y-auto px-[3px] py-8 lg:w-[290px] xl:w-[380px]">
      <LiveStagesSidebar />
      <TrendingProfiles
        users={trendingUsersData?.users || []}
        isLoading={isTrendingUsersLoading}
      />
      <TrendingCommunityProfiles
        communities={trendingGroupsData}
        isLoading={isTrendingGroupsLoading}
      />
    </div>
  );
};
