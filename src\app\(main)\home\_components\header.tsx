"use client";

import { useEffect, useState } from "react";

import { motion, useTransform } from "framer-motion";

import { MiniBanner } from "@/components/banner";
import { useBoundedScroll } from "@/hooks";

import { MenuModal } from "./menu-modal";

export const Header = ({ children }: { children: React.ReactNode }) => {
  const { scrollYBoundedProgress } = useBoundedScroll(300);
  const [isPageTop, setIsPageTop] = useState(true);
  const scrollYBoundedProgressDelayed = useTransform(
    scrollYBoundedProgress,
    [0, 0.3, 1],
    [0, 1, 1],
  );

  const y = useTransform(
    scrollYBoundedProgressDelayed,
    [0, 1],
    ["0%", "-100%"],
  );

  useEffect(() => {
    const handleScroll = () => {
      setIsPageTop(window.scrollY <= 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <motion.div
      className="sticky top-0 z-50 flex flex-col bg-dark-bk bg-opacity-65 pt-[calc(22px+env(safe-area-inset-top))]  backdrop-blur-md sm:hidden"
      style={{
        y: isPageTop ? 0 : y,
      }}
    >
      <div className="flex items-center justify-between px-5 pb-[18px]">
        <div className="flex flex-1 items-center">
          <MenuModal />
        </div>
        <div className="flex flex-1 justify-end py-2 pr-1">
          <MiniBanner />
        </div>
      </div>
      {children}
    </motion.div>
  );
};
