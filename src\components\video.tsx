"use client";

import { ComponentProps, useEffect, useRef } from "react";

import { useInView } from "react-intersection-observer";

export const Video = ({ src, ...props }: ComponentProps<"video">) => {
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const { inView, ref: inViewRef } = useInView({
    threshold: 0.8,
  });

  useEffect(() => {
    if (inView) {
      videoRef.current?.play();
    } else {
      videoRef.current?.pause();
    }
  }, [inView]);

  return (
    <video
      src={src}
      ref={(ref) => {
        videoRef.current = ref;
        inViewRef(ref);
      }}
      className="w-full rounded-[10px]"
      loop
      muted
      controls
      playsInline
      webkit-playsinline="true"
      {...props}
    />
  );
};
