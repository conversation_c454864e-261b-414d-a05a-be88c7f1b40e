import { BottomNav } from "../(main)/_components/bottom-nav";
import { SideNav } from "../(main)/_components/side-nav";

interface ModerationLayoutProps {
  children: React.ReactNode;
}

function ModerationLayout({ children }: ModerationLayoutProps) {
  return (
    <div className="mx-auto flex min-h-[100svh] max-w-[1350px] flex-col flex-nowrap items-stretch justify-center sm:flex-row">
      <header className="relative z-[3] hidden sm:flex sm:flex-shrink-0 sm:flex-grow sm:flex-col sm:items-end">
        <SideNav />
      </header>
      {children}
      <BottomNav />
    </div>
  );
}

export default ModerationLayout;
