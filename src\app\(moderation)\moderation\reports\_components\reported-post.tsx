import { parseISO } from "date-fns";

import { PostLivestreamInfo } from "@/components/livestream/post-livestream-info";
import { ProgressBarLink } from "@/components/progress-bar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Video } from "@/components/video";
import { Thread } from "@/types";
import { formatTimeDistance } from "@/utils";

export const ReportedPost = ({ thread }: { thread: Thread }) => {
  const isLivestream = thread.threadType === "livestream" && thread.livestream;

  return (
    <div className="rounded-lg border border-dark-gray p-3">
      <div className="flex items-center gap-2">
        <ProgressBarLink href={`/${thread.user?.twitterHandle}`}>
          <Avatar className="h-[17px] w-[17px]">
            <AvatarImage src={thread.user?.twitterPicture} />
            <AvatarFallback />
          </Avatar>
        </ProgressBarLink>
        <div className="flex min-w-0 items-center text-sm text-[#878787]">
          <ProgressBarLink
            href={`/${thread.user?.twitterHandle}`}
            className="truncate font-semibold text-[#F4F4F4]"
          >
            {thread.userName}
          </ProgressBarLink>
          ・<span className="truncate">@{thread.user?.twitterHandle}</span>・
          <span className="flex-shrink-0">
            {formatTimeDistance(parseISO(thread.createdDate))}
          </span>
        </div>
      </div>
      {isLivestream && thread.livestream != null ? (
        <div className="mt-2">
          <PostLivestreamInfo
            livestream={thread.livestream}
            host={{
              ...thread.user,
              twitterName: thread?.user?.twitterName ?? thread.userName,
            }}
          />
        </div>
      ) : (
        <>
          <div
            className="mt-2 text-sm text-[#F4F4F4]"
            dangerouslySetInnerHTML={{ __html: thread.content || "" }}
          />
          {thread.images?.length > 0 && (
            <img
              src={thread.images[0].url}
              alt=""
              className="mt-3 max-h-[1000px] w-full rounded-2xl object-cover"
            />
          )}
          {thread.videos?.length > 0 && (
            <Video
              src={thread.videos[0].url}
              className="mt-3 max-h-[500px] rounded-2xl"
            />
          )}
        </>
      )}
    </div>
  );
};
