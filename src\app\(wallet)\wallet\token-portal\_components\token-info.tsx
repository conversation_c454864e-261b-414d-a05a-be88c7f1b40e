import { useState } from "react";

import { InformationCircleOutlineIcon } from "@/components/icons";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import { BREAKPOINTS, useMediaQuery } from "@/hooks/use-media-query";

export const ArenaTokensInfoModal = () => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [open, setOpen] = useState(false);

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <div className="flex w-full justify-center">
          <DialogTrigger asChild>
            <Button
              variant="outline"
              className="mb-4 mt-auto flex items-center gap-[10px] border-none text-off-white"
            >
              <InformationCircleOutlineIcon className="size-6" />
              <span className="text-sm font-semibold underline">
                How do I claim my locked tokens?
              </span>
            </Button>
          </DialogTrigger>
        </div>
        <DialogContent className="gap-4">
          <ArenaTokensInfoModalContent />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button
          variant="outline"
          className="mb-4 mt-auto flex w-full items-center gap-[10px] border-none text-off-white"
        >
          <InformationCircleOutlineIcon className="size-6" />
          <span className="text-sm font-semibold underline">
            How do I claim my locked tokens?
          </span>
        </Button>
      </DrawerTrigger>
      <DrawerContent className="gap-4">
        <ArenaTokensInfoModalContent />
      </DrawerContent>
    </Drawer>
  );
};

export const ArenaTokensStakingInfoModal = () => {
  const isTablet = useMediaQuery(BREAKPOINTS.sm);
  const [open, setOpen] = useState(false);

  if (isTablet) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <div className="flex w-full justify-center">
          <DialogTrigger asChild>
            <Button
              variant="outline"
              className="mb-4 mt-auto flex items-center gap-[10px] border-none text-off-white"
            >
              <InformationCircleOutlineIcon className="size-6" />
              <span className="text-sm font-semibold underline">
                Staking Information
              </span>
            </Button>
          </DialogTrigger>
        </div>
        <DialogContent className="gap-4">
          <ArenaTokensStakingInfoModalContent />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button
          variant="outline"
          className="mb-4 mt-auto flex w-full items-center gap-[10px] border-none text-off-white"
        >
          <InformationCircleOutlineIcon className="size-6" />
          <span className="text-sm font-semibold underline">
            Staking Information
          </span>
        </Button>
      </DrawerTrigger>
      <DrawerContent className="gap-4">
        <ArenaTokensStakingInfoModalContent />
      </DrawerContent>
    </Drawer>
  );
};

const ArenaTokensStakingInfoModalContent = () => (
  <>
    <h3 className="text-base font-semibold leading-[22px] text-off-white">
      How Staking Works?
    </h3>
    <ul className="list-inside list-disc space-y-2">
      <li className="text-sm text-[#B5B5B5]">
        Stake your ARENA tokens with no commitment and receive various ERC-20
        tokens as rewards (e.g., WAVAX, ARENA), with the type of token changing
        over time.
      </li>
      <li className="text-sm text-light-gray-text">
        Rewards are allocated based on the amount staked at the time of
        distribution.
      </li>
      <li className="text-sm text-[#B5B5B5]">
        A deposit fee in ARENA tokens may apply (currently 0%)
      </li>
    </ul>
  </>
);

const ArenaTokensInfoModalContent = () => (
  <>
    <h3 className="text-base font-semibold leading-[22px] text-off-white">
      How to Claim Your Locked Tokens
    </h3>
    <p className="text-sm text-[#B5B5B5]">
      To unlock 100% of your airdrop, you will need to be an active user in the
      Arena. Your eligibility will be based on various activities, including but
      not limited to:
    </p>
    <ul className="mt-2 list-inside list-disc pl-2 text-sm text-[#B5B5B5]">
      <li>Arena Token Staking and Other Usage</li>
      <li>Portfolio Value</li>
      <li>Trading Volume</li>
      <li>Content Contributions (Timeline & Chatroom)</li>
      <li>Referrals</li>
    </ul>
    <p className="mt-2 text-sm text-[#B5B5B5]">
      We compare some metrics across users to ensure fairness. As The Arena
      evolves, new features and activities will also contribute to your score.
    </p>
    <p className="mt-2 text-sm text-[#B5B5B5]">
      The airdrop is designed to reward users who actively add value to the
      Arena, with an adaptive algorithm that recognizes and supports those
      driving the platform&apos;s growth.
    </p>
  </>
);
