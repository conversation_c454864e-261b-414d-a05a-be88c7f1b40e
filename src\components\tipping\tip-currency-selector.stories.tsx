import React, { useState } from "react";

import { SystemCurrency } from "@/api/client/currency";

import { TipCurrencySelector } from "./tip-currency-selector";

export const mockCurrencies: SystemCurrency[] = [
  {
    symbol: "ARENA",
    systemRate: "0.00998564",
    name: "The Arena",
    balance: "62.0783",
    isToken: false,
    photoURL: "",
    contractAddress: "0xB8d7710f7d8349A506b75dD184F05777c82dAd0C",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0.61",
  },
  {
    symbol: "AVAX",
    systemRate: "23.85",
    name: "Avalanche",
    balance: "0.02",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/0ec271f5-5ea6-4beb-c16f-f5c9642f68361745845671523.png",
    contractAddress: "0xEeeeeEeeeEeEeeEeEeEeeEEEeeeeEeeeeeeeEEeE",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0.47",
  },
  {
    symbol: "NOCHILL",
    systemRate: "0.00242924",
    name: "AVAX HAS NO CHILL",
    balance: "431",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/414ecdd0-dfd7-53c7-f498-d06a67078f2e1745845775051.png",
    contractAddress: "0xAcFb898Cff266E53278cC0124fC2C7C94C8cB9a5",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "1.04",
  },
  {
    symbol: "LOK",
    systemRate: "0.00046401",
    name: "LokUnchained",
    balance: "1480",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/1cd1557b-5c5c-6d9a-32d1-3be7abaef6001741995411361.jpeg",
    contractAddress: "0x41CBfD7C8709d1E459CE816703a1Fc882DAa77D3",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0.68",
  },
  {
    symbol: "COQ",
    systemRate: "0.000000866915",
    name: "COQINU",
    balance: "999",
    isToken: false,
    photoURL: "",
    contractAddress: "0x420FcA0121DC28039145009570975747295f2329",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "ABC",
    systemRate: "0.0001575459",
    name: "AuTistiC BoYs CLub",
    balance: "0",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/ddf330bf-76bf-7a08-2177-4a965d7709051742992475055.jpeg",
    contractAddress: "0x18cffcbb1ea17ae665dfd44e6464f09d8618d8cd",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "BOI",
    systemRate: "0.00138522",
    name: "BoiTheBear",
    balance: "0",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/eccb3408-e9a8-10f2-2e86-3e279055e4091741938770976.png",
    contractAddress: "0xACC95Afa65768aa74044E6f6e267AD6417CD3e55",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "EROL",
    systemRate: "0.00029234",
    name: "EROL MUSK",
    balance: "0",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/79e49b6e-c73c-884c-128b-5e55ace300cb1746701817721.jpeg",
    contractAddress: "0xcac4904e1db1589aa17a2ec742f5a6bcf4c4d037",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "JOE",
    systemRate: "0.202102",
    name: "Joe",
    balance: "0",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/1ae02857-a563-3b52-99e0-e4feedc1c2281745845983462.png",
    contractAddress: "0x6e84a6216ea6dacc71ee8e6b0a5b7322eebc0fdd",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "WINK",
    systemRate: "0.00178636",
    name: "Wink Realm",
    balance: "0",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/78c6c405-2bf2-34ad-1217-9783c69bc9591745846100715.jpeg",
    contractAddress: "0x7698A5311DA174A95253Ce86C21ca7272b9B05f8",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "KIMBO",
    systemRate: "0.00002515",
    name: "Kimbo",
    balance: "0",
    isToken: false,
    photoURL: "",
    contractAddress: "0x184ff13b3ebcb25be44e860163a5d8391dd568c1",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "BLUB",
    systemRate: "0.00105933",
    name: "BlubBlobCoin",
    balance: "0",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/6f9c12a0-4fa0-fffd-769e-e9b2ec2c1d151740658138460.png",
    contractAddress: "0x0f669808d88b2b0b3d23214dcd2a1cc6a8b1b5cd",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "MU",
    systemRate: "0.169198",
    name: "Mu Coin",
    balance: "0",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/eb4fc7f9-b20e-594a-0ae2-783f5f79b3dd1742881400112.jpeg",
    contractAddress: "0xD036414fa2BCBb802691491E323BFf1348C5F4Ba",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "KET",
    systemRate: "0.440289",
    name: "Yellow Ket",
    balance: "0",
    isToken: false,
    photoURL: "",
    contractAddress: "0xffff003a6bad9b743d658048742935fffe2b6ed7",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "TECH",
    systemRate: "0.00000475",
    name: "NumberGoUpTech",
    balance: "0",
    isToken: false,
    photoURL: "",
    contractAddress: "0x5ac04b69bde6f67c0bd5d6ba6fd5d816548b066a",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "MEAT",
    systemRate: "0.00000137",
    name: "Meat",
    balance: "0",
    isToken: false,
    photoURL: "",
    contractAddress: "0x47c3118ad183712acd42648e9e522e13690f29a0",
    decimals: 6,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "CHAMP",
    systemRate: "0.0001833103",
    name: "Arena Champion",
    balance: "0",
    isToken: false,
    photoURL: "",
    contractAddress: "0xb0Aa388A35742F2d54A049803BFf49a70EB99659",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "GURS",
    systemRate: "0.00000159",
    name: "GURSONAVAX",
    balance: "0",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/4a6eb443-2cf7-45fa-7908-5675a6bd59ee1745845751099.png",
    contractAddress: "0x223a368ad0e7396165fc629976d77596a51f155c",
    decimals: 18,
    blockchain: "avalanche",
    balanceUsd: "0",
  },
  {
    symbol: "Moutai",
    systemRate: "0.00046421",
    name: "Moutai",
    balance: "0.00",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/8d22ad30-4b84-4021-a204-16c422ce17851745845941881.jpeg",
    contractAddress: "45EgCwcPXYagBC7KqBin4nCFgEZWN7f3Y6nACwxqMCWX",
    decimals: 6,
    blockchain: "solana",
    balanceUsd: "0",
  },
  {
    symbol: "$WIF",
    systemRate: "1.055",
    name: "DOGWIFHAT",
    balance: "0.00",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/d44f463d-f64d-4dde-d783-69955bc580291745846144920.jpeg",
    contractAddress: "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm",
    decimals: 6,
    blockchain: "solana",
    balanceUsd: "0",
  },
  {
    symbol: "Bonk",
    systemRate: "0.00002136",
    name: "Bonk",
    balance: "0.00",
    isToken: false,
    photoURL: "",
    contractAddress: "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
    decimals: 5,
    blockchain: "solana",
    balanceUsd: "0",
  },
  {
    symbol: "USEDCAR",
    systemRate: "0.00220789",
    name: "A Gently Used 2001 Honda",
    balance: "0.00",
    isToken: false,
    photoURL:
      "https://static.starsarena.com/uploads/d126c909-eea6-1811-c6a1-0b57705b30f61745845581467.png",
    contractAddress: "9gwTegFJJErDpWJKjPfLr2g2zrE3nL1v5zpwbtsk3c6P",
    decimals: 9,
    blockchain: "solana",
    balanceUsd: "0",
  },
  {
    symbol: "HARAMBE",
    systemRate: "0.00826484",
    name: "Harambe On Solana",
    balance: "0.00",
    isToken: false,
    photoURL: "",
    contractAddress: "Fch1oixTPri8zxBnmdCEADoJW2toyFHxqDZacQkwdvSP",
    decimals: 9,
    blockchain: "solana",
    balanceUsd: "0",
  },
];

export default {
  title: "Tipping/TipCurrencySelector",
  component: TipCurrencySelector,
};

export const Default = () => {
  const [value, setValue] = useState("AVAX");
  const token: SystemCurrency =
    mockCurrencies.find((c) => c.symbol === value) || mockCurrencies[0];

  return (
    <div style={{ maxWidth: 320 }}>
      <TipCurrencySelector
        token={token}
        sortedCurrencies={mockCurrencies}
        onValueChange={setValue}
      />
    </div>
  );
};
