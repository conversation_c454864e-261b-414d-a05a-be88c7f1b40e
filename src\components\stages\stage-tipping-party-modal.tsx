"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";

import { isEthereumWallet } from "@dynamic-labs/ethereum";
import { useDynamicContext } from "@dynamic-labs/sdk-react-core";
import { zodResolver } from "@hookform/resolvers/zod";
import { useParticipants } from "@livekit/components-react";
import * as RadioGroup from "@radix-ui/react-radio-group";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { RoomEvent } from "livekit-client";
import { useForm } from "react-hook-form";
import { parseUnits } from "viem";
import { z } from "zod";

import { postTippingPartyNotify } from "@/api/client/chat";
import { batchMultiSendDynamic } from "@/api/client/dynamic/send-funds-dynamic";
import { getUserAddressesForMultiSendBatched } from "@/api/client/user";
import { TipPartyContent } from "@/app/(messages)/messages/_components/TipPartyContent";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { TippingInfoModal } from "@/components/tipping-info";
import { TipFormContent } from "@/components/tipping/tip-form-content";
import { TippingRecipients } from "@/components/tipping/tipping-recipients";
import { toast } from "@/components/toast";
import { ArenaDialogHeader } from "@/components/ui/arena-dialog-header";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { fallbackArena } from "@/environments/tokens";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import {
  useAvaxPriceQuery,
  useTippableCurrenciesQuery,
} from "@/queries/currency-queries";
import { useSendTipsMutation } from "@/queries/send-tips-mutation";
import { stageQueries } from "@/queries/stage-queries";
import { Tip } from "@/queries/types/send-tips-data";
import { useUser } from "@/stores";
import { useStageStore } from "@/stores/stage";
import { cn } from "@/utils/cn";
import { formatPrice } from "@/utils/format-token-price";
import { formatNumericValue } from "@/utils/number";

import { Role } from "./constants";
import { useDataChannelsContext } from "./stores/data-channels-context";

const TIP_OPTION = {
  EVERY_PARTICIPANT: "EVERY_PARTICIPANT",
  EVERY_SPEAKER: "EVERY_SPEAKER",
} as const;

type Option = (typeof TIP_OPTION)[keyof typeof TIP_OPTION];

type FormattedParticipant = {
  id: string;
  name: string;
  avatar: string;
  username: string;
  role: Role;
};

interface StageTippingPartyModalProps {
  children: React.ReactNode;
  dataChannelsContext?: ReturnType<typeof useDataChannelsContext>;
  user?: any;
  stageStoreSnapshot?: {
    id: string;
    participants: FormattedParticipant[];
    speakers: any[];
    sortedCurrencies: any[];
    token: any;
  };
}

export const StageTippingPartyModal = ({
  children,
  dataChannelsContext,
  user: userProp,
  stageStoreSnapshot,
}: StageTippingPartyModalProps) => {
  // State for tip amount, option, etc.
  const [tipAmount, setTipAmount] = useState("0");
  const [tipAccurateAmount, setTipAccurateAmount] = useState("0");
  const [singleTipUSD, setSingleTipUSD] = useState("0");
  const [option, setOption] = useState<Option>(TIP_OPTION.EVERY_PARTICIPANT);
  const [isTippingDisabled, setIsTippingDisabled] = useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [open, setOpen] = useState(false);
  const queryClient = useQueryClient();
  const { user: userStore } = useUser();
  const [step, setStep] = useState<1 | 2>(1);

  // Use snapshot if provided, otherwise fallback to previous logic
  const id = stageStoreSnapshot?.id ?? useStageStore((state) => state.id!);
  let participants: FormattedParticipant[] = [];
  let speakers: any[] = [];
  let refetch = () => {};
  if (stageStoreSnapshot) {
    participants = stageStoreSnapshot.participants;
    speakers = stageStoreSnapshot.speakers;
    // no refetch needed in snapshot mode
  } else {
    // fallback to previous logic for production
    const liveParticipants = useParticipants({
      updateOnlyOn: [
        RoomEvent.ParticipantConnected,
        RoomEvent.ParticipantDisconnected,
      ],
    });
    const { user: userStore } = useUser();
    participants = useMemo(() => {
      const currentParticipants = liveParticipants.map((participant) => ({
        id: participant.identity,
        name: participant.attributes?.name,
        avatar: participant.attributes?.avatar,
        username: participant.attributes?.username,
        role: participant.attributes?.role,
      })) as FormattedParticipant[];
      return currentParticipants.filter(
        (participant) => participant.id !== userStore?.id,
      );
    }, [liveParticipants, userStore]);
    const { data: stageSpeakersData, refetch: refetchSpeakers } = useQuery({
      ...stageQueries.stageSpeakers(id),
      staleTime: 10 * 60 * 1000,
    });
    refetch = refetchSpeakers;
    speakers = useMemo(() => {
      return (
        stageSpeakersData?.speakers.filter(
          (speaker) => speaker.userId !== userStore?.id,
        ) ?? []
      );
    }, [stageSpeakersData?.speakers, userStore]);
  }
  const sortedCurrencies =
    stageStoreSnapshot?.sortedCurrencies ??
    useWalletCurrencies({
      user: userStore,
      currenciesData: undefined,
      isCurrenciesLoading: false,
    }).sortedCurrencies;
  const token =
    stageStoreSnapshot?.token ??
    sortedCurrencies.find((t: any) => t.symbol === "ARENA") ??
    fallbackArena;

  const context = dataChannelsContext ?? useDataChannelsContext();
  const { send } = context.chat;

  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useTippableCurrenciesQuery();
  const { data: avaxPrice } = useAvaxPriceQuery();
  const { sortedCurrencies: sortedCurrenciesState } = useWalletCurrencies({
    user: userStore,
    currenciesData,
    isCurrenciesLoading,
  });
  const [symbol, setSymbol] = useState("ARENA");
  const tokenState = useMemo(
    () =>
      sortedCurrenciesState.find((t) => t.symbol === symbol) ?? fallbackArena,
    [sortedCurrenciesState, symbol],
  );

  // Use props if provided, otherwise fallback to state/hooks
  const user = userProp ?? userStore;

  const sendTipsInput = z.object({
    currency: z.string(),
    amountPerTip: z
      .string()
      .min(1, {
        message: "Tip amount is required",
      })
      .refine((v) => !isNaN(parseFloat(v.replace(/,/g, ""))), {
        message: "Tip amount must be a number",
      })
      .refine(
        (v) =>
          calculateSendTips() * parseFloat(v.replace(/,/g, "")) <=
          parseFloat(
            token.isToken
              ? formatPrice(token.balance || "0").toString()
              : (token.balance || "0").toString().replace(/,/g, ""),
          ),
        {
          message: "Insufficient balance",
        },
      ),
    totalAmount: z.string(),
  });
  type sendTipsInputType = z.infer<typeof sendTipsInput>;

  const form = useForm<sendTipsInputType>({
    defaultValues: {
      currency: "ARENA",
      amountPerTip: "",
      totalAmount: "",
    },
    resolver: zodResolver(sendTipsInput),
    mode: "all",
  });

  const { mutateAsync: sendTips, isPending } = useSendTipsMutation({
    onSuccess: () => {
      toast.green("Tips sent!");
      setOpen(false);
      form.reset();
      setTipAmount("0");
      setSingleTipUSD("0");
      setOption(TIP_OPTION.EVERY_PARTICIPANT);
      queryClient.invalidateQueries({
        queryKey: ["wallet", "balance", user?.address, token.symbol],
      });
      setSymbol("ARENA");
    },
  });

  const calculateSendTips = () => {
    if (option === TIP_OPTION.EVERY_SPEAKER) {
      return speakers.length;
    } else {
      return participants.length;
    }
  };

  const { primaryWallet } = useDynamicContext();

  const [isPendingDynamic, setIsPendingDynamic] = useState(false);

  const onSubmit = async (values: sendTipsInputType) => {
    const tips: Tip[] =
      option === TIP_OPTION.EVERY_SPEAKER
        ? speakers.map((speaker) => ({
            userId: speaker.userId,
            amount: tipAmount.replace(/,/g, ""),
          })) ?? []
        : participants.map((participant) => ({
            userId: participant.id,
            amount: tipAmount.replace(/,/g, ""),
          }));

    const message = {
      type: "tip-party",
      data: {
        amount: Number(values.amountPerTip.replace(/,/g, "")),
        currency: values.currency,
        type: option,
      },
    };

    if (user) {
      if (user.address === user.dynamicAddress?.toLowerCase()) {
        setIsPendingDynamic(true);
        const userIds = tips.map((tip) => tip.userId);
        const toAddresses: string[] =
          await getUserAddressesForMultiSendBatched(userIds);

        const decimals = values.currency === "MEAT" ? 6 : 18;
        const amounts = tips.map((tip) =>
          token.isToken
            ? BigInt(tipAccurateAmount)
            : parseUnits(tip.amount, decimals),
        );
        const tippedToken = sortedCurrencies.find(
          (token) => token.symbol === values.currency,
        );

        if (!primaryWallet) {
          return;
        }
        if (!isEthereumWallet(primaryWallet)) {
          toast.danger("This wallet is not an Ethereum wallet");
          setIsPendingDynamic(false);
          return;
        }

        if (toAddresses.length !== amounts.length) {
          toast.danger("Mismatch between number of addresses and amounts");
          setIsPendingDynamic(false);
          return;
        }

        try {
          const { txHash, txData } = await batchMultiSendDynamic(
            primaryWallet,
            toAddresses,
            amounts,
            values.currency,
            tippedToken?.isToken || false,
            tippedToken?.contractAddress,
          );

          try {
            if (txHash && txData && txHash.length && txData.length) {
              for (let i = 0; i < txHash.length; i++) {
                await postTippingPartyNotify({
                  currency: values.currency,
                  txHash: txHash[i],
                  txData: txData[i],
                });
              }
            }
          } catch (error) {
            console.error("Error in postTipNotify:", error);
          }
          await send(JSON.stringify(message));
          toast.green("Tips sent!");
        } catch (error) {
          toast.danger("Error occured while tipping, please try again!");
          console.error("Error in multiSendDynamic:", error);
        } finally {
          setIsPendingDynamic(false);
          setOpen(false);
          form.reset();
          setTipAmount("0");
          setSingleTipUSD("0");
          setOption(TIP_OPTION.EVERY_PARTICIPANT);
          queryClient.invalidateQueries({
            queryKey: ["wallet", "balance", user?.address, token.symbol],
          });
          setSymbol("ARENA");
          queryClient.invalidateQueries({
            queryKey: ["currency", "system"],
          });
        }
      } else {
        try {
          await sendTips({
            currency: values.currency,
            tips,
          });
          await send(JSON.stringify(message));
        } catch (error) {
          console.error("Error in sendTips:", error);
        }
      }
    }
  };

  const tipValue = async (rate: string, value: string) => {
    setSingleTipUSD(
      formatNumericValue((Number(value) * Number(rate)).toFixed(2).toString()),
    );
  };

  const handleOpenChange = useCallback(
    (open: boolean) => {
      setOpen(open);
      if (!open) {
        form.reset();
        setOption(TIP_OPTION.EVERY_PARTICIPANT);
        setSymbol("ARENA");
        setSingleTipUSD("0");
        setTipAmount("0");
        setTipAccurateAmount("0");
      }
    },
    [form],
  );

  useEffect(() => {
    const sanitizedValue = tipAmount.replace(/,/g, "");
    const requiredBalance = Number(sanitizedValue) * calculateSendTips();
    if (
      requiredBalance >
        parseFloat(
          token.isToken
            ? formatPrice(token.balance || "0").toString()
            : (token.balance || "0").toString().replace(/,/g, ""),
        ) ||
      requiredBalance === 0
    ) {
      setIsTippingDisabled(true);
    } else {
      setIsTippingDisabled(false);
    }
    tipValue(
      token.isToken
        ? (
            Number(formatPrice(token.systemRate)) * (avaxPrice?.avax || 0)
          ).toString()
        : token.systemRate,
      sanitizedValue,
    );
  }, [token, tipAmount]);

  useEffect(() => {
    if (open) {
      if (refetch && refetch !== (() => {})) {
        refetch();
      }
    }
  }, [open]);

  useEffect(() => {
    const handleResize = () => {
      const viewportHeight =
        window.visualViewport?.height || window.innerHeight;
      const windowHeight = window.innerHeight;
      if (viewportHeight < windowHeight) {
        setIsKeyboardVisible(true);
      } else {
        setIsKeyboardVisible(false);
      }
    };
    if (typeof visualViewport != "undefined") {
      window.visualViewport?.addEventListener("resize", handleResize);
    }
    return () => {
      if (typeof visualViewport != "undefined") {
        window.visualViewport?.removeEventListener("resize", handleResize);
      }
    };
  }, []);

  const recepients = useMemo(() => {
    if (option === TIP_OPTION.EVERY_SPEAKER) {
      return (speakers ?? []).map((speaker: any) => ({
        id: speaker.userId,
        name: speaker.name ?? speaker.twitterName ?? "",
        avatar: speaker.avatar ?? speaker.twitterPicture ?? "",
        username: speaker.username ?? speaker.twitterHandle ?? "",
        twitterHandle: speaker.twitterHandle ?? speaker.username ?? "",
        twitterPicture: speaker.twitterPicture ?? speaker.avatar ?? "",
        twitterName: speaker.twitterName ?? speaker.name ?? "",
        address: speaker.address ?? speaker.userId ?? "",
      }));
    } else {
      return (participants ?? []).map((participant: any) => ({
        id: participant.id,
        name: participant.name ?? participant.twitterName ?? "",
        avatar: participant.avatar ?? participant.twitterPicture ?? "",
        username: participant.username ?? participant.twitterHandle ?? "",
        twitterHandle: participant.twitterHandle ?? participant.username ?? "",
        twitterPicture: participant.twitterPicture ?? participant.avatar ?? "",
        twitterName: participant.twitterName ?? participant.name ?? "",
        address: participant.address ?? participant.id ?? "",
      }));
    }
  }, [option, speakers, participants]);

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        className={cn(
          "flex h-full w-full flex-grow flex-col bg-dark-bk  px-6 pt-0 backdrop-blur-sm sm:h-fit sm:w-[420px] sm:gap-0 sm:bg-[#1A1A1A] sm:p-6",
          isKeyboardVisible
            ? "flex-grow-0 justify-end pb-[calc(142px+env(safe-area-inset-bottom))]"
            : "flex-grow justify-between",
        )}
      >
        <ArenaDialogHeader
          className="mb-12 sm:mb-8"
          title="Tipping Party"
          showBack={true}
          onBack={step === 2 ? () => setStep(1) : () => setOpen(false)}
        />
        {step === 1 && (
          <TipPartyContent
            options={[
              {
                value: TIP_OPTION.EVERY_PARTICIPANT,
                label: (
                  <>
                    Tip Every
                    <br />
                    Participant
                  </>
                ),
                emoji: "🎧",
              },
              {
                value: TIP_OPTION.EVERY_SPEAKER,
                label: (
                  <>
                    Tip Every
                    <br />
                    Speaker
                  </>
                ),
                emoji: "🎙️",
              },
            ]}
            selected={option}
            setSelected={(v) => setOption(v as Option)}
            infoText={
              <div className="mt-2 text-center text-gray-text">
                <h4 className="text-sm">
                  {option === TIP_OPTION.EVERY_PARTICIPANT
                    ? "Every participant in this stage will receive the same tip."
                    : "Every speaker in this stage will receive the same tip."}
                </h4>
                <p className="text-sm leading-[18px] text-gray-text">
                  {option === TIP_OPTION.EVERY_PARTICIPANT
                    ? "(You will be excluded)"
                    : "(If you are a speaker, you will be excluded)"}
                </p>
              </div>
            }
            onContinue={() => setStep(2)}
            continueLabel="Continue"
          />
        )}
        {step === 2 && (
          <TipFormContent
            recepients={recepients}
            sortedCurrencies={sortedCurrencies}
            setOpen={setOpen}
            distributionMode="equal"
            buttonLabel="Send tips"
            onPartyNotify={async ({ currency, txHash, txData, recipient }) => {
              await postTippingPartyNotify({
                currency,
                txHash,
                txData,
              });
            }}
            onPartyMessage={async ({ message }) => {
              await send(JSON.stringify(message));
            }}
          />
        )}
        <TippingInfoModal />
      </DialogContent>
    </Dialog>
  );
};
