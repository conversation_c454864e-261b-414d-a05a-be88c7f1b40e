import { useEffect, useMemo, useRef, useState } from "react";

import * as RadioGroup from "@radix-ui/react-radio-group";

import { postTippingPartyNotify } from "@/api/client/chat";
import {
  EditTipReceiversModal,
  TipReceiver,
} from "@/app/(messages)/messages/_components/edit-tip-receivers-modal";
import { TippingPartySettingsIcon } from "@/components/icons/tipping-party-settings";
import { TippingInfoModal } from "@/components/tipping-info";
import { TipFormContent } from "@/components/tipping/tip-form-content";
import { ArenaDialogHeader } from "@/components/ui/arena-dialog-header";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useWalletCurrencies } from "@/hooks/use-wallet-currencies";
import { useSharesHoldersInfiniteQuery } from "@/queries";
import { useTippableCurrenciesQuery } from "@/queries/currency-queries";
import { useUser } from "@/stores";
import { cn } from "@/utils/cn";

import { TipPartyContent } from "./TipPartyContent";

enum TipOption {
  EveryUniqueHolder = "EveryUniqueHolder",
  EveryTicket = "EveryTicket",
}

interface TippingPartyModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  ownerUserId: string;
}

export const TippingPartyModal = ({
  open,
  setOpen,
  ownerUserId,
}: TippingPartyModalProps) => {
  const [isEditTipReceiversOpen, setIsEditTipReceiversOpen] = useState(false);
  const [tipReceivers, setTipReceivers] = useState<TipReceiver[]>([]);
  const [step, setStep] = useState<1 | 2>(1);
  const [option, setOption] = useState<TipOption>(TipOption.EveryUniqueHolder);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const { user } = useUser();
  const { data: currenciesData, isLoading: isCurrenciesLoading } =
    useTippableCurrenciesQuery();
  const { sortedCurrencies } = useWalletCurrencies({
    user,
    currenciesData,
    isCurrenciesLoading,
  });
  const {
    data: holdersDataInfinite,
    isLoading: isHoldersLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useSharesHoldersInfiniteQuery({ userId: ownerUserId });
  const holdersData = useMemo(
    () => ({
      holders:
        holdersDataInfinite?.pages.flatMap((page) => page?.holders || []) || [],
    }),
    [holdersDataInfinite],
  );
  useEffect(() => {
    if (!isFetchingNextPage && hasNextPage) {
      fetchNextPage();
    }
  }, [isFetchingNextPage, hasNextPage, holdersDataInfinite]);
  useEffect(() => {
    if (!holdersData) return;
    setTipReceivers(
      holdersData?.holders
        .filter(
          (holder) =>
            holder.traderId !== user?.id && parseFloat(holder.amount) >= 1,
        )
        .map((holder) => ({ ...holder, isChecked: true })),
    );
  }, [holdersData, user?.id]);
  const resetFormRef = useRef<() => void>();
  const handleOpenChange = (open: boolean) => {
    setOpen(open);
    if (!open && resetFormRef.current) {
      resetFormRef.current();
    }
  };
  useEffect(() => {
    const handleResize = () => {
      const viewportHeight =
        window.visualViewport?.height || window.innerHeight;
      const windowHeight = window.innerHeight;
      if (viewportHeight < windowHeight) {
        setIsKeyboardVisible(true);
      } else {
        setIsKeyboardVisible(false);
      }
    };
    if (typeof visualViewport != "undefined") {
      window.visualViewport?.addEventListener("resize", handleResize);
    }
    return () => {
      if (typeof visualViewport != "undefined") {
        window.visualViewport?.removeEventListener("resize", handleResize);
      }
    };
  }, []);
  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent
          className={cn(
            "flex h-full w-full flex-grow flex-col bg-dark-bk  px-6 pt-0 backdrop-blur-sm sm:h-fit sm:w-[420px] sm:gap-0 sm:bg-[#1A1A1A] sm:p-6",
            isKeyboardVisible
              ? "flex-grow-0 justify-end pb-[calc(142px+env(safe-area-inset-bottom))]"
              : "flex-grow justify-between",
          )}
        >
          <ArenaDialogHeader
            title="Tipping Party"
            showBack={true}
            onBack={step === 2 ? () => setStep(1) : undefined}
            rightButton={
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsEditTipReceiversOpen(true)}
                disabled={isHoldersLoading && !holdersData}
              >
                <TippingPartySettingsIcon className="size-4 text-off-white" />
              </Button>
            }
            className="mb-12 sm:mb-8"
          />
          {step === 1 ? (
            <TipPartyContent
              options={[
                {
                  value: TipOption.EveryUniqueHolder,
                  label: (
                    <>
                      Tip Every
                      <br />
                      Unique Holder
                    </>
                  ),
                  emoji: "🙋",
                },
                {
                  value: TipOption.EveryTicket,
                  label: (
                    <>
                      Tip Every
                      <br />
                      Ticket
                    </>
                  ),
                  emoji: <span className="rotate-[15deg]">🎟️</span>,
                },
              ]}
              selected={option}
              setSelected={(v) => setOption(v as TipOption)}
              infoText={
                <>
                  <h4 className="text-sm text-light-gray-text sm:mb-2 sm:text-xs">
                    {option === TipOption.EveryUniqueHolder
                      ? "Every unique holder will receive the same tip, regardless of how many tickets they hold."
                      : "Every single ticket will receive the same tip. For example, if someone holds 5 tickets, they will receive 5 tips."}
                  </h4>
                  <p className="text-sm leading-[18px] sm:text-xs ">
                    If you hold your own ticket, you will be excluded
                  </p>
                </>
              }
              onContinue={() => setStep(2)}
            />
          ) : (
            <TipFormContent
              recepients={tipReceivers
                .filter((tipReceiver) => tipReceiver.isChecked)
                .map((tipReceiver) => tipReceiver.traderUser)}
              setOpen={setOpen}
              sortedCurrencies={sortedCurrencies}
              setResetForm={(fn) => (resetFormRef.current = fn)}
              distributionMode={
                option === TipOption.EveryTicket ? "byTickets" : "equal"
              }
              buttonLabel="Send tips"
              customRecipientsSelector={null}
              onPartyNotify={async ({
                currency,
                txHash,
                txData,
                recipient,
              }) => {
                await postTippingPartyNotify({
                  currency,
                  txHash,
                  txData,
                });
              }}
              className="sm:mb-6"
            />
          )}
          <TippingInfoModal />
        </DialogContent>
      </Dialog>
      <EditTipReceiversModal
        open={isEditTipReceiversOpen}
        setOpen={setIsEditTipReceiversOpen}
        tipReceivers={tipReceivers}
        setTipReceivers={setTipReceivers}
        showTips={option === TipOption.EveryTicket}
      />
    </>
  );
};
