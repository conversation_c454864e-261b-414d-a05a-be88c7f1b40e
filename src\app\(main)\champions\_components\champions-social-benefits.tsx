import {
  Dispatch,
  FC,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { cn } from "@/utils";

interface CardProps {
  url: string;
  header: string;
  description: string;
}

const cards: (CardProps & { id: number })[] = [
  {
    id: 1,
    url: "/icons/champion-mail.svg",
    header: "Voting Rights",
    description:
      "As a Champion, your stake lets you vote on key decisions, directly shaping the future of The Arena.",
  },
  {
    id: 2,
    url: "/icons/pencil.svg",
    header: "Edit and undo your posts.",
    description:
      "Arena Champion allows you to make real-time changes to your content, ensuring it always reflects your intended message.",
  },
  {
    id: 3,
    url: "/icons/check-circle.svg",
    header: "Priority content exposure",
    description:
      "Make sure your posts and comments are prominently displayed and reach a larger audience with <PERSON>.",
  },
  {
    id: 4,
    url: "/icons/paper.svg",
    header: "Sticker Library Access",
    description:
      "Enhance your conversations and express yourself with unique, high-quality stickers only available to Arena Champions.",
  },
  {
    id: 5,
    url: "/icons/champ.svg",
    header: "Special Badge",
    description:
      "Stand out in The Arena with an exclusive Champion Badge, showcasing your elite status and give special access to certain gated activities.",
  },
];

const Card: FC<CardProps> = ({ url, header, description }) => {
  return (
    <div className="flex h-[232px] min-w-[252px] flex-col items-start rounded-[10px] border border-gray-text p-4">
      <img src={url} alt={header} className="mb-4 h-[35px]" />
      <span className="text-[16px] font-semibold">{header}</span>
      <span className="text-[14px] text-light-gray-text">{description}</span>
    </div>
  );
};

interface ChampionsSocialBenefitsProps {
  setIsChampions: Dispatch<SetStateAction<boolean>>;
  isTablet: boolean;
}

export const ChampionsSocialBenefits: FC<ChampionsSocialBenefitsProps> = ({
  setIsChampions,
  isTablet,
}) => {
  const cardContainerRef = useRef<HTMLDivElement>(null);

  const [isLeft, setIsLeft] = useState<boolean>(false);
  const [isRight, setIsRight] = useState<boolean>(true);

  const handleClickOutside = (event: React.MouseEvent<HTMLDivElement>) => {
    const target = event.currentTarget.querySelector(".target");
    if (target && !target.contains(event.target as Node)) {
      setIsChampions(false);
    }
  };

  const scrollCards = (direction: "left" | "right") => {
    if (cardContainerRef.current) {
      const scrollAmountRightFirst = 234;
      const scrollAmountRight = 268;
      const firstScrollAmountLeft = 234;
      const subsequentScrollAmountLeft = 268;
      const { scrollLeft, clientWidth, scrollWidth } = cardContainerRef.current;

      if (direction === "right") {
        if (scrollLeft === 0) {
          cardContainerRef.current.scrollBy({
            left: scrollAmountRightFirst,
            behavior: "smooth",
          });
        } else {
          cardContainerRef.current.scrollBy({
            left: scrollAmountRight,
            behavior: "smooth",
          });
        }

        const isRightBorder =
          scrollLeft + (direction === "right" ? scrollAmountRight : 0) >=
          scrollWidth - clientWidth;
        if (isRightBorder) setIsRight(false);
        else setIsRight(true);

        setIsLeft(true);
      } else {
        if (scrollLeft === scrollWidth - clientWidth) {
          cardContainerRef.current.scrollBy({
            left: -firstScrollAmountLeft,
            behavior: "smooth",
          });
        } else {
          cardContainerRef.current.scrollBy({
            left: -subsequentScrollAmountLeft,
            behavior: "smooth",
          });
        }

        const isLeftBorder =
          scrollLeft -
            (direction === "left" ? subsequentScrollAmountLeft : 0) <=
          0;
        if (isLeftBorder) setIsLeft(false);
        else setIsLeft(true);

        setIsRight(true);
      }
    }
  };

  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (isTablet) e.preventDefault();
    };

    const cardContainer = cardContainerRef.current;
    if (cardContainer) {
      cardContainer.addEventListener("wheel", handleWheel, { passive: false });
    }

    return () => {
      if (cardContainer) {
        cardContainer.removeEventListener("wheel", handleWheel);
      }
    };
  }, []);

  return (
    <div
      className={cn(
        "pb-pwa pointer-events-auto absolute inset-0 z-50 flex h-full justify-center bg-[#000] bg-opacity-60",
        isTablet && "items-center",
      )}
      onClick={handleClickOutside}
    >
      <div className="sticky inset-0 flex h-screen items-center">
        <div
          className={cn(
            "target flex  flex-col gap-2 rounded-[10px] bg-[#000] bg-opacity-80 p-9 backdrop-blur-sm",
            isTablet
              ? "h-[390px] w-[390px]"
              : "fixed bottom-0 left-0 right-0 w-full",
          )}
        >
          <div className="flex items-center gap-4">
            <span className="text-[24px] font-bold">
              Champion Social Benefits
            </span>
          </div>
          <span className="text-[18px] text-light-gray-text">
            A new way to experience the Arena.
          </span>
          <div className="relative">
            <div
              className="hide-scrollbar flex gap-[16px] overflow-x-auto"
              ref={cardContainerRef}
            >
              {cards.map((card) => (
                <Card key={card.id} {...card} />
              ))}
            </div>
            {isTablet && isLeft && (
              <button
                className="absolute left-0 top-1/2 flex h-[26px] w-[26px] -translate-y-1/2 transform items-center justify-center rounded-full bg-[#ea540a] text-white"
                onClick={() => scrollCards("left")}
              >
                <ArrowBackOutlineIcon className="size-5 text-white" />
              </button>
            )}

            {isTablet && isRight && (
              <button
                className="absolute right-0 top-1/2 flex h-[26px] w-[26px] -translate-y-1/2 transform items-center justify-center rounded-full bg-[#ea540a] text-white"
                onClick={() => scrollCards("right")}
              >
                <ArrowBackOutlineIcon className="size-5 rotate-180 text-white" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
