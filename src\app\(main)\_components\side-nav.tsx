"use client";

import { useState } from "react";
import { usePathname, useSelectedLayoutSegments } from "next/navigation";

import { useDynamicContext } from "@dynamic-labs/sdk-react-core";

import { Banner } from "@/components/banner";
import {
  AddOutlineIcon,
  EllipsisHorizontalFilledIcon,
  LiveOutlineIcon,
  LogoIcon,
} from "@/components/icons";
import {
  ChatbubbleOutlineIcon,
  HomeOutlineIcon,
  NotificationsOutlineIcon,
  SearchOutlineIcon,
  WalletOutlineIcon,
} from "@/components/icons-v2";
import { useLivestreamEditor } from "@/components/livestream/hooks/use-livestream-editor";
import { ProgressBarLink } from "@/components/progress-bar";
import { useStageEditor } from "@/components/stages/hooks/use-stage-editor";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useCanPost } from "@/hooks/use-can-post";
import { useCommunityByStrQuery, useUnseenNotificationsQuery } from "@/queries";
import { useHomeStore, useHomeTabStore, usePostStore, useUser } from "@/stores";
import { useFeatureFlagsStore } from "@/stores/flags";
import { useStreamTab } from "@/stores/stream-tab";
import { useTutorialStore } from "@/stores/tutorial";
import { UserFlaggedEnum } from "@/types";
import { CommunityExtended } from "@/types/community";
import { cn } from "@/utils";

import { BannedUserModal } from "../community/_components/community-phase-modals";
import { CommunityQuickBuyModal } from "../community/_components/community-quickBuy-modal";

export const SideNav = () => {
  const { user } = useUser();
  const pathname = usePathname();
  const segments = useSelectedLayoutSegments();
  const tab = useHomeTabStore((state) => state.tab);
  const followingTimelineRef = useHomeStore(
    (state) => state.followingTimelineRef,
  );
  const trendingTimelineRef = useHomeStore(
    (state) => state.trendingTimelineRef,
  );
  const reset = usePostStore((state) => state.reset);
  const showStages = useFeatureFlagsStore((state) => state.stages);
  const setCommunity = usePostStore((state) => state.setCommunity);
  const setReferrer = usePostStore((state) => state.setReferrer);

  const [isBuyModalOpen, setIsBuyModalOpen] = useState(false);
  const [isBanModalOpen, setIsBanModalOpen] = useState(false);

  const isProfileActive = segments[0] === user?.twitterHandle;
  const isBanned = user?.flag === UserFlaggedEnum.SUSPENDED;
  const isTutorialOpen = useTutorialStore((state) => state.isTutorialOpen);
  const isBlurred = isTutorialOpen && pathname === "/" + user?.twitterHandle;

  const { data, isLoading } = useUnseenNotificationsQuery();

  const [_, setStageEditor] = useStageEditor();
  const [__, setLivestreamEditor] = useLivestreamEditor();
  const [streamTab] = useStreamTab();

  const isCommunityPage =
    segments.length === 2 &&
    segments[0] === "community" &&
    Boolean(segments[1]);

  const { data: communityData, isLoading: isCommunityDataLoading } =
    useCommunityByStrQuery(isCommunityPage ? segments[1] : "");

  const showGroupFeedPostButton =
    isCommunityPage && !isCommunityDataLoading && communityData?.community;

  const { setHasEnoughTokens, canPost, isUserBannedFromCommunity } =
    useCanPost(communityData);

  const links = [
    {
      name: "Home",
      href: `/home`,
      isActive: segments.length === 1 && segments[0] === "home",
      icon: HomeOutlineIcon,
      disabled: false,
    },
    {
      name: "Explore",
      href: `/explore`,
      isActive: segments.length === 1 && segments[0] === "explore",
      icon: SearchOutlineIcon,
      disabled: isBanned,
    },
    {
      name: "Notifications",
      href: `/notifications`,
      isActive: segments.length === 1 && segments[0] === "notifications",
      icon: NotificationsOutlineIcon,
      disabled: isBanned,
    },
    ...(showStages
      ? [
          {
            name: "Live",
            href: `/live`,
            isActive: segments.length === 1 && segments[0] === "live",
            icon: LiveOutlineIcon,
            disabled: isBanned,
          },
        ]
      : []),
    // {
    //   name: "Livestreams",
    //   href: `/livestreams`,
    //   isActive: segments.length === 1 && segments[0] === "livestreams",
    //   icon: MicOutlineIcon,
    // },
    {
      name: "Messages",
      href: `/messages`,
      isActive: segments[0] === "messages",
      icon: ChatbubbleOutlineIcon,
      disabled: isBanned,
    },
    {
      name: "Wallet",
      href: `/wallet`,
      isActive: segments.length === 1 && segments[0] === "wallet",
      icon: WalletOutlineIcon,
      disabled: false,
    },
  ];

  const { handleLogOut } = useDynamicContext();

  const handlePostClick = () => {
    if (!showGroupFeedPostButton || (showGroupFeedPostButton && canPost))
      return;
    if (isUserBannedFromCommunity) {
      setIsBanModalOpen(true);
    } else {
      setIsBuyModalOpen(true);
    }
  };

  return (
    <nav
      className={cn(
        "flex w-[88px] select-none flex-col items-end xl:w-[247px]",
      )}
    >
      {isBlurred && <div className="absolute inset-0 z-40 bg-dark-bk/65" />}
      <div className="fixed top-0 h-full xl:w-[247px]">
        <div className="flex h-full flex-col gap-6 p-8 px-4">
          <div
            className={cn(
              "flex size-12 items-center justify-center",
              isBanned && "opacity-20",
            )}
          >
            <LogoIcon className="h-[36px] text-brand-orange" />
          </div>
          {!isBanned && <Banner />}
          <div className="flex flex-col">
            {links.map(
              ({ href, icon: Icon, isActive, name, disabled }, index) => {
                if (isActive) {
                  return (
                    <button
                      key={name + "-side-nav-link"}
                      className={cn(
                        "group py-1.5",
                        isActive ? "text-off-white" : "text-gray-text ",
                        index === 0 && "pt-0",
                        disabled
                          ? "pointer-events-none opacity-20"
                          : "opacity-100",
                      )}
                      onClick={() => {
                        if (segments.length === 1 && segments[0] === "home") {
                          if (tab === "following") {
                            followingTimelineRef.current?.scrollToIndex({
                              index: 0,
                              behavior: "smooth",
                              offset: -300,
                            });
                          } else if (tab === "trending") {
                            trendingTimelineRef.current?.scrollToIndex({
                              index: 0,
                              behavior: "smooth",
                              offset: -300,
                            });
                          }
                        } else {
                          window.scrollTo({
                            top: 0,
                            behavior: "smooth",
                          });
                        }
                      }}
                      tabIndex={disabled ? -1 : 0}
                    >
                      <div className="flex items-center justify-center gap-3 rounded-lg p-[10px] transition-colors group-hover:bg-gray-bg xl:justify-start">
                        <div className="relative">
                          <Icon className="size-6 flex-shrink-0" />
                          {name === "Notifications" &&
                            !isLoading &&
                            data &&
                            data?.count > 0 && (
                              <div className="absolute -right-2 -top-2 flex min-w-5 items-center justify-center rounded bg-dark-gray px-1 py-0.5 text-xs text-off-white">
                                {data.count}
                              </div>
                            )}
                        </div>
                        <span className="hidden text-base font-semibold leading-5 xl:inline">
                          {name}
                        </span>
                      </div>
                    </button>
                  );
                }

                return (
                  <ProgressBarLink
                    href={href}
                    key={name + "-side-nav-link"}
                    className={cn(
                      "group py-1.5",
                      isActive ? "text-off-white" : "text-gray-text ",
                      index === 0 && "pt-0",
                      disabled
                        ? "pointer-events-none opacity-20"
                        : "opacity-100",
                    )}
                    tabIndex={disabled ? -1 : 0}
                  >
                    <div className="flex items-center justify-center gap-3 rounded-lg p-[10px] transition-colors group-hover:bg-gray-bg xl:justify-start">
                      <div className="relative">
                        <Icon className="size-6 flex-shrink-0" />
                        {name === "Notifications" &&
                          !isLoading &&
                          data &&
                          data?.count > 0 && (
                            <div className="absolute -right-2 -top-2 flex min-w-5 items-center justify-center rounded bg-dark-gray px-1 py-0.5 text-xs text-off-white">
                              {data.count}
                            </div>
                          )}
                      </div>
                      <span className="hidden text-base font-semibold leading-5 xl:inline">
                        {name}
                      </span>
                    </div>
                  </ProgressBarLink>
                );
              },
            )}
            <DropdownMenu>
              <DropdownMenuTrigger className="group py-1.5 outline-none">
                <div className="flex items-center justify-center gap-3 rounded-lg p-[10px] text-gray-text transition-colors group-hover:bg-gray-bg xl:justify-start">
                  <EllipsisHorizontalFilledIcon
                    className={cn("size-6 flex-shrink-0 fill-gray-text")}
                  />
                  <span className="hidden text-base font-semibold leading-5 xl:inline">
                    More
                  </span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="start"
                side="top"
                className="hide-scrollbar hidden max-h-[350px] w-[220px] overflow-y-auto sm:block"
                collisionPadding={{
                  bottom: 24,
                }}
              >
                <DropdownMenuItem>
                  <ProgressBarLink href="/create-community">
                    Arena Launch
                  </ProgressBarLink>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <ProgressBarLink
                    href="/referral"
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    Refer & Earn
                  </ProgressBarLink>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <ProgressBarLink
                    href="/bookmarks"
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    Bookmarks
                  </ProgressBarLink>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <ProgressBarLink
                    href="/app-store"
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    Arena App Store
                  </ProgressBarLink>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <ProgressBarLink
                    href="/tokenomics"
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    {`$ARENA Tokenomics`}
                  </ProgressBarLink>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <ProgressBarLink
                    href="/settings"
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    Settings & Support
                  </ProgressBarLink>
                </DropdownMenuItem>

                <DropdownMenuItem asChild>
                  <a
                    href="https://arena.trade/"
                    target="_blank"
                    rel="noreferrer"
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    onClick={(e) => {
                      if (isBanned) {
                        e.preventDefault();
                      }
                    }}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    ArenaBook
                  </a>
                </DropdownMenuItem>
                {user?.isMod && (
                  <DropdownMenuItem asChild>
                    <ProgressBarLink href="/moderation/reports">
                      Moderation
                    </ProgressBarLink>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  onSelect={async () => {
                    await handleLogOut();
                  }}
                >
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <ProgressBarLink
              href={`/` + user?.twitterHandle}
              className={cn(
                "group pt-1.5",
                isProfileActive ? "text-off-white" : "text-gray-text",
                isBanned && "pointer-events-none opacity-20",
              )}
              tabIndex={isBanned ? -1 : 0}
            >
              <div className="flex items-center justify-center gap-3 rounded-lg p-[10px] transition-colors group-hover:bg-gray-bg xl:justify-start">
                <Avatar className="size-6 border border-white xl:border-none">
                  <AvatarImage src={user?.twitterPicture} />
                  <AvatarFallback />
                </Avatar>
                <span className="hidden text-base font-semibold leading-5 xl:inline">
                  Profile
                </span>
              </div>
            </ProgressBarLink>
            {segments.length === 1 &&
            segments[0] === "live" &&
            streamTab === "stages" &&
            showStages ? (
              <>
                <Button
                  className="mx-auto mt-8 size-10 p-0 xl:w-full"
                  onClick={() => {
                    setStageEditor({ composeStage: true });
                  }}
                >
                  <AddOutlineIcon className="size-10 flex-shrink-0 p-[10px] text-white xl:hidden" />
                  <span className="hidden xl:inline">Create Your Stage</span>
                </Button>
              </>
            ) : segments.length === 1 &&
              segments[0] === "live" &&
              streamTab === "livestreams" ? (
              <Button
                className="mx-auto mt-8 size-10 p-0 xl:w-full"
                onClick={() => {
                  setLivestreamEditor({ composeLivestream: true });
                }}
              >
                <AddOutlineIcon className="size-10 flex-shrink-0 p-[10px] text-white xl:hidden" />
                <span className="hidden xl:inline">Create Your Livestream</span>
              </Button>
            ) : (
              <Button
                className="mx-auto mt-8 size-10 p-0 xl:w-full"
                asChild
                onClick={handlePostClick}
              >
                {showGroupFeedPostButton && !canPost ? (
                  <div
                    className={cn(
                      "cursor-pointer",
                      isBanned && "pointer-events-none opacity-20",
                    )}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    <AddOutlineIcon className="size-10 flex-shrink-0 p-[10px] text-white xl:hidden" />
                    <span className="hidden xl:inline">
                      {communityData?.community.ticker
                        ? `Post on $${communityData?.community.ticker}`
                        : "Post"}
                    </span>
                  </div>
                ) : (
                  <ProgressBarLink
                    href="/compose/post"
                    onClick={() => {
                      reset();
                      if (isProfileActive)
                        setReferrer(`/${user.twitterHandle}`);
                      if (showGroupFeedPostButton) {
                        setCommunity(
                          communityData?.community as CommunityExtended,
                        );
                        setReferrer(
                          `/community/${communityData?.community.contractAddress}`,
                        );
                      }
                    }}
                    className={cn(isBanned && "pointer-events-none opacity-20")}
                    tabIndex={isBanned ? -1 : 0}
                  >
                    <AddOutlineIcon className="size-10 flex-shrink-0 p-[10px] text-white xl:hidden" />
                    <span className="hidden xl:inline">
                      {showGroupFeedPostButton
                        ? communityData?.community.ticker
                          ? `Post on $${communityData?.community.ticker}`
                          : "Post"
                        : "Post"}
                    </span>
                  </ProgressBarLink>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>
      {isBuyModalOpen && communityData && (
        <CommunityQuickBuyModal
          community={communityData.community}
          open={isBuyModalOpen}
          setOpen={setIsBuyModalOpen}
          setHasEnoughTokens={setHasEnoughTokens}
        />
      )}
      {isBanModalOpen && (
        <BannedUserModal open={isBanModalOpen} setOpen={setIsBanModalOpen} />
      )}
    </nav>
  );
};
