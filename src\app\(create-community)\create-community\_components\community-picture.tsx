"use client";

import { useRef, useState } from "react";

import * as DialogPrimitive from "@radix-ui/react-dialog";
import { AnimatePresence, motion, useMotionTemplate } from "framer-motion";
import <PERSON><PERSON><PERSON>, { Area } from "react-easy-crop";
import { v4 as uuid } from "uuid";

import { ArrowBackOutlineIcon } from "@/components/icons";
import { AddPhotoIcon } from "@/components/icons-v2/add-photo";
import { toast } from "@/components/toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogOverlay,
  DialogPortal,
} from "@/components/ui/dialog";
import { upload } from "@/utils";
import getCroppedImg from "@/utils/crop-image";

interface Props {
  initialPictureUrl?: string;
  onPictureUpload: (pictureUrl: string) => void;
}
export const CommunityPicture = ({
  initialPictureUrl,
  onPictureUpload: handlePictureUpload,
}: Props) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [open, setOpen] = useState(false);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [previewURL, setPreviewURL] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const width = useMotionTemplate`${progress}%`;
  const [pictureUrl, setPictureUrl] = useState<string | undefined>(
    initialPictureUrl,
  );

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.danger("Uploaded image file cannot exceed 5 MB");
        return;
      }
      setOpen(true);
      const previewURL = URL.createObjectURL(file);
      setPreviewURL(previewURL);
    }
  };
  const resetUploading = () => {
    setIsUploading(false);
    setProgress(0);
    setPreviewURL(null);
    if (inputRef.current) inputRef.current.value = "";
  };
  const handleApply = async () => {
    if (!croppedAreaPixels || !previewURL) return;
    const blob = await getCroppedImg(previewURL, croppedAreaPixels);
    if (!blob) return;
    // @ts-expect-error
    blob.name = "image.jpeg";
    // @ts-expect-error
    blob.lastModified = new Date();
    const file = new File([blob], `${uuid()}.jpg`, { type: "image/jpeg" });
    setIsUploading(true);
    try {
      const res = await upload({
        file,
        onProgressChange: (progress) => {
          setProgress(progress);
        },
      });
      setPictureUrl(res.url);
      handlePictureUpload(res.url);
      resetUploading();
      setOpen(false);
    } catch (error) {
      toast.danger("File upload failed");
      resetUploading();
      setOpen(false);
    }
  };
  return (
    <>
      <div className="relative">
        <div className="absolute -bottom-4 right-6">
          <input
            ref={inputRef}
            style={{ display: "none" }}
            type="file"
            accept="image/*"
            onChange={handleChange}
          />
        </div>
      </div>
      {!pictureUrl && (
        <AddPhotoIcon
          className="cursor-pointer"
          onClick={() => {
            !isUploading && inputRef.current && inputRef.current.click();
          }}
        />
      )}
      {pictureUrl && (
        <Avatar
          className="mt-3 size-[92px] cursor-pointer border border-[#E8E8E8]"
          onClick={() => {
            !isUploading && inputRef.current && inputRef.current.click();
          }}
        >
          <AvatarImage src={pictureUrl} />
          <AvatarFallback />
        </Avatar>
      )}
      <Dialog
        open={open}
        onOpenChange={(open) => {
          setOpen(open);
          !open && resetUploading();
        }}
      >
        <DialogPortal>
          <DialogOverlay className="z-[100]" />
          <DialogPrimitive.DialogTitle className="hidden" />
          <DialogPrimitive.Content className="pt-pwa fixed left-[50%] top-[50%] z-[200] flex h-full w-full max-w-[524px] translate-x-[-50%] translate-y-[-50%] flex-col overflow-hidden bg-dark-bk px-0 pb-0 shadow-lg backdrop-blur-sm  duration-200  sm:rounded-[20px] sm:border sm:border-[rgba(59,59,59,0.30)] sm:bg-[rgba(15,15,15,0.90)] sm:backdrop-blur-sm lg:h-[500px]">
            <div className="sticky inset-x-0 z-[300] flex justify-between bg-dark-bk bg-opacity-60 py-1 pl-6 pr-3">
              <DialogClose
                className="z-[400] cursor-pointer p-2"
                onClick={() => {
                  if (inputRef.current) inputRef.current.value = "";
                  setOpen(false);
                }}
              >
                <ArrowBackOutlineIcon className="size-5 text-off-white" />
              </DialogClose>
              <Button
                variant="ghost"
                className="z-[400]"
                onClick={handleApply}
                disabled={isUploading}
              >
                Apply
              </Button>
            </div>
            <div className=" relative flex flex-1 items-center justify-center overflow-auto ">
              <Cropper
                showGrid={false}
                maxZoom={4}
                image={previewURL || undefined}
                crop={crop}
                zoom={zoom}
                aspect={1}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={(_, croppedAreaPixels) => {
                  setCroppedAreaPixels(croppedAreaPixels);
                }}
              />
            </div>
            <AnimatePresence>
              {isUploading && (
                <motion.div
                  style={{ width }}
                  exit={{ opacity: 0 }}
                  className="absolute bottom-0 left-0 h-1 min-w-4 bg-brand-orange"
                />
              )}
            </AnimatePresence>
          </DialogPrimitive.Content>
        </DialogPortal>
      </Dialog>
      <p
        onClick={() => {
          !isUploading && inputRef.current && inputRef.current.click();
        }}
        className="mb-4 mt-2 cursor-pointer text-xs text-off-white"
      >
        ADD A PHOTO
      </p>
    </>
  );
};
