interface UprisingBase {
  points: number;
  referralsPercent: number;
  tradingPercent: number;
  engagementPercent: number;
}

export interface UprisingUser {
  position: number;
  id: string;
  twitterHandle: string;
  userName: string;
  userPicture: string;
  points: number;
}

export interface UprisingUserDetailed {
  rank: number;
  id: string;
  userHandle: string;
  userName: string;
  userPicture: string;
  points: number;
  referralsPercent: number;
  tradingPercent: number;
  engagementPercent: number;
  badgesBonus: number;
  ownedBadges: string[];
  availableBadges: string[];
}

export interface UprisingMilestone {
  text: string;
  icon_name: string;
  display_name: string;
  milestone_number: number;
  milestone_max_points: number;
  description: string;
}

export interface UprisingQuestCard {
  id: string;
  milestones: UprisingMilestone[];
  quest_type: "milestones" | "event";
  sub_milestones: boolean;
  binary_progress: boolean;
  current_milestone_number: number;
  milestone_current_points: number;
  startDate?: string;
  endDate?: string;
}

export interface UprisingBadge {
  badgeId: number;
  totalPoints: number;
  referralsPercent: number;
  engagementPercent: number;
  tradingPercent: number;
  badgeImageUrl: string;
  totalMemberCount: number; // Use the map to get the total members
  badgeName: string;
}
