"use client";

import { memo } from "react";

import { IS_IPHONE } from "@/utils/window-environment";

import { CustomVideoPlayer } from "./custom-video-player";
import { VideoPlayerIOS } from "./video-player-ios";

interface VideoPlayerProps {
  type?: "full" | "mini";
  device?: "desktop" | "mobile";
}

export const VideoPlayer = memo(
  ({ type = "full", device }: VideoPlayerProps) => {
    if (IS_IPHONE) {
      return <VideoPlayerIOS type={type} device={device} />;
    }

    return <CustomVideoPlayer type={type} device={device} />;
  },
);

VideoPlayer.displayName = "VideoPlayer";
